# 模块1：Go语言高级并发编程

## 模块概述

**学习目标**：深入掌握Go并发编程的高级特性和最佳实践

**学习时长**：2-3周

**前置技能要求**：
- 理解基本的Goroutine和Channel概念
- 能够编写简单的并发程序
- 掌握Go语言基础语法

## 模块结构

本模块分为6个章节，每个章节专注于并发编程的特定方面：

### [第1章：Goroutine调度器原理](./chapter_01/README.md)
- GMP模型深入理解
- 抢占式调度机制
- 工作窃取算法
- 调度器性能优化

### [第2章：Channel高级模式](./chapter_02/README.md)
- select多路复用
- 超时控制机制
- 管道模式设计
- 扇入扇出模式

### [第3章：sync包深入应用](./chapter_03/README.md)
- Mutex和RWMutex详解
- WaitGroup和Once使用
- Pool对象池优化
- Cond条件变量

### [第4章：原子操作与内存模型](./chapter_04/README.md)
- atomic包详细使用
- 内存屏障机制
- CAS操作原理
- Go内存模型理解

### [第5章：并发安全设计](./chapter_05/README.md)
- 竞态条件检测
- 数据竞争避免
- 锁优化策略
- 并发模式最佳实践

### [第6章：Context包应用](./chapter_06/README.md)
- 请求链路控制
- 超时传播机制
- 取消信号处理
- Context最佳实践

## 重点难点

⚠️ **常见陷阱**：
- Goroutine泄漏和资源管理
- Channel死锁和阻塞问题
- 共享内存的并发安全
- 锁竞争和性能瓶颈

## 实践项目

### 项目1：高并发Web爬虫（1周）
- 支持10000+并发请求
- 实现请求限流和重试机制
- 使用Context控制超时和取消
- 内存使用优化和Goroutine池管理

### 项目2：分布式任务队列（1-2周）
- 基于Channel的任务分发
- 工作者池动态扩缩容
- 任务优先级和延迟执行
- 失败重试和死信队列

## 技能验收标准

完成本模块学习后，您应该能够：

- [ ] 能够设计高效的并发架构
- [ ] 掌握Goroutine和Channel的高级用法
- [ ] 能够诊断和解决并发问题
- [ ] 理解Go内存模型和同步原语
- [ ] 编写高性能的并发程序
- [ ] 避免常见的并发编程陷阱

## 学习建议

1. **理论与实践结合**：每学完一个概念立即编写代码验证
2. **循序渐进**：严格按章节顺序学习，确保前置知识掌握
3. **多做练习**：每章的练习题都要认真完成
4. **源码阅读**：阅读Go标准库中并发相关的源码
5. **性能测试**：使用benchmark测试并发程序性能

## 时间估算

- **理论学习**：40小时
- **项目实践**：60小时
- **总计**：100小时

## 参考资料

- [Go官方文档 - Concurrency](https://golang.org/doc/effective_go.html#concurrency)
- [Go Memory Model](https://golang.org/ref/mem)
- [The Go Programming Language - Chapter 8&9](https://www.gopl.io/)
- [Go语言高级编程 - 并发编程](https://chai2010.cn/advanced-go-programming-book/)

## 使用说明

### 学习路径
1. **按顺序学习各章节**：严格按照章节顺序进行学习，确保前置知识掌握
2. **理论与实践结合**：每学完一个概念立即进行代码实践
3. **完成所有练习**：练习题是检验学习效果的重要手段
4. **记录学习笔记**：使用提供的笔记模板记录学习过程
5. **项目实战验证**：通过两个综合项目验证学习成果

### 代码运行
```bash
# 进入模块目录
cd module_01

# 运行示例代码
go run chapter_01/examples/gmp_demo.go

# 运行性能测试
go test -bench=. chapter_01/examples/

# 启用竞态检测
go run -race chapter_01/examples/gmp_demo.go
```

### 学习进度跟踪
使用 [PROGRESS.md](./PROGRESS.md) 文件跟踪学习进度，记录完成情况和学习心得。

## 文件结构说明

```
module_01/
├── README.md              # 模块总览和使用说明
├── PROGRESS.md            # 学习进度跟踪
├── go.mod                 # Go模块依赖管理
├── chapter_01/            # 第1章：Goroutine调度器原理
│   ├── README.md          # 章节说明和学习目标
│   ├── examples/          # 代码示例
│   │   ├── gmp_demo.go    # GMP模型演示
│   │   └── work_stealing.go # 工作窃取演示
│   ├── exercises/         # 练习题
│   │   └── 01_gmp_observation.md
│   ├── notes/             # 学习笔记模板
│   │   └── learning_notes_template.md
│   └── resources/         # 相关资源文件
├── chapter_02/            # 第2章：Channel高级模式
│   ├── README.md
│   ├── examples/
│   │   └── select_basics.go # Select基础用法
│   ├── exercises/
│   ├── notes/
│   └── resources/
├── ... (其他章节结构相同)
└── projects/              # 综合实践项目
    └── README.md          # 项目说明和要求
```

---

**下一步**：开始学习 [第1章：Goroutine调度器原理](./chapter_01/README.md)
