# 第1章：Goroutine调度器原理

## 章节概述

**学习目标**：深入理解Go语言的Goroutine调度器工作原理，掌握GMP模型、抢占式调度和工作窃取算法

**学习时长**：3-4天

**前置知识**：
- 理解基本的Goroutine概念
- 熟悉Go语言基础语法
- 了解操作系统线程和进程概念

## 核心知识点

### 1. GMP模型深入理解

Go语言的调度器采用GMP模型，这是理解Go并发机制的核心：

#### G (Goroutine)
- **定义**：Go语言中的轻量级线程，由Go运行时管理
- **特点**：
  - 初始栈大小仅2KB，可动态扩展
  - 创建和销毁成本极低
  - 由Go运行时调度，而非操作系统

#### M (Machine)
- **定义**：操作系统线程的抽象，真正执行代码的实体
- **特点**：
  - 与操作系统线程一一对应
  - 数量受GOMAXPROCS限制
  - 负责执行Goroutine

#### P (Processor)
- **定义**：逻辑处理器，连接G和M的桥梁
- **特点**：
  - 数量等于GOMAXPROCS设置值
  - 维护本地Goroutine队列
  - 实现工作窃取算法

**代码示例**：[GMP模型演示](./examples/gmp_demo.go)

### 2. 抢占式调度机制

Go 1.14引入了基于信号的抢占式调度，解决了长时间运行的Goroutine无法被抢占的问题：

#### 协作式抢占（Go 1.14之前）
- 依赖函数调用时的调度点
- 长时间运行的循环可能导致调度器饥饿
- 在垃圾回收时可能出现问题

#### 抢占式调度（Go 1.14+）
- 基于信号的异步抢占
- 解决了紧密循环的抢占问题
- 提高了调度器的公平性

**代码示例**：[抢占式调度演示](./examples/preemptive_scheduling.go)

### 3. 工作窃取算法

工作窃取是Go调度器实现负载均衡的核心算法：

#### 基本原理
1. 每个P维护本地Goroutine队列
2. 当本地队列为空时，从其他P的队列"窃取"Goroutine
3. 优先从队列尾部窃取，减少竞争

#### 优势
- 减少全局锁竞争
- 提高CPU利用率
- 实现动态负载均衡

**代码示例**：[工作窃取演示](./examples/work_stealing.go)

### 4. 调度器性能优化

#### 关键优化技术
1. **本地队列优先**：减少全局队列访问
2. **批量操作**：减少锁竞争
3. **自旋等待**：避免频繁的线程切换
4. **网络轮询器集成**：高效处理I/O操作

#### 性能调优参数
- `GOMAXPROCS`：设置P的数量
- `GODEBUG=schedtrace=1000`：调度器跟踪
- `go tool trace`：详细的执行跟踪

**代码示例**：[调度器性能测试](./examples/scheduler_benchmark.go)

## 重点难点

### ⚠️ 常见误区

1. **Goroutine不等于线程**
   - Goroutine是用户态的轻量级线程
   - 多个Goroutine可能运行在同一个操作系统线程上

2. **GOMAXPROCS的设置**
   - 不是越大越好，通常设置为CPU核心数
   - 过大可能导致上下文切换开销增加

3. **调度器的公平性**
   - Go调度器不保证绝对公平
   - 长时间运行的Goroutine可能被抢占

### 🔍 深入理解要点

1. **G-M-P的协作关系**
   - P是调度的核心，连接G和M
   - M可以与不同的P绑定
   - G在P的本地队列中等待调度

2. **调度时机**
   - 系统调用
   - 网络I/O
   - 垃圾回收
   - 主动让出（runtime.Gosched）

3. **性能影响因素**
   - Goroutine创建频率
   - 系统调用频率
   - 锁竞争程度
   - GC压力

## 实践练习

### 练习1：GMP模型观察
**目标**：通过实际代码观察GMP模型的工作过程
**文件**：[GMP观察练习](./exercises/01_gmp_observation.md)

### 练习2：调度器性能分析
**目标**：分析不同场景下调度器的性能表现
**文件**：[调度器分析练习](./exercises/02_scheduler_analysis.md)

## 学习资源

### 官方文档
- [Go调度器设计文档](https://docs.google.com/document/d/1TTj4T2JO42uD5ID9e89oa0sLKhJYD0Y_kqxDv3I3XMw)
- [Go运行时源码](https://github.com/golang/go/tree/master/src/runtime)

### 深度文章
- [Go调度器原理详解](./resources/gmp_architecture.md)
- [参考资料汇总](./resources/references.md)

### 调试工具
```bash
# 查看调度器跟踪信息
GODEBUG=schedtrace=1000 go run main.go

# 生成执行跟踪文件
go run -trace=trace.out main.go
go tool trace trace.out

# 查看Goroutine信息
go tool pprof http://localhost:6060/debug/pprof/goroutine
```

## 学习检查点

完成本章学习后，您应该能够：

- [ ] 清楚解释GMP模型的工作原理
- [ ] 理解抢占式调度的必要性和实现机制
- [ ] 掌握工作窃取算法的基本原理
- [ ] 能够分析和优化Goroutine调度性能
- [ ] 使用调试工具观察调度器行为
- [ ] 避免常见的调度器相关问题

## 下一步

完成本章学习后，请继续学习：
- [第2章：Channel高级模式](../chapter_02/README.md)

---

**学习建议**：
1. 先理解理论概念，再通过代码验证
2. 使用调试工具观察实际的调度行为
3. 完成所有练习题，加深理解
4. 记录学习笔记，使用提供的[笔记模板](./notes/learning_notes_template.md)
