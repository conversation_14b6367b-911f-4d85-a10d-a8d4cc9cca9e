// 工作窃取算法演示程序
// 本程序展示Go调度器的工作窃取机制
package main

import (
	"fmt"
	"math/rand"
	"runtime"
	"sync"
	"sync/atomic"
	"time"
)

// 工作统计
type WorkStats struct {
	totalTasks    int64
	completedTasks int64
	processorWork []*ProcessorStats
}

// 处理器统计
type ProcessorStats struct {
	id           int
	localTasks   int64
	stolenTasks  int64
	executedTasks int64
}

func main() {
	fmt.Println("=== Go调度器工作窃取算法演示 ===")
	
	// 演示1：基本工作窃取机制
	fmt.Println("\n1. 基本工作窃取演示")
	basicWorkStealingDemo()
	
	// 演示2：负载不均衡场景
	fmt.Println("\n2. 负载不均衡场景演示")
	unbalancedWorkloadDemo()
	
	// 演示3：工作窃取效率对比
	fmt.Println("\n3. 工作窃取效率对比")
	workStealingEfficiencyDemo()
	
	// 演示4：动态负载均衡
	fmt.Println("\n4. 动态负载均衡演示")
	dynamicLoadBalancingDemo()
}

// 基本工作窃取演示
func basicWorkStealingDemo() {
	numProcs := runtime.GOMAXPROCS(0)
	numTasks := 1000
	
	fmt.Printf("处理器数量: %d\n", numProcs)
	fmt.Printf("任务总数: %d\n", numTasks)
	
	// 创建任务队列
	tasks := make(chan int, numTasks)
	for i := 0; i < numTasks; i++ {
		tasks <- i
	}
	close(tasks)
	
	// 统计信息
	var completedTasks int64
	var wg sync.WaitGroup
	
	start := time.Now()
	
	// 启动工作者Goroutine
	for i := 0; i < numProcs*2; i++ {
		wg.Add(1)
		go worker(i, tasks, &completedTasks, &wg)
	}
	
	wg.Wait()
	duration := time.Since(start)
	
	fmt.Printf("完成任务数: %d\n", atomic.LoadInt64(&completedTasks))
	fmt.Printf("执行时间: %v\n", duration)
	fmt.Printf("平均每个任务: %v\n", duration/time.Duration(numTasks))
}

// 工作者函数
func worker(id int, tasks <-chan int, completedTasks *int64, wg *sync.WaitGroup) {
	defer wg.Done()
	
	localCompleted := 0
	
	for task := range tasks {
		// 模拟工作负载
		workload := rand.Intn(100) + 50 // 50-150微秒的随机工作量
		time.Sleep(time.Duration(workload) * time.Microsecond)
		
		localCompleted++
		
		// 偶尔打印进度
		if task%100 == 0 {
			fmt.Printf("Worker %d 完成任务 %d\n", id, task)
		}
	}
	
	atomic.AddInt64(completedTasks, int64(localCompleted))
	fmt.Printf("Worker %d 总共完成 %d 个任务\n", id, localCompleted)
}

// 负载不均衡场景演示
func unbalancedWorkloadDemo() {
	fmt.Println("创建不均衡的工作负载...")
	
	// 创建不同类型的任务
	lightTasks := make(chan Task, 500)
	heavyTasks := make(chan Task, 100)
	
	// 轻量任务
	for i := 0; i < 500; i++ {
		lightTasks <- Task{
			ID:       i,
			WorkTime: time.Millisecond,
			Type:     "light",
		}
	}
	close(lightTasks)
	
	// 重量任务
	for i := 0; i < 100; i++ {
		heavyTasks <- Task{
			ID:       i + 500,
			WorkTime: 10 * time.Millisecond,
			Type:     "heavy",
		}
	}
	close(heavyTasks)
	
	var wg sync.WaitGroup
	stats := &WorkStats{
		processorWork: make([]*ProcessorStats, runtime.GOMAXPROCS(0)),
	}
	
	// 初始化统计
	for i := range stats.processorWork {
		stats.processorWork[i] = &ProcessorStats{id: i}
	}
	
	start := time.Now()
	
	// 启动轻量任务处理器
	for i := 0; i < 4; i++ {
		wg.Add(1)
		go taskWorker(i, lightTasks, stats.processorWork[i%len(stats.processorWork)], &wg)
	}
	
	// 启动重量任务处理器
	for i := 0; i < 2; i++ {
		wg.Add(1)
		go taskWorker(i+4, heavyTasks, stats.processorWork[(i+4)%len(stats.processorWork)], &wg)
	}
	
	wg.Wait()
	duration := time.Since(start)
	
	fmt.Printf("总执行时间: %v\n", duration)
	printWorkStats(stats)
}

// 任务结构
type Task struct {
	ID       int
	WorkTime time.Duration
	Type     string
}

// 任务工作者
func taskWorker(workerID int, tasks <-chan Task, stats *ProcessorStats, wg *sync.WaitGroup) {
	defer wg.Done()
	
	for task := range tasks {
		// 执行任务
		time.Sleep(task.WorkTime)
		
		atomic.AddInt64(&stats.executedTasks, 1)
		
		if task.ID%50 == 0 {
			fmt.Printf("Worker %d 完成 %s 任务 %d\n", workerID, task.Type, task.ID)
		}
	}
}

// 工作窃取效率对比
func workStealingEfficiencyDemo() {
	fmt.Println("比较有无工作窃取的性能差异...")
	
	// 测试1：模拟无工作窃取（固定分配）
	fmt.Println("\n测试1：固定任务分配（无工作窃取）")
	fixedAllocationTime := testFixedAllocation()
	
	// 测试2：模拟工作窃取
	fmt.Println("\n测试2：动态任务分配（模拟工作窃取）")
	workStealingTime := testWorkStealing()
	
	fmt.Printf("\n性能对比：\n")
	fmt.Printf("固定分配时间: %v\n", fixedAllocationTime)
	fmt.Printf("工作窃取时间: %v\n", workStealingTime)
	fmt.Printf("性能提升: %.2f%%\n", 
		float64(fixedAllocationTime-workStealingTime)/float64(fixedAllocationTime)*100)
}

// 测试固定分配
func testFixedAllocation() time.Duration {
	numWorkers := runtime.GOMAXPROCS(0)
	tasksPerWorker := 100
	
	var wg sync.WaitGroup
	start := time.Now()
	
	for i := 0; i < numWorkers; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			
			// 每个worker处理固定数量的任务
			for j := 0; j < tasksPerWorker; j++ {
				// 模拟不均匀的工作负载
				workTime := time.Duration(rand.Intn(1000)+500) * time.Microsecond
				if workerID == 0 {
					// 第一个worker的任务更重
					workTime *= 3
				}
				time.Sleep(workTime)
			}
		}(i)
	}
	
	wg.Wait()
	return time.Since(start)
}

// 测试工作窃取
func testWorkStealing() time.Duration {
	numWorkers := runtime.GOMAXPROCS(0)
	totalTasks := numWorkers * 100
	
	// 共享任务队列
	tasks := make(chan int, totalTasks)
	for i := 0; i < totalTasks; i++ {
		tasks <- i
	}
	close(tasks)
	
	var wg sync.WaitGroup
	start := time.Now()
	
	for i := 0; i < numWorkers; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			
			for task := range tasks {
				// 模拟不均匀的工作负载
				workTime := time.Duration(rand.Intn(1000)+500) * time.Microsecond
				if task < 100 {
					// 前100个任务更重
					workTime *= 3
				}
				time.Sleep(workTime)
			}
		}(i)
	}
	
	wg.Wait()
	return time.Since(start)
}

// 动态负载均衡演示
func dynamicLoadBalancingDemo() {
	fmt.Println("演示动态负载均衡...")
	
	// 创建多个本地队列模拟P的本地队列
	numProcessors := runtime.GOMAXPROCS(0)
	localQueues := make([]chan int, numProcessors)
	
	for i := range localQueues {
		localQueues[i] = make(chan int, 100)
	}
	
	// 不均匀地分配初始任务
	for i := 0; i < 500; i++ {
		// 大部分任务分配给第一个队列
		queueIndex := 0
		if i > 400 {
			queueIndex = i % numProcessors
		}
		
		select {
		case localQueues[queueIndex] <- i:
		default:
			// 队列满了，分配给其他队列
			localQueues[i%numProcessors] <- i
		}
	}
	
	// 关闭所有队列
	for _, queue := range localQueues {
		close(queue)
	}
	
	var wg sync.WaitGroup
	var totalProcessed int64
	
	start := time.Now()
	
	// 启动处理器
	for i := 0; i < numProcessors; i++ {
		wg.Add(1)
		go dynamicProcessor(i, localQueues, &totalProcessed, &wg)
	}
	
	wg.Wait()
	duration := time.Since(start)
	
	fmt.Printf("总处理任务数: %d\n", atomic.LoadInt64(&totalProcessed))
	fmt.Printf("总执行时间: %v\n", duration)
}

// 动态处理器（模拟工作窃取）
func dynamicProcessor(id int, queues []chan int, totalProcessed *int64, wg *sync.WaitGroup) {
	defer wg.Done()
	
	localProcessed := 0
	stolen := 0
	
	// 首先处理本地队列
	for task := range queues[id] {
		time.Sleep(time.Millisecond) // 模拟工作
		localProcessed++
	}
	
	// 本地队列空了，尝试从其他队列窃取
	for i := 0; i < len(queues); i++ {
		if i == id {
			continue // 跳过自己的队列
		}
		
		// 尝试从其他队列窃取任务
		for {
			select {
			case task, ok := <-queues[i]:
				if !ok {
					break // 队列已关闭
				}
				time.Sleep(time.Millisecond) // 模拟工作
				stolen++
			default:
				goto nextQueue // 队列为空，尝试下一个
			}
		}
		nextQueue:
	}
	
	total := localProcessed + stolen
	atomic.AddInt64(totalProcessed, int64(total))
	
	fmt.Printf("处理器 %d: 本地任务=%d, 窃取任务=%d, 总计=%d\n", 
		id, localProcessed, stolen, total)
}

// 打印工作统计
func printWorkStats(stats *WorkStats) {
	fmt.Println("\n工作统计:")
	for _, procStats := range stats.processorWork {
		if procStats.executedTasks > 0 {
			fmt.Printf("处理器 %d: 执行任务=%d\n", 
				procStats.id, procStats.executedTasks)
		}
	}
}

/*
运行示例：
go run work_stealing.go

使用调度器跟踪运行：
GODEBUG=schedtrace=1000 go run work_stealing.go

这将显示详细的调度器信息，包括：
- 每个P的本地队列长度
- 全局队列长度
- 运行中的M数量
- 空闲的P数量

预期输出将展示工作窃取算法如何提高负载均衡和整体性能。
*/
