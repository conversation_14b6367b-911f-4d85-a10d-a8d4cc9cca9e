// 抢占式调度演示程序
// 本程序展示Go 1.14+的抢占式调度机制
package main

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"sync/atomic"
	"time"
)

func main() {
	fmt.Println("=== Go抢占式调度演示 ===")
	fmt.Printf("Go版本: %s\n", runtime.Version())
	fmt.Printf("GOMAXPROCS: %d\n", runtime.GOMAXPROCS(0))

	// 演示1：协作式调度的问题（Go 1.14之前的行为）
	fmt.Println("\n1. 协作式调度问题演示")
	cooperativeSchedulingIssue()

	// 演示2：抢占式调度的解决方案
	fmt.Println("\n2. 抢占式调度解决方案")
	preemptiveSchedulingSolution()

	// 演示3：不同类型的抢占场景
	fmt.Println("\n3. 抢占场景分析")
	preemptionScenarios()

	// 演示4：抢占对性能的影响
	fmt.Println("\n4. 抢占性能影响分析")
	preemptionPerformanceImpact()
}

// 协作式调度问题演示
func cooperativeSchedulingIssue() {
	fmt.Println("创建一个CPU密集型的Goroutine...")

	var counter int64
	var otherGoroutineRan int64

	// 启动一个CPU密集型的Goroutine
	go func() {
		// 紧密循环，在Go 1.14之前可能不会被抢占
		for i := 0; i < 1000000000; i++ {
			atomic.AddInt64(&counter, 1)

			// 注释掉下面这行来观察协作式调度的问题
			// runtime.Gosched() // 主动让出CPU
		}
		fmt.Println("CPU密集型Goroutine完成")
	}()

	// 启动其他Goroutine来测试是否能得到调度
	var wg sync.WaitGroup
	for i := 0; i < 3; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			// 等待一小段时间，看是否能得到调度机会
			time.Sleep(100 * time.Millisecond)
			atomic.AddInt64(&otherGoroutineRan, 1)
			fmt.Printf("其他Goroutine %d 得到了调度机会\n", id)
		}(i)
	}

	// 等待一段时间观察
	time.Sleep(500 * time.Millisecond)

	fmt.Printf("计数器值: %d\n", atomic.LoadInt64(&counter))
	fmt.Printf("其他Goroutine运行次数: %d\n", atomic.LoadInt64(&otherGoroutineRan))

	wg.Wait()
}

// 抢占式调度解决方案演示
func preemptiveSchedulingSolution() {
	fmt.Println("演示抢占式调度如何解决调度公平性问题...")

	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	var fairnessCounter [4]int64
	var wg sync.WaitGroup

	// 启动多个不同类型的Goroutine
	for i := 0; i < 4; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			switch id {
			case 0:
				// CPU密集型任务
				cpuIntensiveTask(ctx, &fairnessCounter[id])
			case 1:
				// I/O密集型任务
				ioIntensiveTask(ctx, &fairnessCounter[id])
			case 2:
				// 混合型任务
				mixedTask(ctx, &fairnessCounter[id])
			case 3:
				// 短时间任务
				shortTask(ctx, &fairnessCounter[id])
			}
		}(i)
	}

	// 监控调度公平性
	go monitorFairness(ctx, &fairnessCounter)

	wg.Wait()

	fmt.Println("\n最终统计:")
	for i, count := range fairnessCounter {
		fmt.Printf("Goroutine %d 执行次数: %d\n", i, count)
	}
}

// CPU密集型任务
func cpuIntensiveTask(ctx context.Context, counter *int64) {
	for {
		select {
		case <-ctx.Done():
			return
		default:
			// CPU密集型计算
			sum := 0
			for i := 0; i < 100000; i++ {
				sum += i * i
			}
			atomic.AddInt64(counter, 1)
		}
	}
}

// I/O密集型任务
func ioIntensiveTask(ctx context.Context, counter *int64) {
	for {
		select {
		case <-ctx.Done():
			return
		default:
			// 模拟I/O操作
			time.Sleep(time.Millisecond)
			atomic.AddInt64(counter, 1)
		}
	}
}

// 混合型任务
func mixedTask(ctx context.Context, counter *int64) {
	for {
		select {
		case <-ctx.Done():
			return
		default:
			// 一些计算
			sum := 0
			for i := 0; i < 10000; i++ {
				sum += i
			}

			// 一些I/O
			time.Sleep(100 * time.Microsecond)
			atomic.AddInt64(counter, 1)
		}
	}
}

// 短时间任务
func shortTask(ctx context.Context, counter *int64) {
	for {
		select {
		case <-ctx.Done():
			return
		default:
			// 短时间计算
			_ = 1 + 1
			atomic.AddInt64(counter, 1)

			// 主动让出，模拟协作式调度
			runtime.Gosched()
		}
	}
}

// 监控调度公平性
func monitorFairness(ctx context.Context, counters *[4]int64) {
	ticker := time.NewTicker(500 * time.Millisecond)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			fmt.Printf("实时统计: ")
			for i, counter := range counters {
				fmt.Printf("G%d=%d ", i, atomic.LoadInt64(&counter))
			}
			fmt.Printf("(Goroutines: %d)\n", runtime.NumGoroutine())
		}
	}
}

// 抢占场景分析
func preemptionScenarios() {
	fmt.Println("分析不同的抢占场景...")

	// 场景1：系统调用抢占
	fmt.Println("\n场景1：系统调用抢占")
	syscallPreemptionDemo()

	// 场景2：垃圾回收抢占
	fmt.Println("\n场景2：垃圾回收抢占")
	gcPreemptionDemo()

	// 场景3：信号抢占（Go 1.14+）
	fmt.Println("\n场景3：信号抢占")
	signalPreemptionDemo()
}

// 系统调用抢占演示
func syscallPreemptionDemo() {
	var wg sync.WaitGroup
	var syscallCount int64

	// 启动系统调用密集型Goroutine
	wg.Add(1)
	go func() {
		defer wg.Done()

		for i := 0; i < 100; i++ {
			// 系统调用会导致M与P分离，触发调度
			time.Sleep(time.Microsecond)
			atomic.AddInt64(&syscallCount, 1)
		}
	}()

	// 启动其他Goroutine
	for i := 0; i < 3; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			fmt.Printf("Goroutine %d 在系统调用期间得到调度\n", id)
		}(i)
	}

	wg.Wait()
	fmt.Printf("系统调用次数: %d\n", atomic.LoadInt64(&syscallCount))
}

// 垃圾回收抢占演示
func gcPreemptionDemo() {
	// 创建一些垃圾来触发GC
	var data [][]byte

	var wg sync.WaitGroup

	// 启动内存分配Goroutine
	wg.Add(1)
	go func() {
		defer wg.Done()

		for i := 0; i < 1000; i++ {
			// 分配内存，可能触发GC
			chunk := make([]byte, 1024*1024) // 1MB
			data = append(data, chunk)

			if i%100 == 0 {
				fmt.Printf("已分配 %d MB\n", i+1)
			}
		}
	}()

	// 启动其他Goroutine
	for i := 0; i < 2; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			for j := 0; j < 10; j++ {
				fmt.Printf("Goroutine %d 迭代 %d\n", id, j)
				time.Sleep(100 * time.Millisecond)
			}
		}(i)
	}

	wg.Wait()

	// 强制GC
	runtime.GC()
	fmt.Printf("最终数据大小: %d MB\n", len(data))
}

// 信号抢占演示（Go 1.14+）
func signalPreemptionDemo() {
	fmt.Println("演示基于信号的抢占...")

	var preemptedCount int64
	var normalCount int64

	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	var wg sync.WaitGroup

	// 启动紧密循环Goroutine
	wg.Add(1)
	go func() {
		defer wg.Done()

		for {
			select {
			case <-ctx.Done():
				return
			default:
				// 紧密循环，依赖信号抢占
				for i := 0; i < 1000000; i++ {
					_ = i * i
				}
				atomic.AddInt64(&preemptedCount, 1)
			}
		}
	}()

	// 启动正常Goroutine
	for i := 0; i < 3; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			for {
				select {
				case <-ctx.Done():
					return
				default:
					atomic.AddInt64(&normalCount, 1)
					time.Sleep(10 * time.Millisecond)
				}
			}
		}(i)
	}

	wg.Wait()

	fmt.Printf("紧密循环Goroutine执行次数: %d\n", atomic.LoadInt64(&preemptedCount))
	fmt.Printf("正常Goroutine执行次数: %d\n", atomic.LoadInt64(&normalCount))
}

// 抢占性能影响分析
func preemptionPerformanceImpact() {
	fmt.Println("分析抢占对性能的影响...")

	// 测试1：无抢占场景
	fmt.Println("\n测试1：协作式调度性能")
	cooperativePerf := measureCooperativePerformance()

	// 测试2：有抢占场景
	fmt.Println("\n测试2：抢占式调度性能")
	preemptivePerf := measurePreemptivePerformance()

	fmt.Printf("\n性能对比:\n")
	fmt.Printf("协作式调度: %v\n", cooperativePerf)
	fmt.Printf("抢占式调度: %v\n", preemptivePerf)

	overhead := float64(preemptivePerf-cooperativePerf) / float64(cooperativePerf) * 100
	fmt.Printf("抢占开销: %.2f%%\n", overhead)
}

// 测量协作式调度性能
func measureCooperativePerformance() time.Duration {
	start := time.Now()

	var wg sync.WaitGroup

	for i := 0; i < 4; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()

			sum := 0
			for j := 0; j < 10000000; j++ {
				sum += j

				// 定期主动让出CPU
				if j%100000 == 0 {
					runtime.Gosched()
				}
			}
		}()
	}

	wg.Wait()
	return time.Since(start)
}

// 测量抢占式调度性能
func measurePreemptivePerformance() time.Duration {
	start := time.Now()

	var wg sync.WaitGroup

	for i := 0; i < 4; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()

			sum := 0
			for j := 0; j < 10000000; j++ {
				sum += j
				// 不主动让出CPU，依赖抢占
			}
		}()
	}

	wg.Wait()
	return time.Since(start)
}

/*
运行示例：
go run preemptive_scheduling.go

使用调度器跟踪运行：
GODEBUG=schedtrace=1000,scheddetail=1 go run preemptive_scheduling.go

这将显示详细的调度器信息，包括抢占统计。

在Go 1.14+中，你应该看到即使是紧密循环的Goroutine也能被抢占，
从而保证其他Goroutine得到公平的调度机会。
*/
