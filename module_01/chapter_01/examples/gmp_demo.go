// GMP模型演示程序
// 本程序展示Go调度器的GMP模型工作原理
package main

import (
	"fmt"
	"runtime"
	"sync"
	"time"
)

// 演示GMP模型的基本概念
func main() {
	fmt.Println("=== Go调度器GMP模型演示 ===")
	
	// 显示当前系统信息
	showSystemInfo()
	
	// 演示1：基本的Goroutine调度
	fmt.Println("\n1. 基本Goroutine调度演示")
	basicGoroutineDemo()
	
	// 演示2：观察P的数量对性能的影响
	fmt.Println("\n2. GOMAXPROCS影响演示")
	gomaxprocsDemo()
	
	// 演示3：Goroutine的生命周期
	fmt.Println("\n3. Goroutine生命周期演示")
	goroutineLifecycleDemo()
	
	// 演示4：M的创建和销毁
	fmt.Println("\n4. M(线程)管理演示")
	threadManagementDemo()
}

// 显示系统基本信息
func showSystemInfo() {
	fmt.Printf("CPU核心数: %d\n", runtime.NumCPU())
	fmt.Printf("GOMAXPROCS: %d\n", runtime.GOMAXPROCS(0))
	fmt.Printf("当前Goroutine数: %d\n", runtime.NumGoroutine())
}

// 基本Goroutine调度演示
func basicGoroutineDemo() {
	var wg sync.WaitGroup
	
	// 创建多个Goroutine
	for i := 0; i < 5; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			
			// 模拟工作负载
			for j := 0; j < 3; j++ {
				fmt.Printf("Goroutine %d 正在工作，迭代 %d，运行在线程 %d\n", 
					id, j, getGoroutineID())
				
				// 主动让出CPU，触发调度
				runtime.Gosched()
				time.Sleep(10 * time.Millisecond)
			}
		}(i)
	}
	
	wg.Wait()
	fmt.Printf("所有Goroutine完成，当前Goroutine数: %d\n", runtime.NumGoroutine())
}

// GOMAXPROCS影响演示
func gomaxprocsDemo() {
	// 保存原始设置
	originalProcs := runtime.GOMAXPROCS(0)
	
	// 测试不同的GOMAXPROCS设置
	testCases := []int{1, 2, runtime.NumCPU()}
	
	for _, procs := range testCases {
		runtime.GOMAXPROCS(procs)
		fmt.Printf("\n设置GOMAXPROCS=%d\n", procs)
		
		start := time.Now()
		runCPUIntensiveTask()
		duration := time.Since(start)
		
		fmt.Printf("执行时间: %v\n", duration)
	}
	
	// 恢复原始设置
	runtime.GOMAXPROCS(originalProcs)
}

// CPU密集型任务
func runCPUIntensiveTask() {
	var wg sync.WaitGroup
	numTasks := 4
	
	for i := 0; i < numTasks; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			
			// CPU密集型计算
			sum := 0
			for j := 0; j < 1000000; j++ {
				sum += j
			}
		}(i)
	}
	
	wg.Wait()
}

// Goroutine生命周期演示
func goroutineLifecycleDemo() {
	fmt.Printf("开始时Goroutine数: %d\n", runtime.NumGoroutine())
	
	// 创建一个channel用于同步
	done := make(chan bool)
	
	// 创建长时间运行的Goroutine
	go longRunningGoroutine(done)
	
	// 等待一段时间观察
	time.Sleep(100 * time.Millisecond)
	fmt.Printf("运行中Goroutine数: %d\n", runtime.NumGoroutine())
	
	// 通知Goroutine结束
	done <- true
	time.Sleep(10 * time.Millisecond)
	
	fmt.Printf("结束后Goroutine数: %d\n", runtime.NumGoroutine())
}

// 长时间运行的Goroutine
func longRunningGoroutine(done chan bool) {
	ticker := time.NewTicker(50 * time.Millisecond)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			fmt.Printf("长时间运行的Goroutine仍在工作...\n")
		case <-done:
			fmt.Printf("长时间运行的Goroutine收到结束信号\n")
			return
		}
	}
}

// M(线程)管理演示
func threadManagementDemo() {
	fmt.Printf("开始时Goroutine数: %d\n", runtime.NumGoroutine())
	
	// 创建大量Goroutine来观察M的创建
	var wg sync.WaitGroup
	numGoroutines := 100
	
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			
			// 模拟系统调用，可能导致M的创建
			time.Sleep(time.Millisecond)
			
			if id%20 == 0 {
				fmt.Printf("Goroutine %d 完成\n", id)
			}
		}(i)
	}
	
	// 在Goroutine运行时检查状态
	time.Sleep(10 * time.Millisecond)
	fmt.Printf("运行时Goroutine数: %d\n", runtime.NumGoroutine())
	
	wg.Wait()
	fmt.Printf("完成后Goroutine数: %d\n", runtime.NumGoroutine())
	
	// 强制垃圾回收，清理资源
	runtime.GC()
	time.Sleep(10 * time.Millisecond)
	fmt.Printf("GC后Goroutine数: %d\n", runtime.NumGoroutine())
}

// 获取当前Goroutine ID（仅用于演示，生产环境不推荐）
func getGoroutineID() int {
	// 注意：这是一个简化的实现，实际的Goroutine ID获取更复杂
	// 在生产环境中，不应该依赖Goroutine ID
	return runtime.NumGoroutine()
}

// 调度器状态监控函数
func monitorScheduler() {
	ticker := time.NewTicker(time.Second)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			var m runtime.MemStats
			runtime.ReadMemStats(&m)
			
			fmt.Printf("调度器状态 - Goroutines: %d, Threads: %d\n", 
				runtime.NumGoroutine(), runtime.NumCPU())
		}
	}
}

// 演示调度器的抢占机制
func preemptionDemo() {
	fmt.Println("=== 调度器抢占机制演示 ===")
	
	// 创建一个CPU密集型的Goroutine
	go func() {
		for {
			// 紧密循环，在Go 1.14之前可能不会被抢占
			_ = 1 + 1
		}
	}()
	
	// 创建其他Goroutine观察是否能得到调度
	var wg sync.WaitGroup
	for i := 0; i < 3; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			fmt.Printf("Goroutine %d 得到了调度机会\n", id)
		}(i)
	}
	
	wg.Wait()
}

/*
运行示例：
go run gmp_demo.go

预期输出：
=== Go调度器GMP模型演示 ===
CPU核心数: 8
GOMAXPROCS: 8
当前Goroutine数: 1

1. 基本Goroutine调度演示
Goroutine 0 正在工作，迭代 0，运行在线程 1
Goroutine 1 正在工作，迭代 0，运行在线程 1
...

使用调试选项运行：
GODEBUG=schedtrace=1000 go run gmp_demo.go

这将显示详细的调度器跟踪信息。
*/
