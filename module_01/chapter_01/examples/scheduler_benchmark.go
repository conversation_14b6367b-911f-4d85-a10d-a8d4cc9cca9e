// 调度器性能基准测试
// 本程序提供各种调度器性能测试和分析工具
package main

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"sync/atomic"
	"testing"
	"time"
)

func main() {
	fmt.Println("=== Go调度器性能基准测试 ===")
	
	// 运行所有基准测试
	runAllBenchmarks()
	
	// 调度器压力测试
	fmt.Println("\n=== 调度器压力测试 ===")
	schedulerStressTest()
	
	// 不同GOMAXPROCS设置的性能对比
	fmt.Println("\n=== GOMAXPROCS性能对比 ===")
	gomaxprocsComparison()
	
	// 调度器延迟测试
	fmt.Println("\n=== 调度器延迟测试 ===")
	schedulerLatencyTest()
}

// 运行所有基准测试
func runAllBenchmarks() {
	benchmarks := []struct {
		name string
		fn   func() time.Duration
	}{
		{"Goroutine创建", benchmarkGoroutineCreation},
		{"Channel通信", benchmarkChannelCommunication},
		{"Mutex竞争", benchmarkMutexContention},
		{"Context切换", benchmarkContextSwitching},
		{"工作窃取", benchmarkWorkStealing},
	}
	
	fmt.Println("基准测试结果:")
	fmt.Println("测试项目\t\t执行时间\t\t操作数/秒")
	fmt.Println("----------------------------------------")
	
	for _, bench := range benchmarks {
		duration := bench.fn()
		opsPerSec := float64(time.Second) / float64(duration)
		fmt.Printf("%-15s\t%v\t\t%.0f\n", bench.name, duration, opsPerSec)
	}
}

// Goroutine创建基准测试
func benchmarkGoroutineCreation() time.Duration {
	const numGoroutines = 10000
	
	start := time.Now()
	
	var wg sync.WaitGroup
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			// 最小工作量
			_ = 1 + 1
		}()
	}
	wg.Wait()
	
	return time.Since(start) / numGoroutines
}

// Channel通信基准测试
func benchmarkChannelCommunication() time.Duration {
	const numMessages = 10000
	
	ch := make(chan int, 1)
	
	start := time.Now()
	
	var wg sync.WaitGroup
	
	// 发送者
	wg.Add(1)
	go func() {
		defer wg.Done()
		for i := 0; i < numMessages; i++ {
			ch <- i
		}
		close(ch)
	}()
	
	// 接收者
	wg.Add(1)
	go func() {
		defer wg.Done()
		for range ch {
			// 处理消息
		}
	}()
	
	wg.Wait()
	
	return time.Since(start) / numMessages
}

// Mutex竞争基准测试
func benchmarkMutexContention() time.Duration {
	const numOperations = 10000
	const numGoroutines = 10
	
	var mu sync.Mutex
	var counter int
	
	start := time.Now()
	
	var wg sync.WaitGroup
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for j := 0; j < numOperations/numGoroutines; j++ {
				mu.Lock()
				counter++
				mu.Unlock()
			}
		}()
	}
	wg.Wait()
	
	return time.Since(start) / numOperations
}

// Context切换基准测试
func benchmarkContextSwitching() time.Duration {
	const numSwitches = 10000
	
	ch1 := make(chan struct{})
	ch2 := make(chan struct{})
	
	start := time.Now()
	
	var wg sync.WaitGroup
	
	// Goroutine 1
	wg.Add(1)
	go func() {
		defer wg.Done()
		for i := 0; i < numSwitches/2; i++ {
			ch1 <- struct{}{}
			<-ch2
		}
	}()
	
	// Goroutine 2
	wg.Add(1)
	go func() {
		defer wg.Done()
		for i := 0; i < numSwitches/2; i++ {
			<-ch1
			ch2 <- struct{}{}
		}
	}()
	
	wg.Wait()
	
	return time.Since(start) / numSwitches
}

// 工作窃取基准测试
func benchmarkWorkStealing() time.Duration {
	const numTasks = 10000
	
	tasks := make(chan int, numTasks)
	for i := 0; i < numTasks; i++ {
		tasks <- i
	}
	close(tasks)
	
	start := time.Now()
	
	var wg sync.WaitGroup
	numWorkers := runtime.GOMAXPROCS(0) * 2
	
	for i := 0; i < numWorkers; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for range tasks {
				// 模拟工作
				time.Sleep(time.Microsecond)
			}
		}()
	}
	
	wg.Wait()
	
	return time.Since(start) / numTasks
}

// 调度器压力测试
func schedulerStressTest() {
	fmt.Println("开始调度器压力测试...")
	
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	
	var stats StressTestStats
	
	// 启动多种类型的Goroutine
	var wg sync.WaitGroup
	
	// CPU密集型Goroutine
	for i := 0; i < runtime.GOMAXPROCS(0); i++ {
		wg.Add(1)
		go cpuIntensiveWorker(ctx, &stats.cpuOps, &wg)
	}
	
	// I/O密集型Goroutine
	for i := 0; i < runtime.GOMAXPROCS(0)*2; i++ {
		wg.Add(1)
		go ioIntensiveWorker(ctx, &stats.ioOps, &wg)
	}
	
	// 短时间任务Goroutine
	for i := 0; i < runtime.GOMAXPROCS(0)*4; i++ {
		wg.Add(1)
		go shortTaskWorker(ctx, &stats.shortOps, &wg)
	}
	
	// 监控Goroutine
	wg.Add(1)
	go monitorStressTest(ctx, &stats, &wg)
	
	wg.Wait()
	
	fmt.Printf("\n压力测试结果:\n")
	fmt.Printf("CPU密集型操作: %d\n", atomic.LoadInt64(&stats.cpuOps))
	fmt.Printf("I/O密集型操作: %d\n", atomic.LoadInt64(&stats.ioOps))
	fmt.Printf("短时间任务: %d\n", atomic.LoadInt64(&stats.shortOps))
	fmt.Printf("最大Goroutine数: %d\n", atomic.LoadInt64(&stats.maxGoroutines))
}

// 压力测试统计
type StressTestStats struct {
	cpuOps        int64
	ioOps         int64
	shortOps      int64
	maxGoroutines int64
}

// CPU密集型工作者
func cpuIntensiveWorker(ctx context.Context, counter *int64, wg *sync.WaitGroup) {
	defer wg.Done()
	
	for {
		select {
		case <-ctx.Done():
			return
		default:
			// CPU密集型计算
			sum := 0
			for i := 0; i < 100000; i++ {
				sum += i * i
			}
			atomic.AddInt64(counter, 1)
		}
	}
}

// I/O密集型工作者
func ioIntensiveWorker(ctx context.Context, counter *int64, wg *sync.WaitGroup) {
	defer wg.Done()
	
	for {
		select {
		case <-ctx.Done():
			return
		case <-time.After(time.Millisecond):
			atomic.AddInt64(counter, 1)
		}
	}
}

// 短时间任务工作者
func shortTaskWorker(ctx context.Context, counter *int64, wg *sync.WaitGroup) {
	defer wg.Done()
	
	for {
		select {
		case <-ctx.Done():
			return
		default:
			// 短时间任务
			_ = 1 + 1
			atomic.AddInt64(counter, 1)
			runtime.Gosched() // 主动让出
		}
	}
}

// 监控压力测试
func monitorStressTest(ctx context.Context, stats *StressTestStats, wg *sync.WaitGroup) {
	defer wg.Done()
	
	ticker := time.NewTicker(time.Second)
	defer ticker.Stop()
	
	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			currentGoroutines := int64(runtime.NumGoroutine())
			if currentGoroutines > atomic.LoadInt64(&stats.maxGoroutines) {
				atomic.StoreInt64(&stats.maxGoroutines, currentGoroutines)
			}
			
			fmt.Printf("当前状态 - Goroutines: %d, CPU: %d, I/O: %d, Short: %d\n",
				currentGoroutines,
				atomic.LoadInt64(&stats.cpuOps),
				atomic.LoadInt64(&stats.ioOps),
				atomic.LoadInt64(&stats.shortOps))
		}
	}
}

// GOMAXPROCS性能对比
func gomaxprocsComparison() {
	originalProcs := runtime.GOMAXPROCS(0)
	defer runtime.GOMAXPROCS(originalProcs)
	
	testCases := []int{1, 2, 4, runtime.NumCPU(), runtime.NumCPU() * 2}
	
	fmt.Println("GOMAXPROCS\t执行时间\t\tGoroutine数\t吞吐量")
	fmt.Println("--------------------------------------------------------")
	
	for _, procs := range testCases {
		if procs > runtime.NumCPU()*4 {
			continue // 跳过过大的值
		}
		
		runtime.GOMAXPROCS(procs)
		duration, goroutines, throughput := measurePerformance()
		
		fmt.Printf("%d\t\t%v\t\t%d\t\t%.0f ops/s\n",
			procs, duration, goroutines, throughput)
	}
}

// 测量性能
func measurePerformance() (time.Duration, int, float64) {
	const testDuration = 2 * time.Second
	const numWorkers = 20
	
	ctx, cancel := context.WithTimeout(context.Background(), testDuration)
	defer cancel()
	
	var operations int64
	var wg sync.WaitGroup
	
	start := time.Now()
	
	for i := 0; i < numWorkers; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			
			for {
				select {
				case <-ctx.Done():
					return
				default:
					// 混合工作负载
					sum := 0
					for j := 0; j < 1000; j++ {
						sum += j
					}
					atomic.AddInt64(&operations, 1)
				}
			}
		}()
	}
	
	wg.Wait()
	duration := time.Since(start)
	
	ops := atomic.LoadInt64(&operations)
	throughput := float64(ops) / duration.Seconds()
	
	return duration, runtime.NumGoroutine(), throughput
}

// 调度器延迟测试
func schedulerLatencyTest() {
	fmt.Println("测试调度器延迟...")
	
	const numTests = 1000
	latencies := make([]time.Duration, numTests)
	
	for i := 0; i < numTests; i++ {
		latencies[i] = measureSchedulingLatency()
	}
	
	// 计算统计信息
	var total time.Duration
	min := latencies[0]
	max := latencies[0]
	
	for _, latency := range latencies {
		total += latency
		if latency < min {
			min = latency
		}
		if latency > max {
			max = latency
		}
	}
	
	avg := total / time.Duration(numTests)
	
	fmt.Printf("调度延迟统计 (基于 %d 次测试):\n", numTests)
	fmt.Printf("平均延迟: %v\n", avg)
	fmt.Printf("最小延迟: %v\n", min)
	fmt.Printf("最大延迟: %v\n", max)
	
	// 计算百分位数
	// 简化版本，实际应该排序后计算
	fmt.Printf("延迟分布: < 1μs: %d%%, < 10μs: %d%%, < 100μs: %d%%\n",
		countBelow(latencies, time.Microsecond)*100/numTests,
		countBelow(latencies, 10*time.Microsecond)*100/numTests,
		countBelow(latencies, 100*time.Microsecond)*100/numTests)
}

// 测量调度延迟
func measureSchedulingLatency() time.Duration {
	ch := make(chan time.Time, 1)
	
	// 发送当前时间
	go func() {
		ch <- time.Now()
	}()
	
	// 接收并计算延迟
	start := <-ch
	return time.Since(start)
}

// 计算小于指定值的数量
func countBelow(latencies []time.Duration, threshold time.Duration) int {
	count := 0
	for _, latency := range latencies {
		if latency < threshold {
			count++
		}
	}
	return count
}

// 基准测试函数（用于go test）
func BenchmarkGoroutineCreation(b *testing.B) {
	for i := 0; i < b.N; i++ {
		var wg sync.WaitGroup
		wg.Add(1)
		go func() {
			defer wg.Done()
		}()
		wg.Wait()
	}
}

func BenchmarkChannelSend(b *testing.B) {
	ch := make(chan int, 1)
	defer close(ch)
	
	for i := 0; i < b.N; i++ {
		ch <- i
		<-ch
	}
}

func BenchmarkMutexLock(b *testing.B) {
	var mu sync.Mutex
	
	for i := 0; i < b.N; i++ {
		mu.Lock()
		mu.Unlock()
	}
}

func BenchmarkAtomicAdd(b *testing.B) {
	var counter int64
	
	for i := 0; i < b.N; i++ {
		atomic.AddInt64(&counter, 1)
	}
}

/*
运行方式：

1. 直接运行程序：
go run scheduler_benchmark.go

2. 运行基准测试：
go test -bench=. scheduler_benchmark.go

3. 运行带内存分析的基准测试：
go test -bench=. -benchmem scheduler_benchmark.go

4. 生成CPU profile：
go test -bench=. -cpuprofile=cpu.prof scheduler_benchmark.go
go tool pprof cpu.prof

5. 使用调度器跟踪：
GODEBUG=schedtrace=1000 go run scheduler_benchmark.go

这些测试将帮助你理解：
- Goroutine创建的开销
- 调度器的延迟特性
- 不同GOMAXPROCS设置的影响
- 调度器在高负载下的表现
*/
