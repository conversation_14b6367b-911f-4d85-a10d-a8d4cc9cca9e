# 练习2：调度器性能分析与优化

## 练习目标

通过实际的性能分析和优化实践，掌握Go调度器的性能调优技巧，学会识别和解决调度器相关的性能问题。

## 前置知识

- 完成练习1：GMP模型观察
- 理解Go性能分析工具（pprof, trace）
- 熟悉基准测试编写

## 练习任务

### 任务1：调度器性能瓶颈识别

**场景描述**：给定一个存在调度器性能问题的程序，要求识别瓶颈并提出优化方案。

```go
// 问题程序：scheduler_bottleneck.go
package main

import (
    "fmt"
    "runtime"
    "sync"
    "time"
)

// 这是一个存在调度器性能问题的程序
func problematicScheduler() {
    const numGoroutines = 10000
    const workDuration = time.Millisecond
    
    var wg sync.WaitGroup
    
    // 问题1：过度创建Goroutine
    for i := 0; i < numGoroutines; i++ {
        wg.Add(1)
        go func(id int) {
            defer wg.Done()
            
            // 问题2：不合理的工作负载
            time.Sleep(workDuration)
            
            // 问题3：频繁的系统调用
            fmt.Printf("Goroutine %d completed\n", id)
        }(i)
    }
    
    wg.Wait()
}

func main() {
    start := time.Now()
    problematicScheduler()
    fmt.Printf("Total time: %v\n", time.Since(start))
}
```

**任务要求**：
1. 分析上述程序的性能问题
2. 使用性能分析工具识别瓶颈
3. 提出具体的优化方案
4. 实现优化版本并对比性能

### 任务2：工作者池模式优化

**要求**：实现一个高效的工作者池，解决Goroutine过度创建的问题

```go
// 创建文件：worker_pool_optimization.go
package main

import (
    "context"
    "sync"
    "time"
)

// WorkerPool 工作者池接口
type WorkerPool interface {
    Submit(task func()) error
    Close() error
    Stats() PoolStats
}

// PoolStats 池统计信息
type PoolStats struct {
    ActiveWorkers   int
    QueuedTasks     int
    CompletedTasks  int64
    TotalWorkers    int
}

// TODO: 实现高效的工作者池
type OptimizedWorkerPool struct {
    // 在这里定义你的字段
}

func NewOptimizedWorkerPool(size int) *OptimizedWorkerPool {
    // TODO: 实现构造函数
}

func (p *OptimizedWorkerPool) Submit(task func()) error {
    // TODO: 实现任务提交
}

func (p *OptimizedWorkerPool) Close() error {
    // TODO: 实现优雅关闭
}

func (p *OptimizedWorkerPool) Stats() PoolStats {
    // TODO: 实现统计信息
}
```

**优化要求**：
1. 支持动态调整工作者数量
2. 实现任务队列缓冲
3. 提供优雅关闭机制
4. 包含详细的性能统计

### 任务3：调度器友好的程序设计

**要求**：设计一个调度器友好的并发程序

```go
// 创建文件：scheduler_friendly_design.go
package main

// 设计一个处理大量并发请求的HTTP服务器
// 要求：
// 1. 高效的Goroutine管理
// 2. 合理的资源使用
// 3. 良好的调度器性能

type SchedulerFriendlyServer struct {
    // TODO: 定义服务器结构
}

func NewSchedulerFriendlyServer() *SchedulerFriendlyServer {
    // TODO: 实现构造函数
}

func (s *SchedulerFriendlyServer) HandleRequest(req Request) Response {
    // TODO: 实现请求处理
    // 考虑：
    // - 如何避免为每个请求创建新的Goroutine
    // - 如何合理使用系统调用
    // - 如何优化内存分配
}
```

### 任务4：性能基准测试

**要求**：为优化前后的程序编写详细的基准测试

```go
// 创建文件：scheduler_benchmark_test.go
package main

import (
    "testing"
    "runtime"
)

// 基准测试：Goroutine创建开销
func BenchmarkGoroutineCreation(b *testing.B) {
    // TODO: 测试原始版本的Goroutine创建开销
}

func BenchmarkWorkerPoolCreation(b *testing.B) {
    // TODO: 测试工作者池版本的开销
}

// 基准测试：调度器延迟
func BenchmarkSchedulerLatency(b *testing.B) {
    // TODO: 测试调度器延迟
}

// 基准测试：吞吐量
func BenchmarkThroughput(b *testing.B) {
    // TODO: 测试系统吞吐量
}

// 内存分配测试
func BenchmarkMemoryAllocation(b *testing.B) {
    // TODO: 测试内存分配模式
}
```

## 性能分析工具使用

### 1. CPU Profile分析

```bash
# 生成CPU profile
go test -bench=. -cpuprofile=cpu.prof

# 分析CPU profile
go tool pprof cpu.prof
(pprof) top10
(pprof) list main.problematicScheduler
(pprof) web
```

### 2. 内存Profile分析

```bash
# 生成内存profile
go test -bench=. -memprofile=mem.prof

# 分析内存使用
go tool pprof mem.prof
(pprof) top10
(pprof) list main.problematicScheduler
```

### 3. 执行跟踪分析

```bash
# 生成执行跟踪
go test -bench=. -trace=trace.out

# 分析执行跟踪
go tool trace trace.out
```

### 4. 调度器跟踪

```bash
# 启用调度器跟踪
GODEBUG=schedtrace=1000,scheddetail=1 go run scheduler_bottleneck.go
```

## 分析重点

### 1. 调度器相关指标

- **Goroutine数量**：过多的Goroutine会增加调度开销
- **P利用率**：空闲P表示资源浪费
- **M数量**：过多M表示系统调用频繁
- **队列长度**：长队列表示负载不均衡

### 2. 性能瓶颈类型

- **创建开销**：频繁创建销毁Goroutine
- **调度延迟**：Goroutine等待调度时间过长
- **锁竞争**：多个Goroutine竞争同一资源
- **系统调用**：频繁的系统调用导致M与P分离

### 3. 优化策略

- **池化技术**：使用对象池减少分配
- **批处理**：批量处理减少调度次数
- **异步I/O**：使用异步I/O减少阻塞
- **负载均衡**：合理分配工作负载

## 实验步骤

### 步骤1：问题识别

1. 运行问题程序，记录基础性能数据
2. 使用pprof分析CPU和内存使用
3. 使用trace分析调度器行为
4. 识别主要性能瓶颈

### 步骤2：优化实现

1. 根据分析结果设计优化方案
2. 实现工作者池模式
3. 优化系统调用使用
4. 改进内存分配模式

### 步骤3：性能验证

1. 编写基准测试对比优化效果
2. 使用相同的分析工具验证改进
3. 记录优化前后的性能数据
4. 分析优化效果和剩余问题

### 步骤4：深入优化

1. 针对剩余问题进一步优化
2. 调整GOMAXPROCS等参数
3. 优化数据结构和算法
4. 验证最终优化效果

## 预期结果

完成练习后，你应该能够：

1. **识别调度器瓶颈**：快速定位调度器相关的性能问题
2. **使用分析工具**：熟练使用pprof、trace等工具
3. **设计优化方案**：提出有效的调度器优化策略
4. **实现性能优化**：编写高效的并发程序

## 性能目标

优化后的程序应该达到以下目标：

- **Goroutine创建开销**：减少50%以上
- **内存分配**：减少30%以上
- **调度器延迟**：减少40%以上
- **整体吞吐量**：提升100%以上

## 常见优化技巧

### 1. Goroutine池化

```go
// 使用工作者池而不是为每个任务创建新的Goroutine
pool := NewWorkerPool(runtime.GOMAXPROCS(0))
defer pool.Close()

for _, task := range tasks {
    pool.Submit(task)
}
```

### 2. 批量处理

```go
// 批量处理减少调度次数
const batchSize = 100
for i := 0; i < len(items); i += batchSize {
    end := i + batchSize
    if end > len(items) {
        end = len(items)
    }
    processBatch(items[i:end])
}
```

### 3. 减少系统调用

```go
// 使用缓冲减少系统调用
buf := make([]byte, 0, 1024)
for _, item := range items {
    buf = append(buf, item.Bytes()...)
    if len(buf) >= 1024 {
        writer.Write(buf)
        buf = buf[:0]
    }
}
```

## 提交要求

1. **优化后的代码**：完整的优化实现
2. **性能分析报告**：详细的性能分析和对比
3. **基准测试结果**：优化前后的性能数据
4. **优化总结**：优化策略和效果总结

## 评分标准

- **问题识别**（25%）：准确识别性能瓶颈
- **优化实现**（35%）：优化方案的有效性和实现质量
- **性能提升**（25%）：实际的性能改进效果
- **分析深度**（15%）：对优化原理的理解和分析

---

**截止时间**：请在完成练习1后的5天内完成此练习。

**提示**：重点关注调度器的工作原理，优化时要考虑实际的业务场景和资源限制。
