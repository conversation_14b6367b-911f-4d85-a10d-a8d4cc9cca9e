# 练习1：GMP模型观察与分析

## 练习目标

通过实际编程和观察，深入理解Go调度器的GMP模型工作原理，掌握调度器调试技巧。

## 前置知识

- 理解GMP模型的基本概念
- 熟悉Go语言基础语法
- 了解并发编程基础

## 练习任务

### 任务1：观察基本的GMP行为

**要求**：编写程序观察不同场景下的GMP模型行为

```go
// 创建文件：gmp_observation.go
package main

import (
    "fmt"
    "runtime"
    "sync"
    "time"
)

func main() {
    // TODO: 实现以下功能
    // 1. 显示当前系统的基本信息
    // 2. 创建不同数量的Goroutine观察调度行为
    // 3. 使用GODEBUG=schedtrace观察调度器状态
}
```

**具体要求**：
1. 显示CPU核心数、GOMAXPROCS设置、当前Goroutine数量
2. 创建10个、100个、1000个Goroutine，观察调度器行为变化
3. 每个Goroutine执行不同类型的工作负载（CPU密集型、I/O密集型）
4. 记录并分析调度器跟踪信息

**运行命令**：
```bash
GODEBUG=schedtrace=1000 go run gmp_observation.go
```

### 任务2：GOMAXPROCS影响分析

**要求**：分析GOMAXPROCS设置对程序性能的影响

```go
// 在gmp_observation.go中添加函数
func analyzeGOMAXPROCS() {
    // TODO: 实现以下功能
    // 1. 测试不同GOMAXPROCS设置下的性能
    // 2. 记录执行时间和资源使用情况
    // 3. 分析最优的GOMAXPROCS设置
}
```

**测试场景**：
- GOMAXPROCS = 1, 2, 4, 8, 16
- CPU密集型任务
- I/O密集型任务
- 混合型任务

### 任务3：P的本地队列观察

**要求**：通过代码观察P的本地队列工作机制

```go
// 创建文件：local_queue_demo.go
func demonstrateLocalQueue() {
    // TODO: 实现以下功能
    // 1. 创建大量短时间Goroutine
    // 2. 观察本地队列的填充和消费
    // 3. 分析队列长度变化
}
```

## 观察要点

### 1. 调度器跟踪信息解读

使用`GODEBUG=schedtrace=1000`时，输出格式如下：
```
SCHED 1004ms: gomaxprocs=4 idleprocs=0 threads=5 spinningthreads=0 idlethreads=1 runqueue=0 [4 0 0 1]
```

**字段含义**：
- `gomaxprocs`: P的数量
- `idleprocs`: 空闲P的数量
- `threads`: M的总数
- `spinningthreads`: 自旋等待的M数量
- `idlethreads`: 空闲M的数量
- `runqueue`: 全局队列中的G数量
- `[4 0 0 1]`: 每个P的本地队列长度

### 2. 关键观察点

1. **P的利用率**：`idleprocs`应该尽可能小
2. **队列分布**：本地队列vs全局队列的G分布
3. **M的数量**：M的创建和销毁模式
4. **自旋线程**：`spinningthreads`的数量变化

## 实验步骤

### 步骤1：环境准备

1. 确保Go版本 >= 1.14（支持抢占式调度）
2. 准备性能监控工具
3. 了解系统硬件配置

### 步骤2：基础观察

1. 运行基本的GMP演示程序
2. 记录不同负载下的调度器状态
3. 分析Goroutine的创建和销毁模式

### 步骤3：深入分析

1. 使用`go tool trace`生成详细跟踪
2. 分析调度器的决策过程
3. 识别性能瓶颈

### 步骤4：优化实验

1. 调整GOMAXPROCS设置
2. 优化Goroutine的工作模式
3. 验证优化效果

## 预期结果

完成练习后，你应该能够：

1. **理解GMP关系**：清楚G、M、P之间的协作关系
2. **解读调度信息**：能够分析schedtrace输出的含义
3. **识别性能问题**：发现调度器相关的性能瓶颈
4. **优化调度行为**：知道如何调整程序以获得更好的调度性能

## 常见问题与解答

### Q1: 为什么有时候看到的M数量比GOMAXPROCS大？

**A**: M的数量可能超过P的数量，原因包括：
- 系统调用导致M与P分离
- 网络I/O操作
- CGO调用
- 垃圾回收器需要额外的M

### Q2: 什么时候会出现全局队列中有G？

**A**: 以下情况会导致G进入全局队列：
- 本地队列满了（长度达到256）
- 系统调用返回时P已被其他M占用
- 工作窃取时从全局队列获取G

### Q3: 如何判断调度器性能是否良好？

**A**: 关键指标包括：
- `idleprocs`接近0（P充分利用）
- 本地队列长度适中（不为0也不过长）
- `spinningthreads`数量合理
- 全局队列长度较小

## 扩展练习

### 扩展1：网络I/O对调度的影响

编写程序测试网络I/O操作对调度器的影响：

```go
func networkIOImpact() {
    // TODO: 创建HTTP客户端请求
    // 观察网络I/O如何影响M和P的关系
}
```

### 扩展2：垃圾回收与调度

观察垃圾回收过程中的调度行为：

```go
func gcSchedulingImpact() {
    // TODO: 创建大量内存分配
    // 观察GC期间的调度器状态变化
}
```

### 扩展3：自定义调度策略

尝试通过程序设计影响调度器行为：

```go
func customSchedulingStrategy() {
    // TODO: 设计特定的Goroutine模式
    // 观察对调度器的影响
}
```

## 提交要求

1. **代码文件**：完整的Go源代码文件
2. **实验报告**：包含观察结果和分析
3. **性能数据**：调度器跟踪信息和性能测试结果
4. **问题总结**：遇到的问题和解决方案

## 评分标准

- **代码质量**（30%）：代码正确性、可读性、注释完整性
- **实验完整性**（25%）：是否完成所有要求的任务
- **分析深度**（25%）：对观察结果的理解和分析质量
- **创新性**（20%）：扩展练习的完成情况和创新点

## 参考资料

- [Go调度器设计文档](https://docs.google.com/document/d/1TTj4T2JO42uD5ID9e89oa0sLKhJYD0Y_kqxDv3I3XMw)
- [Go运行时源码](https://github.com/golang/go/tree/master/src/runtime)
- [调度器跟踪工具文档](https://golang.org/pkg/runtime/)

---

**截止时间**：请在学习第1章后的3天内完成此练习。

**提示**：建议先完成基础任务，再尝试扩展练习。遇到问题时，可以参考示例代码和官方文档。
