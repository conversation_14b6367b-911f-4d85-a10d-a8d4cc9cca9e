# Go调度器GMP架构深度解析

## 概述

Go语言的调度器是其并发模型的核心，采用了GMP（Goroutine-Machine-Processor）三层架构。这种设计使得Go能够高效地管理大量的轻量级线程（Goroutine），实现了M:N的调度模型。

## 历史演进

### Go 1.0 - GM模型
- **问题**：全局锁竞争严重，扩展性差
- **架构**：只有G和M，所有Goroutine共享一个全局队列
- **缺陷**：
  - 全局锁导致性能瓶颈
  - 缺乏局部性，缓存效率低
  - 无法充分利用多核优势

### Go 1.1 - GMP模型
- **改进**：引入P（Processor）概念
- **优势**：
  - 本地队列减少锁竞争
  - 工作窃取实现负载均衡
  - 更好的缓存局部性

### Go 1.14+ - 抢占式调度
- **改进**：基于信号的异步抢占
- **解决问题**：紧密循环的Goroutine无法被抢占

## GMP模型详解

### G (Goroutine) - 协程

#### 数据结构
```go
type g struct {
    stack       stack     // 栈信息
    stackguard0 uintptr   // 栈溢出检查
    m           *m        // 当前运行的M
    sched       gobuf     // 调度信息
    atomicstatus uint32   // 状态
    goid        int64     // Goroutine ID
    // ... 其他字段
}
```

#### 状态转换
```
_Gidle    -> _Grunnable -> _Grunning -> _Gwaiting
   ↑                                        ↓
   ←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←
```

**状态说明**：
- `_Gidle`：刚分配，未初始化
- `_Grunnable`：可运行，在队列中等待
- `_Grunning`：正在运行
- `_Gwaiting`：等待状态（如等待channel、系统调用等）
- `_Gdead`：已结束，可被重用

#### 栈管理
- **初始大小**：2KB（Go 1.4+）
- **动态扩展**：按需增长，最大1GB（64位系统）
- **栈分裂**：当栈空间不足时自动扩展
- **栈收缩**：GC时回收未使用的栈空间

### M (Machine) - 操作系统线程

#### 数据结构
```go
type m struct {
    g0      *g        // 调度栈
    curg    *g        // 当前运行的G
    p       puintptr  // 关联的P
    nextp   puintptr  // 下一个P
    spinning bool     // 是否在自旋等待
    blocked  bool     // 是否被阻塞
    // ... 其他字段
}
```

#### 生命周期
1. **创建**：
   - 程序启动时创建主M
   - 需要时动态创建新M
   - 最大数量：10000（可通过debug.SetMaxThreads调整）

2. **运行**：
   - 执行G的代码
   - 处理系统调用
   - 参与垃圾回收

3. **销毁**：
   - 长时间空闲时销毁
   - 程序结束时清理

#### M的类型
- **普通M**：执行用户代码
- **系统M**：执行运行时代码（如GC）
- **信号M**：处理信号

### P (Processor) - 逻辑处理器

#### 数据结构
```go
type p struct {
    id          int32
    status      uint32        // 状态
    link        puintptr      // 链表指针
    m           muintptr      // 关联的M
    mcache      *mcache       // 内存分配缓存
    runqhead    uint32        // 本地队列头
    runqtail    uint32        // 本地队列尾
    runq        [256]guintptr // 本地队列
    runnext     guintptr      // 下一个运行的G
    // ... 其他字段
}
```

#### 状态转换
```
_Pidle -> _Prunning -> _Psyscall -> _Pgcstop
   ↑         ↓           ↓           ↓
   ←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←
```

**状态说明**：
- `_Pidle`：空闲状态
- `_Prunning`：运行状态
- `_Psyscall`：系统调用状态
- `_Pgcstop`：GC停止状态

#### 本地队列
- **容量**：256个Goroutine
- **FIFO**：先进先出队列
- **runnext**：优先运行的G（用于优化局部性）

## 调度流程

### 调度循环
```go
// 简化的调度循环
func schedule() {
    gp := findrunnable()  // 查找可运行的G
    execute(gp)           // 执行G
    // G执行完毕或被抢占后返回这里
    goto schedule
}
```

### 查找可运行的G
```go
func findrunnable() *g {
    // 1. 检查本地队列
    if gp := runqget(_p_); gp != nil {
        return gp
    }
    
    // 2. 检查全局队列
    if sched.runqsize != 0 {
        gp := globrunqget(_p_, 0)
        if gp != nil {
            return gp
        }
    }
    
    // 3. 工作窃取
    for i := 0; i < 4; i++ {
        for enum := stealOrder.start(fastrand()); !enum.done(); enum.next() {
            p2 := &allp[enum.position()]
            if gp := runqsteal(_p_, p2, stealRunNextG); gp != nil {
                return gp
            }
        }
    }
    
    // 4. 检查网络轮询器
    if netpollinited() && atomic.Load(&netpollWaiters) > 0 {
        if gp := netpoll(false); gp != nil {
            return gp
        }
    }
    
    return nil
}
```

### 工作窃取算法

#### 基本原理
1. **本地优先**：优先从本地队列获取G
2. **全局补充**：本地队列空时从全局队列获取
3. **窃取机制**：全局队列也空时从其他P窃取

#### 窃取策略
```go
func runqsteal(_p_, p2 *p, stealRunNextG bool) *g {
    t := p2.runqtail
    n := t - p2.runqhead
    n = n - n/2  // 窃取一半
    
    if n == 0 {
        return nil
    }
    
    // 从队列尾部窃取
    h := atomic.LoadAcq(&p2.runqhead)
    if t-h < n {
        return nil
    }
    
    return p2.runq[(h+n-1)%uint32(len(p2.runq))]
}
```

#### 窃取顺序
- **随机化**：避免多个P同时窃取同一个P
- **公平性**：确保所有P都有机会被窃取
- **效率**：优先窃取队列较长的P

## 抢占机制

### 协作式抢占
- **触发点**：函数调用、系统调用、channel操作
- **检查机制**：栈增长检查时进行抢占检查
- **局限性**：紧密循环无法被抢占

### 抢占式调度（Go 1.14+）
- **信号机制**：使用SIGURG信号
- **异步抢占**：可以在任意时刻抢占
- **安全性**：确保在安全点进行抢占

#### 抢占流程
```go
// 1. 调度器标记需要抢占
gp.preempt = true
gp.stackguard0 = stackPreempt

// 2. 发送抢占信号
signalM(gp.m, sigPreempt)

// 3. 信号处理器处理抢占
func doSigPreempt(gp *g, ctxt *sigctxt) {
    if wantAsyncPreempt(gp) {
        asyncPreempt(ctxt)
    }
}
```

## 系统调用处理

### 阻塞系统调用
1. **M与P分离**：M进入系统调用，P寻找新的M
2. **P的重新分配**：空闲M获取P继续调度
3. **M的返回**：系统调用返回时尝试重新获取P

### 非阻塞系统调用
- **网络I/O**：使用netpoller处理
- **文件I/O**：在某些平台上使用异步I/O

## 性能优化

### 调度器优化技术
1. **本地队列**：减少全局锁竞争
2. **工作窃取**：实现负载均衡
3. **自旋等待**：避免频繁的线程切换
4. **批量操作**：减少调度开销

### 内存优化
1. **mcache**：每个P有独立的内存缓存
2. **对象池**：重用Goroutine栈和结构体
3. **栈管理**：动态调整栈大小

### 网络优化
1. **netpoller**：高效的网络事件处理
2. **边缘触发**：减少系统调用次数
3. **批量处理**：一次处理多个网络事件

## 调试和监控

### 调度器跟踪
```bash
GODEBUG=schedtrace=1000 go run main.go
```

输出格式：
```
SCHED 1004ms: gomaxprocs=4 idleprocs=0 threads=5 spinningthreads=0 idlethreads=1 runqueue=0 [4 0 0 1]
```

### 执行跟踪
```bash
go tool trace trace.out
```

### 性能分析
```bash
go tool pprof cpu.prof
```

## 最佳实践

### 1. 合理设置GOMAXPROCS
```go
// 通常设置为CPU核心数
runtime.GOMAXPROCS(runtime.NumCPU())
```

### 2. 避免Goroutine泄漏
```go
// 使用context控制Goroutine生命周期
ctx, cancel := context.WithCancel(context.Background())
defer cancel()

go func(ctx context.Context) {
    for {
        select {
        case <-ctx.Done():
            return
        default:
            // 工作逻辑
        }
    }
}(ctx)
```

### 3. 合理使用缓冲channel
```go
// 避免不必要的阻塞
ch := make(chan int, 100)  // 带缓冲的channel
```

### 4. 减少系统调用
```go
// 使用缓冲I/O
writer := bufio.NewWriter(file)
defer writer.Flush()
```

## 总结

GMP模型是Go语言高并发能力的基础，通过精心设计的三层架构实现了高效的M:N调度。理解GMP模型的工作原理对于编写高性能的Go程序至关重要。

关键要点：
1. **G**：轻量级协程，栈可动态调整
2. **M**：操作系统线程，执行G的代码
3. **P**：逻辑处理器，连接G和M的桥梁
4. **工作窃取**：实现负载均衡的核心算法
5. **抢占式调度**：保证调度公平性
6. **性能优化**：通过本地队列、批量操作等技术提升性能
