# 第1章参考资料汇总

## 官方文档

### Go语言官方资源
- **[Go官方文档](https://golang.org/doc/)**
  - 权威的Go语言文档
  - 包含语言规范、教程和最佳实践

- **[Go Blog - Concurrency](https://blog.golang.org/concurrency)**
  - Go官方博客关于并发的文章
  - 深入解释Go并发模型的设计理念

- **[Go Memory Model](https://golang.org/ref/mem)**
  - Go内存模型官方规范
  - 理解并发程序中的内存可见性

- **[Effective Go - Concurrency](https://golang.org/doc/effective_go.html#concurrency)**
  - 官方并发编程指南
  - 包含Goroutine和Channel的最佳实践

### 运行时源码
- **[Go Runtime Source](https://github.com/golang/go/tree/master/src/runtime)**
  - Go运行时源码
  - 调度器实现的第一手资料

- **[proc.go](https://github.com/golang/go/blob/master/src/runtime/proc.go)**
  - 调度器核心实现
  - 包含schedule()、findrunnable()等关键函数

- **[runtime2.go](https://github.com/golang/go/blob/master/src/runtime/runtime2.go)**
  - 运行时数据结构定义
  - G、M、P结构体的详细定义

## 设计文档

### 调度器设计
- **[Scalable Go Scheduler Design Doc](https://docs.google.com/document/d/1TTj4T2JO42uD5ID9e89oa0sLKhJYD0Y_kqxDv3I3XMw)**
  - Dmitry Vyukov的调度器设计文档
  - GMP模型的原始设计思路

- **[Go Preemptive Scheduler Design](https://github.com/golang/proposal/blob/master/design/24543-non-cooperative-preemption.md)**
  - 抢占式调度器设计提案
  - Go 1.14抢占式调度的设计原理

### 历史演进
- **[Go's work-stealing scheduler](https://rakyll.org/scheduler/)**
  - JBD的调度器分析文章
  - 工作窃取算法的详细解释

## 技术博客

### 深度分析文章
- **[Go调度器原理](https://www.ardanlabs.com/blog/2018/08/scheduling-in-go-part1.html)**
  - Ardan Labs的三部曲系列
  - 从基础到高级的完整分析

- **[图解Go运行时调度器](https://tonybai.com/2017/06/23/an-intro-to-goroutine-scheduler/)**
  - Tony Bai的调度器图解
  - 中文技术博客的经典文章

- **[Go语言调度器源代码情景分析](https://www.cnblogs.com/zkweb/p/7815600.html)**
  - 详细的源码分析
  - 结合代码理解调度器工作流程

### 性能优化
- **[Go Performance Tips](https://github.com/dgryski/go-perfbook)**
  - Damian Gryski的性能优化指南
  - 包含调度器相关的优化技巧

- **[High Performance Go Workshop](https://dave.cheney.net/high-performance-go-workshop/dotgo-paris.html)**
  - Dave Cheney的高性能Go研讨会
  - 实用的性能优化技术

## 学术论文

### 调度算法
- **[Work-stealing for multi-core systems](https://dl.acm.org/doi/10.1145/1810479.1810504)**
  - 工作窃取算法的学术研究
  - 理论基础和性能分析

- **[The Go Programming Language and Environment](https://talks.golang.org/2015/gophercon-goevolution.slide)**
  - Go语言设计演进
  - 包含调度器设计的历史背景

### 并发模型
- **[Communicating Sequential Processes](https://www.cs.cmu.edu/~crary/819-f09/Hoare78.pdf)**
  - Tony Hoare的CSP论文
  - Go并发模型的理论基础

## 视频资源

### 技术演讲
- **[GopherCon 2018: Kavya Joshi - The Scheduler Saga](https://www.youtube.com/watch?v=YHRO5WQGh0k)**
  - Kavya Joshi关于调度器的演讲
  - 生动的调度器工作原理解释

- **[dotGo 2017: JBD - Go's work-stealing scheduler](https://www.youtube.com/watch?v=Yx6FBsGNOp4)**
  - JBD关于工作窃取的演讲
  - 深入浅出的算法解释

### 在线课程
- **[Go并发编程实战](https://time.geekbang.org/course/intro/160)**
  - 极客时间的Go并发课程
  - 系统性的并发编程学习

## 工具和调试

### 性能分析工具
- **[pprof使用指南](https://golang.org/pkg/net/http/pprof/)**
  - Go官方性能分析工具
  - CPU和内存性能分析

- **[go tool trace](https://golang.org/cmd/trace/)**
  - 执行跟踪工具
  - 可视化调度器行为

### 调试技巧
- **[GODEBUG环境变量](https://golang.org/pkg/runtime/)**
  - 运行时调试选项
  - schedtrace等调度器跟踪参数

- **[Delve调试器](https://github.com/go-delve/delve)**
  - Go语言调试器
  - 可以调试Goroutine状态

## 开源项目

### 学习项目
- **[Go语言高级编程](https://github.com/chai2010/advanced-go-programming-book)**
  - 柴树杉等人的开源书籍
  - 包含详细的调度器分析

- **[Go语言圣经](https://github.com/gopl-zh/gopl-zh.github.com)**
  - The Go Programming Language中文版
  - 经典的Go语言学习资源

### 实践项目
- **[Go并发模式](https://github.com/lotusirous/go-concurrency-patterns)**
  - 常见的Go并发模式实现
  - 实用的代码示例

- **[Goroutine池实现](https://github.com/panjf2000/ants)**
  - 高性能的Goroutine池
  - 学习池化技术的好例子

## 社区资源

### 技术社区
- **[Go语言中文网](https://studygolang.com/)**
  - 中文Go语言社区
  - 丰富的技术文章和讨论

- **[Gopher Academy](https://gopheracademy.com/)**
  - Go语言技术文章
  - 高质量的技术内容

- **[r/golang](https://www.reddit.com/r/golang/)**
  - Reddit Go语言社区
  - 活跃的技术讨论

### 问答平台
- **[Stack Overflow - Go](https://stackoverflow.com/questions/tagged/go)**
  - Go语言相关问题
  - 调度器问题的解答

- **[Go Forum](https://forum.golangbridge.org/)**
  - Go语言官方论坛
  - 技术问题讨论

## 书籍推荐

### 入门书籍
- **《Go程序设计语言》**
  - Alan Donovan & Brian Kernighan
  - Go语言权威指南

- **《Go语言实战》**
  - William Kennedy等
  - 实战导向的Go学习

### 进阶书籍
- **《Go语言高级编程》**
  - 柴树杉 & 曹春晖
  - 深入Go运行时机制

- **《Go并发编程实战》**
  - 郝林
  - 专注于并发编程的深度解析

### 系统编程
- **《现代操作系统》**
  - Andrew Tanenbaum
  - 理解操作系统调度原理

- **《深入理解计算机系统》**
  - Randal Bryant & David O'Hallaron
  - 系统级编程的经典教材

## 实验环境

### 在线环境
- **[Go Playground](https://play.golang.org/)**
  - 在线Go代码运行环境
  - 快速验证代码片段

- **[Go by Example](https://gobyexample.com/)**
  - Go语言示例代码
  - 包含并发相关示例

### 本地环境
- **[Go安装指南](https://golang.org/doc/install)**
  - 官方安装指导
  - 各平台的安装方法

- **[VS Code Go扩展](https://marketplace.visualstudio.com/items?itemName=golang.Go)**
  - 强大的Go开发环境
  - 集成调试和性能分析工具

## 持续学习

### 技术博客订阅
- **[Go Blog](https://blog.golang.org/)**
  - Go官方博客
  - 最新技术动态

- **[Dave Cheney's Blog](https://dave.cheney.net/)**
  - Go核心开发者博客
  - 深度技术文章

### 会议和活动
- **[GopherCon](https://www.gophercon.com/)**
  - 全球最大的Go语言会议
  - 最新技术趋势

- **[Go Time Podcast](https://changelog.com/gotime)**
  - Go语言技术播客
  - 定期更新的技术讨论

## 学习路径建议

### 初学者路径
1. 阅读官方文档和Effective Go
2. 完成Go Tour和Go by Example
3. 阅读《Go程序设计语言》
4. 实践基础并发程序

### 进阶路径
1. 深入学习调度器原理
2. 阅读运行时源码
3. 学习性能分析工具
4. 参与开源项目

### 专家路径
1. 研究调度器设计文档
2. 贡献Go运行时代码
3. 分享技术文章和演讲
4. 指导其他开发者

---

**更新日期**：2024年7月
**维护者**：Go学习路线图项目组

**使用建议**：
- 根据自己的水平选择合适的资源
- 理论学习与实践相结合
- 定期回顾和更新知识
- 积极参与社区讨论
