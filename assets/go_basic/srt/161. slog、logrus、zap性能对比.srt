1
00:00:00,320 --> 00:00:02,700
那么我们之前已经讲了 log grass 

2
00:00:02,700 --> 00:00:03,860
讲了 zip 

3
00:00:03,860 --> 00:00:05,840
我们在基础篇也讲

4
00:00:05,840 --> 00:00:07,695
购物员标准库的 s log 

5
00:00:07,695 --> 00:00:11,000
我们把这三种方式进行一个性能的对比

6
00:00:11,000 --> 00:00:12,700
我们就不对比标准库的 log 了

7
00:00:12,700 --> 00:00:16,320
因为标准库 log 它并不支持那个级别的控制

8
00:00:16,320 --> 00:00:18,640
在生产环境中几乎没人会用

9
00:00:18,640 --> 00:00:20,350
为了保证对比的公平性嘛

10
00:00:20,350 --> 00:00:22,150
那么我们答案的所有位置

11
00:00:22,150 --> 00:00:24,650
都是这样的一个字符串啊

12
00:00:24,650 --> 00:00:25,190
常量

13
00:00:25,190 --> 00:00:29,292
然后呢，一致的时间格式都是这个时间格式

14
00:00:29,292 --> 00:00:33,750
初始化我们的 s log 对应的文件是 slog 点 log 

15
00:00:33,750 --> 00:00:36,880
它需要去上报日志发生的位置

16
00:00:36,880 --> 00:00:38,750
级别呢是 info 级别

17
00:00:38,750 --> 00:00:39,920
下面这一坨代码

18
00:00:39,920 --> 00:00:41,940
主要是为了控制那个时间格式啊

19
00:00:41,940 --> 00:00:43,920
时间格式设置为这样一种格式

20
00:00:43,920 --> 00:00:48,060
然后 as log 它默认的是那个 JSON 格式

21
00:00:48,060 --> 00:00:49,620
但是 as log 有一个特点

22
00:00:49,620 --> 00:00:52,690
就是它没有支持那个支持的滚动

23
00:00:52,690 --> 00:00:55,440
所以在这样一个性能对比实验里面

24
00:00:55,440 --> 00:00:57,480
slog 实际上是占了便宜了

25
00:00:57,480 --> 00:01:02,210
看一下 log grass 级别是 info 设置为 JSON 格式

26
00:01:02,210 --> 00:01:04,159
指定时间格式

27
00:01:04,159 --> 00:01:07,350
这边指定了带这个日志滚动上报位置

28
00:01:07,350 --> 00:01:10,840
看下 sap zapp 也是要支持日志的滚动

29
00:01:10,840 --> 00:01:13,860
指定时间格式指定为 JSON 格式

30
00:01:13,860 --> 00:01:16,410
级别为 info 上报位置

31
00:01:16,410 --> 00:01:19,130
所以三个框架提供的功能都是一样的

32
00:01:19,130 --> 00:01:21,590
除了 i s log 不支持日志滚动之外

33
00:01:21,590 --> 00:01:24,620
这边呢，就是开始搞基准测试了

34
00:01:24,620 --> 00:01:25,540
对于初始化

35
00:01:25,540 --> 00:01:28,580
我们是排除在了这个计时之外啊

36
00:01:28,580 --> 00:01:31,280
初始化之后才开始进行计时

37
00:01:31,280 --> 00:01:32,500
reset timer 

38
00:01:32,500 --> 00:01:35,090
除了打印这个日志内容本身之外呢

39
00:01:35,090 --> 00:01:37,910
他们都会带上另外的两个 field 

40
00:01:37,910 --> 00:01:40,320
这 name 等于 t k q edge 等于18

41
00:01:40,320 --> 00:01:42,750
每一个框架都会带 ZEPPP 呢

42
00:01:42,750 --> 00:01:44,840
这个写法会更麻烦一点

43
00:01:44,840 --> 00:01:46,990
但是呢，它可以规避反射

44
00:01:46,990 --> 00:01:48,560
最终在我本机上

45
00:01:48,560 --> 00:01:50,640
这个基准测试跑完之后的话

46
00:01:50,640 --> 00:01:52,205
是这样一个结果

47
00:01:52,205 --> 00:01:53,500
从这边来看的话

48
00:01:53,500 --> 00:01:57,920
还是标准库的 s log 性能会更高一些啊

49
00:01:57,920 --> 00:02:01,820
它每个 OP 号是只需要4700纳秒

50
00:02:01,820 --> 00:02:03,900
其次是 ZB 8000纳秒

51
00:02:03,900 --> 00:02:06,800
最慢的是 log grass 11000纳米

52
00:02:06,800 --> 00:02:07,840
还是那句话啊

53
00:02:07,840 --> 00:02:09,940
这个 s log 之所以表现这么优异

54
00:02:09,940 --> 00:02:12,670
是因为它没有那个日志滚动功能

55
00:02:12,670 --> 00:02:16,270
那前面这个数字21万、9万、14万

56
00:02:16,270 --> 00:02:18,990
它对应的就是 for 循环的次数

57
00:02:18,990 --> 00:02:22,760
实际也就是说打印的日志的条数啊

58
00:02:22,760 --> 00:02:24,920
你可以到对应的那个日志文件里面

59
00:02:24,920 --> 00:02:28,155
看看有没有这么多行日志

60
00:02:28,155 --> 00:02:30,460
既然 s log 是直接打开文

61
00:02:30,460 --> 00:02:31,600
往文件里面去写

62
00:02:31,600 --> 00:02:33,420
没有这个日志的滚动功能

63
00:02:33,420 --> 00:02:35,490
那我们也可以把 log grass 

64
00:02:35,490 --> 00:02:37,740
把这个滚动呢也给它除掉

65
00:02:37,740 --> 00:02:40,550
也替换成简单的打开文件

66
00:02:40,550 --> 00:02:42,520
对应的来到 zip 里边

67
00:02:42,520 --> 00:02:45,540
也是把之前的日志滚动功能给干掉

68
00:02:45,540 --> 00:02:48,187
替换成直接打开一个文件

69
00:02:48,187 --> 00:02:50,390
那在这样一个公平的情况之下

70
00:02:50,390 --> 00:02:51,430
我们再来对比

71
00:02:51,430 --> 00:02:53,850
结果就是这样子的

72
00:02:53,850 --> 00:02:55,890
zip 还是最快的

73
00:02:55,890 --> 00:02:57,930
只需要4300纳秒

74
00:02:57,930 --> 00:02:59,510
那 s log 稍微慢一点

75
00:02:59,510 --> 00:03:01,455
需要4800纳秒

76
00:03:01,455 --> 00:03:03,390
LOGRASS 就很慢了

77
00:03:03,390 --> 00:03:04,450
所以如果是我的话

78
00:03:04,450 --> 00:03:05,750
我选择 s log 

79
00:03:05,750 --> 00:03:08,490
一方面不需要引入额外的第三方库

80
00:03:08,490 --> 00:03:11,130
另外一方面呢，也不需要像 zip 那

81
00:03:11,130 --> 00:03:12,450
代码写起来比较麻烦
