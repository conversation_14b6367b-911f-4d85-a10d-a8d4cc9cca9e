1
00:00:00,900 --> 00:00:02,940
那么用户模块结束之后

2
00:00:02,940 --> 00:00:05,400
咱们就来看这个新闻模块啊

3
00:00:05,400 --> 00:00:07,260
其实跟用户管理模块是一样的

4
00:00:07,260 --> 00:00:09,400
套路都是先写 database 啊

5
00:00:09,400 --> 00:00:11,230
先写这个数据库这一层

6
00:00:11,230 --> 00:00:13,780
往上呢，写 handler 这一层

7
00:00:13,780 --> 00:00:16,239
那 handler 现在是把后端接口写好了吗

8
00:00:16,239 --> 00:00:18,600
我们就该写这个前端页面这一次

9
00:00:18,600 --> 00:00:19,940
当然，实际工作中

10
00:00:19,940 --> 00:00:23,140
我们的后端和前端是并行进行的

11
00:00:23,140 --> 00:00:26,350
咱们只需要把这个接口去解约定好，对吧

12
00:00:26,350 --> 00:00:29,110
你要请求我的哪个路径要传入参数

13
00:00:29,110 --> 00:00:30,270
参数名叫什么

14
00:00:30,270 --> 00:00:31,810
先通过文档约定好

15
00:00:31,810 --> 00:00:33,972
然后前后端可以并行开发

16
00:00:33,972 --> 00:00:36,760
那么前段、后段代码都写好之后的话

17
00:00:36,760 --> 00:00:38,640
最后咱们在 main 函数这边呢

18
00:00:38,640 --> 00:00:41,680
把对应的路由把它给关联起来嘛

19
00:00:41,680 --> 00:00:44,360
好，先来从数据库这一层看起

20
00:00:44,360 --> 00:00:46,620
那最开始我们要定义的是

21
00:00:46,620 --> 00:00:49,400
关于新闻的一个模型

22
00:00:49,400 --> 00:00:50,757
模型映射嘛

23
00:00:50,757 --> 00:00:54,360
得跟那个表里面的每个字段进行对应

24
00:00:54,360 --> 00:00:58,472
所以这些都是跟表里面的字段一一对应的

25
00:00:58,472 --> 00:01:00,260
但是有两个是横杠

26
00:01:00,260 --> 00:01:02,140
横杠表示不进行映射

27
00:01:02,140 --> 00:01:03,180
就是表里面没有

28
00:01:03,180 --> 00:01:04,660
但是结构体里面有

29
00:01:04,660 --> 00:01:06,590
比如说这个新闻啊

30
00:01:06,590 --> 00:01:09,847
表里面没有他的这个发布者的用户名称

31
00:01:09,847 --> 00:01:11,840
但是在前端页面上

32
00:01:11,840 --> 00:01:15,617
咱们需要展示发布者的用户名

33
00:01:15,617 --> 00:01:18,640
发布者 id 反而是不需要展示的

34
00:01:18,640 --> 00:01:20,660
就是他嘛

35
00:01:20,660 --> 00:01:23,092
新闻发布者的用户名

36
00:01:23,092 --> 00:01:25,280
还有就是新闻的发布时间

37
00:01:25,280 --> 00:01:27,120
那关于这个新闻发布时间啊

38
00:01:27,120 --> 00:01:30,965
实际上咱们是在后端对时间做了格式化

39
00:01:30,965 --> 00:01:33,820
直接把这样一个字符串传给前端

40
00:01:33,820 --> 00:01:35,800
前端就不需要做任何工作了啊

41
00:01:35,800 --> 00:01:37,530
直接展示就可以了

42
00:01:37,530 --> 00:01:41,030
所以本来这个时间它是一个 time 类型

43
00:01:41,030 --> 00:01:44,530
但是呢，咱们需要在后端转成一个字符串类型

44
00:01:44,530 --> 00:01:44,930
所以呢

45
00:01:44,930 --> 00:01:47,590
我就专门搞了一个额外的

46
00:01:47,590 --> 00:01:50,040
字符串类型的成员

47
00:01:50,040 --> 00:01:56,077
实际上就是对这个 post time 执行了格式化

48
00:01:56,077 --> 00:01:58,380
所以啊，我在设计这个结构的时候呢

49
00:01:58,380 --> 00:02:02,400
不光光是在考虑如何跟表进行对应

50
00:02:02,400 --> 00:02:05,390
同时我还要考虑我将来怎么返回

51
00:02:05,390 --> 00:02:07,270
给前端看一下

52
00:02:07,270 --> 00:02:09,390
关于新闻的增删改查

53
00:02:09,390 --> 00:02:12,340
第一个是我要发布一个新闻

54
00:02:12,340 --> 00:02:13,870
那发布新闻的话

55
00:02:13,870 --> 00:02:17,325
需要把新闻的标题和正文给传进来

56
00:02:17,325 --> 00:02:20,690
同时呢，我也知道这个新闻是谁发布的啊

57
00:02:20,690 --> 00:02:21,270
u i d 

58
00:02:21,270 --> 00:02:22,090
当然 UI

59
00:02:22,090 --> 00:02:24,462
我们实际上是从那个 cookie 里面

60
00:02:24,462 --> 00:02:26,020
把它给读出来

61
00:02:26,020 --> 00:02:28,360
那么我们在写 database 啊

62
00:02:28,360 --> 00:02:29,880
就是数据库这一层的话

63
00:02:29,880 --> 00:02:31,160
你不用关心啊

64
00:02:31,160 --> 00:02:32,420
这个 UID 怎么来

65
00:02:32,420 --> 00:02:36,610
那至于说从什么 cookie 里面去反解 UID 

66
00:02:36,610 --> 00:02:37,630
这一坨代码呢

67
00:02:37,630 --> 00:02:41,060
应该放到 handle 内层去写

68
00:02:41,060 --> 00:02:42,470
database 这一层啊

69
00:02:42,470 --> 00:02:44,890
先不用去太关心业务的细节

70
00:02:44,890 --> 00:02:47,472
反正你要求传一个 UID 进来

71
00:02:47,472 --> 00:02:49,300
那这样的话，根据 UID 啊

72
00:02:49,300 --> 00:02:50,500
根据标题和正文

73
00:02:50,500 --> 00:02:53,100
就能够去构造一个新闻实体

74
00:02:53,100 --> 00:02:55,640
那发布时间当然是此时此刻了

75
00:02:55,640 --> 00:02:59,260
然后呢，这个删除时间要置为 new 啊

76
00:02:59,260 --> 00:03:00,750
这个地方一定要置为 new 

77
00:03:00,750 --> 00:03:03,410
因为如果你不确定的话

78
00:03:03,410 --> 00:03:05,410
那么我们的这个 go 元啊

79
00:03:05,410 --> 00:03:08,855
它会默认的给这个删除时间赋一个零值

80
00:03:08,855 --> 00:03:14,077
然后呢，会把这个零值写到这个表里面去

81
00:03:14,077 --> 00:03:16,160
就付给这个 dd time 

82
00:03:16,160 --> 00:03:19,545
但是一旦这个它有值了

83
00:03:19,545 --> 00:03:23,040
就证明这条记录已经被删除了是吧

84
00:03:23,040 --> 00:03:25,060
咱们的判断逻辑是说

85
00:03:25,060 --> 00:03:26,840
如果这个字段为 NO 的话

86
00:03:26,840 --> 00:03:28,810
代表没有被删除啊

87
00:03:28,810 --> 00:03:31,870
所以的话，我们一定要为了保持它是 NO 

88
00:03:31,870 --> 00:03:35,130
那么我们对应到 go 代码里面呢

89
00:03:35,130 --> 00:03:36,720
就把它设置为 new 

90
00:03:36,720 --> 00:03:40,170
通过 create 去插入数据库

91
00:03:40,170 --> 00:03:42,472
这是新闻的发布

92
00:03:42,472 --> 00:03:44,630
然后会涉到删除

93
00:03:44,630 --> 00:03:46,500
删除的话咱们说过啊

94
00:03:46,500 --> 00:03:47,660
不并不是真删

95
00:03:47,660 --> 00:03:51,170
只是说给那个删除时间赋值

96
00:03:51,170 --> 00:03:52,870
本质上是修改啊

97
00:03:52,870 --> 00:03:54,010
实际上是修改

98
00:03:54,010 --> 00:03:58,130
所以这边是通过这个 update 啊

99
00:03:58,130 --> 00:04:01,125
来给这个字段呢赋值

100
00:04:01,125 --> 00:04:04,890
那对应的 where 条件就是你要删除哪个新闻吧

101
00:04:04,890 --> 00:04:06,410
你把姓名 id 传进来

102
00:04:06,410 --> 00:04:09,292
where id 等于某某某

103
00:04:09,292 --> 00:04:11,620
同时呢有一个 and 的条件

104
00:04:11,620 --> 00:04:14,780
就 and 它本来的这个删除时间啊

105
00:04:14,780 --> 00:04:16,380
is NO ，它就是空的

106
00:04:16,380 --> 00:04:17,921
因为你不能重复删除

107
00:04:17,921 --> 00:04:19,410
如果第二次删的话

108
00:04:19,410 --> 00:04:22,029
实际上根据我这个 where 条件

109
00:04:22,029 --> 00:04:24,342
就找不到任何记录了

110
00:04:24,342 --> 00:04:27,280
那同理，将来我们的查询

111
00:04:27,280 --> 00:04:28,360
我们的修改

112
00:04:28,360 --> 00:04:31,867
其实默认的都要把这个条件给带上

113
00:04:31,867 --> 00:04:35,160
好，要确保这个删除时间 is now 

114
00:04:35,160 --> 00:04:36,550
好，修改新闻

115
00:04:36,550 --> 00:04:39,890
比方说你想把标题或正文稍微改一改，对吧

116
00:04:39,890 --> 00:04:42,420
把新闻 id 传进来

117
00:04:42,420 --> 00:04:46,555
把修改后的标题和正文传进来

118
00:04:46,555 --> 00:04:48,630
那么我们要修改多练嘛

119
00:04:48,630 --> 00:04:49,530
就标题和正文

120
00:04:49,530 --> 00:04:51,520
所以呢，使用的是 updates 

121
00:04:51,520 --> 00:04:53,400
还有个地方是加了一个 S 啊

122
00:04:53,400 --> 00:04:57,430
上面的话只修改一列是 update ，没加 S 

123
00:04:57,430 --> 00:04:59,000
那这边加 S 的话

124
00:04:59,000 --> 00:05:01,320
它对应的是一个 map 作为参数啊

125
00:05:01,320 --> 00:05:03,600
你要修改的是 title 和 article 

126
00:05:03,600 --> 00:05:07,400
这两列对应的 where 条件是 id 等于某某某

127
00:05:07,400 --> 00:05:09,960
并且呢，删除时间为空

128
00:05:09,960 --> 00:05:14,570
通过 model 来指定表明你要修改哪个表

129
00:05:14,570 --> 00:05:15,950
然后还要查询

130
00:05:15,950 --> 00:05:20,025
这是我们要进到这个新闻的详情页码

131
00:05:20,025 --> 00:05:22,030
前端，把新 id 传过来

132
00:05:22,030 --> 00:05:23,960
你要把新闻的正文呐

133
00:05:23,960 --> 00:05:26,330
标题呀、发布者呀、发布时间啊

134
00:05:26,330 --> 00:05:28,082
全部返回给前端

135
00:05:28,082 --> 00:05:31,280
所以这边就是 select 查询嘛

136
00:05:31,280 --> 00:05:34,420
这个是默认的 where 条件

137
00:05:34,420 --> 00:05:37,180
然后我这个 id 呢

138
00:05:37,180 --> 00:05:40,040
是给这个结构体赋值了啊

139
00:05:40,040 --> 00:05:42,860
通过这个结构体暗含着

140
00:05:42,860 --> 00:05:45,080
就他就默认的带了一个 where 条件

141
00:05:45,080 --> 00:05:46,520
就 where i d 等于它

142
00:05:46,520 --> 00:05:47,880
哎，把这个结果呢

143
00:05:47,880 --> 00:05:50,170
再付给这个 news first 

144
00:05:50,170 --> 00:05:51,350
就只取第一条嘛

145
00:05:51,350 --> 00:05:52,870
他肯定最多只有一条结果

146
00:05:52,870 --> 00:05:54,290
因为是 id 嘛

147
00:05:54,290 --> 00:05:55,430
唯一的

148
00:05:55,430 --> 00:05:57,790
那这个地方我在返回结果的时候呢

149
00:05:57,790 --> 00:06:03,090
我还顺带的去给他的这个 view post time 给它赋值了

150
00:06:03,090 --> 00:06:04,900
因为它是一个字符串嘛

151
00:06:04,900 --> 00:06:09,080
啊，需要从这个 time 格式化把它转为字符串

152
00:06:09,080 --> 00:06:10,320
然后再返回

153
00:06:10,320 --> 00:06:12,500
所以这是在数据库这一集

154
00:06:12,500 --> 00:06:16,350
那我为什么只给这个 view post time 赋值了

155
00:06:16,350 --> 00:06:20,300
而没有给这个 username 赋值呢

156
00:06:20,300 --> 00:06:22,642
因为你需要连表去查另外一个表

157
00:06:22,642 --> 00:06:25,000
用户表才能够得到用户名吗

158
00:06:25,000 --> 00:06:26,550
为什么没有给它赋值呢

159
00:06:26,550 --> 00:06:28,835
因为我的一个原则是说

160
00:06:28,835 --> 00:06:30,970
那么我在数据库这一层

161
00:06:30,970 --> 00:06:33,130
我所有的这个函数啊

162
00:06:33,130 --> 00:06:35,130
都是单表查询

163
00:06:35,130 --> 00:06:37,037
就只涉及到一个表

164
00:06:37,037 --> 00:06:39,020
如果说你在这个函数里面

165
00:06:39,020 --> 00:06:41,460
同时又去查那个用户表了

166
00:06:41,460 --> 00:06:43,570
那么就违背了我的一个设计原则

167
00:06:43,570 --> 00:06:47,990
就是我既然把它分开到了 news 点 go 和 use 点 go 

168
00:06:47,990 --> 00:06:49,410
两个购物文件里面

169
00:06:49,410 --> 00:06:51,440
那么我认为一个 go 文件啊

170
00:06:51,440 --> 00:06:52,960
就只操作一个表

171
00:06:52,960 --> 00:06:55,190
就不要这个交替

172
00:06:55,190 --> 00:06:56,460
那么所以呢

173
00:06:56,460 --> 00:06:59,040
将来我如果想给那个用户名赋值的话

174
00:06:59,040 --> 00:07:02,232
我是把它放到了 handler 那一层去做

175
00:07:02,232 --> 00:07:04,370
然后我们思考一下这个页面怎么做

176
00:07:04,370 --> 00:07:06,230
这是新闻的列表页

177
00:07:06,230 --> 00:07:07,590
在这个列表里面的话

178
00:07:07,590 --> 00:07:10,317
虽然只展示标题、作者和发布时间

179
00:07:10,317 --> 00:07:12,200
但是它毕竟是一个列表对吧

180
00:07:12,200 --> 00:07:13,200
那列表可能有分页嘛

181
00:07:13,200 --> 00:07:16,020
比方说我点这个箭头，哎

182
00:07:16,020 --> 00:07:18,560
可以来到第二页，对吧

183
00:07:18,560 --> 00:07:19,760
然后这是第三页

184
00:07:19,760 --> 00:07:22,320
可以往前翻第二页

185
00:07:22,320 --> 00:07:26,180
第一页新闻可能会非常多

186
00:07:26,180 --> 00:07:28,180
所以呢，你便要分一页展示

187
00:07:28,180 --> 00:07:30,257
那么分页展示的时候呢

188
00:07:30,257 --> 00:07:32,750
通常需要展示总数，对吧

189
00:07:32,750 --> 00:07:34,120
总共有七篇

190
00:07:34,120 --> 00:07:36,390
那么这个接口该怎么设计啊

191
00:07:36,390 --> 00:07:38,510
一般最好我们都是传两个参数

192
00:07:38,510 --> 00:07:41,770
第一个就是你设计的每一页里面

193
00:07:41,770 --> 00:07:43,110
展示几条新闻

194
00:07:43,110 --> 00:07:45,510
就称之为 page size 

195
00:07:45,510 --> 00:07:48,217
此处 page size 等于三

196
00:07:48,217 --> 00:07:50,180
然后就是你要展示第几页

197
00:07:50,180 --> 00:07:51,980
第一页、第二页、第三页，对吧

198
00:07:51,980 --> 00:07:53,945
把这两个参数传给后端

199
00:07:53,945 --> 00:07:56,160
那后端把对付数据返回给前端

200
00:07:56,160 --> 00:07:56,800
同时呢

201
00:07:56,800 --> 00:08:00,900
后端还要把库里面总共的没有被删除的量

202
00:08:00,900 --> 00:08:02,695
也返回给前端

203
00:08:02,695 --> 00:08:05,400
注意啊，是你每请求一页

204
00:08:05,400 --> 00:08:08,570
我都会把这个总数返回给你

205
00:08:08,570 --> 00:08:10,140
而且默认情况下

206
00:08:10,140 --> 00:08:13,500
我是按照这个发布时间降序排列的啊

207
00:08:13,500 --> 00:08:16,777
最后发布的显示在第一页

208
00:08:16,777 --> 00:08:18,510
看一下这个函数啊

209
00:08:18,510 --> 00:08:21,480
把这个第几页和每页大小呢

210
00:08:21,480 --> 00:08:23,417
作为参数传进来

211
00:08:23,417 --> 00:08:27,080
返回的是一个由新闻构成的集合

212
00:08:27,080 --> 00:08:27,845
一个切片

213
00:08:27,845 --> 00:08:29,580
而第一个参数 int 

214
00:08:29,580 --> 00:08:32,869
表示是库里面总共有多少条新闻

215
00:08:32,869 --> 00:08:35,502
但是未被删除的

216
00:08:35,502 --> 00:08:36,669
所以第一步啊

217
00:08:36,669 --> 00:08:38,987
我是要去拿到这个总量

218
00:08:38,987 --> 00:08:43,020
那么总分的话是通过调用 count 函数啊

219
00:08:43,020 --> 00:08:45,240
直接把这个数量呢

220
00:08:45,240 --> 00:08:47,715
付给一个因特留斯

221
00:08:47,715 --> 00:08:49,200
而 our 是有 where 条件的

222
00:08:49,200 --> 00:08:52,520
where 条件就是删除时间为空啊

223
00:08:52,520 --> 00:08:53,600
还没有被删除

224
00:08:54,670 --> 00:08:58,217
然后再来看这个切片该怎么生成

225
00:08:58,217 --> 00:09:01,907
where 条件是没有被删除

226
00:09:01,907 --> 00:09:05,390
然后按照这个发布时间降序排列

227
00:09:05,390 --> 00:09:10,035
然后你不是要只拿到当前这一页的数据吗

228
00:09:10,035 --> 00:09:12,950
我通过 limit 和 offset 来完成

229
00:09:12,950 --> 00:09:16,030
那 limit 表示你要取几条结果

230
00:09:16,030 --> 00:09:18,850
这个 limit 就是一页的大小啊

231
00:09:18,850 --> 00:09:19,900
psize 

232
00:09:19,900 --> 00:09:22,360
我只要三个结果

233
00:09:22,360 --> 00:09:25,010
那么我当前是请求第二页

234
00:09:25,010 --> 00:09:26,170
那么请求第二页

235
00:09:26,170 --> 00:09:29,010
就意味着我要把第一页的那三个给跳过

236
00:09:29,010 --> 00:09:32,030
所以这个 offset 是什么呢

237
00:09:32,030 --> 00:09:34,130
这个 offset 就应该是

238
00:09:35,380 --> 00:09:37,520
每页大小乘以上

239
00:09:37,520 --> 00:09:40,415
当前页数减一，2-1嘛

240
00:09:40,415 --> 00:09:42,080
等于一，1×3=3

241
00:09:42,080 --> 00:09:44,240
就我要跳过前三

242
00:09:44,240 --> 00:09:46,340
往后再给我取三个

243
00:09:46,340 --> 00:09:49,157
这个就是第二页的展示内容

244
00:09:49,157 --> 00:09:51,550
好，把这个结果呢，付给这个切片

245
00:09:51,550 --> 00:09:56,165
最后把总量和这个切片进行返回

246
00:09:56,165 --> 00:09:57,650
在返回之前的话

247
00:09:57,650 --> 00:09:59,790
我还是需要去便利这个切片啊

248
00:09:59,790 --> 00:10:01,430
给切片里面每一个元素呢

249
00:10:01,430 --> 00:10:04,772
给他的这个 view post time 进行赋值

250
00:10:04,772 --> 00:10:08,090
跟上面我查询单个是吧

251
00:10:08,090 --> 00:10:09,730
根据 id 查询单个新

252
00:10:09,730 --> 00:10:10,870
这个逻辑是一样的嘛

253
00:10:10,870 --> 00:10:13,490
都需要给这个数据库里面没有的字段

254
00:10:13,490 --> 00:10:15,027
额外的赋值

255
00:10:15,027 --> 00:10:16,440
这是 dB 这一层

256
00:10:16,440 --> 00:10:18,330
我们再来看 handler 这一层

257
00:10:18,330 --> 00:10:23,200
我们来到 HLE 展开简 news 

258
00:10:23,200 --> 00:10:24,930
第一个发布新闻

259
00:10:24,930 --> 00:10:26,820
那发布新闻的

260
00:10:26,820 --> 00:10:29,120
就是我们点这个发表按钮

261
00:10:29,120 --> 00:10:30,370
还有这个页面对吧

262
00:10:30,370 --> 00:10:34,570
用户在前一个页面上填好了标题和正文

263
00:10:34,570 --> 00:10:36,850
然后点提交就发布了

264
00:10:36,850 --> 00:10:40,230
发布的话对应到的就是这个 header 

265
00:10:40,230 --> 00:10:43,300
那么我从这个 context 里面

266
00:10:43,300 --> 00:10:45,690
我需要取得正文和标题

267
00:10:45,690 --> 00:10:48,222
哎，又要搞那个参数绑定了

268
00:10:48,222 --> 00:10:50,350
所以呢，我就在 handler 这边啊

269
00:10:50,350 --> 00:10:52,090
又搞了个对应的 model 对吧

270
00:10:52,090 --> 00:10:56,812
model 里面主要是为了跟我的请求参数进行绑定

271
00:10:56,812 --> 00:10:59,820
包含标题、正文

272
00:10:59,820 --> 00:11:00,900
这个 id 的话

273
00:11:00,900 --> 00:11:02,560
目前这个接口里面用不到 id 

274
00:11:02,560 --> 00:11:03,900
他接口可能会用到 id 

275
00:11:03,900 --> 00:11:05,907
所以呢，把 id 给放这了

276
00:11:05,907 --> 00:11:06,870
绑定完之后

277
00:11:06,870 --> 00:11:08,610
我就可以从这个 news 

278
00:11:08,610 --> 00:11:12,370
从参数里面来拿到标题和正文

279
00:11:12,370 --> 00:11:16,220
然后去调这个 database 这一层的发布新闻

280
00:11:16,220 --> 00:11:17,310
而此时呢

281
00:11:17,310 --> 00:11:19,145
需要传 UID 

282
00:11:19,145 --> 00:11:20,850
这个 UID 怎么来

283
00:11:20,850 --> 00:11:22,180
UID 的话

284
00:11:22,180 --> 00:11:25,040
他是从那个 cookie 里面取出来的

285
00:11:25,040 --> 00:11:27,260
而这个地方呢，我实际上没有取 cookie 

286
00:11:27,260 --> 00:11:30,620
而是直接从这个 context 里面把 UID 取出来

287
00:11:30,620 --> 00:11:31,760
为什么可以这么搞呢

288
00:11:31,760 --> 00:11:33,255
就是因为啊

289
00:11:33,255 --> 00:11:35,270
来到我的 main 函数

290
00:11:35,270 --> 00:11:37,400
我在定义路由的时候

291
00:11:37,400 --> 00:11:39,820
就这个啊， post news 

292
00:11:39,820 --> 00:11:43,260
在正式进入到发布新闻这个 handler 之前

293
00:11:43,260 --> 00:11:44,940
他会先过一个 WTH 

294
00:11:44,940 --> 00:11:46,970
而在 voice 里面

295
00:11:46,970 --> 00:11:50,680
他就从那个 cookie 里面把 UID 给提出来

296
00:11:50,680 --> 00:11:54,375
把 UID 放到了 context 里面去

297
00:11:54,375 --> 00:11:55,560
那所以呢

298
00:11:55,560 --> 00:11:57,302
刚才看

299
00:11:57,302 --> 00:11:59,990
我就可以直接从 context 里面取出

300
00:11:59,990 --> 00:12:02,250
当前登录的用户 id 

301
00:12:02,250 --> 00:12:03,630
好，把新闻呢

302
00:12:03,630 --> 00:12:05,535
写到库里面去

303
00:12:05,535 --> 00:12:10,510
然后把写成功的新闻 id 再返回给前端

304
00:12:10,510 --> 00:12:12,500
为什么我要把这个新闻 id 

305
00:12:12,500 --> 00:12:13,990
立刻返回给前端呢

306
00:12:13,990 --> 00:12:16,440
因为啊，我随便填一下

307
00:12:16,440 --> 00:12:19,620
就是我发布成功之后点提

308
00:12:19,620 --> 00:12:20,640
好，成功了

309
00:12:20,640 --> 00:12:21,760
那成功之后的话

310
00:12:21,760 --> 00:12:26,430
它需要自动的跳转到这个新闻的详情页，对吧

311
00:12:26,430 --> 00:12:28,670
那这个新闻详情页实际上是我在

312
00:12:28,670 --> 00:12:32,110
你看 UL 里面背后这个12是什么

313
00:12:32,110 --> 00:12:34,970
这个12就是这个新闻的 id 

314
00:12:34,970 --> 00:12:35,910
那我前段

315
00:12:35,910 --> 00:12:36,947
我得知

316
00:12:36,947 --> 00:12:39,480
我刚刚创建的这个新闻 id 是几呀

317
00:12:39,480 --> 00:12:43,070
这样的话我才能去拼接这个请求的 URLL 嘛

318
00:12:43,070 --> 00:12:47,615
所以呢，是在这个地方后端给我返回的 id 

319
00:12:47,615 --> 00:12:49,230
好，刚刚才看到了对吧

320
00:12:49,230 --> 00:12:52,270
前端把这个新闻 id 传过来啊

321
00:12:52,270 --> 00:12:54,260
我想获得新闻的详情

322
00:12:54,260 --> 00:12:57,247
而从那个 restful 风

323
00:12:57,247 --> 00:12:58,960
就是直接把这个12

324
00:12:58,960 --> 00:13:01,160
要把这个 UR 路径来当成一个参数吗

325
00:13:01,160 --> 00:13:02,860
从这个地方来取得1

326
00:13:02,860 --> 00:13:07,730
这个参数值呢，是通过调 context terroh 得

327
00:13:07,730 --> 00:13:11,357
这是一个字符串转成数字

328
00:13:11,357 --> 00:13:13,220
然后直接调数据库对吧

329
00:13:13,220 --> 00:13:16,385
根据 id 来获得这个新闻的详情

330
00:13:16,385 --> 00:13:20,760
那么在正式返回这个新闻详情之前的话

331
00:13:20,760 --> 00:13:24,860
哎，我拿这个新闻的用户 id 业务去查了用户表

332
00:13:24,860 --> 00:13:27,240
来获得这个用户的用户名

333
00:13:27,240 --> 00:13:28,460
把这个用户名呢

334
00:13:28,460 --> 00:13:31,780
付给了这个新闻的这个发布的名称

335
00:13:31,780 --> 00:13:35,370
为什么需要这个新闻的发布者用户名呢

336
00:13:35,370 --> 00:13:38,360
是因为我在前端的这个地方是吧

337
00:13:38,360 --> 00:13:39,500
在新闻详情页上

338
00:13:39,500 --> 00:13:41,460
我需要知道用户名是什么

339
00:13:41,460 --> 00:13:45,880
然后为前端返回的是这样一个网页啊

340
00:13:45,880 --> 00:13:47,282
浅动页面

341
00:13:47,282 --> 00:13:50,030
只不过说在这个页面里面呢

342
00:13:50,030 --> 00:13:51,770
需要填充一些信息啊

343
00:13:51,770 --> 00:13:53,810
需要把那个标题正文填充过去

344
00:13:53,810 --> 00:13:57,690
而是通过带了一个结构体给它填充的

345
00:13:57,690 --> 00:13:58,550
那么有时候的话

346
00:13:58,550 --> 00:14:01,145
我们也可以带一个 map 

347
00:14:01,145 --> 00:14:02,550
不管是带 map 也好

348
00:14:02,550 --> 00:14:04,370
还是带这个结构体也好

349
00:14:04,370 --> 00:14:06,550
那最终我们在前端页面里面

350
00:14:06,550 --> 00:14:09,187
我们看一下这个 news ，点 HTML 

351
00:14:09,187 --> 00:14:10,560
我们看这个地方是吧

352
00:14:10,560 --> 00:14:12,760
点 title 和点 content 

353
00:14:12,760 --> 00:14:18,440
那这个 title 和 content 就是那个结构体的成员变量

354
00:14:18,440 --> 00:14:19,970
我们看这个 news 

355
00:14:19,970 --> 00:14:23,102
就这个 title 和 content 

356
00:14:23,102 --> 00:14:26,250
这个是删除新闻的 HLER 

357
00:14:26,250 --> 00:14:30,410
那么我需要从这个 restful 风格参数里面

358
00:14:30,410 --> 00:14:33,510
获得要删除的博客 id 

359
00:14:33,510 --> 00:14:37,410
因为发起删除它是从这个新闻的详情

360
00:14:37,410 --> 00:14:37,810
这对吧

361
00:14:37,810 --> 00:14:39,530
详情页这有一个删除按钮嘛

362
00:14:39,530 --> 00:14:42,257
所以你在这个地方是能够知道

363
00:14:42,257 --> 00:14:44,160
这个新闻答 id 的

364
00:14:44,160 --> 00:14:47,072
因为你 UL 里面就有新闻外 id 嘛

365
00:14:47,072 --> 00:14:48,910
所以我点这个删除引呢

366
00:14:48,910 --> 00:14:51,840
能够把新 m id 传给后端

367
00:14:51,840 --> 00:14:54,005
那后端拿到新 v id 

368
00:14:54,005 --> 00:14:58,530
他直接去调这个数据库的删除函数

369
00:14:58,530 --> 00:14:59,710
那只不过呢

370
00:14:59,710 --> 00:15:00,690
在删除之前

371
00:15:00,690 --> 00:15:02,112
他要做一个验证

372
00:15:02,112 --> 00:15:05,810
他验证一下你当前用户有没有登录

373
00:15:05,810 --> 00:15:06,740
如果登录了

374
00:15:06,740 --> 00:15:11,080
那么你用户 id 跟这个新闻的发布者 id 

375
00:15:11,080 --> 00:15:12,570
是不是同一个人

376
00:15:12,570 --> 00:15:14,210
就是同一个人的前提下

377
00:15:14,210 --> 00:15:16,247
你才能够去删除这个行为

378
00:15:16,247 --> 00:15:17,120
所以啊

379
00:15:17,120 --> 00:15:20,240
你看我又从这个 context 里面

380
00:15:20,240 --> 00:15:22,975
去取得当前登录的用户 id 

381
00:15:22,975 --> 00:15:27,160
然后呢，我去判断一下这个新闻 id 

382
00:15:27,160 --> 00:15:29,840
它是不是属于这个登录者 id 的

383
00:15:29,840 --> 00:15:31,160
而这个判断函数呢

384
00:15:31,160 --> 00:15:36,772
在下面根据新闻 id 获得新闻这个实体

385
00:15:36,772 --> 00:15:39,130
看一下这个新闻的作者 id 

386
00:15:39,130 --> 00:15:41,170
跟这个 UID 是否相等

387
00:15:41,170 --> 00:15:41,992
是吧

388
00:15:41,992 --> 00:15:44,300
是否有这个从属关系吗

389
00:15:44,300 --> 00:15:45,600
那如果不是的话

390
00:15:45,600 --> 00:15:48,410
对不起，没有权限删除啊

391
00:15:48,410 --> 00:15:49,930
否则才可以继续往后走啊

392
00:15:49,930 --> 00:15:53,980
才可以调这个 database 去删除这个新闻

393
00:15:53,980 --> 00:15:54,520
当然了

394
00:15:54,520 --> 00:15:55,680
这个是假删啊

395
00:15:55,680 --> 00:15:58,895
只是给那个删除时间赋了一个值而已

396
00:15:58,895 --> 00:16:00,040
再来看修改

397
00:16:00,040 --> 00:16:01,780
那其实修改跟上面这个删除

398
00:16:01,780 --> 00:16:04,000
就逻辑非常类似了

399
00:16:04,000 --> 00:16:06,080
通过 BD 来绑定

400
00:16:06,080 --> 00:16:09,912
你希望修改之后的标题和正文是什么

401
00:16:09,912 --> 00:16:12,410
调数据库的修改函数啊

402
00:16:12,410 --> 00:16:16,080
把修改为最新的标题和正文

403
00:16:16,080 --> 00:16:19,690
那在吸管之前还是要去判断一

404
00:16:19,690 --> 00:16:21,630
当前登录者的用户 id 

405
00:16:21,630 --> 00:16:24,900
是不是曾经发布过这一篇新闻

406
00:16:24,900 --> 00:16:27,242
是否有修改权限

407
00:16:27,242 --> 00:16:29,240
再来看这个新闻的列表页

408
00:16:29,240 --> 00:16:30,835
就分页查询嘛

409
00:16:30,835 --> 00:16:33,540
我打算是从那个 URL 里面问号

410
00:16:33,540 --> 00:16:38,347
后面去提取出页数和每页大小这两个参数

411
00:16:38,347 --> 00:16:43,782
然后去调数据库来获得对应的新闻信息

412
00:16:43,782 --> 00:16:48,340
在最终把这个集合返回给前端页面之前

413
00:16:49,370 --> 00:16:51,270
我会去调那个数据库啊

414
00:16:51,270 --> 00:16:56,082
根据新闻的作者 id 来得到对应的用户名

415
00:16:56,082 --> 00:16:59,715
因为在列表页也需要展示发布者的用户名嘛

416
00:16:59,715 --> 00:17:02,740
最终返回的是这样一个前端页面

417
00:17:02,740 --> 00:17:04,380
只不过在这个页面里面的话

418
00:17:04,380 --> 00:17:06,780
我需要给它填充一些信息

419
00:17:06,780 --> 00:17:08,829
包括这个总数啊

420
00:17:08,829 --> 00:17:11,079
而这个 data 它实际上是一个集合啊

421
00:17:11,079 --> 00:17:12,038
一个切片

422
00:17:12,038 --> 00:17:15,160
包括我要把当前的这个页数啊

423
00:17:15,160 --> 00:17:18,529
配件也一并的返回给前端

424
00:17:18,529 --> 00:17:20,790
那这个配解之所以返回前端

425
00:17:20,790 --> 00:17:21,949
是因为啊

426
00:17:21,949 --> 00:17:24,730
我在这个地方展示的这个一啊

427
00:17:24,730 --> 00:17:28,060
或者说展示的这个二这个数字

428
00:17:28,060 --> 00:17:29,650
那其实就是什么

429
00:17:29,650 --> 00:17:32,585
就是由后端给我返回过来的

430
00:17:32,585 --> 00:17:36,650
那前端在拿到这样一个切片之后的话

431
00:17:36,650 --> 00:17:40,510
它需要通过 range 啊去便利这个集合

432
00:17:40,510 --> 00:17:42,690
那实际就涉及到 go 原里

433
00:17:42,690 --> 00:17:44,892
hml template 这个语法了

434
00:17:44,892 --> 00:17:47,040
这边是 data 对吧

435
00:17:47,040 --> 00:17:49,287
我们来看一下前端代

436
00:17:49,287 --> 00:17:53,455
这边啊，通过 range 哈去便利这个 data 

437
00:17:53,455 --> 00:17:54,810
这边是 and 对吧

438
00:17:54,810 --> 00:17:57,070
这是 for 循环的开始位置

439
00:17:57,070 --> 00:17:58,150
这个结束位置嘛

440
00:17:58,150 --> 00:17:59,550
那么在这个之间啊

441
00:17:59,550 --> 00:18:00,530
在这个之间的

442
00:18:00,530 --> 00:18:03,040
就拿到了某一条新闻

443
00:18:03,040 --> 00:18:04,650
切片里面的某一个元素

444
00:18:04,650 --> 00:18:06,390
那针对这一个元素来说

445
00:18:06,390 --> 00:18:08,915
它又有什么 id 呀

446
00:18:08,915 --> 00:18:13,890
什么标题呀、发布者的用户名啊、发布时间呐

447
00:18:13,890 --> 00:18:15,840
他把这些信息呢

448
00:18:15,840 --> 00:18:18,285
放到不同的列里面去

449
00:18:18,285 --> 00:18:20,840
然后你这个 for 循环能够 for 循环多少次

450
00:18:20,840 --> 00:18:22,920
那么我就往前端页面里面去

451
00:18:22,920 --> 00:18:24,300
给他插入几个 T 2

452
00:18:24,300 --> 00:18:26,010
就插入几行嘛

453
00:18:26,010 --> 00:18:28,640
然后这边还提供了一个 HLER 一个接口

454
00:18:28,640 --> 00:18:32,300
就是我要判断一下一个新闻

455
00:18:32,300 --> 00:18:35,820
它是不是由当前登录者发布的啊

456
00:18:35,820 --> 00:18:37,850
你把这个新 id 传过来

457
00:18:37,850 --> 00:18:40,100
然后我可以从 cookie 里面来取得

458
00:18:40,100 --> 00:18:41,760
当前登录的用户 id 

459
00:18:41,760 --> 00:18:45,390
判断一下这个新闻是不是这个用户发布的

460
00:18:45,390 --> 00:18:47,420
为什么需要这样一个接口呢

461
00:18:47,420 --> 00:18:48,800
就是因为

462
00:18:48,800 --> 00:18:51,600
比如说我进到这篇新闻的详情页

463
00:18:52,610 --> 00:18:57,330
那么创建者和当前登录者是同一个人吗

464
00:18:57,330 --> 00:19:01,670
所以的话，这边有这个删除和修改按钮

465
00:19:01,670 --> 00:19:03,000
但如果换一篇

466
00:19:03,000 --> 00:19:04,650
我想返回列表页

467
00:19:04,650 --> 00:19:06,470
我换另外一个新闻

468
00:19:06,470 --> 00:19:07,835
好，这个

469
00:19:07,835 --> 00:19:08,860
那么这个新闻

470
00:19:08,860 --> 00:19:11,820
他的这个发布者和当前登录者

471
00:19:11,820 --> 00:19:12,900
就不是同一个人

472
00:19:12,900 --> 00:19:14,150
那不是同一个人的话

473
00:19:14,150 --> 00:19:17,860
在左上角就没有那个删除和修改按钮

474
00:19:17,860 --> 00:19:18,780
所以的话

475
00:19:18,780 --> 00:19:19,840
前端页面啊

476
00:19:19,840 --> 00:19:21,400
在加载这个页面的时候

477
00:19:21,400 --> 00:19:23,090
他可以判断一下

478
00:19:23,090 --> 00:19:26,770
我要不要展示删除和修改按钮

479
00:19:26,770 --> 00:19:30,260
所以呢，他会把这个新闻的 id 啊

480
00:19:30,260 --> 00:19:31,080
传给后端

481
00:19:31,080 --> 00:19:32,070
问问后端

482
00:19:32,070 --> 00:19:36,205
那么对应的这是我们的这个接口

483
00:19:36,205 --> 00:19:38,660
所以我们看一下，在定义路由时

484
00:19:38,660 --> 00:19:40,760
像这个发布新闻啊

485
00:19:40,760 --> 00:19:44,632
前面会过一个 worth 中间件

486
00:19:44,632 --> 00:19:47,330
包括删除新闻

487
00:19:47,330 --> 00:19:49,620
包括修改新闻

488
00:19:49,620 --> 00:19:51,842
都要过一个 words 文

489
00:19:51,842 --> 00:19:54,087
来取得那个用户 id 吗

490
00:19:54,087 --> 00:19:55,330
而且这个地方呢

491
00:19:55,330 --> 00:19:57,450
还用到了基安的分组

492
00:19:57,450 --> 00:20:01,550
就是我认为后面的跟新闻相关的这一条路径

493
00:20:01,550 --> 00:20:03,260
他们都有一个公共的前缀

494
00:20:03,260 --> 00:20:04,555
就是这个 news 

495
00:20:04,555 --> 00:20:06,160
所以这些路径啊

496
00:20:06,160 --> 00:20:08,180
他们实际上前面都有一个 news 

497
00:20:08,180 --> 00:20:10,135
因为他们都属于这个分组嘛

498
00:20:10,135 --> 00:20:11,380
这边还有一行

499
00:20:11,380 --> 00:20:13,020
就是说如果说用户请求的时候

500
00:20:13,020 --> 00:20:17,380
整个网站根目录就是 IP 加多少号

501
00:20:17,380 --> 00:20:19,030
直接回车了

502
00:20:19,030 --> 00:20:22,800
那么这样的话，我会把它通过 redirect 啊

503
00:20:22,800 --> 00:20:26,500
把它重定向到我的新闻列表页

504
00:20:26,500 --> 00:20:31,220
就重定向到这个 header 里面来啊

505
00:20:31,220 --> 00:20:35,150
因为我在整个上面其实都没有去定义

506
00:20:35,150 --> 00:20:38,230
我的那个根目录对应的汉字是什么啊

507
00:20:38,230 --> 00:20:40,780
根目录是在这儿进行定义的

508
00:20:40,780 --> 00:20:41,950
比如说我现在啊

509
00:20:41,950 --> 00:20:44,330
就指数 local host 多少号

510
00:20:44,330 --> 00:20:45,670
我直接回车

511
00:20:45,670 --> 00:20:46,540
注意看

512
00:20:46,540 --> 00:20:50,390
它会自动的把我重定向到 news 这个目录下

513
00:20:50,390 --> 00:20:52,390
给我展示这个新闻的列表页
