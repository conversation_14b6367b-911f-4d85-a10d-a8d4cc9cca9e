1
00:00:00,159 --> 00:00:01,700
这节课再回到服务端

2
00:00:01,700 --> 00:00:02,640
看左边这个图

3
00:00:02,640 --> 00:00:04,600
我们的 client 它要发言

4
00:00:04,600 --> 00:00:08,757
发言呢，是先跟服务端建立好这个 WEBSOCKET 连接

5
00:00:08,757 --> 00:00:11,290
通过连接把每一次发言发给服务端

6
00:00:11,290 --> 00:00:13,200
由服务端打给 MQ 

7
00:00:13,200 --> 00:00:15,950
那么为什么不是 client 

8
00:00:15,950 --> 00:00:18,222
直接把这个消息打给 MQ 呢

9
00:00:18,222 --> 00:00:20,440
因为不论是说直接打给 MQ 

10
00:00:20,440 --> 00:00:22,700
还是说中间通过 server 中转一下

11
00:00:22,700 --> 00:00:24,020
对 MQ 来说

12
00:00:24,020 --> 00:00:25,630
它的压力是一样的

13
00:00:25,630 --> 00:00:28,100
那这个地方我们之所以要过一下 server 

14
00:00:28,100 --> 00:00:30,660
是因为呢，我们想对用户的这个发言啊

15
00:00:30,660 --> 00:00:31,820
进行一些检测

16
00:00:31,820 --> 00:00:33,970
比如说你的 fire new 里面

17
00:00:33,970 --> 00:00:37,080
不能包含一些违法违规信息啊

18
00:00:37,080 --> 00:00:40,400
涉嫌欺诈的、黄赌毒的不让发

19
00:00:40,400 --> 00:00:44,200
这样的话呢，我们在 server 这边就直接拦截了啊

20
00:00:44,200 --> 00:00:45,650
就不会发给 MQ 

21
00:00:45,650 --> 00:00:48,170
包括一些反爬策略啊

22
00:00:48,170 --> 00:00:52,010
如果说你是疯狂的以高频的去发消息

23
00:00:52,010 --> 00:00:55,237
我们在 server 端进行一个频率检测

24
00:00:55,237 --> 00:00:58,380
发现爬虫直接把你给拦截下来啊

25
00:00:58,380 --> 00:01:00,540
再比如说很多 app 里面呢

26
00:01:00,540 --> 00:01:03,780
他都不让用户去发那个 java 链接啊

27
00:01:03,780 --> 00:01:05,740
不让进行这个私下联系

28
00:01:05,740 --> 00:01:07,940
所以呢，你发这个消息里面

29
00:01:07,940 --> 00:01:11,070
有没有包含私人的连接方式吧

30
00:01:11,070 --> 00:01:15,632
也是在 server 这边进行一个关键词的过滤

31
00:01:15,632 --> 00:01:18,100
那么看一下服务端的第三个接口啊

32
00:01:18,100 --> 00:01:20,845
speak 就是用户要发言

33
00:01:20,845 --> 00:01:24,800
那么 find 的话用户就把那个消息传给服务端吧

34
00:01:24,800 --> 00:01:25,720
而这个消息呢

35
00:01:25,720 --> 00:01:30,420
就是我们的 common 里面的这个 message 结构体

36
00:01:30,420 --> 00:01:32,892
这个详细内容啊

37
00:01:32,892 --> 00:01:35,500
它是从谁发给谁的

38
00:01:35,500 --> 00:01:37,510
需要把这些信息传给服务端

39
00:01:37,510 --> 00:01:38,530
由服务端呢

40
00:01:38,530 --> 00:01:41,090
去给它生成一个全局唯一的 id 啊

41
00:01:41,090 --> 00:01:43,672
由服务端来指定这个时间

42
00:01:43,672 --> 00:01:44,780
之前我们讲过啊

43
00:01:44,780 --> 00:01:46,320
就这个消息时间啊

44
00:01:46,320 --> 00:01:48,447
我们是以服务端接收到

45
00:01:48,447 --> 00:01:50,110
这叫消息的时间为准

46
00:01:50,110 --> 00:01:52,930
将来用户看到的那个消息排序

47
00:01:52,930 --> 00:01:56,105
就是按照这个时间来进行排序的

48
00:01:56,105 --> 00:01:58,690
点到 speak 这个 handler 里面去

49
00:01:58,690 --> 00:02:02,030
好，一上来呢，就是先把这个 HTTP 连接

50
00:02:02,030 --> 00:02:05,275
把它升级为一个 web socket 连接

51
00:02:05,275 --> 00:02:07,050
upgrade 升级一下

52
00:02:07,050 --> 00:02:08,770
在这个 defer 里面呢

53
00:02:08,770 --> 00:02:10,759
把这个连接给它关闭掉

54
00:02:10,759 --> 00:02:14,450
然后开启一个异步的阶乘去做心跳保持

55
00:02:14,450 --> 00:02:16,530
就是周期性的去发送 ping 嘛

56
00:02:16,530 --> 00:02:18,550
好，往下走看看啊

57
00:02:18,550 --> 00:02:21,870
我们这个 speak 核心功能

58
00:02:21,870 --> 00:02:24,030
是要接收来自于客户端的消息

59
00:02:24,030 --> 00:02:26,240
把这个消息呢打给 MQ 

60
00:02:26,240 --> 00:02:29,840
所以这边是从连接里面读出这个消息啊

61
00:02:29,840 --> 00:02:32,207
保底是一个 by 的切片

62
00:02:32,207 --> 00:02:34,480
然后呢，进行 JSON 的反序列化对吧

63
00:02:34,480 --> 00:02:37,240
我要把它转成 message 这样一个结构体嘛

64
00:02:37,240 --> 00:02:38,140
然后呢

65
00:02:38,140 --> 00:02:43,910
在服务端给这个 message id 和 message time 来进行赋值

66
00:02:43,910 --> 00:02:45,880
呃，严格来讲，这个 id 啊

67
00:02:45,880 --> 00:02:47,580
应该是一个全局唯一 id 

68
00:02:47,580 --> 00:02:50,180
应该使用类似于 snowflake 这样的算法

69
00:02:50,180 --> 00:02:51,580
这边就简单起见啊

70
00:02:51,580 --> 00:02:54,197
就直接使用时间戳来给它复制

71
00:02:54,197 --> 00:02:59,120
那么再把这个消息打给我们的 rabbit mq 之前

72
00:02:59,120 --> 00:03:02,000
哎，我先过了一个 intercept 

73
00:03:02,000 --> 00:03:04,245
过了一个拦截检测

74
00:03:04,245 --> 00:03:07,580
好，那这个拦截检测这个函数点进去的话

75
00:03:07,580 --> 00:03:09,500
就跟你的具体业务相关了

76
00:03:09,500 --> 00:03:11,977
你想执行哪些拦截检测呢

77
00:03:11,977 --> 00:03:13,830
啊，这一般只是示意一下啊

78
00:03:13,830 --> 00:03:14,590
我只是做了一个

79
00:03:14,590 --> 00:03:17,562
这个是否消息能为空的检测

80
00:03:17,562 --> 00:03:20,020
那实际当中你可能要检测一下

81
00:03:20,020 --> 00:03:21,200
同一个用户 id 

82
00:03:21,200 --> 00:03:24,260
它的这个发言频率是否过快啊

83
00:03:24,260 --> 00:03:27,580
里面是否包含一些违法违规信息啊

84
00:03:27,580 --> 00:03:29,660
是否涉嫌这个私下交易

85
00:03:29,660 --> 00:03:30,780
私下联系等等

86
00:03:30,780 --> 00:03:33,720
全部在这个函数里面来完成

87
00:03:33,720 --> 00:03:34,920
如果需要拦截

88
00:03:34,920 --> 00:03:36,380
就返回一个 error 

89
00:03:36,380 --> 00:03:37,480
如果不需要拦截

90
00:03:37,480 --> 00:03:39,050
返回 new 就可以了

91
00:03:39,050 --> 00:03:41,240
好，所以这边如果是拦截的话

92
00:03:41,240 --> 00:03:45,592
直接 continue 啊，进入到下一轮的 for 循环

93
00:03:45,592 --> 00:03:47,980
那否则呢，就会往下走

94
00:03:47,980 --> 00:03:49,770
打给我们的 MQ 

95
00:03:49,770 --> 00:03:53,092
来看一下这个 sin 函数是如何实现的

96
00:03:53,092 --> 00:03:54,240
这个函数啊

97
00:03:54,240 --> 00:03:57,567
又会来到我们的 MQ 点 go 这个文件里面来

98
00:03:57,567 --> 00:04:01,160
首先我把这个 message 执行 JSON 序列化

99
00:04:01,160 --> 00:04:03,410
序列化之后就是这个 MSG 嘛

100
00:04:03,410 --> 00:04:05,077
这样一个 bad 切片

101
00:04:05,077 --> 00:04:08,140
然后呢，调这个 publish 啊

102
00:04:08,140 --> 00:04:13,670
把这个 m s g message 写给我们的 rabbit mq 

103
00:04:13,670 --> 00:04:15,988
那么写给哪个交换机呢

104
00:04:15,988 --> 00:04:17,029
注意看这个图

105
00:04:17,029 --> 00:04:18,540
就是我们的这个 message 啊

106
00:04:18,540 --> 00:04:21,079
啊，你不用关心写给哪个队列

107
00:04:21,079 --> 00:04:24,120
你只需要关心写给哪个交换机就可以了

108
00:04:24,120 --> 00:04:28,570
交换机他来负责把这个消息转发给哪些队列

109
00:04:28,570 --> 00:04:32,310
所以在 produce 生产这一侧啊

110
00:04:32,310 --> 00:04:35,510
我们只需要指定交换机名称就可以了

111
00:04:35,510 --> 00:04:37,722
那发送给哪个交换机呢

112
00:04:37,722 --> 00:04:41,250
比如说这个消息是 U 1发送给 U 2的

113
00:04:41,250 --> 00:04:43,240
那 U 1发送给 U 2的话

114
00:04:43,240 --> 00:04:46,610
他这个交换机名称就应该叫 U 2对吧

115
00:04:46,610 --> 00:04:47,955
所以看一下啊

116
00:04:47,955 --> 00:04:52,360
这个 exchange 是通过参数传进来的

117
00:04:52,360 --> 00:04:54,630
我们回到刚才那个调用的地方

118
00:04:54,630 --> 00:04:57,110
好，这个 exchange 节啊，就是什么

119
00:04:57,110 --> 00:04:59,410
就是 to 啊，消息发给谁

120
00:04:59,410 --> 00:05:03,800
所以这个 to 呢，它不光是那个接收方的用户 id 

121
00:05:03,800 --> 00:05:05,960
也包含了那个 U 那个前缀

122
00:05:05,960 --> 00:05:06,700
当然了

123
00:05:06,700 --> 00:05:08,240
他也可能是一个群组啊

124
00:05:08,240 --> 00:05:09,580
就包含 G 那个前缀

125
00:05:09,580 --> 00:05:11,655
G 加上那个群组 id 

126
00:05:11,655 --> 00:05:13,740
所以不管是一对私聊

127
00:05:13,740 --> 00:05:15,740
还是往群里面去发消息啊

128
00:05:15,740 --> 00:05:18,280
都是走这个逻辑

129
00:05:18,280 --> 00:05:20,857
也都会走这个过滤逻辑

130
00:05:20,857 --> 00:05:23,930
好，那如果是一堆单聊的话

131
00:05:23,930 --> 00:05:26,940
那是不是还得有下面这个流程

132
00:05:26,940 --> 00:05:29,717
就是 U 1给 U 2发消息

133
00:05:29,717 --> 00:05:33,120
那这个消息本身其实也需要发到 U 1

134
00:05:33,120 --> 00:05:35,202
这个交换机里面去，对吧

135
00:05:35,202 --> 00:05:37,090
所以呢，这边呢，判断一下

136
00:05:37,090 --> 00:05:38,210
先判断一下哦

137
00:05:38,210 --> 00:05:39,845
这个接收方啊

138
00:05:39,845 --> 00:05:41,550
它是否带这个前缀

139
00:05:41,550 --> 00:05:43,590
就它是否带这个 U 啊

140
00:05:43,590 --> 00:05:45,110
是否带 U 这个前缀

141
00:05:45,110 --> 00:05:46,640
如果带 U 这个前缀

142
00:05:46,640 --> 00:05:48,620
说明是发给某一个个人的

143
00:05:48,620 --> 00:05:49,380
是私聊

144
00:05:49,380 --> 00:05:51,107
那么私聊的话

145
00:05:51,107 --> 00:05:53,030
哎，会走这个逻辑

146
00:05:53,030 --> 00:05:58,000
就是呢，它会打到 from 这个交换机里面去啊

147
00:05:58,000 --> 00:06:00,660
打到 U 1那个交换机里面去嘛

148
00:06:00,660 --> 00:06:02,160
这是服务端接口

149
00:06:02,160 --> 00:06:04,427
看一下客户端怎么调

150
00:06:04,427 --> 00:06:05,610
来到 client 

151
00:06:05,610 --> 00:06:07,290
在这个 client 点 go 里面

152
00:06:07,290 --> 00:06:09,130
它也有一个 send 

153
00:06:09,130 --> 00:06:11,655
已经把这个 message 准备好了

154
00:06:11,655 --> 00:06:13,350
这边有一个 connection 呐

155
00:06:13,350 --> 00:06:14,890
全局的变量

156
00:06:14,890 --> 00:06:17,837
它是一个 web socket 连接

157
00:06:17,837 --> 00:06:19,920
如果这个连接已经建立好了

158
00:06:19,920 --> 00:06:22,520
那么呢，直接走到下面来啊

159
00:06:22,520 --> 00:06:24,440
通过 write JSON 对吧

160
00:06:24,440 --> 00:06:26,190
把结构体传进来

161
00:06:26,190 --> 00:06:27,660
执行计算序列化

162
00:06:27,660 --> 00:06:30,020
再传给服务端就可以了

163
00:06:30,020 --> 00:06:31,335
完事了

164
00:06:31,335 --> 00:06:33,700
那无非就是说最开始的时候呢

165
00:06:33,700 --> 00:06:35,500
这个连接还没建立好

166
00:06:35,500 --> 00:06:38,407
所以呢，需要临时的去先创建这个连接

167
00:06:38,407 --> 00:06:40,310
通过 DIO 是吧

168
00:06:40,310 --> 00:06:41,370
web socket die 嘛

169
00:06:41,370 --> 00:06:42,870
这边是 WS 

170
00:06:42,870 --> 00:06:44,190
这边是 speak 啊

171
00:06:44,190 --> 00:06:45,830
跟我们刚才这个服务端啊

172
00:06:45,830 --> 00:06:46,935
看下服务端

173
00:06:46,935 --> 00:06:48,890
服务端这边是 speak 嘛

174
00:06:48,890 --> 00:06:50,140
所以呢

175
00:06:50,140 --> 00:06:54,402
来到客户端这边也应该是 speak 这个路径

176
00:06:54,402 --> 00:06:57,327
建立好我们的 web socket 连接

177
00:06:57,327 --> 00:06:59,210
那么连接建立好之后的话

178
00:06:59,210 --> 00:07:00,370
有一些基本设置啊

179
00:07:00,370 --> 00:07:04,310
比如说你要设置一下你的 ping handler 

180
00:07:04,310 --> 00:07:06,250
当然了，这个地方代码其实可以不写啊

181
00:07:06,250 --> 00:07:07,850
就完全可以把它给注释掉

182
00:07:07,850 --> 00:07:08,630
为什么呢

183
00:07:08,630 --> 00:07:10,590
因为默认情况下

184
00:07:10,590 --> 00:07:12,590
你收到聘消息的话

185
00:07:12,590 --> 00:07:16,680
就是要给对方去返回一个 pd 啊

186
00:07:16,680 --> 00:07:17,580
这是默认行为

187
00:07:17,580 --> 00:07:18,780
所以写不写都可以

188
00:07:18,780 --> 00:07:22,120
包括这个 close handler 其实也是默认行为啊

189
00:07:22,120 --> 00:07:24,260
那对方给我发过来一个 close message 

190
00:07:24,260 --> 00:07:26,830
我就应该去关闭这个链接

191
00:07:26,830 --> 00:07:28,840
但是还是特别强调一点啊

192
00:07:28,840 --> 00:07:32,540
就是说什么时候触发这个 ping handler 

193
00:07:32,540 --> 00:07:34,990
有两个条件需要同时满足

194
00:07:34,990 --> 00:07:39,230
第一个，对方给我发送了一个 ping 消息啊

195
00:07:39,230 --> 00:07:40,790
第二个，我自己啊

196
00:07:40,790 --> 00:07:43,790
我这边我需要去调用 read 函数
