1
00:00:00,600 --> 00:00:02,960
在进行参数绑定的同时呢

2
00:00:02,960 --> 00:00:06,040
也可以对参数啊进行一些合法性检查

3
00:00:06,040 --> 00:00:08,940
这些 gm 框架都可以自动帮我完成

4
00:00:08,940 --> 00:00:11,965
那关键是要做一些什么检查呢

5
00:00:11,965 --> 00:00:14,390
我们通过配置 tag 就可以搞定

6
00:00:14,390 --> 00:00:16,440
假如说我们的参数呢

7
00:00:16,440 --> 00:00:18,960
封装成了 user 这样一个结构体

8
00:00:18,960 --> 00:00:20,802
参数里面包含

9
00:00:20,802 --> 00:00:26,132
name 、 score 、 ENROLMENT 和 graduation 、入学时间和毕业时间

10
00:00:26,132 --> 00:00:27,760
那针对这个 name 啊

11
00:00:27,760 --> 00:00:30,340
我们需要指定它对应的参数名称

12
00:00:30,340 --> 00:00:34,207
它在这个 form 表单里面应该叫什么名字对吧

13
00:00:34,207 --> 00:00:38,290
或者呢，是在那个 URL 的问号后面的参数里面

14
00:00:38,290 --> 00:00:41,887
它对应的教授名字通过 form 来进行指定

15
00:00:41,887 --> 00:00:45,160
那这个合法性校验规则是什么呢

16
00:00:45,160 --> 00:00:48,120
这边只是指定了一个 binding required 

17
00:00:48,120 --> 00:00:50,200
表示这个参数必须传

18
00:00:50,200 --> 00:00:51,327
不能不传

19
00:00:51,327 --> 00:00:52,790
再来看这个 score 

20
00:00:52,790 --> 00:00:54,150
这个 score 后面啊

21
00:00:54,150 --> 00:00:56,710
它的 binding 里面呢有两项

22
00:00:56,710 --> 00:00:58,410
一项是 required 啊

23
00:00:58,410 --> 00:01:00,075
参数必须

24
00:01:00,075 --> 00:01:03,320
逗号前面还有一个 GT 等于零

25
00:01:03,320 --> 00:01:07,102
这个 gt 呀，是 greater than 的缩写

26
00:01:07,102 --> 00:01:08,580
就是要大于零

27
00:01:08,580 --> 00:01:09,100
注意啊

28
00:01:09,100 --> 00:01:11,080
他可不是说要大于等于零

29
00:01:11,080 --> 00:01:12,300
你不要以为有个等号啊

30
00:01:12,300 --> 00:01:13,890
它实际上是说要大于零

31
00:01:13,890 --> 00:01:16,380
就是说你传的这个参数 score 啊

32
00:01:16,380 --> 00:01:17,930
它必须得大于零

33
00:01:17,930 --> 00:01:19,100
小于零的

34
00:01:19,100 --> 00:01:21,002
直接会返回一个错误

35
00:01:21,002 --> 00:01:22,890
再来看这个 enrollment 

36
00:01:22,890 --> 00:01:25,615
它的数据类型是时间 time 点 time 

37
00:01:25,615 --> 00:01:27,150
然后这个 binding 啊

38
00:01:27,150 --> 00:01:28,770
binding required 必传

39
00:01:28,770 --> 00:01:31,220
然后有一个 before today 

40
00:01:31,220 --> 00:01:33,150
而这个 before today 是什么意思呢

41
00:01:33,150 --> 00:01:34,210
这个 before today 啊

42
00:01:34,210 --> 00:01:37,960
实际上是我们自己定义的一种校验方法

43
00:01:37,960 --> 00:01:40,425
我们看怎么自己定义

44
00:01:40,425 --> 00:01:42,250
其实我在 main 函数这边啊

45
00:01:42,250 --> 00:01:46,170
通过调用这个 register addition 啊

46
00:01:46,170 --> 00:01:49,427
我自己注册了一个参数校验器

47
00:01:49,427 --> 00:01:52,210
给它起了个名字叫做 before today 

48
00:01:52,210 --> 00:01:55,325
所以呢，这个 before today 这个字符串

49
00:01:55,325 --> 00:01:59,790
我就可以把它用在这个 tag 里面

50
00:01:59,790 --> 00:02:03,800
那关键是这个计算器内部逻辑是什么

51
00:02:03,800 --> 00:02:06,600
内部逻辑就通过这个 before today 

52
00:02:06,600 --> 00:02:07,760
这个函数来定义的

53
00:02:07,760 --> 00:02:09,440
我们看一下这是一个什么函数

54
00:02:09,440 --> 00:02:12,462
好，这是函数的完整逻辑

55
00:02:12,462 --> 00:02:15,410
它必须满足这样一种函数签名嘛

56
00:02:15,410 --> 00:02:19,090
啊，传递一个 field level 参数进来

57
00:02:19,090 --> 00:02:20,170
返回一个布尔

58
00:02:20,170 --> 00:02:22,450
布尔为 true 的话表示校验

59
00:02:22,450 --> 00:02:25,270
通过 false 表示这个参数不合法

60
00:02:25,270 --> 00:02:27,580
这个里边呢，还会用到反射啊

61
00:02:27,580 --> 00:02:30,400
这个 field 它是反射里面的 value 

62
00:02:30,400 --> 00:02:34,280
然后通过调 interface 把它转成一个任意类型

63
00:02:34,280 --> 00:02:35,160
空接口嘛

64
00:02:35,160 --> 00:02:36,840
再通过类型断言

65
00:02:36,840 --> 00:02:39,900
因为咱们断定这个 before today 

66
00:02:39,900 --> 00:02:43,072
它修饰的必然是一个时间类型嘛

67
00:02:43,072 --> 00:02:44,150
断言啊

68
00:02:44,150 --> 00:02:45,390
如果断言失败的话

69
00:02:45,390 --> 00:02:46,730
就直接返回 false 了

70
00:02:46,730 --> 00:02:47,880
断言成功的话

71
00:02:47,880 --> 00:02:49,160
进一步判断一下

72
00:02:49,160 --> 00:02:52,920
他是否比这个当前时刻要早

73
00:02:52,920 --> 00:02:55,330
是的话，校验成功啊

74
00:02:55,330 --> 00:02:56,870
否则校验失败

75
00:02:56,870 --> 00:02:59,330
所以不管是对什么样的数据类型

76
00:02:59,330 --> 00:03:02,050
你都可以按照你自己的逻辑

77
00:03:02,050 --> 00:03:03,705
来进行一个校验

78
00:03:03,705 --> 00:03:07,070
这边呢，还是通过 tag 来指定了这个时间类型

79
00:03:07,070 --> 00:03:09,050
就虽然说你这个是 time 点 time 

80
00:03:09,050 --> 00:03:12,490
但是呢，我并不要求你在参数里

81
00:03:12,490 --> 00:03:14,830
具体到时、分、秒甚至毫秒啊

82
00:03:14,830 --> 00:03:19,030
你只需要指定到年月、日以横杠风格就可以了

83
00:03:19,030 --> 00:03:21,670
它在对这个字符串进行剪切的时候

84
00:03:21,670 --> 00:03:23,682
它采用的是东八区

85
00:03:23,682 --> 00:03:26,010
再来看这个毕业时间

86
00:03:26,010 --> 00:03:27,340
他这边有一个什么呢

87
00:03:27,340 --> 00:03:31,615
他这边有一个 g t field 等于 ENROLMENT 

88
00:03:31,615 --> 00:03:32,927
什么意思呢

89
00:03:32,927 --> 00:03:34,850
首先说这个 gt 是吧

90
00:03:34,850 --> 00:03:37,410
这个 gt 的话跟上面这个 gt 是一样的

91
00:03:37,410 --> 00:03:38,570
都表示大于了

92
00:03:38,570 --> 00:03:41,370
但后面还有一个 field 就表示啊

93
00:03:41,370 --> 00:03:43,420
我要大于的这个东西呢

94
00:03:43,420 --> 00:03:46,130
它是另外一个 field 啊

95
00:03:46,130 --> 00:03:48,080
是这个 enrollment 

96
00:03:48,080 --> 00:03:50,370
就是说我的毕业时间呢

97
00:03:50,370 --> 00:03:53,980
他必须要大于这个入学时间才是合法的

98
00:03:53,980 --> 00:03:54,410
好

99
00:03:54,410 --> 00:03:55,010
所以啊

100
00:03:55,010 --> 00:03:58,070
你把所有的计算规则全部通过 tag 

101
00:03:58,070 --> 00:03:59,710
在结构体里面就定义

102
00:03:59,710 --> 00:04:00,200
好

103
00:04:00,200 --> 00:04:04,180
然后呢，我们到时候在这个 GAN 的 handler 里面

104
00:04:04,180 --> 00:04:07,530
直接调用 should bend 来进行绑定嘛

105
00:04:07,530 --> 00:04:08,960
在绑定的过程中

106
00:04:08,960 --> 00:04:12,132
就会去完成参数的合法性校验

107
00:04:12,132 --> 00:04:14,430
所以就可能会既不通过啊

108
00:04:14,430 --> 00:04:15,350
既然不通过的话呢

109
00:04:15,350 --> 00:04:17,335
会返回一个 error 

110
00:04:17,335 --> 00:04:20,410
那我们需要从这个 L 里面来获得它

111
00:04:20,410 --> 00:04:21,810
到底是为什么不通过

112
00:04:21,810 --> 00:04:23,860
到底是哪一个字段失败了

113
00:04:23,860 --> 00:04:24,710
哪个字段

114
00:04:24,710 --> 00:04:26,970
因为违反了哪一个规则失

115
00:04:26,970 --> 00:04:29,990
全部是通过这个 process error 函数来完成的

116
00:04:29,990 --> 00:04:31,170
看一下这个函数

117
00:04:31,170 --> 00:04:33,590
因为这个 L 是一个接口嘛

118
00:04:33,590 --> 00:04:35,030
咱们需要把这个接口呢

119
00:04:35,030 --> 00:04:36,750
转成一个具体的类型

120
00:04:36,750 --> 00:04:38,887
哎，转成这样一种类型

121
00:04:38,887 --> 00:04:40,140
然后就好办了

122
00:04:40,140 --> 00:04:43,770
然后的话咱们就可以去便利这个类型

123
00:04:43,770 --> 00:04:45,630
这个类型实际上是一个切片啊

124
00:04:45,630 --> 00:04:49,660
因为你可能是多个字段都校验失败了嘛

125
00:04:49,660 --> 00:04:52,062
便利每一个校验失败的情况

126
00:04:52,062 --> 00:04:55,060
那么从这个 validation l 里面

127
00:04:55,060 --> 00:04:56,880
咱们可以拿到两个重要信息

128
00:04:56,880 --> 00:04:58,740
一个是 field 

129
00:04:58,740 --> 00:05:00,300
一个是 tag 

130
00:05:00,300 --> 00:05:01,910
这个 FTH 就表示说

131
00:05:01,910 --> 00:05:05,190
它对应到刚才的这个结构体里面

132
00:05:05,190 --> 00:05:07,770
是哪一个成员变量交换失败了

133
00:05:07,770 --> 00:05:09,820
而这个 tag 指的是

134
00:05:09,820 --> 00:05:12,290
它具体是违反了哪一个约束

135
00:05:12,290 --> 00:05:14,580
比如是违反了这个 required 

136
00:05:14,580 --> 00:05:16,222
还是违反了这个 gt 

137
00:05:16,222 --> 00:05:17,590
根据这两个字段呢

138
00:05:17,590 --> 00:05:20,010
咱们来拼接这样一个错误信息

139
00:05:20,010 --> 00:05:21,490
把这个错误信息呢

140
00:05:21,490 --> 00:05:24,045
放到这个切片里面去

141
00:05:24,045 --> 00:05:25,030
最后呢

142
00:05:25,030 --> 00:05:29,677
把所有错误信息通过封号来进行拼接返回

143
00:05:29,677 --> 00:05:32,527
那返回的这个错误信息呢

144
00:05:32,527 --> 00:05:34,880
会拼接在这个地方

145
00:05:34,880 --> 00:05:38,740
通过 JN 的 context 返回给前端

146
00:05:38,740 --> 00:05:40,352
把服务端洗一下

147
00:05:40,352 --> 00:05:42,480
然后这边我准备了几种情况啊

148
00:05:42,480 --> 00:05:43,600
第一种情况的话

149
00:05:43,600 --> 00:05:47,150
如果你是这样去请求这些参数啊

150
00:05:47,150 --> 00:05:48,090
都是合法的

151
00:05:48,090 --> 00:05:50,130
比如 name 它是必填的啊

152
00:05:50,130 --> 00:05:53,427
有 score 呢是大于零的，等于一嘛

153
00:05:53,427 --> 00:05:55,487
这个是入学时间

154
00:05:55,487 --> 00:05:58,030
他是早于当前时刻的

155
00:05:58,030 --> 00:05:59,490
这个是毕业时间

156
00:05:59,490 --> 00:06:02,652
他是晚于这个入学时间的

157
00:06:02,652 --> 00:06:04,760
咱们就把这个 URL 呢

158
00:06:04,760 --> 00:06:08,025
直接拷贝到我的浏览器里面去

159
00:06:08,025 --> 00:06:10,540
好，这次呢，是完全没有问题啊

160
00:06:10,540 --> 00:06:12,445
能够顺利的拿到结果

161
00:06:12,445 --> 00:06:13,940
因为一切顺利的话

162
00:06:13,940 --> 00:06:15,980
这边是会返回一个 JSON 啊

163
00:06:15,980 --> 00:06:17,900
就直接把这个 user 作为 JSO

164
00:06:17,900 --> 00:06:19,580
就返回给了客户端

165
00:06:19,580 --> 00:06:21,420
我们来验证一个有问题的情况

166
00:06:21,420 --> 00:06:22,560
比如说这次呢

167
00:06:22,560 --> 00:06:24,700
咱们没传 name 啊

168
00:06:24,700 --> 00:06:25,740
name 是必填吗

169
00:06:25,740 --> 00:06:27,860
咱们如果不传 name 的话

170
00:06:27,860 --> 00:06:29,677
会发生什么情况

171
00:06:29,677 --> 00:06:33,180
把它粘到我们的浏览器里面去

172
00:06:33,180 --> 00:06:34,320
请求一下

173
00:06:34,320 --> 00:06:37,070
好，这边说参数绑定失败

174
00:06:37,070 --> 00:06:39,017
字段呢，是 name 

175
00:06:39,017 --> 00:06:40,760
它不满足哪一个条件呢

176
00:06:40,760 --> 00:06:42,770
不满足这个 required 

177
00:06:42,770 --> 00:06:46,437
就因为我们返回了这样一个字符串呢

178
00:06:46,437 --> 00:06:48,680
这次咱们把这个 score 值为零

179
00:06:48,680 --> 00:06:50,220
再来请求一次

180
00:06:50,220 --> 00:06:52,330
他说这个字段 score 啊

181
00:06:52,330 --> 00:06:54,490
不满足 gt 

182
00:06:54,490 --> 00:06:55,620
大于吧

183
00:06:55,620 --> 00:06:57,900
这次让这个入学时间呢

184
00:06:57,900 --> 00:07:00,110
晚于当前时刻

185
00:07:00,110 --> 00:07:01,800
请求一下

186
00:07:01,800 --> 00:07:05,617
字段入学时间不满足 before today 

187
00:07:05,617 --> 00:07:09,720
这次是让这个毕业时间早于入学时间

188
00:07:09,720 --> 00:07:11,300
请求一下是吧

189
00:07:11,300 --> 00:07:15,340
字段毕业时间不满足 gt ； field 

190
00:07:15,340 --> 00:07:18,260
我要求它要大于另外一个 field 

191
00:07:18,260 --> 00:07:19,472
它不满足

192
00:07:19,472 --> 00:07:21,250
关于这个参数校验啊

193
00:07:21,250 --> 00:07:23,370
既然还提供了很多其他规则

194
00:07:23,370 --> 00:07:25,777
我们这边呢，来粗略的过一下

195
00:07:25,777 --> 00:07:28,060
刚才在代码里面给大家演示的

196
00:07:28,060 --> 00:07:29,940
只有一个 GT 嘛

197
00:07:29,940 --> 00:07:32,800
然后这个 before today 是我们自己定义的

198
00:07:32,800 --> 00:07:34,400
那除了 gt 之外

199
00:07:34,400 --> 00:07:35,740
还有很多做其他的

200
00:07:35,740 --> 00:07:38,395
比方说跟范围相关的

201
00:07:38,395 --> 00:07:42,420
跟这个跨字段相关的、跟字符串相关的

202
00:07:42,420 --> 00:07:44,470
和这个唯一性相关的

203
00:07:44,470 --> 00:07:45,910
范围相关的啊

204
00:07:45,910 --> 00:07:48,220
可以指定最小值

205
00:07:48,220 --> 00:07:51,460
最大值要等于、要不等于、要大于大于等

206
00:07:51,460 --> 00:07:52,750
小于小于等于

207
00:07:52,750 --> 00:07:56,197
以及说从一个集合里面取一个值

208
00:07:56,197 --> 00:07:58,510
这是对于数字类型的变量

209
00:07:58,510 --> 00:08:00,397
如果是字符串的话

210
00:08:00,397 --> 00:08:03,440
要求字符串长度必须是等于十

211
00:08:03,440 --> 00:08:07,500
或者说字符串长度必须大于六个长度

212
00:08:07,500 --> 00:08:08,850
小于十个长度

213
00:08:08,850 --> 00:08:11,692
这个 gt 表示要大于十个长度

214
00:08:11,692 --> 00:08:14,070
同理，对于切片数组 map 

215
00:08:14,070 --> 00:08:16,582
它实际上约束的都是长度

216
00:08:16,582 --> 00:08:18,360
像这种跨字段的话

217
00:08:18,360 --> 00:08:20,887
刚才演示了这个 gt field 

218
00:08:20,887 --> 00:08:24,010
那甚至呢，你不光是可以跟另外一个字段比

219
00:08:24,010 --> 00:08:25,510
你甚至可以和另外一个

220
00:08:25,510 --> 00:08:27,210
结构体的某个字段比

221
00:08:27,210 --> 00:08:30,300
就说呢，本来是 g t field 

222
00:08:30,300 --> 00:08:33,030
现在呢，中间插一个 cs 

223
00:08:33,030 --> 00:08:35,291
这表示 cross struct 

224
00:08:35,291 --> 00:08:35,919
当然了

225
00:08:35,919 --> 00:08:37,220
这个 gt 可以变

226
00:08:37,220 --> 00:08:38,781
你也可以是小于嘛

227
00:08:38,781 --> 00:08:42,199
然后跟字符串相关的还有很多约束

228
00:08:42,199 --> 00:08:44,169
它必须包含某个子串呐

229
00:08:44,169 --> 00:08:46,130
啊，必须以谁为前缀

230
00:08:46,130 --> 00:08:47,482
以谁为后缀呀

231
00:08:47,482 --> 00:08:50,195
不能包含某个字串啊等等

232
00:08:50,195 --> 00:08:53,400
唯一性约束呢，是加一个 unique 关键字

233
00:08:53,400 --> 00:08:55,160
那关键看这个约束

234
00:08:55,160 --> 00:08:56,935
它是用来修饰谁的

235
00:08:56,935 --> 00:08:59,580
假如说是修饰数组切片

236
00:08:59,580 --> 00:09:02,380
意思是说这个数组切片里面呢

237
00:09:02,380 --> 00:09:04,270
不能存在重复的元素

238
00:09:04,270 --> 00:09:05,500
如果修饰 map 

239
00:09:05,500 --> 00:09:07,600
因为 map 里面本来 K 就不重复嘛

240
00:09:07,600 --> 00:09:11,230
所以他这个唯一性实际上是在约束 map 的 value 

241
00:09:11,230 --> 00:09:13,270
value 不能存在重复的

242
00:09:13,270 --> 00:09:14,910
再来看这个情况

243
00:09:14,910 --> 00:09:16,990
它呢是一个切片

244
00:09:16,990 --> 00:09:20,040
同时切片里面的每一个元素呢

245
00:09:20,040 --> 00:09:21,890
又是一个结构体

246
00:09:21,890 --> 00:09:25,100
那这样的话你加一个 unique 什么意思

247
00:09:25,100 --> 00:09:28,700
你是要求切片里面的每一个结构体

248
00:09:28,700 --> 00:09:29,970
互不重复吗

249
00:09:29,970 --> 00:09:33,365
它这边啊，还归指定等于 name 

250
00:09:33,365 --> 00:09:36,300
意思是说切片里面每一个 user 

251
00:09:36,300 --> 00:09:39,680
它们的 name 这个字段不能重复
