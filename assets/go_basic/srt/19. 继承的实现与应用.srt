1
00:00:00,679 --> 00:00:03,200
看一下购员如何实现继承啊

2
00:00:03,200 --> 00:00:05,540
注意继承这是一种民间叫法

3
00:00:05,540 --> 00:00:07,140
我们是从其他语言里面

4
00:00:07,140 --> 00:00:09,080
把这概念借鉴过来的

5
00:00:09,080 --> 00:00:12,285
购员官方并不承认继承

6
00:00:12,285 --> 00:00:16,390
官方的说法是 go 员并不存在继承这种语法

7
00:00:16,390 --> 00:00:17,560
到底怎么回事呢

8
00:00:17,560 --> 00:00:18,715
我们来看一下

9
00:00:18,715 --> 00:00:21,080
这边定义了一个简单的结构

10
00:00:21,080 --> 00:00:23,975
user ，包含姓名和年龄

11
00:00:23,975 --> 00:00:25,720
下面是另外一个结构体

12
00:00:25,720 --> 00:00:30,190
video 包含长度、名称和作者

13
00:00:30,190 --> 00:00:33,230
而这个作者呢，他是一个 user 是吧

14
00:00:33,230 --> 00:00:35,995
是另外一种结构体的这种数据类型

15
00:00:35,995 --> 00:00:39,620
然后我这边是创建了一个 user 的实例

16
00:00:39,620 --> 00:00:42,850
然后创建一个 video 所对应的实例

17
00:00:42,850 --> 00:00:45,310
注意一下这个 author 

18
00:00:45,310 --> 00:00:47,800
我赋值是用这个 U 是吧

19
00:00:47,800 --> 00:00:49,530
用这个 user 类型嘛

20
00:00:49,530 --> 00:00:52,615
来给作者赋值的

21
00:00:52,615 --> 00:00:53,470
好

22
00:00:53,470 --> 00:00:58,180
然后我去打印 video 的长度和名称

23
00:00:58,180 --> 00:01:01,200
那么如何去拿到这个 video 的

24
00:01:01,200 --> 00:01:04,382
作者的姓名和年龄的

25
00:01:04,382 --> 00:01:05,700
通过这种形式啊

26
00:01:05,700 --> 00:01:07,840
先是 V 点 water 是吧

27
00:01:07,840 --> 00:01:09,800
你先拿到这个 WSER 

28
00:01:09,800 --> 00:01:11,260
然后再点啊

29
00:01:11,260 --> 00:01:12,970
再拿到这个 name 

30
00:01:12,970 --> 00:01:15,660
OK ，这种表达式是比较清晰的

31
00:01:15,660 --> 00:01:17,902
那现在来增大难度

32
00:01:17,902 --> 00:01:22,250
假如说我把这个 water 这个成员变量名称

33
00:01:22,250 --> 00:01:24,165
把它给删掉

34
00:01:24,165 --> 00:01:25,390
保存一下

35
00:01:25,390 --> 00:01:26,405
看啊

36
00:01:26,405 --> 00:01:27,670
上完之后的话

37
00:01:27,670 --> 00:01:31,700
在这个地方其实没有任何的红线飘红

38
00:01:31,700 --> 00:01:34,727
没有任何语法错误是完全 OK 的

39
00:01:34,727 --> 00:01:38,860
或者你可以把它称之为匿名成员

40
00:01:38,860 --> 00:01:40,847
没有名称，只有类型

41
00:01:40,847 --> 00:01:41,890
那匿名的话

42
00:01:41,890 --> 00:01:44,170
将来我通过这个 video 

43
00:01:44,170 --> 00:01:47,050
我怎么去访问这个 name 和 agent 呢

44
00:01:47,050 --> 00:01:49,270
因为你原先的话有名称

45
00:01:49,270 --> 00:01:51,190
你还可以通过这个名称是吧

46
00:01:51,190 --> 00:01:53,810
来间接的访问 name 和 IG 

47
00:01:53,810 --> 00:01:55,517
现在没有名称了咋办

48
00:01:55,517 --> 00:01:56,500
没有名称了

49
00:01:56,500 --> 00:01:59,140
你可以直接把中间这一步删掉啊

50
00:01:59,140 --> 00:02:02,450
删掉这个也删掉

51
00:02:02,450 --> 00:02:03,460
保存一下

52
00:02:03,460 --> 00:02:05,090
好，完全 OK 

53
00:02:05,090 --> 00:02:07,162
先把这个 water 删掉

54
00:02:07,162 --> 00:02:08,960
15号线注释掉

55
00:02:08,960 --> 00:02:10,779
我们先来看这啊

56
00:02:10,779 --> 00:02:12,950
那么这个 V 点 A 减 V 呢

57
00:02:12,950 --> 00:02:14,627
本来是没有 A 级

58
00:02:14,627 --> 00:02:17,300
那由于它是有一个匿名成员嘛

59
00:02:17,300 --> 00:02:18,860
现在我跳过匿名成

60
00:02:18,860 --> 00:02:20,720
直接访问这个 A 级啊

61
00:02:20,720 --> 00:02:24,280
就好比是 video 有了 A 减一样

62
00:02:24,280 --> 00:02:25,020
而 A 姐

63
00:02:25,020 --> 00:02:28,760
你可以认为它是从 user 里面继承过来的啊

64
00:02:28,760 --> 00:02:31,560
所以你可以理解为 video 从

65
00:02:32,880 --> 00:02:37,470
user 里啊，继承打引号啊

66
00:02:37,470 --> 00:02:39,420
long age 

67
00:02:39,420 --> 00:02:43,112
所以 V 可以直接使用这个 age 

68
00:02:43,112 --> 00:02:44,930
这只是继承成员变量

69
00:02:44,930 --> 00:02:47,230
那成员方法也是一样道理啊

70
00:02:47,230 --> 00:02:49,130
可以直接继承过来

71
00:02:49,130 --> 00:02:53,120
那问题是第18行19行 V 点 name 

72
00:02:53,120 --> 00:02:57,170
那 V 点 name 到底是访问 video 自身的 name 呢

73
00:02:57,170 --> 00:03:00,435
还是访问它父类的这个 name 呢

74
00:03:00,435 --> 00:03:02,410
那如果直接这样写的话

75
00:03:02,410 --> 00:03:05,817
它实际上访问的是它自身的内容

76
00:03:05,817 --> 00:03:08,820
如果你想访问父类的 name 的话

77
00:03:08,820 --> 00:03:10,130
那么得这样写

78
00:03:10,130 --> 00:03:14,355
V 点 user 保存一下点 name 

79
00:03:14,355 --> 00:03:17,160
比如说你这不是匿名成员吗

80
00:03:17,160 --> 00:03:18,180
匿名，匿名

81
00:03:18,180 --> 00:03:21,220
如果你非要使用一个名称的话

82
00:03:21,220 --> 00:03:24,170
就直接使用这个变量的类型

83
00:03:24,170 --> 00:03:25,810
来充当变量名称

84
00:03:25,810 --> 00:03:26,150
对吧

85
00:03:26,150 --> 00:03:28,250
你直接使用类型来充当名称嘛

86
00:03:28,250 --> 00:03:29,370
就可以了

87
00:03:29,370 --> 00:03:34,210
这是访问父类的 name 

88
00:03:34,210 --> 00:03:36,500
这个是访问它自己的 name 

89
00:03:36,500 --> 00:03:38,100
所以我们的第16

90
00:03:38,100 --> 00:03:40,700
这个构造 value 结构体

91
00:03:40,700 --> 00:03:44,410
只给他自己的 length 跟 name 赋值了

92
00:03:44,410 --> 00:03:46,830
没有给这个 user 赋值

93
00:03:46,830 --> 00:03:49,557
那我们可以直接这样写

94
00:03:49,557 --> 00:03:52,070
本来之前我们这边写的是 author 

95
00:03:52,070 --> 00:03:53,970
现在 author 匿名了没有了

96
00:03:53,970 --> 00:03:57,262
那么就直接使用类型来充当名称

97
00:03:57,262 --> 00:03:58,820
这就是所谓的继承
