1
00:00:00,480 --> 00:00:02,600
前面我们已经多次的讲到函数

2
00:00:02,600 --> 00:00:05,767
那这次呢，我们来正式的介绍一下函数

3
00:00:05,767 --> 00:00:07,470
首先说 main 函数

4
00:00:07,470 --> 00:00:09,130
它是程序的入口

5
00:00:09,130 --> 00:00:10,130
必须叫 main 

6
00:00:10,130 --> 00:00:12,170
而且呢括号里面为空

7
00:00:12,170 --> 00:00:14,277
而且没有任何的返回值

8
00:00:14,277 --> 00:00:16,389
这是 main 函数的特点

9
00:00:16,389 --> 00:00:18,500
下面是一个普通函数

10
00:00:18,500 --> 00:00:20,240
它有两个参数

11
00:00:20,240 --> 00:00:21,260
A 和 B 

12
00:00:21,260 --> 00:00:22,500
这两个参数呢

13
00:00:22,500 --> 00:00:24,280
都是 int 整形

14
00:00:24,280 --> 00:00:26,957
并且呢，没有返回值

15
00:00:26,957 --> 00:00:30,650
里面把 A 加 B 的和又赋给了 A 

16
00:00:30,650 --> 00:00:35,537
然后 return ， return 就是函数整个就返回了

17
00:00:35,537 --> 00:00:38,160
所以第18行写不写没有任何作用

18
00:00:38,160 --> 00:00:40,700
因为函数已经返回了嘛

19
00:00:40,700 --> 00:00:42,660
那像下面这种形式

20
00:00:42,660 --> 00:00:45,120
函数也可以不写 return 

21
00:00:45,120 --> 00:00:46,530
那么它默认的啊

22
00:00:46,530 --> 00:00:48,510
把最后一行代码执行完之后

23
00:00:48,510 --> 00:00:50,370
这个函数默认的就退出了嘛

24
00:00:50,370 --> 00:00:52,552
所以也不需要去写那个 return 

25
00:00:52,552 --> 00:00:54,780
同时我们发现 A 跟 B 

26
00:00:54,780 --> 00:00:57,240
由于它们的类型一样的

27
00:00:57,240 --> 00:00:58,500
都是整形

28
00:00:58,500 --> 00:01:00,200
所以呢，给 A 逗号

29
00:01:00,200 --> 00:01:03,530
B 这个数据类型可以只写一次

30
00:01:03,530 --> 00:01:05,360
对于2.1这样一个函数

31
00:01:05,360 --> 00:01:07,040
我定义好这个函数之后

32
00:01:07,040 --> 00:01:09,300
我将来怎么去调用这个函数呢

33
00:01:09,300 --> 00:01:10,910
直接二个一啊

34
00:01:10,910 --> 00:01:13,510
把两个整数传进来就可以了

35
00:01:13,510 --> 00:01:16,690
这边呢， CD 我们称之为食残

36
00:01:16,690 --> 00:01:19,410
而这边这个 A 、 B 我们称之为形参

37
00:01:19,410 --> 00:01:22,690
就说我实际上在调这个函的时候

38
00:01:22,690 --> 00:01:25,270
传的真实数据是三和五

39
00:01:25,270 --> 00:01:26,400
所以叫实参嘛

40
00:01:26,400 --> 00:01:28,920
而这个 A 和 B 呢，只是一个形式

41
00:01:28,920 --> 00:01:30,977
这是一个占位符而已

42
00:01:30,977 --> 00:01:32,430
我们看这样一个函数

43
00:01:32,430 --> 00:01:35,160
它实际上没有任何的输入参数

44
00:01:35,160 --> 00:01:37,597
也没有任何的范围值

45
00:01:37,597 --> 00:01:39,707
再来看下面这个函数

46
00:01:39,707 --> 00:01:41,530
它有两个输入参数

47
00:01:41,530 --> 00:01:43,790
同时呢，它有一个返回值

48
00:01:43,790 --> 00:01:45,150
那如果有返回值的话

49
00:01:45,150 --> 00:01:48,130
就必须显示的写明 return 啊

50
00:01:48,130 --> 00:01:50,830
要返回一个整形变量

51
00:01:50,830 --> 00:01:54,237
你甚至可以在声明函数的时候

52
00:01:54,237 --> 00:01:57,560
一同把这个返回值这个变量啊

53
00:01:57,560 --> 00:01:58,400
名字叫什么

54
00:01:58,400 --> 00:02:00,472
也声明好，我就叫 C 

55
00:02:00,472 --> 00:02:02,130
那既然说你要访问 C 的话

56
00:02:02,130 --> 00:02:06,310
就相当于是已经把 C 这个变量给声明好了

57
00:02:06,310 --> 00:02:08,647
已经给它分配内存了

58
00:02:08,647 --> 00:02:09,740
那这样的话

59
00:02:09,740 --> 00:02:11,800
你就在函数体里面

60
00:02:11,800 --> 00:02:13,940
直接去使用这个词就可以了

61
00:02:13,940 --> 00:02:17,110
它跟我们上面的这种写法还不太一样

62
00:02:17,110 --> 00:02:18,420
我们上面这种写法

63
00:02:18,420 --> 00:02:21,720
C 在第37行是第一次出现

64
00:02:21,720 --> 00:02:23,860
那第一次出现需要先声明

65
00:02:23,860 --> 00:02:25,740
要么通过 while 来声明

66
00:02:25,740 --> 00:02:28,500
要么呢加一个冒号

67
00:02:28,500 --> 00:02:31,260
而由于这边 C 已经声明过了

68
00:02:31,260 --> 00:02:34,770
所以的话你再给 C 赋值就不用

69
00:02:34,770 --> 00:02:37,260
也不能再加冒号了

70
00:02:37,260 --> 00:02:41,740
而且我们上面 return 返回需要显示

71
00:02:41,740 --> 00:02:43,740
指明你要返回谁呀，是吧

72
00:02:43,740 --> 00:02:45,060
A 、 B 、 C 都是整数

73
00:02:45,060 --> 00:02:46,120
你到底要返回谁呢

74
00:02:46,120 --> 00:02:47,555
我说我要返回 C 

75
00:02:47,555 --> 00:02:50,540
而这边的话已经提前说好了

76
00:02:50,540 --> 00:02:51,900
就是要返回 C 嘛

77
00:02:51,900 --> 00:02:55,220
所以这边直接写一个 return 就够了

78
00:02:55,220 --> 00:02:56,900
而且这种情况下

79
00:02:56,900 --> 00:02:59,970
如果我把 return 这一行注释掉

80
00:02:59,970 --> 00:03:02,270
这边有一个红线报错

81
00:03:02,270 --> 00:03:03,860
就是说上面的话

82
00:03:03,860 --> 00:03:06,580
如果我们函数没有返回值

83
00:03:06,580 --> 00:03:08,520
那么这个 return 你可以不写

84
00:03:08,520 --> 00:03:10,272
但下面的话

85
00:03:10,272 --> 00:03:12,600
函数需要有返回值

86
00:03:12,600 --> 00:03:14,570
那么你就必须写 return 啊

87
00:03:14,570 --> 00:03:16,770
哪怕 return 后面什么都不跟

88
00:03:16,770 --> 00:03:18,470
光秃秃的一个 retur

89
00:03:18,470 --> 00:03:20,960
你也是必须要写的

90
00:03:20,960 --> 00:03:22,410
再看下面这个

91
00:03:22,410 --> 00:03:24,897
他演示是说一个函数啊

92
00:03:24,897 --> 00:03:27,780
它其实可以有多个返回值啊

93
00:03:27,780 --> 00:03:28,840
比如这边只有两个

94
00:03:28,840 --> 00:03:30,430
但你也可以有三个、四个

95
00:03:30,430 --> 00:03:32,360
如果是超过一个返回值的话

96
00:03:32,360 --> 00:03:35,695
那么呢，需要把它放在小括号里面

97
00:03:35,695 --> 00:03:37,800
你像我上面只有一个符号值嘛

98
00:03:37,800 --> 00:03:38,960
啊，可以放小括号

99
00:03:38,960 --> 00:03:39,920
而这边的话

100
00:03:39,920 --> 00:03:41,860
超过一个需要放小括号里面

101
00:03:41,860 --> 00:03:43,380
用逗号进行分隔

102
00:03:43,380 --> 00:03:45,460
那既然设两个返回值

103
00:03:45,460 --> 00:03:48,800
那对应的 return 后面就要通过逗号，对吧

104
00:03:48,800 --> 00:03:50,820
你得拿两个值出来

105
00:03:50,820 --> 00:03:52,655
个数得对应上

106
00:03:52,655 --> 00:03:55,100
这是讲了一下函数的几种形式

107
00:03:55,100 --> 00:03:56,590
比较简单

108
00:03:56,590 --> 00:03:59,060
然后我们讲一个比较困扰

109
00:03:59,060 --> 00:03:59,820
很多选的问题

110
00:03:59,820 --> 00:04:01,190
就是关于指针

111
00:04:01,190 --> 00:04:04,000
我们再回到这个函数来看看

112
00:04:04,000 --> 00:04:09,255
那本来传的参数呢，是三和五，对吧

113
00:04:09,255 --> 00:04:10,660
三后传进来

114
00:04:10,660 --> 00:04:12,500
那么3+5=8

115
00:04:12,500 --> 00:04:14,752
把这个八呢，付给了 A 

116
00:04:14,752 --> 00:04:16,050
那 A 就变成八了

117
00:04:16,050 --> 00:04:18,110
是不是意味着对应的

118
00:04:18,110 --> 00:04:20,681
我这个 C 就变成八了呢

119
00:04:20,681 --> 00:04:24,010
我们可以把 C 打印出来看一看

120
00:04:24,010 --> 00:04:27,040
好，我们发现这个 C 呀，它还是三

121
00:04:27,040 --> 00:04:28,760
并没有变成八

122
00:04:28,760 --> 00:04:30,265
这个是为什么呢

123
00:04:30,265 --> 00:04:32,120
这边我要讲出一个 go 圆里面

124
00:04:32,120 --> 00:04:33,980
非常非常重要的一个原则

125
00:04:33,980 --> 00:04:38,597
就是 go 圆里面你给函数传递参数

126
00:04:38,597 --> 00:04:43,240
实际上他是把参数完整的拷贝了一份

127
00:04:43,240 --> 00:04:46,310
把那个备份传给了这个函数

128
00:04:46,310 --> 00:04:47,720
要发生拷贝

129
00:04:47,720 --> 00:04:48,967
第二点

130
00:04:48,967 --> 00:04:50,740
函数如果有返回值

131
00:04:50,740 --> 00:04:53,580
那么这个返回值时就它是拷贝了一份

132
00:04:53,580 --> 00:04:56,327
把备份返回给了外界

133
00:04:56,327 --> 00:04:57,590
第三点

134
00:04:57,590 --> 00:04:58,870
在构位里面

135
00:04:58,870 --> 00:05:00,970
哪怕是一种等号

136
00:05:00,970 --> 00:05:04,877
通过等号赋值也是要发生拷贝的

137
00:05:04,877 --> 00:05:08,917
好，我们先来说这个函数参数拷贝位的问题

138
00:05:08,917 --> 00:05:12,270
那本来在内存里面为 C 和 D 

139
00:05:12,270 --> 00:05:14,470
这两个整数各占八个字节嘛

140
00:05:14,470 --> 00:05:17,155
啊，给他们一共分配了16个字节

141
00:05:17,155 --> 00:05:20,810
然后的话你要去调这个 arc 1这个函数嘛

142
00:05:20,810 --> 00:05:21,630
于是乎呢

143
00:05:21,630 --> 00:05:24,405
他就另外找了16个字节

144
00:05:24,405 --> 00:05:26,590
把三和五拷贝了一份

145
00:05:26,590 --> 00:05:30,832
把新的内存空间传给了这个函数

146
00:05:30,832 --> 00:05:32,520
所以在这个函数里面呢

147
00:05:32,520 --> 00:05:34,300
你进行各种操作对吧

148
00:05:34,300 --> 00:05:36,565
你想给 A 重新赋值

149
00:05:36,565 --> 00:05:39,840
你是把这个83+5=8吗

150
00:05:39,840 --> 00:05:43,520
把八放到了那一块新的内存空间里面

151
00:05:43,520 --> 00:05:46,917
并没有影响原始的这个 C 跟 D 

152
00:05:46,917 --> 00:05:50,890
那么如果就想影响原始的 CD 怎么办呢

153
00:05:50,890 --> 00:05:52,772
你需要传指针

154
00:05:52,772 --> 00:05:55,000
就如同这边这个地方

155
00:05:55,000 --> 00:05:58,270
好，在整形前面我加一个星

156
00:05:58,270 --> 00:05:59,890
就表示指针类型

157
00:05:59,890 --> 00:06:02,162
指针就是地址嘛

158
00:06:02,162 --> 00:06:06,590
比如说本来这个 C 的地址号是503

159
00:06:06,590 --> 00:06:09,190
D 呢是603

160
00:06:09,190 --> 00:06:13,145
那我就是把这503和 B 03这两个地址号

161
00:06:13,145 --> 00:06:15,460
好比是你宿舍的门牌号吧

162
00:06:15,460 --> 00:06:19,947
好，把宿舍门牌号传给了2.3这个函数

163
00:06:19,947 --> 00:06:22,430
然后我到这个函数里面呢

164
00:06:22,430 --> 00:06:24,690
A 、 B 都是那个指针嘛

165
00:06:24,690 --> 00:06:26,500
都是门牌号

166
00:06:26,500 --> 00:06:29,450
然后我在门牌号前面加一个星

167
00:06:29,450 --> 00:06:30,550
就表示什么

168
00:06:30,550 --> 00:06:31,870
指针的解析

169
00:06:31,870 --> 00:06:35,997
把它从原始的指针形式就转成了

170
00:06:35,997 --> 00:06:37,300
非直指形式

171
00:06:37,300 --> 00:06:41,752
就是从这个新号 int 转成了 int 

172
00:06:41,752 --> 00:06:44,250
因为如果是直接 A 加 B 的话

173
00:06:44,250 --> 00:06:47,950
是那个503+603是两个地址号相加

174
00:06:47,950 --> 00:06:52,507
那我显然我想要的是3+5嘛

175
00:06:52,507 --> 00:06:56,890
那么根据指针如何取得它指向的内容本身呢

176
00:06:56,890 --> 00:06:59,230
就是在指针前面加一个星号

177
00:06:59,230 --> 00:07:01,570
好，3+5=8

178
00:07:01,570 --> 00:07:02,850
把这个八付给谁呢

179
00:07:02,850 --> 00:07:05,610
把这个八付给原始的那个 A 

180
00:07:05,610 --> 00:07:09,210
也就是付给了这一块内存

181
00:07:09,210 --> 00:07:11,180
从三变成了八

182
00:07:11,180 --> 00:07:13,920
同时呢，他把原先的这个 B 呀

183
00:07:13,920 --> 00:07:15,977
也改成了888

184
00:07:15,977 --> 00:07:18,650
就拿着603那个地址号

185
00:07:18,650 --> 00:07:21,410
他把这个888写到了603

186
00:07:21,410 --> 00:07:23,160
那个地址里面去了

187
00:07:23,160 --> 00:07:26,750
影响的是原始的这个变量 D 

188
00:07:26,750 --> 00:07:29,930
好，我们调一下这个 ARG 3

189
00:07:29,930 --> 00:07:32,870
那既然二个三是要传两个指针嘛

190
00:07:32,870 --> 00:07:36,195
那么就必须加一个取值符号

191
00:07:36,195 --> 00:07:39,620
从原始的整形转成整形指针

192
00:07:39,620 --> 00:07:44,175
然后我们可以把这个 C 和 D 打出来看看

193
00:07:44,175 --> 00:07:47,030
好，那么 C 呢，是3+5=8

194
00:07:47,030 --> 00:07:50,450
而这个 D 呢，是强制的被改成了8888

195
00:07:50,450 --> 00:07:53,050
就是这边同样道理啊

196
00:07:53,050 --> 00:07:55,267
那么我们来分析这个函数

197
00:07:55,267 --> 00:07:57,160
他不是要返回一个整数吗

198
00:07:57,160 --> 00:08:00,680
好，我在第38行搞了一个全新的变量 C 

199
00:08:00,680 --> 00:08:02,960
相当于说我申请了一块

200
00:08:02,960 --> 00:08:05,417
八个字节的内存空间

201
00:08:05,417 --> 00:08:09,200
这个内存空间里面放的就是这个 A 的值啊

202
00:08:09,200 --> 00:08:10,480
应该是八，对吧

203
00:08:10,480 --> 00:08:11,400
三角等于八嘛

204
00:08:11,400 --> 00:08:12,950
把八放到 C 里面

205
00:08:12,950 --> 00:08:14,470
然后呢，我返回这个 C 

206
00:08:14,470 --> 00:08:18,505
注意，当我在执行这个 return 返回的时候

207
00:08:18,505 --> 00:08:23,750
我实际上是对这个 C 拷贝了一份

208
00:08:23,750 --> 00:08:28,420
把这个备份返回给了这个函数的调用方

209
00:08:28,420 --> 00:08:31,150
那我们在很多时候发现一个函数

210
00:08:31,150 --> 00:08:33,890
如果它是要返回一个结构体的话

211
00:08:33,890 --> 00:08:37,089
通常呢会返回结构体指针

212
00:08:37,089 --> 00:08:39,429
因为如果不返回指针的话

213
00:08:39,429 --> 00:08:42,260
那么我要把这个结构体拷贝一份

214
00:08:42,260 --> 00:08:45,830
如果结构体里面成员变量特别多

215
00:08:45,830 --> 00:08:48,750
那这个拷贝的开销就会比较大

216
00:08:48,750 --> 00:08:51,070
所以为了降低拷贝开销呢

217
00:08:51,070 --> 00:08:52,270
我返回一个指针

218
00:08:52,270 --> 00:08:53,650
就只返回一个地址号码

219
00:08:53,650 --> 00:08:55,510
一个地址就是一个数字而已

220
00:08:55,510 --> 00:08:57,620
这个开销就很小

221
00:08:57,620 --> 00:09:00,310
包括之前我们在讲结构体的时候

222
00:09:00,310 --> 00:09:02,677
这边搞了一个结构体

223
00:09:02,677 --> 00:09:04,660
注意看第25行

224
00:09:04,660 --> 00:09:05,960
我把这个结构体呢

225
00:09:05,960 --> 00:09:07,660
付给了另外一个变量 U 1

226
00:09:07,660 --> 00:09:09,877
那显然 U 1也是一个结构体

227
00:09:09,877 --> 00:09:11,930
这个时候把一个结构体

228
00:09:11,930 --> 00:09:13,810
通过等号赋给另外一个结构体

229
00:09:13,810 --> 00:09:16,400
它实际上也是要发生拷贝的

230
00:09:16,400 --> 00:09:19,790
也就意味着我如果通过 UE 是吧

231
00:09:19,790 --> 00:09:21,595
我把这个 name 改了

232
00:09:21,595 --> 00:09:24,060
改成叫张三的话

233
00:09:24,060 --> 00:09:26,830
那么此时这个 U 啊

234
00:09:26,830 --> 00:09:30,780
U 它的内部依然是大乔乔

235
00:09:30,780 --> 00:09:32,300
并没有被改成张三

236
00:09:32,300 --> 00:09:36,950
因为 U 和 UE 他们两个是完全不同的

237
00:09:36,950 --> 00:09:38,120
那一层空间

238
00:09:38,120 --> 00:09:40,460
只是说在刚开始的时候

239
00:09:40,460 --> 00:09:43,220
U 1跟 U 里面存放内容是一样的

240
00:09:43,220 --> 00:09:44,520
但后面的话

241
00:09:44,520 --> 00:09:45,800
修改其中一

242
00:09:45,800 --> 00:09:47,890
完全不影响另外一方

243
00:09:47,890 --> 00:09:50,412
因为发生了拷贝

244
00:09:50,412 --> 00:09:52,670
同理，一个函数参数

245
00:09:52,670 --> 00:09:55,620
如果这个参数它是一个结构体类型的话

246
00:09:55,620 --> 00:09:58,070
那么当我去调用这个函数

247
00:09:58,070 --> 00:09:59,370
传一个结构体的时候

248
00:09:59,370 --> 00:10:02,940
也是把那个结构体的备份扔给了这个函数

249
00:10:02,940 --> 00:10:04,080
你在这个函数里面

250
00:10:04,080 --> 00:10:06,020
不管怎么去修改这个结构体

251
00:10:06,020 --> 00:10:07,440
修改的都是那个备份

252
00:10:07,440 --> 00:10:09,375
并不影响原始结构体

253
00:10:09,375 --> 00:10:13,022
除非说你传的是那个结构体指针

254
00:10:13,022 --> 00:10:17,530
那么你在函数里面是通过指针操控的

255
00:10:17,530 --> 00:10:19,872
原始的那一块内存空间

256
00:10:19,872 --> 00:10:22,060
就能够修改原始的结构体
