1
00:00:00,440 --> 00:00:02,940
看一下发布者、订阅者模式

2
00:00:02,940 --> 00:00:06,440
这边呢，所画的发布者一和订阅者一

3
00:00:06,440 --> 00:00:07,240
订阅者二

4
00:00:07,240 --> 00:00:08,380
这三方呢

5
00:00:08,380 --> 00:00:11,680
实际上是分布式里面的三台服务器啊

6
00:00:11,680 --> 00:00:13,962
三个完全独立的进程

7
00:00:13,962 --> 00:00:16,710
你可以想象成是有三个小组啊

8
00:00:16,710 --> 00:00:17,710
三个右方

9
00:00:17,710 --> 00:00:20,455
那从这个发布的一上面

10
00:00:20,455 --> 00:00:21,890
从这台服务器上面

11
00:00:21,890 --> 00:00:23,230
他发布了一个消息

12
00:00:23,230 --> 00:00:25,570
这个消息呢，发给了 REDIS 啊

13
00:00:25,570 --> 00:00:29,630
准确来说是报到了 REDIS 的一个频道里面去

14
00:00:29,630 --> 00:00:31,560
然后呢，另外两台服务器

15
00:00:31,560 --> 00:00:32,840
另外两个购物进程

16
00:00:32,840 --> 00:00:35,230
他们可以去订阅这个频道

17
00:00:35,230 --> 00:00:37,490
一旦频道里面有数据进了的话

18
00:00:37,490 --> 00:00:41,247
那这两个订阅方就都能够收到这个消息

19
00:00:41,247 --> 00:00:43,300
相当于是一种广播机制吧，对吧

20
00:00:43,300 --> 00:00:44,700
这边广播一个消息

21
00:00:44,700 --> 00:00:47,370
然后这两方呢，就收到这个消息

22
00:00:47,370 --> 00:00:48,380
那其实啊

23
00:00:48,380 --> 00:00:51,020
这个发布电影关系可以更复杂

24
00:00:51,020 --> 00:00:53,170
那比如说作为一个发布方

25
00:00:53,170 --> 00:00:57,130
它可以把多个不同的频道里面去发布消息

26
00:00:57,130 --> 00:00:59,830
第二个发布方也可以往这些频道里面呢

27
00:00:59,830 --> 00:01:00,770
去发布消息

28
00:01:00,770 --> 00:01:01,830
同一个频道啊

29
00:01:01,830 --> 00:01:04,940
可以被多个订阅方所订阅

30
00:01:04,940 --> 00:01:08,070
一个订阅方可以订阅多个频道

31
00:01:08,070 --> 00:01:09,700
我们来举一个例子啊

32
00:01:09,700 --> 00:01:12,140
比如说这个发布方一

33
00:01:12,140 --> 00:01:13,500
它相当于什么呢

34
00:01:13,500 --> 00:01:17,160
相当于我们的一个电商的后端优步

35
00:01:17,160 --> 00:01:18,760
那么用户的所有行为

36
00:01:18,760 --> 00:01:22,665
都是通过这个右部的代码来进行的

37
00:01:22,665 --> 00:01:25,240
哎，用户点击了某件商品

38
00:01:25,240 --> 00:01:27,290
他要查看这个商品的详情

39
00:01:27,290 --> 00:01:28,220
于是乎呢

40
00:01:28,220 --> 00:01:30,540
就把这个用户的点击行为呀

41
00:01:30,540 --> 00:01:33,300
发到了频道一里面去啊

42
00:01:33,300 --> 00:01:35,990
这个是跟点击相关数据

43
00:01:35,990 --> 00:01:38,060
那这个数据谁比较关心呢

44
00:01:38,060 --> 00:01:40,220
哎，推荐组比较关心

45
00:01:40,220 --> 00:01:42,120
假如说他是推荐组吧

46
00:01:42,120 --> 00:01:42,880
他比较关心

47
00:01:42,880 --> 00:01:43,450
因什么呢

48
00:01:43,450 --> 00:01:46,330
因为推荐组他获得用户的喜好啊

49
00:01:46,330 --> 00:01:47,390
用户点击了

50
00:01:47,390 --> 00:01:49,480
所以说你对他有点感兴趣

51
00:01:49,480 --> 00:01:51,980
所以呢，他需要收集几个数据，诶

52
00:01:51,980 --> 00:01:53,362
他比较关心

53
00:01:53,362 --> 00:01:56,650
然后呢，用户又购买了这个商品

54
00:01:56,650 --> 00:01:57,950
那购买行为呢

55
00:01:57,950 --> 00:02:01,780
我们认为你把它发送到评到二里面去

56
00:02:01,780 --> 00:02:02,760
这是购买

57
00:02:02,760 --> 00:02:05,050
那购买数据谁比较关心呢

58
00:02:05,050 --> 00:02:08,169
首先推荐组比较关心，对吧

59
00:02:08,169 --> 00:02:09,150
因为你购买了

60
00:02:09,150 --> 00:02:11,790
说明你对它非常的感兴趣嘛

61
00:02:11,790 --> 00:02:14,030
有助于刻画用户画像

62
00:02:14,030 --> 00:02:16,820
同时呢，物流组也比较关心

63
00:02:16,820 --> 00:02:18,000
你既然购买了

64
00:02:18,000 --> 00:02:19,940
那我得通知物流啊

65
00:02:19,940 --> 00:02:21,637
啊，通知仓库发货呀

66
00:02:21,637 --> 00:02:24,332
所以他也会订阅这份数据

67
00:02:24,332 --> 00:02:24,820
诶

68
00:02:24,820 --> 00:02:25,540
这样的话

69
00:02:25,540 --> 00:02:27,180
不同类型的数据呢

70
00:02:27,180 --> 00:02:29,220
发到不同的频道里面去

71
00:02:29,220 --> 00:02:30,500
然后我们强调一下

72
00:02:30,500 --> 00:02:33,750
就是当我的这个 subscriber 啊

73
00:02:33,750 --> 00:02:37,317
订阅方它的程序启动之前

74
00:02:37,317 --> 00:02:40,520
如果有人往频道里面去写入数据的话

75
00:02:40,520 --> 00:02:44,160
那那些数据这个订阅方他是收不到的

76
00:02:44,160 --> 00:02:46,860
就只有说我已经启动好了

77
00:02:46,860 --> 00:02:48,360
那程序部署起来了

78
00:02:48,360 --> 00:02:51,340
此后的话， channel 里面有新数据进来

79
00:02:51,340 --> 00:02:53,300
我才能够接触到

80
00:02:53,300 --> 00:02:55,237
来看一下代码怎么写

81
00:02:55,237 --> 00:02:57,770
这里封装了一个发布函数

82
00:02:57,770 --> 00:02:59,510
核心是传一个 context 

83
00:02:59,510 --> 00:03:01,690
然后再传一个 release client 

84
00:03:01,690 --> 00:03:02,770
把那个频道

85
00:03:02,770 --> 00:03:05,130
频道实际上就是一个字符串啊

86
00:03:05,130 --> 00:03:05,870
频道传过来

87
00:03:05,870 --> 00:03:08,920
包括你想往频道里面发布的数

88
00:03:08,920 --> 00:03:10,140
内容是一个 any 

89
00:03:10,140 --> 00:03:12,420
任意类型都可以传进来

90
00:03:12,420 --> 00:03:15,140
因为你可以把这个频道理解为是 key 嘛

91
00:03:15,140 --> 00:03:17,280
这个 message 理解为是 value 啊

92
00:03:17,280 --> 00:03:19,457
把 key value 呢写进 EDDI 

93
00:03:19,457 --> 00:03:21,690
直接调用 publish 

94
00:03:21,690 --> 00:03:24,070
写入 key 和 value 

95
00:03:24,070 --> 00:03:24,490
注意啊

96
00:03:24,490 --> 00:03:26,112
这个地方不是 S 

97
00:03:26,112 --> 00:03:28,200
因为咱们这个不是普通的 KY 6

98
00:03:28,200 --> 00:03:30,800
咱们这个是支持发布地面的嘛

99
00:03:30,800 --> 00:03:34,740
所以通过调 publish 它返回的这个 CMD 呢

100
00:03:34,740 --> 00:03:38,460
我们通过 CMD 调这个 value 能够获得什么

101
00:03:38,460 --> 00:03:39,260
能够获得

102
00:03:39,260 --> 00:03:43,550
目前啊，有几个订阅方在订阅这个频道

103
00:03:43,550 --> 00:03:46,670
然后这边用到了这个 context 啊

104
00:03:46,670 --> 00:03:48,710
这个地方 context 是有实际作用的啊

105
00:03:48,710 --> 00:03:50,350
我们从这个 context 里面呢

106
00:03:50,350 --> 00:03:54,267
通过调 value 来获得这个发布方的 name 

107
00:03:54,267 --> 00:03:55,240
就将来呀

108
00:03:55,240 --> 00:03:58,960
我们去调这个 publish 函数传的 context 

109
00:03:58,960 --> 00:04:01,200
它不是一个完全空的 context 

110
00:04:01,200 --> 00:04:03,970
我们会把这个发布者 name 呢

111
00:04:03,970 --> 00:04:06,050
放到 context 里面来

112
00:04:06,050 --> 00:04:07,420
再看一下怎么订阅

113
00:04:07,420 --> 00:04:10,430
订阅的话，你核心是要指定 channel 嘛

114
00:04:10,430 --> 00:04:13,405
啊，你要订阅哪些个频道

115
00:04:13,405 --> 00:04:15,430
通过调 subscribe 函

116
00:04:15,430 --> 00:04:18,048
把所有频道呢直接传过来

117
00:04:18,048 --> 00:04:20,030
你定义了这么多频道

118
00:04:20,030 --> 00:04:22,770
那么呢，这边开启一个无限循环

119
00:04:22,770 --> 00:04:24,410
从频道里面取出数据

120
00:04:24,410 --> 00:04:26,510
这一旦说频道里面有数据

121
00:04:26,510 --> 00:04:30,190
那么这个 receive message 就可以把数据取出来

122
00:04:30,190 --> 00:04:31,810
如果频道里面没有数据

123
00:04:31,810 --> 00:04:34,540
那么这个 receive message 叫阻塞

124
00:04:34,540 --> 00:04:36,850
好，取出一个 message 

125
00:04:36,850 --> 00:04:39,250
这个 message 是一个普通的字符串吗

126
00:04:39,250 --> 00:04:41,390
并不是，它是一个结构体

127
00:04:41,390 --> 00:04:47,160
就是说我们从这个 message 里面调这个 payload 

128
00:04:47,160 --> 00:04:49,800
能够取得这个消息的本身啊

129
00:04:49,800 --> 00:04:51,157
消息内容本身

130
00:04:51,157 --> 00:04:54,690
同时呢，由于我不是订阅了多个频道吗

131
00:04:54,690 --> 00:04:56,350
那我怎么知道这个消息

132
00:04:56,350 --> 00:04:58,570
是从哪个频道里面取出来的

133
00:04:58,570 --> 00:05:00,950
通过调 message 点 channel 

134
00:05:00,950 --> 00:05:03,385
还能够把那个频道名称给取出来

135
00:05:03,385 --> 00:05:06,930
同时呢，我传的这个 context 也不是空的啊

136
00:05:06,930 --> 00:05:09,392
它里面包含了订阅者的 name 

137
00:05:09,392 --> 00:05:11,280
如果中途发生了 error 

138
00:05:11,280 --> 00:05:13,420
那么就 break 退出整个 for 循环

139
00:05:13,420 --> 00:05:17,707
最后呢，通过 defer 把这个 subscriber 把它给关闭掉

140
00:05:17,707 --> 00:05:20,490
好，这是对发布和订阅进行了封装

141
00:05:20,490 --> 00:05:23,310
我们看一下这个实验是怎么设计的

142
00:05:23,310 --> 00:05:26,330
这边打算搞一套比较复杂的发布者

143
00:05:26,330 --> 00:05:27,870
订阅者关系

144
00:05:27,870 --> 00:05:30,270
打算搞两个发布方

145
00:05:30,270 --> 00:05:33,210
我这边是使用的两个线程

146
00:05:33,210 --> 00:05:34,490
来模拟两个发布者

147
00:05:34,490 --> 00:05:36,490
但实际上在分布式环境下

148
00:05:36,490 --> 00:05:39,660
我们一般的话是通过一个进程

149
00:05:39,660 --> 00:05:41,770
来代表一个八五方

150
00:05:41,770 --> 00:05:44,040
打算使用两个 channel 啊

151
00:05:44,040 --> 00:05:45,447
channel 1和 channel 2

152
00:05:45,447 --> 00:05:47,450
然后这边又搞了两个 context 

153
00:05:47,450 --> 00:05:50,110
他们用来给那个订阅方来使嘛

154
00:05:50,110 --> 00:05:52,142
订阅方三，订阅方四

155
00:05:52,142 --> 00:05:53,860
起了两个携程

156
00:05:53,860 --> 00:05:57,937
这两个协程呢，是启动两个订阅方

157
00:05:57,937 --> 00:05:59,850
因为我得先启动订阅房

158
00:05:59,850 --> 00:06:01,410
后启动发布房

159
00:06:01,410 --> 00:06:03,090
因为如果先启动发布房

160
00:06:03,090 --> 00:06:05,202
往里面发布数据了

161
00:06:05,202 --> 00:06:06,560
后启动订阅方

162
00:06:06,560 --> 00:06:09,560
那订阅方是拿不到他启动之前的数据的

163
00:06:09,560 --> 00:06:10,720
所以我故意的啊

164
00:06:10,720 --> 00:06:12,635
先启动了订阅方

165
00:06:12,635 --> 00:06:14,460
那目前来看，这两个 DN 方

166
00:06:14,460 --> 00:06:16,820
他们订阅的一个是 channel 1

167
00:06:16,820 --> 00:06:18,557
一个是 channel 2

168
00:06:18,557 --> 00:06:19,590
休息一秒钟

169
00:06:19,590 --> 00:06:23,170
然后呢，去启动了两个发布方

170
00:06:23,170 --> 00:06:24,910
这两个发布方啊

171
00:06:24,910 --> 00:06:29,610
他们是向同一个频道里面去发送数据啊

172
00:06:29,610 --> 00:06:31,800
都往频道一里面去发送数据

173
00:06:31,800 --> 00:06:36,710
那这样的话，实际上只有这个 context 3啊

174
00:06:36,710 --> 00:06:39,190
这个订阅三它能够收到消息

175
00:06:39,190 --> 00:06:40,870
因为这个四的话

176
00:06:40,870 --> 00:06:43,310
它监听的是频道二嘛

177
00:06:43,310 --> 00:06:44,580
频道二里面没有数据

178
00:06:44,580 --> 00:06:46,710
我们把这个单侧跑起来

179
00:06:46,710 --> 00:06:48,680
这边确实能看到啊

180
00:06:48,680 --> 00:06:50,930
发布者一和发布者二啊

181
00:06:50,930 --> 00:06:54,750
他们都各自的向频道一里面去发布了内容

182
00:06:54,750 --> 00:06:56,330
然后呢，订阅者三

183
00:06:56,330 --> 00:06:59,490
他从频道一里面收到了这两条消息

184
00:06:59,490 --> 00:07:02,600
而订阅者四呢，没有收到任何消息

185
00:07:02,600 --> 00:07:04,510
然后再来看第二个实验

186
00:07:04,510 --> 00:07:06,710
第二个实验这边搞了啊

187
00:07:06,710 --> 00:07:08,302
这个订阅方五

188
00:07:08,302 --> 00:07:09,960
然后去启动这个地方

189
00:07:09,960 --> 00:07:13,745
这个地方的话，它是同时监听了两个频道

190
00:07:13,745 --> 00:07:15,900
然后呢，又启动了两个集成

191
00:07:15,900 --> 00:07:18,910
他们都是往频道二里面去发送数据

192
00:07:18,910 --> 00:07:21,160
那么频道二里面发了两条数据

193
00:07:21,160 --> 00:07:25,850
这两条数据应该都会被订阅方五给收到

194
00:07:25,850 --> 00:07:29,082
同时的话也会被订阅方四收到

195
00:07:29,082 --> 00:07:31,260
那这里我们看到订阅方四呢

196
00:07:31,260 --> 00:07:32,960
他收到了这两条消息

197
00:07:32,960 --> 00:07:34,160
然后订阅方五呢

198
00:07:34,160 --> 00:07:35,990
也收到了这两个消息

199
00:07:35,990 --> 00:07:39,320
两个发布方往频道里面去发布消息的时候

200
00:07:39,320 --> 00:07:40,260
他们发现呢

201
00:07:40,260 --> 00:07:42,930
这个频道已经有两个订阅者了

202
00:07:42,930 --> 00:07:45,160
subscriber 4和 SUBSEQUER 5吧
