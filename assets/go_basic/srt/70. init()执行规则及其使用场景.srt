1
00:00:00,659 --> 00:00:01,900
在构建开发中

2
00:00:01,900 --> 00:00:04,570
我们可能会时不时的使用 INIT 函数

3
00:00:04,570 --> 00:00:07,630
就是这个小写的 e it 函数

4
00:00:07,630 --> 00:00:09,000
确实会带来一些便利

5
00:00:09,000 --> 00:00:10,520
但是呢，坑也比较多

6
00:00:10,520 --> 00:00:12,232
我们一起来看一下

7
00:00:12,232 --> 00:00:16,190
首先，在这个 project prepare 这个目录下啊

8
00:00:16,190 --> 00:00:18,130
我搞了一个一点 go 

9
00:00:18,130 --> 00:00:20,582
这是一个普通的 go 文件

10
00:00:20,582 --> 00:00:24,080
当然这个 go 文件名称不一定非得叫 in int 点 go 

11
00:00:24,080 --> 00:00:24,740
我说过啊

12
00:00:24,740 --> 00:00:25,460
在购物类里面

13
00:00:25,460 --> 00:00:27,300
这个文件名称没有任何作用啊

14
00:00:27,300 --> 00:00:29,242
文件名称可以随便取

15
00:00:29,242 --> 00:00:30,410
在这个文件里面呢

16
00:00:30,410 --> 00:00:32,910
我搞了一个全局的变量

17
00:00:32,910 --> 00:00:35,030
而且是一个大写开头嘛

18
00:00:35,030 --> 00:00:36,230
包外可见啊

19
00:00:36,230 --> 00:00:38,067
可导出的一个变量

20
00:00:38,067 --> 00:00:40,260
目前它是一个空指针

21
00:00:40,260 --> 00:00:41,787
还没有给它赋值

22
00:00:41,787 --> 00:00:45,410
然后在一个所谓的一定的函数里面呢

23
00:00:45,410 --> 00:00:47,990
我去给这个 REG 对吧

24
00:00:47,990 --> 00:00:50,000
给这个变量进行了赋值

25
00:00:50,000 --> 00:00:52,930
大家先不用管这个变量类型是什么意思啊

26
00:00:52,930 --> 00:00:54,677
它是一个正则表达式

27
00:00:54,677 --> 00:00:57,700
这个复程序可能会返回一个 error 吗

28
00:00:57,700 --> 00:00:59,640
那如果说 error 不为空的话

29
00:00:59,640 --> 00:01:01,130
会直接 panic 

30
00:01:01,130 --> 00:01:03,725
直接结束整个购进程

31
00:01:03,725 --> 00:01:05,278
如果成功的话

32
00:01:05,278 --> 00:01:07,262
打印一个语句

33
00:01:07,262 --> 00:01:09,730
然后我发现这个 in 函数

34
00:01:09,730 --> 00:01:12,630
它跟普通函数有一个很重要的区别

35
00:01:12,630 --> 00:01:16,580
就是普通函数的话是不能出现重名函数的

36
00:01:16,580 --> 00:01:18,360
而这个一函数呢

37
00:01:18,360 --> 00:01:20,735
可以反复出现好多次，对吧

38
00:01:20,735 --> 00:01:22,060
又出现第二次啊

39
00:01:22,060 --> 00:01:23,617
没有任何语法错误

40
00:01:23,617 --> 00:01:25,670
在第二个函数里面呢

41
00:01:25,670 --> 00:01:30,407
它去使用了刚才的这个全局变量 REG 

42
00:01:30,407 --> 00:01:32,820
他调用它的某一个方法嘛

43
00:01:32,820 --> 00:01:33,962
match 

44
00:01:33,962 --> 00:01:34,970
那所以啊

45
00:01:34,970 --> 00:01:36,450
在这个地方我们警惕一下

46
00:01:36,450 --> 00:01:40,010
那么实际上我们的第二个 in 函数

47
00:01:40,010 --> 00:01:42,730
它依赖第一个 in 函数

48
00:01:42,730 --> 00:01:45,280
它需要第一个先执行啊

49
00:01:45,280 --> 00:01:46,540
才可以执行第二个

50
00:01:46,540 --> 00:01:48,855
如果直接执行第二个的话

51
00:01:48,855 --> 00:01:52,920
那么你会发现这个地方会报一个空指针

52
00:01:52,920 --> 00:01:56,050
因为这个 REG 还没有初始刷好

53
00:01:56,050 --> 00:01:57,110
它还是空指针吧

54
00:01:57,110 --> 00:02:00,725
啊，你不能一上来就去执行这个 match 函数

55
00:02:00,725 --> 00:02:02,310
再来看我们的 main 函数

56
00:02:02,310 --> 00:02:05,510
main 函数是放在了一定的这个目录下

57
00:02:05,510 --> 00:02:06,010
当然了

58
00:02:06,010 --> 00:02:08,150
这个目录名称可以随便取啊

59
00:02:08,150 --> 00:02:09,531
不一定非得叫 INIT 

60
00:02:09,531 --> 00:02:10,940
里面有个 A 点 go 

61
00:02:10,940 --> 00:02:14,062
A 点 go 里面呢，有我们的 main 函数

62
00:02:14,062 --> 00:02:17,610
main 函数只是随便打印了一个字符串

63
00:02:17,610 --> 00:02:18,610
仅此而已

64
00:02:18,610 --> 00:02:20,610
而在这个购物空间里面呢

65
00:02:20,610 --> 00:02:24,260
它又出现了两个一一的函数是吧

66
00:02:24,260 --> 00:02:25,220
一个在 main 前面

67
00:02:25,220 --> 00:02:27,105
一个在 min 后面

68
00:02:27,105 --> 00:02:28,880
在第一个函数里面呢

69
00:02:28,880 --> 00:02:32,695
又会去调用刚才那个 REG 

70
00:02:32,695 --> 00:02:34,400
因为这个时候已经跨包了嘛

71
00:02:34,400 --> 00:02:36,600
因为现在我的包叫 min 

72
00:02:36,600 --> 00:02:40,750
刚才这个包叫 project prepare 跨包了

73
00:02:40,750 --> 00:02:41,250
所以呢

74
00:02:41,250 --> 00:02:44,830
你需要在这个变量前面加上对应的包名

75
00:02:44,830 --> 00:02:49,350
同时需要在 import 里面把它给引进来

76
00:02:49,350 --> 00:02:51,577
再回忆一下我们的 import 语句

77
00:02:51,577 --> 00:02:55,200
一上来这部分是我的 module 名称

78
00:02:55,200 --> 00:02:58,960
module 名称在我的 go 点 mod 文件里面有提示

79
00:02:58,960 --> 00:03:00,620
module 名称是什么

80
00:03:00,620 --> 00:03:03,360
把后呢，就是那个目录名称

81
00:03:03,360 --> 00:03:05,260
就是这一集的目录名称

82
00:03:05,260 --> 00:03:07,877
注意啊，不是那个 package 名称

83
00:03:07,877 --> 00:03:10,610
这个地方才是 pk 键名称

84
00:03:10,610 --> 00:03:12,180
第二个 in

85
00:03:12,180 --> 00:03:14,770
随便打印一个字符串

86
00:03:14,770 --> 00:03:17,230
我们搞了这么多 in 的函数

87
00:03:17,230 --> 00:03:20,350
最后呢，在 main 函数里面居然没有把他们吊起来

88
00:03:20,350 --> 00:03:23,175
那那这些函数它会执行吗

89
00:03:23,175 --> 00:03:24,880
我们来跑一

90
00:03:24,880 --> 00:03:26,530
go run 

91
00:03:26,530 --> 00:03:31,510
好，后面我要写上这个 A 点 go 所在的目录

92
00:03:34,340 --> 00:03:36,020
先是 project repair 

93
00:03:36,020 --> 00:03:37,720
然后是这个 INIT 

94
00:03:37,720 --> 00:03:38,800
这一级目

95
00:03:38,800 --> 00:03:41,100
回车看一下

96
00:03:41,100 --> 00:03:42,590
输出这么多

97
00:03:42,590 --> 00:03:45,540
那这个 init r EG success 

98
00:03:45,540 --> 00:03:47,540
这个明显是在我们的

99
00:03:49,130 --> 00:03:50,890
这个函数里面对吧

100
00:03:50,890 --> 00:03:52,370
输出了这样一行语句嘛

101
00:03:52,370 --> 00:03:55,680
然后是 INIT 是否匹配

102
00:03:55,680 --> 00:04:00,217
正则表达式应该是我们的这一个函数输出的

103
00:04:00,217 --> 00:04:02,170
然后来到了 init log 

104
00:04:02,170 --> 00:04:03,330
应该是我们的 A 点

105
00:04:03,330 --> 00:04:06,730
go 里面的这个地方输出的

106
00:04:06,730 --> 00:04:07,440
对吧

107
00:04:07,440 --> 00:04:08,830
main 是否匹配

108
00:04:08,830 --> 00:04:10,980
应该是第13行输出的

109
00:04:10,980 --> 00:04:12,750
然后是一定 database 

110
00:04:12,750 --> 00:04:15,810
应该是我们的这个地方入住的

111
00:04:15,810 --> 00:04:16,930
最后是 server 

112
00:04:16,930 --> 00:04:19,772
start 是我们的命函数注释的

113
00:04:19,772 --> 00:04:21,480
所以啊，从这个地方话

114
00:04:21,480 --> 00:04:23,820
我发现所有的一函数

115
00:04:23,820 --> 00:04:28,485
它都是在 main 函数之前就已经执行了

116
00:04:28,485 --> 00:04:30,250
那同时我们来分析一

117
00:04:30,250 --> 00:04:31,980
这么多 in 的函数

118
00:04:31,980 --> 00:04:34,860
它们的执行顺序是什么

119
00:04:34,860 --> 00:04:38,800
首先来说，我们发现在同一个文件内部是吧

120
00:04:38,800 --> 00:04:43,447
同一个购物文件内部如果存在多个 in 函数

121
00:04:43,447 --> 00:04:47,230
那么他们的执行顺序就是我们的书写顺序

122
00:04:47,230 --> 00:04:47,650
对吧

123
00:04:47,650 --> 00:04:51,360
你在代码里面先写了哪个 int 函数

124
00:04:51,360 --> 00:04:54,350
它就先执行了一个 int 函数

125
00:04:54,350 --> 00:04:57,820
那如果是我的 main 函数里面

126
00:04:57,820 --> 00:05:00,620
我引用了另外一个包是吧

127
00:05:00,620 --> 00:05:03,740
另外一个包里面它也存在 int 函数的话

128
00:05:03,740 --> 00:05:04,300
那么呢

129
00:05:04,300 --> 00:05:09,290
它实际上会先去执行这个包里面的 int 函数

130
00:05:09,290 --> 00:05:13,300
然后再来执行自己包里面的 int 函数

131
00:05:13,300 --> 00:05:14,370
所以到这个地方

132
00:05:14,370 --> 00:05:15,420
大家会发

133
00:05:15,420 --> 00:05:17,630
构圆里面这个音的函数

134
00:05:17,630 --> 00:05:19,270
它的这种执行顺序啊

135
00:05:19,270 --> 00:05:21,390
实际上规则还是蛮复杂的

136
00:05:21,390 --> 00:05:24,907
刚才我们讲的还算是比较简单的情况

137
00:05:24,907 --> 00:05:27,320
当你的项目代码比较多的时候

138
00:05:27,320 --> 00:05:32,652
那么各种包之间这种引用关系会非常复杂

139
00:05:32,652 --> 00:05:34,630
甚至于说一个包下面

140
00:05:34,630 --> 00:05:36,570
它不会有多个 go 文件吗

141
00:05:36,570 --> 00:05:40,240
每一个构件里面有可能都会有一的函数

142
00:05:40,240 --> 00:05:41,440
那这种情况下

143
00:05:41,440 --> 00:05:42,960
各个购物文件里面

144
00:05:42,960 --> 00:05:44,360
他们的 in 的函数

145
00:05:44,360 --> 00:05:47,147
执行的先后顺序又是什么样子的

146
00:05:47,147 --> 00:05:49,570
很容易把人搞得头大

147
00:05:49,570 --> 00:05:51,312
很难理清楚

148
00:05:51,312 --> 00:05:53,140
那么我们在实际工作中

149
00:05:53,140 --> 00:05:58,110
也建议大家尽量少的去使用 int 函数

150
00:05:58,110 --> 00:06:00,120
如果你使用 int 函数的话

151
00:06:00,120 --> 00:06:01,610
那么需要保证一点

152
00:06:01,610 --> 00:06:04,160
就是我们在这个函数里面

153
00:06:04,160 --> 00:06:07,330
不要去对外部有任何依赖

154
00:06:07,330 --> 00:06:10,127
比如说这个函

155
00:06:10,127 --> 00:06:11,990
它就对于外部有依赖

156
00:06:11,990 --> 00:06:14,552
因为它依赖了 R 、 E 、 G 

157
00:06:14,552 --> 00:06:18,310
它实际上依赖其他函数先执行

158
00:06:18,310 --> 00:06:19,480
所以这种情况下

159
00:06:19,480 --> 00:06:22,540
实际上不适合使用 in 的函数啊

160
00:06:22,540 --> 00:06:24,500
它适合做一个普通的函数

161
00:06:27,560 --> 00:06:29,120
再比方说这边

162
00:06:29,120 --> 00:06:33,097
假设这是一个打算初始化日志的一个函数

163
00:06:33,097 --> 00:06:34,730
那么在实际情况下

164
00:06:34,730 --> 00:06:36,870
可能对外部也是有依赖的

165
00:06:36,870 --> 00:06:39,782
我们也改成一个普通的函数

166
00:06:39,782 --> 00:06:42,357
包括这个初始化数据库连接

167
00:06:42,357 --> 00:06:44,090
可能也需要对外部依赖

168
00:06:44,090 --> 00:06:46,110
比如说他可能本身就依赖于

169
00:06:46,110 --> 00:06:48,280
先把日志初始化好

170
00:06:48,280 --> 00:06:51,467
所以呢，也不要使用 int 函数

171
00:06:51,467 --> 00:06:54,550
你就写成 init database 

172
00:06:54,550 --> 00:06:59,100
然后呢，在你的 main 函数里面去显示的组织

173
00:06:59,100 --> 00:07:01,350
这些初始化顺序

174
00:07:01,350 --> 00:07:06,740
由你认为应该先执行 prepare 包里面的这个 check 函数

175
00:07:06,740 --> 00:07:09,780
然后呢，应该先执行 int logger 

176
00:07:09,780 --> 00:07:14,255
然后才可以执行我们的 int database 

177
00:07:14,255 --> 00:07:18,870
好，我们显示的去指定他们的这个先后顺序

178
00:07:18,870 --> 00:07:21,165
而对于这个函数

179
00:07:21,165 --> 00:07:23,640
它是真正适合使用 int 的

180
00:07:23,640 --> 00:07:26,460
因为它不依赖任何外部的变量嘛

181
00:07:26,460 --> 00:07:28,500
往往是一种简单的

182
00:07:28,500 --> 00:07:30,680
给全局变量赋值的场景呢

183
00:07:30,680 --> 00:07:32,220
比较适合使用 red 层

184
00:07:32,220 --> 00:07:36,292
因为本来我是可以直接这样写的

185
00:07:36,292 --> 00:07:40,080
直接把这个等号赋值呢，放在后面

186
00:07:40,080 --> 00:07:44,277
但是由于这个 compile 函数返回的是两个值

187
00:07:44,277 --> 00:07:45,870
还有一个 L 吗

188
00:07:45,870 --> 00:07:47,880
他如果只返回一个值

189
00:07:47,880 --> 00:07:50,942
那我直接这样写就可以了

190
00:07:50,942 --> 00:07:52,590
但偏偏返回两个值

191
00:07:52,590 --> 00:07:54,210
所以呢，被逼无奈

192
00:07:54,210 --> 00:07:58,400
我只能是放到一个函数体里面去执行

193
00:07:58,400 --> 00:07:59,810
这个 in 的函数

194
00:07:59,810 --> 00:08:02,250
也仅仅是干了这样一件单纯的事情

195
00:08:02,250 --> 00:08:03,927
没有干其他任何事情

196
00:08:03,927 --> 00:08:07,660
同时这个地方我们也发现了 panic ，对吧

197
00:08:07,660 --> 00:08:11,485
我们主动通过代码去触发一个 panic 

198
00:08:11,485 --> 00:08:13,440
就是说我们都知道了

199
00:08:13,440 --> 00:08:17,215
这个是在初始化阶段就会去执行的函数

200
00:08:17,215 --> 00:08:19,780
而这个又是一个很重要的全局变量

201
00:08:19,780 --> 00:08:22,520
一旦这个全局变量没有出小成功

202
00:08:22,520 --> 00:08:26,050
那么我们认为系统在后续的执行过程中

203
00:08:26,050 --> 00:08:28,267
肯定会发生重大错误

204
00:08:28,267 --> 00:08:32,617
倒不如直接让他在启动阶段就直接失败

205
00:08:32,617 --> 00:08:37,296
所以呢，直接通过 panic 来结束整个 go 进程

206
00:08:37,296 --> 00:08:39,700
另外，我们在后面的项目开发里面呢

207
00:08:39,700 --> 00:08:41,620
经常会看到这样的写法

208
00:08:41,620 --> 00:08:43,020
就是在 import 里

209
00:08:43,020 --> 00:08:45,100
我们去引入了某一个包

210
00:08:47,480 --> 00:08:48,940
然后在这个包前面呢

211
00:08:48,940 --> 00:08:51,025
它有一个下划线

212
00:08:51,025 --> 00:08:52,340
之前我们讲过

213
00:08:52,340 --> 00:08:53,460
在这个包前

214
00:08:53,460 --> 00:08:54,740
你可以加这样一个东西

215
00:08:54,740 --> 00:08:57,060
相当于是一个包的别名吗

216
00:08:57,060 --> 00:08:59,797
那这个下划线算是什么别名呢

217
00:08:59,797 --> 00:09:01,710
这个呀，主要是因为

218
00:09:03,140 --> 00:09:04,380
在构圆里面

219
00:09:04,380 --> 00:09:06,480
如果我们引入了一个包

220
00:09:06,480 --> 00:09:08,890
那么呢，就必须使用这个包

221
00:09:08,890 --> 00:09:13,940
比如说我在第13行去使用了 project prepare 这个包

222
00:09:13,940 --> 00:09:16,420
如果我把第13行注释掉的话

223
00:09:16,420 --> 00:09:18,757
再把第18行注释掉

224
00:09:18,757 --> 00:09:21,830
这样的话我在整个 A 点 go 里面

225
00:09:21,830 --> 00:09:25,150
就不再需要 prepare project 这个包了

226
00:09:25,150 --> 00:09:25,940
所以的话

227
00:09:25,940 --> 00:09:27,760
我们的 import 这边呢

228
00:09:27,760 --> 00:09:29,140
就会有一个红线飘红

229
00:09:29,140 --> 00:09:31,160
因为你引入了这个包

230
00:09:31,160 --> 00:09:31,640
但是呢

231
00:09:31,640 --> 00:09:34,800
你却没有在这个购物文件里面使用这个包

232
00:09:34,800 --> 00:09:36,230
就是一个语法错误

233
00:09:36,230 --> 00:09:38,940
你必须把第四行也给删掉

234
00:09:38,940 --> 00:09:40,487
这样才可以

235
00:09:40,487 --> 00:09:41,820
就如同什么呢

236
00:09:41,820 --> 00:09:44,202
就如同我们在 go 代码里面

237
00:09:44,202 --> 00:09:45,910
我们声明了一个变量

238
00:09:45,910 --> 00:09:48,350
但是呢，你却没有去使用它

239
00:09:48,350 --> 00:09:51,870
那这个时候呢，也是一个红线的报错

240
00:09:51,870 --> 00:09:53,050
所以这一点上

241
00:09:53,050 --> 00:09:55,250
go 语跟其他语言还是很不一样啊

242
00:09:55,250 --> 00:09:56,250
其他语言里面的话

243
00:09:56,250 --> 00:09:57,730
你说你引入了一个包

244
00:09:57,730 --> 00:09:59,110
但是没有使用它

245
00:09:59,110 --> 00:10:00,630
这个也不算什么错误

246
00:10:00,630 --> 00:10:02,300
包括你声明了一个变量

247
00:10:02,300 --> 00:10:04,400
没有去使用它也不算什么错误

248
00:10:04,400 --> 00:10:07,247
但是构员的要求会更加严格一些

249
00:10:07,247 --> 00:10:11,270
然后再说回我们的第六行和第八行，对吧

250
00:10:11,270 --> 00:10:12,100
这个下划线

251
00:10:12,100 --> 00:10:14,090
我们在整个 go 代码里面

252
00:10:14,090 --> 00:10:18,687
确实没有去直接使用 p proof 和这个 MYSQL 

253
00:10:18,687 --> 00:10:21,677
但是你为什么要引入这两个包呢

254
00:10:21,677 --> 00:10:26,770
就是因为他实际上想去通过 import 引入某个包

255
00:10:26,770 --> 00:10:30,982
来去自动的执行这个包里面的1 int 函数

256
00:10:30,982 --> 00:10:33,900
比如说我们可以按住 CTRL 键

257
00:10:33,900 --> 00:10:36,572
点到 p proof 这个包里面去

258
00:10:36,572 --> 00:10:38,410
那我们发现它这个地方

259
00:10:38,410 --> 00:10:40,610
确实有一个一的函数啊

260
00:10:40,610 --> 00:10:44,650
这个地方是想去定义一些 HTTP 的路由

261
00:10:44,650 --> 00:10:48,930
我们后面讲 HTP 编程会讲到这个语法

262
00:10:48,930 --> 00:10:53,070
再比方说这个 SQL driver mysql 

263
00:10:53,070 --> 00:10:55,632
我们点到这个包的源码里面去

264
00:10:55,632 --> 00:10:59,912
这个包下面有一个 driver 点 go 

265
00:10:59,912 --> 00:11:01,830
好在这个文件里面呢

266
00:11:01,830 --> 00:11:04,280
它确实也有一个一的函数

267
00:11:04,280 --> 00:11:07,572
打算去注册这个 MYSQL 驱动

268
00:11:07,572 --> 00:11:10,380
所以啊，当我们引入了一个包

269
00:11:10,380 --> 00:11:11,020
但是呢

270
00:11:11,020 --> 00:11:15,110
又没有在后续代码里面去使用这个包的话

271
00:11:15,110 --> 00:11:17,130
那么前面就要加个下划线

272
00:11:17,130 --> 00:11:19,492
防止它红线报错嘛

273
00:11:19,492 --> 00:11:21,700
而引入这个包的目的

274
00:11:21,700 --> 00:11:25,720
是想去自动执行这个包里面的 in 的函数
