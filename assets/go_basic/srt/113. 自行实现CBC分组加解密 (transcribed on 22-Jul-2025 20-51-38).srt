1
00:00:00,000 --> 00:00:07,000
通过扇结构 我们知道我们的拟要K通常是比较短的 而名文可能会很长

2
00:00:07,000 --> 00:00:12,000
所以一般我们需要对名文先进行分组 每组划分成固定的长度

3
00:00:12,000 --> 00:00:18,000
那最后一组可能会比较短 所以需要进行拍底填充 把填充够一个组的长度

4
00:00:18,000 --> 00:00:26,000
然后每个组可以分别的进行加密 组跟组之间实际上没有什么关联 这样的话有利于并行计算

5
00:00:26,000 --> 00:00:33,000
那为了加大这个破解难度的话 我们可以让组和组之间产生关联 比如右边这个图

6
00:00:33,000 --> 00:00:41,000
就是用所谓的CBC 分组链接模式 第一组还是跟网上一样 正常的执行加密

7
00:00:41,000 --> 00:00:48,000
那么这个加密的结果 它会和第二组的名文先进行一个抑获运算

8
00:00:48,000 --> 00:00:55,000
这个符号表示抑获运算 抑获完之后再去按照常规的加密算法来生成第二组的密文

9
00:00:55,000 --> 00:01:01,000
然后第二组的密文再和第三组的明文进行一抑获运算 然后再进行加密研算

10
00:01:01,000 --> 00:01:08,000
由于后一组总是依赖前组的加密结构 所以这个算法没办法并行之间 只能是串行之间

11
00:01:08,000 --> 00:01:13,000
但是可以加大破简单度 我们就者善解的代码

12
00:01:13,000 --> 00:01:17,000
来改造一下使得它支持这种CBC加密模式

13
00:01:17,000 --> 00:01:23,000
这边我先把这个承认方法block size改造一下 把它改成是承认变量

14
00:01:23,000 --> 00:01:26,000
放到结构体里面去 整形

15
00:01:26,000 --> 00:01:30,000
然后它是一个固定值 就是8 直接写死

16
00:01:30,000 --> 00:01:32,000
这个承认方法就可以不要了

17
00:01:32,000 --> 00:01:37,000
然后我们需要把所有的这种承认方法加小块的一把 把小块去掉

18
00:01:37,000 --> 00:01:47,000
那我们直接Ctrl F 把替换 选中block size加小块 然后按住Ctrl F 展开这个 然后下面是替换

19
00:01:47,000 --> 00:01:53,000
把带小块的 我们把它替换为不带小块的 点这个全部替换

20
00:01:53,000 --> 00:01:59,000
好 就全部把小块的给删掉了 刚才PPT里面演示的分组模式叫做CBC

21
00:01:59,000 --> 00:02:05,000
那实际上分组模式除了CBC之外 还有其他几种模式 所以这边我们不如直接搞成一个枚举吧

22
00:02:05,000 --> 00:02:13,000
每一局在购源里面实际上就是一个长量 长量组 第一个是IOTA

23
00:02:13,000 --> 00:02:19,000
然后CBC是一种 加来可能还有其他种 你就直接往这个地方去加就可以了

24
00:02:19,000 --> 00:02:24,000
所以第一个实际上是0 0我不用 CBC实际上是1

25
00:02:24,000 --> 00:02:28,000
给这个0取个名称 叫做那

26
00:02:28,000 --> 00:02:33,000
然后我们的结构体里面也包含一个成员变量 叫做分组模式

27
00:02:33,000 --> 00:02:35,000
block mode

28
00:02:35,000 --> 00:02:39,000
它是个整形 实际上就取自于这个名称里面的某一个值嘛

29
00:02:39,000 --> 00:02:43,000
那么需要在构的函数里面呢 把这个分组模式给传进来

30
00:02:43,000 --> 00:02:47,000
block mode 付给block mode

31
00:02:47,000 --> 00:02:50,000
然后来看这个加密函数如何改造

32
00:02:50,000 --> 00:02:53,000
第一步先对密文执行 padding 这个是必须的

33
00:02:53,000 --> 00:02:59,000
第二步呢 先初时化这个密文 申请内存空间 这个也可以

34
00:02:59,000 --> 00:03:03,000
然后就开始每组是吧 分组执行加密了

35
00:03:03,000 --> 00:03:09,000
那么在每组加密的时候呢 我们的CBC它依赖上一组的加密结果

36
00:03:09,000 --> 00:03:13,000
加密结果 所以我们需要搞一个变量 来存储上一组的加密结果

37
00:03:13,000 --> 00:03:15,000
叫做proveCypher

38
00:03:15,000 --> 00:03:21,000
它是一组嘛 所以这个长度的话应该就是8

39
00:03:21,000 --> 00:03:23,000
1.block size

40
00:03:23,000 --> 00:03:27,000
这个是的话 里面它全部是0

41
00:03:27,000 --> 00:03:31,000
到复合循环里面来 我们拿到begin和end

42
00:03:31,000 --> 00:03:35,000
是吧 拿到这一组的下标的其实位置

43
00:03:35,000 --> 00:03:38,000
然后对这一组呢 进行我们的加密算

44
00:03:38,000 --> 00:03:42,000
我们把这个加密算把它弄到外面去 搞成一个单独的函数

45
00:03:42,000 --> 00:03:48,000
这边来一个encrypt block 指加密某一组嘛

46
00:03:48,000 --> 00:03:52,000
我们专注在这一组 那这一组的话 明文我们用plane

47
00:03:52,000 --> 00:03:57,000
密文呢 用Cypher来表示 它们长度都是八个字节

48
00:03:57,000 --> 00:03:58,000
byte切片

49
00:03:58,000 --> 00:04:02,000
就实际上我们的输入是密文 这个Cypher实际上是输出嘛

50
00:04:02,000 --> 00:04:05,000
只不过我把它放到入仓里面来了

51
00:04:05,000 --> 00:04:09,000
为什么这么搞呢 因为我们的这个密文实际上

52
00:04:09,000 --> 00:04:13,000
我在第三上里面已经申请好那一次空间了

53
00:04:13,000 --> 00:04:17,000
如果我这个函数再返回一个Cypher的话

54
00:04:17,000 --> 00:04:21,000
那么就意味着我要在函数里面再去申请一块

55
00:04:21,000 --> 00:04:24,000
长度为八个字节的Cypher

56
00:04:24,000 --> 00:04:27,000
需要开辟额外的那次空间吧

57
00:04:27,000 --> 00:04:30,000
但是我想把它作为入仓直接传进来

58
00:04:30,000 --> 00:04:33,000
这样的话 我可以就地的去修改这个Cypher

59
00:04:33,000 --> 00:04:38,000
现在是我把第30行这个Cypher的其中某一段给修改了

60
00:04:38,000 --> 00:04:40,000
我把这个内存号循环拷备过来

61
00:04:40,000 --> 00:04:45,000
这样的话 这是从零开始的小于这个长度吧

62
00:04:45,000 --> 00:04:50,000
那这边是plane 这边就不需要减了 这边是J

63
00:04:50,000 --> 00:04:51,000
好 这样就可以了

64
00:04:51,000 --> 00:04:56,000
什么意思呢 我这个plane和Cypher是专注于某一组

65
00:04:56,000 --> 00:04:59,000
所以它们长度都是八个字节

66
00:04:59,000 --> 00:05:02,000
最开始的时候 这个Cypher里面是全部为0

67
00:05:02,000 --> 00:05:07,000
因为我们看 第32行 这样一make的话是将里面全部为0

68
00:05:07,000 --> 00:05:11,000
然后通过我设定好的加密算法

69
00:05:11,000 --> 00:05:14,000
给Cypher的每一个元素分别复制就可以了

70
00:05:14,000 --> 00:05:17,000
所以这样的话 我们这个for循环就可以删掉了

71
00:05:17,000 --> 00:05:21,000
直接调用上面我们写好的函数

72
00:05:21,000 --> 00:05:23,000
只加密当前这一组

73
00:05:23,000 --> 00:05:25,000
这一组的话 名文是谁呢

74
00:05:25,000 --> 00:05:29,000
名文的话实际上是plane padding

75
00:05:29,000 --> 00:05:33,000
当前这一组就是从begin到end

76
00:05:33,000 --> 00:05:40,000
同理我们的Cypher也是只专注于当前这一组

77
00:05:40,000 --> 00:05:42,000
从begin到end

78
00:05:42,000 --> 00:05:45,000
这是每一组独立的执行加密

79
00:05:45,000 --> 00:05:47,000
但如果是使用CBC呢

80
00:05:47,000 --> 00:05:50,000
前一组结果对后一组是有影响的

81
00:05:50,000 --> 00:05:54,000
那本质上是要对本次加密的输入

82
00:05:54,000 --> 00:05:56,000
这个play对弄手脚

83
00:05:56,000 --> 00:05:58,000
所以我们再搞一个函数

84
00:05:58,000 --> 00:06:00,000
我们再搞一个函数

85
00:06:00,000 --> 00:06:03,000
叫做confuse block

86
00:06:03,000 --> 00:06:04,000
混淆嘛

87
00:06:04,000 --> 00:06:05,000
plane

88
00:06:05,000 --> 00:06:07,000
对原始的这个密文进行混淆

89
00:06:07,000 --> 00:06:10,000
那是我们依赖上一组的密文

90
00:06:10,000 --> 00:06:14,000
所以是prove cipher

91
00:06:14,000 --> 00:06:18,000
它们都是长度为8的这样一个byte切片

92
00:06:18,000 --> 00:06:21,000
我要混出的目标是这个plane嘛

93
00:06:21,000 --> 00:06:23,000
所以要修改这个plane

94
00:06:23,000 --> 00:06:25,000
那其实也是这样一个后循环了

95
00:06:25,000 --> 00:06:31,000
plane等于plane和上一组的这个加密结果

96
00:06:31,000 --> 00:06:32,000
执行异货因算

97
00:06:32,000 --> 00:06:34,000
比如说这个地方

98
00:06:34,000 --> 00:06:36,000
它是第二组的名文

99
00:06:36,000 --> 00:06:37,000
那这个名文呢

100
00:06:37,000 --> 00:06:39,000
并不会直接输给加密算法

101
00:06:39,000 --> 00:06:41,000
而是这个名文

102
00:06:41,000 --> 00:06:43,000
它先跟上组这个密文

103
00:06:43,000 --> 00:06:44,000
先执行一个异货因算

104
00:06:44,000 --> 00:06:46,000
再输给加密算法

105
00:06:46,000 --> 00:06:48,000
那这一步呢

106
00:06:48,000 --> 00:06:51,000
我们就放到了这个confuse block

107
00:06:51,000 --> 00:06:52,000
函数里面来进行

108
00:06:52,000 --> 00:06:54,000
这是说针对CBC

109
00:06:54,000 --> 00:06:56,000
但如果是none的话

110
00:06:56,000 --> 00:07:00,000
其实我们不需要去动这个plane

111
00:07:00,000 --> 00:07:02,000
保持不动就可以了

112
00:07:02,000 --> 00:07:04,000
所以这边我来一个分支

113
00:07:04,000 --> 00:07:05,000
switch

114
00:07:05,000 --> 00:07:08,000
我自己的block mode

115
00:07:08,000 --> 00:07:12,000
如果是CBC的话

116
00:07:12,000 --> 00:07:16,000
那么才是按照这种方式来进行混淆

117
00:07:16,000 --> 00:07:20,000
对这个plane进行一个混淆

118
00:07:20,000 --> 00:07:22,000
如果是其他模式的话

119
00:07:22,000 --> 00:07:24,000
那么我就什么也不做

120
00:07:24,000 --> 00:07:26,000
保持这个pin保持不动

121
00:07:26,000 --> 00:07:30,000
这个pin指的是某一组的名文

122
00:07:30,000 --> 00:07:32,000
所以对应到我们这边来

123
00:07:32,000 --> 00:07:36,000
在真正执行这个加密之前呢

124
00:07:36,000 --> 00:07:38,000
应该先执行一个混淆

125
00:07:38,000 --> 00:07:40,000
en.confuse

126
00:07:40,000 --> 00:07:44,000
那么这个是原始的

127
00:07:44,000 --> 00:07:46,000
当前这一组的名文

128
00:07:46,000 --> 00:07:50,000
然后上一组的加密结果是preview cipher

129
00:07:50,000 --> 00:07:52,000
所以通过第47行

130
00:07:52,000 --> 00:07:56,000
我就改变了这个plane padding

131
00:07:56,000 --> 00:07:58,000
然后把改变之后的plane padding

132
00:07:58,000 --> 00:08:00,000
再扔给我的加密函数

133
00:08:00,000 --> 00:08:02,000
加密结果呢

134
00:08:02,000 --> 00:08:06,000
这个会放到这个Cipher的当前这一组里面去

135
00:08:06,000 --> 00:08:08,000
然后Cipher的当前这一组

136
00:08:08,000 --> 00:08:11,000
就成为了下一次的preview cipher

137
00:08:11,000 --> 00:08:12,000
对吧

138
00:08:12,000 --> 00:08:16,000
所以我要赶紧的去重置这个preview cipher copy

139
00:08:16,000 --> 00:08:20,000
我直接把当前这一组的加密结果呢

140
00:08:20,000 --> 00:08:23,000
把它拷贝到这个preview cipher里面去

141
00:08:23,000 --> 00:08:25,000
那么我们关注一下

142
00:08:25,000 --> 00:08:26,000
在最开始的时候

143
00:08:26,000 --> 00:08:27,000
i等于0的时候

144
00:08:27,000 --> 00:08:29,000
对第0组而言

145
00:08:29,000 --> 00:08:31,000
就这里面对第0组而言

146
00:08:31,000 --> 00:08:34,000
它实际上并没有跟任何东西先执行混淆

147
00:08:34,000 --> 00:08:35,000
对吧

148
00:08:35,000 --> 00:08:36,000
因为它不存在上一组

149
00:08:36,000 --> 00:08:38,000
那我们这种写法有成问题

150
00:08:38,000 --> 00:08:40,000
就是当i等于0时

151
00:08:40,000 --> 00:08:43,000
你直接这样进行一个confuse

152
00:08:43,000 --> 00:08:45,000
会不会跟我们的算法设计

153
00:08:45,000 --> 00:08:46,000
相违背

154
00:08:46,000 --> 00:08:48,000
其实不会问题

155
00:08:48,000 --> 00:08:49,000
为什么呢

156
00:08:49,000 --> 00:08:51,000
因为当i等于0的时候

157
00:08:51,000 --> 00:08:54,000
我们这个preview cipher里面

158
00:08:54,000 --> 00:08:55,000
它还是全部是0

159
00:08:55,000 --> 00:08:56,000
对吧

160
00:08:56,000 --> 00:08:57,000
全部是0

161
00:08:57,000 --> 00:08:59,000
而我们这个confuse操作本身

162
00:08:59,000 --> 00:09:02,000
就是执行一个一货运算

163
00:09:02,000 --> 00:09:04,000
善极后我们讲过

164
00:09:04,000 --> 00:09:05,000
任何数值

165
00:09:05,000 --> 00:09:08,000
它跟一个全0的数

166
00:09:08,000 --> 00:09:09,000
执行一货运算

167
00:09:09,000 --> 00:09:11,000
还是它自身

168
00:09:11,000 --> 00:09:13,000
这是我们这边的第一组图

169
00:09:13,000 --> 00:09:14,000
是吧

170
00:09:14,000 --> 00:09:17,000
任何如此跟全0一货还是自身

171
00:09:17,000 --> 00:09:20,000
所以的话当i等临时对第0组而言

172
00:09:20,000 --> 00:09:25,000
这个confuse做不做实际上都不会改变我们的plane padding

173
00:09:25,000 --> 00:09:27,000
不改变当前这一组的输入

174
00:09:27,000 --> 00:09:31,000
然后来看一下这个解密函数该如何改造

175
00:09:31,000 --> 00:09:34,000
我先把内层后循环先搞到外面去

176
00:09:34,000 --> 00:09:37,000
这边搞一个decrypt block

177
00:09:37,000 --> 00:09:38,000
我只关注某一组

178
00:09:38,000 --> 00:09:40,000
对这组执行解密

179
00:09:40,000 --> 00:09:42,000
由于是使用的易义混算

180
00:09:42,000 --> 00:09:44,000
所以解密跟加密其实很像

181
00:09:44,000 --> 00:09:47,000
加密是要给Cypher复制

182
00:09:47,000 --> 00:09:50,000
而解密是要去修改Plan

183
00:09:50,000 --> 00:09:51,000
给Plan复制

184
00:09:51,000 --> 00:09:54,000
那么就是我们的plane

185
00:09:54,000 --> 00:09:57,000
它等于等于密文Cypher

186
00:09:57,000 --> 00:10:01,000
密文和k执行易混算得到明文

187
00:10:01,000 --> 00:10:03,000
这是我们的核心解密算法

188
00:10:03,000 --> 00:10:06,000
然后把这个解密算法呢

189
00:10:06,000 --> 00:10:11,000
直接考P到下面来替换掉这个内层后循环

190
00:10:11,000 --> 00:10:15,000
明文当前这一组和密文当前这一组

191
00:10:15,000 --> 00:10:17,000
这是明文当前这一组

192
00:10:17,000 --> 00:10:19,000
这是密文当前这一组

193
00:10:19,000 --> 00:10:22,000
所以这个后循环实际上是在分组

194
00:10:22,000 --> 00:10:25,000
每组每组的单独执行解密嘛

195
00:10:25,000 --> 00:10:28,000
如果每组之间是互相没有关联的

196
00:10:28,000 --> 00:10:29,000
独立的话当然可以这样搞

197
00:10:29,000 --> 00:10:32,000
但如果是CBC的话就不能这样解密了

198
00:10:32,000 --> 00:10:34,000
我们来看这个CBC模式

199
00:10:34,000 --> 00:10:37,000
由于上一组跟后一组是有关联的

200
00:10:37,000 --> 00:10:39,000
所以的话我们解密呢

201
00:10:39,000 --> 00:10:41,000
得从最后一组开启

202
00:10:41,000 --> 00:10:42,000
从后往前进解密

203
00:10:42,000 --> 00:10:44,000
拿到最后一组的这个密文

204
00:10:44,000 --> 00:10:47,000
我们施加解密运算

205
00:10:47,000 --> 00:10:49,000
那么这个解密的结果

206
00:10:49,000 --> 00:10:52,000
实际上还不是最后一组的明文

207
00:10:52,000 --> 00:10:54,000
因为当初加密的时候

208
00:10:54,000 --> 00:10:56,000
这个加密的输入呢

209
00:10:56,000 --> 00:10:58,000
是最后一组的明文和上一组的密文

210
00:10:58,000 --> 00:11:00,000
执行易互运算得来的

211
00:11:00,000 --> 00:11:03,000
所以现在我们解密出来的这个结果

212
00:11:03,000 --> 00:11:06,000
他需要再跟上一组的密文

213
00:11:06,000 --> 00:11:08,000
执行一次易互运算

214
00:11:08,000 --> 00:11:13,000
才能够还原最初真正的这组密文

215
00:11:13,000 --> 00:11:14,000
那又是解密嘛

216
00:11:14,000 --> 00:11:17,000
所以每一组的密文我们都知道的

217
00:11:17,000 --> 00:11:19,000
这边把代码改造一下

218
00:11:19,000 --> 00:11:22,000
我们不能从前往后去解密了

219
00:11:22,000 --> 00:11:24,000
得从后往前来解密

220
00:11:24,000 --> 00:11:25,000
从后往前的话

221
00:11:25,000 --> 00:11:28,000
每一组的begin和and该如何计算呢

222
00:11:28,000 --> 00:11:30,000
我们这样搞

223
00:11:30,000 --> 00:11:32,000
先算出总的组数

224
00:11:32,000 --> 00:11:36,000
就是密文的长度除以8

225
00:11:36,000 --> 00:11:39,000
除以1n.block size

226
00:11:39,000 --> 00:11:40,000
总共有这么多组

227
00:11:40,000 --> 00:11:42,000
然后这个i呢

228
00:11:42,000 --> 00:11:44,000
就表示组的编号

229
00:11:44,000 --> 00:11:46,000
它从最后一组开始

230
00:11:46,000 --> 00:11:49,000
从blocknum-1

231
00:11:49,000 --> 00:11:51,000
这是最后一组的编号

232
00:11:51,000 --> 00:11:52,000
只要这个i呢

233
00:11:52,000 --> 00:11:53,000
还大于等于0

234
00:11:53,000 --> 00:11:56,000
因为0也是合理下标

235
00:11:56,000 --> 00:11:57,000
比0组

236
00:11:57,000 --> 00:11:58,000
大于等于0就可以

237
00:11:58,000 --> 00:12:00,000
i呢--

238
00:12:00,000 --> 00:12:01,000
好

239
00:12:01,000 --> 00:12:03,000
因为i是这个组的下标嘛

240
00:12:03,000 --> 00:12:06,000
所以反映到这个Cypher的下标

241
00:12:06,000 --> 00:12:07,000
这个begin呢

242
00:12:07,000 --> 00:12:11,000
就应该是i乘以8

243
00:12:11,000 --> 00:12:14,000
Inc.block size

244
00:12:14,000 --> 00:12:16,000
比如说i等于0

245
00:12:16,000 --> 00:12:17,000
D0组

246
00:12:17,000 --> 00:12:18,000
对D0组而言

247
00:12:18,000 --> 00:12:20,000
它的这个其实位置

248
00:12:20,000 --> 00:12:21,000
就是0乘以8

249
00:12:21,000 --> 00:12:22,000
是0

250
00:12:22,000 --> 00:12:23,000
那结束位置呢

251
00:12:23,000 --> 00:12:26,000
结束位置实际上应该是

252
00:12:26,000 --> 00:12:29,000
这个begin再加上8

253
00:12:29,000 --> 00:12:31,000
我们想一下

254
00:12:31,000 --> 00:12:33,000
现在i等于block size

255
00:12:33,000 --> 00:12:34,000
是最后一组

256
00:12:34,000 --> 00:12:35,000
最后一组的话

257
00:12:35,000 --> 00:12:38,000
我们先对最后一组执行解密

258
00:12:38,000 --> 00:12:41,000
得到这个解密的结果

259
00:12:41,000 --> 00:12:43,000
那解密的结果呢

260
00:12:43,000 --> 00:12:46,000
这个print还不是真正的名文

261
00:12:46,000 --> 00:12:48,000
还需要跟上一组的密文

262
00:12:48,000 --> 00:12:49,000
再执行一次

263
00:12:49,000 --> 00:12:51,000
一或认算

264
00:12:51,000 --> 00:12:53,000
所以上一组的密文是谁呢

265
00:12:53,000 --> 00:12:56,000
prev cipher

266
00:12:56,000 --> 00:12:59,000
等于这个cipher begin and end

267
00:12:59,000 --> 00:13:01,000
是当前这组的密文

268
00:13:01,000 --> 00:13:05,000
上一组的密文自然是上标下标

269
00:13:05,000 --> 00:13:06,000
分别都减去8

270
00:13:06,000 --> 00:13:07,000
好

271
00:13:07,000 --> 00:13:09,000
这个是上一组的密文

272
00:13:09,000 --> 00:13:11,000
但是我们要警惕一下

273
00:13:11,000 --> 00:13:14,000
如果说这个i已经等于0了

274
00:13:14,000 --> 00:13:15,000
它不存在上一组

275
00:13:15,000 --> 00:13:17,000
你这样的话begin减去8

276
00:13:17,000 --> 00:13:18,000
会减去复出来

277
00:13:18,000 --> 00:13:19,000
变成复吧

278
00:13:19,000 --> 00:13:20,000
所以下标非吧

279
00:13:20,000 --> 00:13:22,000
那我们需要做一个特殊判断

280
00:13:22,000 --> 00:13:25,000
如果说i等于0的话

281
00:13:25,000 --> 00:13:29,000
那其实这个prev cipher应该是不存在

282
00:13:29,000 --> 00:13:31,000
或者说是全0

283
00:13:31,000 --> 00:13:33,000
所以我们把这个变量放在上面

284
00:13:33,000 --> 00:13:34,000
声明一下

285
00:13:34,000 --> 00:13:35,000
iOS的话

286
00:13:35,000 --> 00:13:38,000
才能够这样来执行运算

287
00:13:38,000 --> 00:13:39,000
第0组的话

288
00:13:39,000 --> 00:13:41,000
这个prev cipher呢

289
00:13:41,000 --> 00:13:44,000
它就等于make

290
00:13:44,000 --> 00:13:46,000
长度为8

291
00:13:46,000 --> 00:13:48,000
en.block size

292
00:13:48,000 --> 00:13:52,000
这个是全0

293
00:13:52,000 --> 00:13:55,000
然后我们需要让这个plane padding呢

294
00:13:55,000 --> 00:13:58,000
跟这个prev cipher再执行一次

295
00:13:58,000 --> 00:13:59,000
易货运算

296
00:13:59,000 --> 00:14:00,000
易货的结果呢

297
00:14:00,000 --> 00:14:02,000
再付给plane padding

298
00:14:02,000 --> 00:14:04,000
这样才是真正的密文

299
00:14:04,000 --> 00:14:08,000
所以它实际上是为了跟当初的confuse block

300
00:14:08,000 --> 00:14:10,000
进行一个相反的运算

301
00:14:10,000 --> 00:14:11,000
逆运算

302
00:14:11,000 --> 00:14:14,000
那我们写一个deconfuse

303
00:14:14,000 --> 00:14:15,000
根据刚才描述

304
00:14:15,000 --> 00:14:19,000
我们是希望这个plane和上运组密文

305
00:14:19,000 --> 00:14:20,000
执行易货运算

306
00:14:20,000 --> 00:14:21,000
再付给plane

307
00:14:21,000 --> 00:14:24,000
那实际上跟这个结果

308
00:14:24,000 --> 00:14:26,000
是完全一样的

309
00:14:26,000 --> 00:14:30,000
所以我们实际上这个confuse和deconfuse

310
00:14:30,000 --> 00:14:32,000
代码是一模一样的

311
00:14:32,000 --> 00:14:35,000
这个主要归功于我们使用的是易货运算

312
00:14:35,000 --> 00:14:37,000
那如果是其他运算的话

313
00:14:37,000 --> 00:14:38,000
就不一定是一模一样

314
00:14:38,000 --> 00:14:40,000
所以的话我们还是分成两个函数

315
00:14:40,000 --> 00:14:42,000
这样可图性会更好一些

316
00:14:42,000 --> 00:14:47,000
那所以这边我们再来一次en.deconfuse

317
00:14:47,000 --> 00:14:49,000
这个是明文

318
00:14:49,000 --> 00:14:52,000
然后上一组的密文

319
00:14:52,000 --> 00:14:53,000
好

320
00:14:53,000 --> 00:14:55,000
我们这个都是就地的事情

321
00:14:55,000 --> 00:14:57,000
只有我们不需要开辟额外的内存空间

322
00:14:57,000 --> 00:14:59,000
你把这个plane传过来

323
00:14:59,000 --> 00:15:00,000
在函数里面呢

324
00:15:00,000 --> 00:15:02,000
我就会去修改这个plane

325
00:15:02,000 --> 00:15:03,000
好

326
00:15:03,000 --> 00:15:06,000
这样的话经过多轮的for循环之后

327
00:15:06,000 --> 00:15:08,000
针对每一组我倒着

328
00:15:08,000 --> 00:15:11,000
从后往前把每一组分别的执行解密

329
00:15:11,000 --> 00:15:12,000
最后一步啊

330
00:15:12,000 --> 00:15:14,000
那么这个plane padding

331
00:15:14,000 --> 00:15:15,000
它虽然是明文

332
00:15:15,000 --> 00:15:16,000
但是呢

333
00:15:16,000 --> 00:15:18,000
因为当初在最开始加密的时候

334
00:15:18,000 --> 00:15:20,000
末尾追加了

335
00:15:20,000 --> 00:15:22,000
一组元素填充了嘛

336
00:15:22,000 --> 00:15:23,000
所以到最后一步呢

337
00:15:23,000 --> 00:15:24,000
还需要反填充

338
00:15:25,000 --> 00:15:26,000
安排定了

339
00:15:27,000 --> 00:15:28,000
我们来测试一下

340
00:15:28,000 --> 00:15:29,000
这样的话

341
00:15:29,000 --> 00:15:30,000
这些构则函数呢

342
00:15:30,000 --> 00:15:33,000
需要多传一个分组模式

343
00:15:33,000 --> 00:15:36,000
那我们先传一个num

344
00:15:36,000 --> 00:15:39,000
然后我们再把这个代码再拷贝一份

345
00:15:39,000 --> 00:15:43,000
我们再测试一下这个CBC

346
00:15:43,000 --> 00:15:46,000
CBC冒号全部去掉

347
00:15:46,000 --> 00:15:48,000
把这个单测命令呢

348
00:15:48,000 --> 00:15:49,000
再执行一次

349
00:15:49,000 --> 00:15:50,000
好

350
00:15:50,000 --> 00:15:51,000
这次是pass啊

351
00:15:51,000 --> 00:15:52,000
完全通过

352
00:15:52,000 --> 00:15:53,000
我们再看过

