1
00:00:00,520 --> 00:00:01,640
上一节课我们讲

2
00:00:01,640 --> 00:00:04,040
如果对方传过来的请求数据

3
00:00:04,040 --> 00:00:05,840
是一个 JSON 字符串的话

4
00:00:05,840 --> 00:00:07,370
我们在服务端这边呢

5
00:00:07,370 --> 00:00:11,050
还需要先读到所有的请求提

6
00:00:11,050 --> 00:00:14,250
然后呢，自行做这个 JSON 的反序列化

7
00:00:14,250 --> 00:00:16,900
但实际上有一种更简单的方法

8
00:00:16,900 --> 00:00:19,417
就是直接进行参数绑定

9
00:00:19,417 --> 00:00:22,190
不管这个请求体是什么类型的数据

10
00:00:22,190 --> 00:00:25,750
我们都可以把它直接移步转成一个结构体

11
00:00:25,750 --> 00:00:27,247
来看一下怎么做

12
00:00:27,247 --> 00:00:30,760
首先啊，我们需要定义好这样一个结构体

13
00:00:30,760 --> 00:00:32,439
它里面包含若干属性啊

14
00:00:32,439 --> 00:00:36,082
比方说姓名、地址和 key words 

15
00:00:36,082 --> 00:00:38,370
关键是后面这个 tag 

16
00:00:38,370 --> 00:00:40,730
我们来看一下这个 tag 里面啊

17
00:00:40,730 --> 00:00:42,290
它有多个 tag 

18
00:00:42,290 --> 00:00:45,887
因为每个 tag 中间是用空格来进行分割嘛

19
00:00:45,887 --> 00:00:51,000
form JSON u r i XML yao bending 是吧

20
00:00:51,000 --> 00:00:51,990
好几个 tag 

21
00:00:51,990 --> 00:00:54,530
那其中最后这个板顶

22
00:00:54,530 --> 00:00:57,570
这个是跟参数绑定校验相关的

23
00:00:57,570 --> 00:01:01,380
它表示是说这个参数是必须传的

24
00:01:01,380 --> 00:01:03,880
如果没有这个 binding required 

25
00:01:03,880 --> 00:01:06,010
就表示这个参数可有可无

26
00:01:06,010 --> 00:01:07,280
关于这个参数校验

27
00:01:07,280 --> 00:01:08,840
我们后面再讲这几个

28
00:01:08,840 --> 00:01:11,110
我们先关注前面这几个 tag 

29
00:01:11,110 --> 00:01:11,790
这个 form 

30
00:01:11,790 --> 00:01:16,410
就是说将来如果你的请求数据是通过 post form 

31
00:01:16,410 --> 00:01:18,560
通过提交表单传过来的

32
00:01:18,560 --> 00:01:22,690
或者呢是通过 get 请求在 UR 里面

33
00:01:22,690 --> 00:01:26,050
通过那个问号后面加各种 K 等于 value 

34
00:01:26,050 --> 00:01:27,950
这种形式传过来的话

35
00:01:27,950 --> 00:01:31,890
那么对应的那个 K 就应该叫 username 

36
00:01:31,890 --> 00:01:32,890
那这样的话

37
00:01:32,890 --> 00:01:37,010
它的 value 呢才会付给这个结构体的 name 成员

38
00:01:37,010 --> 00:01:38,322
这个杰森

39
00:01:38,322 --> 00:01:42,590
如果将来你传的请求体是一个 JSON 数据的话

40
00:01:42,590 --> 00:01:46,050
那么在 JSON 里面它就应该叫 name 

41
00:01:46,050 --> 00:01:50,967
这样的话呢，就能够转成这个学生结构体的 name 

42
00:01:50,967 --> 00:01:55,180
这个 URI 它对应的是那个 rascal 风格请求

43
00:01:55,180 --> 00:01:57,210
你直接把那个参数

44
00:01:57,210 --> 00:01:59,820
作为请求路径的一部分嘛

45
00:01:59,820 --> 00:02:01,140
那么在这种情况下

46
00:02:01,140 --> 00:02:04,757
对应的参数名称得取为 user 

47
00:02:04,757 --> 00:02:08,300
你的请求数据也可以是 XML 格式

48
00:02:08,300 --> 00:02:09,979
也可以是 YAO 格式

49
00:02:09,979 --> 00:02:13,800
那么每一种格式它都指定了你的 key 的名称

50
00:02:13,800 --> 00:02:15,100
应该叫什么吧

51
00:02:15,100 --> 00:02:16,385
各不一样嘛

52
00:02:16,385 --> 00:02:20,610
同理啊，对这个 address 也是叫法可以都不一样

53
00:02:20,610 --> 00:02:22,300
最后这边有一个 key words 

54
00:02:22,300 --> 00:02:24,465
key words 呢，它是一个切片

55
00:02:24,465 --> 00:02:26,980
那么在购物代码里面是切片的话

56
00:02:26,980 --> 00:02:28,680
它对应到客户端啊

57
00:02:28,680 --> 00:02:30,420
对应到前端是什么呢

58
00:02:30,420 --> 00:02:34,405
实际上就是那个浏览器里面的复选框

59
00:02:34,405 --> 00:02:36,360
有单选和多选嘛

60
00:02:36,360 --> 00:02:37,100
多选的话

61
00:02:37,100 --> 00:02:41,715
它其实对应到 HTML 代码里面就叫做 check box 

62
00:02:41,715 --> 00:02:43,760
那么这个 check box 它是一个组件

63
00:02:43,760 --> 00:02:47,005
只不过说一个组件里面有多个取值

64
00:02:47,005 --> 00:02:49,990
那么后端呢，就是可以对应成切片

65
00:02:49,990 --> 00:02:51,180
这是第一步啊

66
00:02:51,180 --> 00:02:52,340
定义好这个结构体

67
00:02:52,340 --> 00:02:54,635
关键是把这些 tag 呢给写好

68
00:02:54,635 --> 00:02:56,080
然后我们来看一下啊

69
00:02:56,080 --> 00:03:00,590
比如说你通过 post 来请求这个路径的话

70
00:03:00,590 --> 00:03:01,840
看客户端是吧

71
00:03:01,840 --> 00:03:03,732
我去请求这个路径

72
00:03:03,732 --> 00:03:07,270
是把这样一个结构体呢先传进来

73
00:03:07,270 --> 00:03:09,970
我们点进去看看是怎么请求的

74
00:03:09,970 --> 00:03:12,990
我们是发起了一个 post form 请求啊

75
00:03:12,990 --> 00:03:17,070
通过 URL 点 values 来指定表单数据

76
00:03:17,070 --> 00:03:19,460
这边叫做 username 

77
00:03:19,460 --> 00:03:21,520
哎， username 这个名称啊

78
00:03:21,520 --> 00:03:24,240
实际上跟我们这边这个 username 

79
00:03:24,240 --> 00:03:25,830
刚好是能够对应上的

80
00:03:25,830 --> 00:03:27,832
这边是叫 ADDR 

81
00:03:27,832 --> 00:03:31,190
那跟我们服务端这边的这个 form 

82
00:03:31,190 --> 00:03:32,970
ATD 2是能够对应上的

83
00:03:32,970 --> 00:03:33,890
那也就是说

84
00:03:33,890 --> 00:03:35,920
我把这样一个表单数据

85
00:03:35,920 --> 00:03:37,350
提交给服务端

86
00:03:37,350 --> 00:03:39,280
那么服务端怎么获取呢

87
00:03:39,280 --> 00:03:42,632
服务端只需要调用 should 的 B 

88
00:03:42,632 --> 00:03:47,130
就能够把请求参数直接赋给这个结构体

89
00:03:47,130 --> 00:03:48,840
再比方说下面这个

90
00:03:48,840 --> 00:03:53,360
如果你通过 post 来请求这个 JSON 路径的话

91
00:03:53,360 --> 00:03:54,820
看一下我们的客户端

92
00:03:54,820 --> 00:03:57,520
客户端啊，请求这个 JSON 路径

93
00:03:57,520 --> 00:03:58,922
看一下怎么请求

94
00:03:58,922 --> 00:04:00,730
他是把这样一个结构体呢

95
00:04:00,730 --> 00:04:02,950
先转成 JSON 字符串是吧

96
00:04:02,950 --> 00:04:05,090
把这个 JSON 字符串呢

97
00:04:05,090 --> 00:04:07,322
作为请求体发给了服务端

98
00:04:07,322 --> 00:04:08,802
那么服务端呢

99
00:04:08,802 --> 00:04:11,760
他直接调用竖的半的 JSON 

100
00:04:11,760 --> 00:04:15,370
就可以把请求体直接付给这个结构体

101
00:04:15,370 --> 00:04:17,040
我们之前是怎么搞的

102
00:04:17,040 --> 00:04:20,769
我们之前是先把请求体全部读出来

103
00:04:20,769 --> 00:04:23,850
然后再显示调用 JASON 鞍马手

104
00:04:23,850 --> 00:04:25,440
才拿到一个结构体

105
00:04:25,440 --> 00:04:28,200
现在呢，把这两行合成了一行

106
00:04:28,200 --> 00:04:32,010
直接调用 should 的 band 的 JSON 就搞定了

107
00:04:32,010 --> 00:04:33,870
同理，你可以是 JSON 

108
00:04:33,870 --> 00:04:35,910
那自然也可以是其他格式

109
00:04:35,910 --> 00:04:38,600
比如输入的 band 的 XML 

110
00:04:38,600 --> 00:04:40,550
对应的路径呢是 XML 

111
00:04:40,550 --> 00:04:43,250
这边 should 的 band 的 YAO 

112
00:04:43,250 --> 00:04:45,070
对应的路径呢是 yo 

113
00:04:45,070 --> 00:04:46,627
我们看一下客户端

114
00:04:46,627 --> 00:04:47,360
客户端

115
00:04:47,360 --> 00:04:51,120
我要去准备这样一个 XML 格式的数据的话

116
00:04:51,120 --> 00:04:55,810
直接调用购员标准库给我们提供好的 XML 

117
00:04:55,810 --> 00:04:58,080
这个包它又一个码数啊

118
00:04:58,080 --> 00:04:59,920
你把一个结构体传过来

119
00:04:59,920 --> 00:05:01,420
他就可以把这个结构体呢

120
00:05:01,420 --> 00:05:04,130
转成 XML 这种格式

121
00:05:04,130 --> 00:05:07,900
然后呢，通过请求体发给服务端

122
00:05:07,900 --> 00:05:09,572
再看一下杨某

123
00:05:09,572 --> 00:05:13,590
压墨的话就不是构建标准库自带的了

124
00:05:13,590 --> 00:05:15,720
它是来自于这个包

125
00:05:15,720 --> 00:05:18,390
需要先通过 go get 下下巴

126
00:05:18,390 --> 00:05:20,310
然后提交给服务端

127
00:05:20,310 --> 00:05:22,910
所以服务端依然是通过数字版本

128
00:05:22,910 --> 00:05:25,105
来转为结构体

129
00:05:25,105 --> 00:05:28,487
那客户端也可以通过 restful 风格

130
00:05:28,487 --> 00:05:31,740
把参数直接放在请求路径里面

131
00:05:31,740 --> 00:05:33,020
那这种形式的话

132
00:05:33,020 --> 00:05:36,065
看下服务端如何来获得对应的参数

133
00:05:36,065 --> 00:05:37,440
首先在路径里

134
00:05:37,440 --> 00:05:40,080
你得指定这是一种 restful 风格，对吧

135
00:05:40,080 --> 00:05:42,590
比方说你得加个冒号啊

136
00:05:42,590 --> 00:05:46,520
新号啊，代代表这个地方有个参数

137
00:05:46,520 --> 00:05:50,545
然后呢，调用 should 的 band 的 URI 

138
00:05:50,545 --> 00:05:53,200
就会把路径里面的参数啊

139
00:05:53,200 --> 00:05:54,990
直接付给这个结构体

140
00:05:54,990 --> 00:05:58,617
然后呢，从结构体里面就可以取出对应的数据

141
00:05:58,617 --> 00:06:00,600
来组织你的响应

142
00:06:00,600 --> 00:06:02,470
这边讲一个实际工作

143
00:06:02,470 --> 00:06:03,610
可能会遇到的一个

144
00:06:03,610 --> 00:06:05,200
比较棘手的问题

145
00:06:05,200 --> 00:06:07,510
这是我们的请求数据啊

146
00:06:07,510 --> 00:06:10,550
本质上是从请求体里面读出来的

147
00:06:10,550 --> 00:06:12,250
请求体是一个流嘛

148
00:06:12,250 --> 00:06:14,652
那这个流的话，你只能读一次

149
00:06:14,652 --> 00:06:16,300
第二次再读就没有了

150
00:06:16,300 --> 00:06:19,010
因为第一次已经走到那个 EOF 了嘛

151
00:06:19,010 --> 00:06:20,840
走到 ride moi 了

152
00:06:20,840 --> 00:06:24,130
那我们的各种输的 bend 

153
00:06:24,130 --> 00:06:27,040
本质上都是在读那个流嘛

154
00:06:27,040 --> 00:06:29,350
那意味着你只能办的一次啊

155
00:06:29,350 --> 00:06:30,390
只能绑定一次

156
00:06:30,390 --> 00:06:33,660
第二次绑定数据已经为空了

157
00:06:33,660 --> 00:06:36,730
但有时候啊，我们需要绑定多次

158
00:06:36,730 --> 00:06:40,060
比如说我们可能在中间件里面

159
00:06:40,060 --> 00:06:42,432
需要绑定一次来获得参数

160
00:06:42,432 --> 00:06:46,210
然后在正式的业务 handle 里面需要再绑定一次

161
00:06:46,210 --> 00:06:48,290
需要二次的获得这个参数

162
00:06:48,290 --> 00:06:51,640
再比方说，可能时间跨度比较久了

163
00:06:51,640 --> 00:06:53,480
我们的这个请求参数啊

164
00:06:53,480 --> 00:06:54,520
格式变了

165
00:06:54,520 --> 00:06:55,880
我们在服务端呢

166
00:06:55,880 --> 00:06:59,500
需要兼容新老两种不同的格式

167
00:06:59,500 --> 00:07:01,700
比如老的格式是 JSON 

168
00:07:01,700 --> 00:07:03,520
新的格式是 XML 

169
00:07:03,520 --> 00:07:05,155
那怎么兼容呢

170
00:07:05,155 --> 00:07:07,127
一个想法就是说

171
00:07:07,127 --> 00:07:10,500
我们先去通过这个 BD 呀

172
00:07:10,500 --> 00:07:12,810
先试图往 JASON 去转

173
00:07:12,810 --> 00:07:14,955
往 JASON 去绑定

174
00:07:14,955 --> 00:07:16,830
那如果绑定失败了

175
00:07:16,830 --> 00:07:19,535
我们再尝试往 XM 去绑定

176
00:07:19,535 --> 00:07:20,630
但问题是

177
00:07:20,630 --> 00:07:22,150
如果绑定失败的话

178
00:07:22,150 --> 00:07:24,550
你第一次已经把那个留给浪费了

179
00:07:24,550 --> 00:07:26,770
第二次就读不到数据了

180
00:07:26,770 --> 00:07:27,890
怎么办呢

181
00:07:27,890 --> 00:07:29,410
哎，这种情况下

182
00:07:29,410 --> 00:07:31,310
咱们可以使用另外一个方法

183
00:07:31,310 --> 00:07:36,060
就是呢，使用 should band body with 

184
00:07:36,060 --> 00:07:38,060
哎，这个方法呢

185
00:07:38,060 --> 00:07:41,680
他会把从流里面读出来的数据啊

186
00:07:41,680 --> 00:07:43,070
保存一份

187
00:07:43,070 --> 00:07:46,370
保存到我们的 context 里面去

188
00:07:46,370 --> 00:07:47,330
这样的话

189
00:07:47,330 --> 00:07:50,190
你就可以反复多次的去使用

190
00:07:50,190 --> 00:07:51,852
请求 T 里的数据了

191
00:07:51,852 --> 00:07:54,620
即使是在中间件这样的场景之下

192
00:07:54,620 --> 00:07:59,200
因为每一个 handler 都会把这个 context 传进去嘛

193
00:07:59,200 --> 00:08:00,840
所以在每一个中间件里面

194
00:08:00,840 --> 00:08:02,740
你都可以拿到这个 context 啊

195
00:08:02,740 --> 00:08:06,397
都可以拿到那个请求体里面的完整数据

196
00:08:06,397 --> 00:08:07,710
看代码这边啊

197
00:08:07,710 --> 00:08:11,190
它是把你要付给那个结构体传进来

198
00:08:11,190 --> 00:08:15,135
然后第二个呢，需要指定具体的这个数据类型

199
00:08:15,135 --> 00:08:16,690
这边是指定为 JSON 

200
00:08:16,690 --> 00:08:18,310
如果 JASON 绑定失败了

201
00:08:18,310 --> 00:08:21,770
那么呢就尝试按照 XML 来进行绑定

202
00:08:21,770 --> 00:08:22,730
依次推啊

203
00:08:22,730 --> 00:08:24,430
比方说如果又失败了是吧

204
00:08:24,430 --> 00:08:26,507
按照杨某还要进行绑定

205
00:08:26,507 --> 00:08:29,540
对应的路径是 multi type 对吧

206
00:08:29,540 --> 00:08:30,380
这个路径的话

207
00:08:30,380 --> 00:08:32,340
它支持多种数据类型

208
00:08:32,340 --> 00:08:36,037
那于是乎呢，我们的客户端这边

209
00:08:36,037 --> 00:08:36,750
哎

210
00:08:36,750 --> 00:08:40,380
我们就去提交了各种各样的请求数据

211
00:08:40,380 --> 00:08:41,628
按理来说

212
00:08:41,628 --> 00:08:43,650
不管提交什么类型

213
00:08:43,650 --> 00:08:46,410
服务端都能够正常解析

214
00:08:46,410 --> 00:08:49,100
客户端都能够正常的拿到响应

215
00:08:49,100 --> 00:08:53,230
我们把这个 post wall 调一下试一试

216
00:08:53,230 --> 00:08:55,360
好，这边我们看一下啊

217
00:08:55,360 --> 00:08:57,650
不管是提交 JSON 也好

218
00:08:57,650 --> 00:08:59,470
提交 XML 也好

219
00:08:59,470 --> 00:09:01,370
提交 yo 也好

220
00:09:01,370 --> 00:09:03,830
都能够顺利的拿到响应
