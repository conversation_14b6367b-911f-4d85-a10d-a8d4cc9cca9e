1
00:00:00,420 --> 00:00:03,540
我们知道操作系统默认会打开三个文件

2
00:00:03,540 --> 00:00:06,400
标准输入、标准输出和标准错误

3
00:00:06,400 --> 00:00:08,300
先来看标准输入

4
00:00:08,300 --> 00:00:11,360
就他可以接收并读取用户的输入

5
00:00:11,360 --> 00:00:12,500
那从哪儿输入呢

6
00:00:12,500 --> 00:00:14,242
从终端输入

7
00:00:14,242 --> 00:00:17,790
看一下如何通过 go 语言来获得这个输入

8
00:00:17,790 --> 00:00:20,442
第十行，先输出一个提示

9
00:00:20,442 --> 00:00:24,150
第11行声明了一个字符串变量

10
00:00:24,150 --> 00:00:25,220
第12行呢

11
00:00:25,220 --> 00:00:28,180
通过 FMTSCP ，哎

12
00:00:28,180 --> 00:00:32,216
CAF 就是打算去扫描用户的终端输入的内容

13
00:00:32,216 --> 00:00:34,030
那用户终端输入的内容呢

14
00:00:34,030 --> 00:00:36,960
会付给 word 这个变量

15
00:00:36,960 --> 00:00:40,310
你打算在一个函数里面去给一个变量赋值

16
00:00:40,310 --> 00:00:42,910
所以呢，必须要传对应的指针形式

17
00:00:42,910 --> 00:00:44,407
加一个指符号

18
00:00:44,407 --> 00:00:45,500
因为是字符串嘛

19
00:00:45,500 --> 00:00:48,060
所以这边是百分号 S 

20
00:00:48,060 --> 00:00:54,320
注意我们以往的 print 是输出输出的终端

21
00:00:54,320 --> 00:00:58,460
而这个 SCP 是从终端读取内容

22
00:00:58,460 --> 00:01:01,852
来付给 go 语言里面的某一个变量

23
00:01:01,852 --> 00:01:03,530
拿到这个 word 之后

24
00:01:03,530 --> 00:01:05,750
我们再把这个 word 进行一个输出

25
00:01:05,750 --> 00:01:09,225
看一看是不是真正用户的输入

26
00:01:09,225 --> 00:01:12,250
那除了可以把用户的输入转成字符串之外

27
00:01:12,250 --> 00:01:13,950
也可以转成其他类型

28
00:01:13,950 --> 00:01:15,970
比如说转成整形

29
00:01:15,970 --> 00:01:18,252
依然是使用的 SCP 

30
00:01:18,252 --> 00:01:20,800
此时呢，要使用百分号 D 

31
00:01:20,800 --> 00:01:22,420
而且我们发现其实啊

32
00:01:22,420 --> 00:01:26,012
它可以支持一下子输入多个单词

33
00:01:26,012 --> 00:01:28,210
那我们在输入这两个单词的时候呢

34
00:01:28,210 --> 00:01:30,130
注意要用空格分隔

35
00:01:30,130 --> 00:01:32,890
它会自动的把空格前和空格后

36
00:01:32,890 --> 00:01:35,080
赋给这两个不同的变量

37
00:01:35,080 --> 00:01:35,730
然后呢

38
00:01:35,730 --> 00:01:38,950
我们拿到这两个变量就可以执行各种运算

39
00:01:38,950 --> 00:01:41,190
比如说执行一个加法运算

40
00:01:41,190 --> 00:01:43,185
然后进行一个输出

41
00:01:43,185 --> 00:01:44,710
上面这两种方式

42
00:01:44,710 --> 00:01:45,850
我们发现其实啊

43
00:01:45,850 --> 00:01:48,210
它都是按照空格进行分割的

44
00:01:48,210 --> 00:01:51,095
那假如说我输入的是一个整体

45
00:01:51,095 --> 00:01:53,770
这个整体里面本身就包含空格

46
00:01:53,770 --> 00:01:56,330
我不希望按照空格把它打散

47
00:01:56,330 --> 00:01:57,350
怎么办呢

48
00:01:57,350 --> 00:01:59,640
那么就不能使用 scarf 

49
00:01:59,640 --> 00:02:02,350
我们得把这个标准输入

50
00:02:02,350 --> 00:02:05,250
看成是一个普通的文件来使用

51
00:02:05,250 --> 00:02:08,499
那我们以往从文件里面读取数据

52
00:02:08,499 --> 00:02:11,830
自然是使用的 read 这个函数

53
00:02:11,830 --> 00:02:13,637
我们先打开一个文件

54
00:02:13,637 --> 00:02:15,300
通过 open 打开

55
00:02:15,300 --> 00:02:16,650
返回的是个 file 

56
00:02:16,650 --> 00:02:18,760
通过 file 调这个 read 

57
00:02:18,760 --> 00:02:22,500
而这个标准输入本身它就是一种 file 类型

58
00:02:22,500 --> 00:02:24,892
所以呢，自然它也可以调 read 

59
00:02:24,892 --> 00:02:27,250
那读取的内容放到哪呢

60
00:02:27,250 --> 00:02:31,272
放到这个 content 就放到这个 bat 切片里面去

61
00:02:31,272 --> 00:02:35,100
那从文件里面成功读取了 N 个字节

62
00:02:35,100 --> 00:02:39,800
我们把 content 前 N 个字节转成字符串进行输出

63
00:02:39,800 --> 00:02:43,220
这就是用户在终端输入的这一行内容

64
00:02:43,220 --> 00:02:45,110
我们把这个程序跑起来

65
00:02:45,110 --> 00:02:46,010
跑的时候呢

66
00:02:46,010 --> 00:02:47,190
我们不能使用单色

67
00:02:47,190 --> 00:02:49,240
得使用普通的幂函数

68
00:02:49,240 --> 00:02:51,580
因为像这种 SCAFFF 的话

69
00:02:51,580 --> 00:02:53,040
在单词里面不生效

70
00:02:53,040 --> 00:02:54,970
所以只能是 go run 

71
00:02:54,970 --> 00:02:57,460
好，他先输出第十行

72
00:02:57,460 --> 00:02:59,340
请输入一个单词

73
00:02:59,340 --> 00:03:02,202
那么随便输入一个够啦

74
00:03:02,202 --> 00:03:04,490
然后呢，然后还有回车

75
00:03:04,490 --> 00:03:06,490
我们就按下回车键

76
00:03:06,490 --> 00:03:08,870
才会把这个信息内容呢

77
00:03:08,870 --> 00:03:10,360
提交给我们的代码

78
00:03:10,360 --> 00:03:10,940
也就是说

79
00:03:10,940 --> 00:03:14,130
我们的代码实际上会在第12行这阻塞

80
00:03:14,130 --> 00:03:17,570
他还在等待用户的终端输入

81
00:03:17,570 --> 00:03:19,750
当我们按下回车键之后

82
00:03:19,750 --> 00:03:22,765
那么第12行这个阻塞就解除了

83
00:03:22,765 --> 00:03:24,560
它就会继续往后走

84
00:03:24,560 --> 00:03:25,900
把用户的输入呢

85
00:03:25,900 --> 00:03:29,595
在输出的终端就是有这样一个输出

86
00:03:29,595 --> 00:03:32,030
紧接着执行了第15行

87
00:03:32,030 --> 00:03:33,860
请输入两个整形

88
00:03:33,860 --> 00:03:35,805
那我们输入两个整数

89
00:03:35,805 --> 00:03:37,740
用空格进行分隔

90
00:03:37,740 --> 00:03:40,560
注意中间不要按回车键

91
00:03:40,560 --> 00:03:42,160
因为你每次按下回车键

92
00:03:42,160 --> 00:03:45,860
那我们的这个 SCANF 就会解除阻塞

93
00:03:45,860 --> 00:03:47,092
就会往后执行

94
00:03:47,092 --> 00:03:50,630
我们一定要把两个数字全部输入完之后

95
00:03:50,630 --> 00:03:53,137
再来按一下回车键

96
00:03:53,137 --> 00:03:54,720
好，那么我们代码呢

97
00:03:54,720 --> 00:03:57,580
就接收到了23空格

98
00:03:57,580 --> 00:03:58,180
45

99
00:03:58,180 --> 00:04:01,782
按照空格分隔分别赋给了 word 1和 word 2

100
00:04:01,782 --> 00:04:05,190
最后输出它们的和呢，等于68

101
00:04:05,190 --> 00:04:07,700
然后请输入一行内容

102
00:04:07,700 --> 00:04:12,100
我们输入 welcome to china 

103
00:04:12,100 --> 00:04:13,590
然后回车

104
00:04:13,590 --> 00:04:15,410
好，那么这整行内容呢

105
00:04:15,410 --> 00:04:18,180
就会被放到 content 里面去

106
00:04:18,180 --> 00:04:21,661
然后呢，他又对 content 进行了输出

107
00:04:21,661 --> 00:04:24,450
这段代码里有些细节我们要注意一下

108
00:04:24,450 --> 00:04:26,387
我们再来一次

109
00:04:26,387 --> 00:04:28,040
请输入一个单词

110
00:04:28,040 --> 00:04:30,840
我们呢，如果输入两个会发生什么情况

111
00:04:30,840 --> 00:04:34,350
比如说我先输入一个 GOL 空格

112
00:04:34,350 --> 00:04:36,075
我再输一个 hello 

113
00:04:36,075 --> 00:04:38,170
然后按下回车键是吧

114
00:04:38,170 --> 00:04:40,440
可以把这两个单词呢

115
00:04:40,440 --> 00:04:43,060
都传给我们的第12行

116
00:04:43,060 --> 00:04:44,520
看出来是什么结果

117
00:04:44,520 --> 00:04:45,645
回车

118
00:04:45,645 --> 00:04:47,250
好，发现啊

119
00:04:47,250 --> 00:04:50,120
他一下子就把所有代码执行完了

120
00:04:50,120 --> 00:04:53,200
这就意味着我们输入了两个单词

121
00:04:53,200 --> 00:04:56,670
那么这个 go line 实际上确实付给了这个 word 

122
00:04:56,670 --> 00:04:58,080
他这边输出是吧

123
00:04:58,080 --> 00:04:59,882
只有一个 go l 

124
00:04:59,882 --> 00:05:02,110
然后后面这个单词 hello 啊

125
00:05:02,110 --> 00:05:05,980
它会提交给第19行的 SCP 

126
00:05:05,980 --> 00:05:10,520
甚至还会提交给第24行的这个 read d 就行

127
00:05:10,520 --> 00:05:12,720
这边它七大数整数嘛

128
00:05:12,720 --> 00:05:14,140
但是我们是字母

129
00:05:14,140 --> 00:05:15,990
所以呢，解题出错了

130
00:05:15,990 --> 00:05:17,390
结果和是零

131
00:05:17,390 --> 00:05:20,150
而最后面这个 RIO 呢

132
00:05:20,150 --> 00:05:23,090
他认为是你输入的这行内容

133
00:05:23,090 --> 00:05:26,487
直接付给了 content 进行了输出

134
00:05:26,487 --> 00:05:27,900
所以我们在输的时候啊

135
00:05:27,900 --> 00:05:29,670
这个空格一定不要滥用

136
00:05:29,670 --> 00:05:33,130
否则他可能会把空格后面内容呢

137
00:05:33,130 --> 00:05:35,165
送给后续的 SCP 

138
00:05:35,165 --> 00:05:36,400
然后再来看代码

139
00:05:36,400 --> 00:05:37,340
这边还有个细节

140
00:05:37,340 --> 00:05:40,395
就这边有一个写一个 N 对吧

141
00:05:40,395 --> 00:05:43,920
这边也有一个斜杠弯换行吗

142
00:05:43,920 --> 00:05:46,190
那我能把这个换行符去掉吗

143
00:05:46,190 --> 00:05:47,330
来试一下

144
00:05:47,330 --> 00:05:50,300
假如我把第12行这个换行符去掉

145
00:05:50,300 --> 00:05:52,592
我们再来执行一遍

146
00:05:52,592 --> 00:05:54,050
run 一下

147
00:05:54,050 --> 00:05:55,630
请输入一个单词

148
00:05:55,630 --> 00:05:59,860
好，我输入一个 hello 回车

149
00:05:59,860 --> 00:06:04,637
你看他确实把这个 hello 付给了这个 word 

150
00:06:04,637 --> 00:06:09,000
然后呢，他紧接着直接执行了第20行

151
00:06:09,000 --> 00:06:10,980
就说他这个第19行啊

152
00:06:10,980 --> 00:06:12,457
它并没有阻塞

153
00:06:12,457 --> 00:06:14,970
他认为你已经输入了内容

154
00:06:14,970 --> 00:06:16,950
只不过呢，他获得到为空

155
00:06:16,950 --> 00:06:19,530
把空付给了 word 跟 word 2

156
00:06:19,530 --> 00:06:21,662
所以实际上还是零嘛

157
00:06:21,662 --> 00:06:22,760
没有复制成功

158
00:06:22,760 --> 00:06:25,330
导致这个结果呢， sun 是零

159
00:06:25,330 --> 00:06:29,607
而由于第二次 SCAF 这边有一个换行符

160
00:06:29,607 --> 00:06:32,250
所以呢，没有影响到第三次

161
00:06:32,250 --> 00:06:37,240
我们还第三次还是可以正常的输入一行内容

162
00:06:37,240 --> 00:06:40,572
能够正常的获取到这一行内容

163
00:06:40,572 --> 00:06:43,960
所以我们是按下回车键来提交的

164
00:06:43,960 --> 00:06:46,060
那么我们在 SCAF 这呢

165
00:06:46,060 --> 00:06:48,960
最后还是应该加上一个幻化符

166
00:06:48,960 --> 00:06:51,520
再来看标准输出和标准错误

167
00:06:52,560 --> 00:06:55,180
s std ， out 和 SDL 

168
00:06:55,180 --> 00:06:57,300
这两个呀，其实都是输出到终端

169
00:06:57,300 --> 00:07:00,740
跟我们以往使用的这个 print 

170
00:07:00,740 --> 00:07:02,110
其实是一样的效果

171
00:07:02,110 --> 00:07:03,475
作用里面

172
00:07:03,475 --> 00:07:06,267
out 和这个 error 其实没什么区别

173
00:07:06,267 --> 00:07:09,230
只是说在操作系统这个层面上

174
00:07:09,230 --> 00:07:11,860
那么 out 它的文件句柄是一

175
00:07:11,860 --> 00:07:13,250
error 是二

176
00:07:13,250 --> 00:07:15,715
这个 in 呢是零

177
00:07:15,715 --> 00:07:19,190
但是我发现在各位里面打出来这个文件极品

178
00:07:19,190 --> 00:07:20,330
它并不是固定

179
00:07:20,330 --> 00:07:22,740
零、一、二每次都在变

180
00:07:22,740 --> 00:07:24,770
而且从操作系统层面来讲

181
00:07:24,770 --> 00:07:28,310
这个标准输出它是有行缓冲的

182
00:07:28,310 --> 00:07:30,350
所以叫行缓冲嘛

183
00:07:30,350 --> 00:07:33,050
就它必须碰见换行符

184
00:07:33,050 --> 00:07:35,200
才会真正的输出的终端

185
00:07:35,200 --> 00:07:37,130
而这个标准的错误呢

186
00:07:37,130 --> 00:07:38,570
是无缓冲的啊

187
00:07:38,570 --> 00:07:40,090
它不需要通过键按行符

188
00:07:40,090 --> 00:07:42,830
就能够及时的把内容输出到终端

189
00:07:42,830 --> 00:07:45,850
这个规则大家如果用 C 语言的话能够发现

190
00:07:45,850 --> 00:07:46,890
但是是用 go 圆的话

191
00:07:46,890 --> 00:07:48,270
其实并没有这个规律

192
00:07:48,270 --> 00:07:51,390
比如说我的第35行

193
00:07:51,390 --> 00:07:54,510
这个标准错误是带了一个根号符

194
00:07:54,510 --> 00:07:55,990
而我的第34行呢

195
00:07:55,990 --> 00:07:58,372
标准输出并没有带根号符

196
00:07:58,372 --> 00:08:02,150
但是呢，它也能及时的输出到终端

197
00:08:02,150 --> 00:08:04,720
我们把这个函数抛起来看一下

198
00:08:04,720 --> 00:08:06,000
转起来

199
00:08:06,000 --> 00:08:07,190
好

200
00:08:07,190 --> 00:08:10,380
这三个它们的文件句柄分别

201
00:08:10,380 --> 00:08:11,800
这么多啊

202
00:08:11,800 --> 00:08:13,395
其实没有什么固定规律

203
00:08:13,395 --> 00:08:16,112
三、二、三行输出一个内容

204
00:08:16,112 --> 00:08:18,580
34行输出一个 A 、 B 、 C 

205
00:08:18,580 --> 00:08:21,845
然后呢是35行输出一个一、二、三

206
00:08:21,845 --> 00:08:23,830
A 、 B 、 C 后面没有根号符

207
00:08:23,830 --> 00:08:26,135
热二、三后面有一个根号符

208
00:08:26,135 --> 00:08:27,730
如果我们再运行一次的话

209
00:08:27,730 --> 00:08:31,030
你会发现这个文件句柄又变了

210
00:08:31,030 --> 00:08:32,130
它并不是固定的

211
00:08:32,130 --> 00:08:32,730
01
