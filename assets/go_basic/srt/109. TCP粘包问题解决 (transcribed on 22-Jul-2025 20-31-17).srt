1
00:00:00,000 --> 00:00:29,980
看一下tcp的占报问题实际就是说tcp它面向自协流而udp面向报文什么是报文就是应用层它传的一个数据就是一个报文比方说对方他给我发一个hellohello是核心数据那么这个hello就是一个报文那如果对方他通过叫write给我发过来的是一个800字论文的话那么整个论文就是一个报文所以一个报文是什么取决对方拖

2
00:00:29,980 --> 00:00:33,560
通过一次的write调用他给你发的是什么

3
00:00:34,340 --> 00:00:50,980
那么如果是udp的话对方write的一个内容那我这边调read读出来的就是这个内容如果在我这边调read之前对方已经通过三次write给我发送了三个报文那么我第一次调read我只能把第一个报文给读出来

4
00:00:51,480 --> 00:00:53,280
第二次调read把第二个报文读出来

5
00:00:53,780 --> 00:00:57,880
所以这个报文之间的分割呀udp协议本身已经帮我们做了

6
00:00:57,880 --> 00:01:00,440
那么我们去读出去就很方便

7
00:01:00,960 --> 00:01:02,480
但tcp就很麻烦

8
00:01:03,000 --> 00:01:04,540
tcp呢它是一个流

9
00:01:05,300 --> 00:01:09,400
这个协议并不会告诉我们报文跟报文是在什么地方分割的

10
00:01:09,920 --> 00:01:15,280
对方一上来通过write连续三次write给我发送了三个报文

11
00:01:15,800 --> 00:01:18,100
那tcp的话对另外一段而言

12
00:01:18,620 --> 00:01:21,440
他从流里面读出来的就是一个一个字节

13
00:01:22,200 --> 00:01:24,500
他并不知道我这次需要读几个字节

14
00:01:24,500 --> 00:01:27,580
他并不知道第一个报文长度为多少

15
00:01:28,340 --> 00:01:30,640
所以呢对方一下子发过来三个报文

16
00:01:30,900 --> 00:01:31,660
我这边

17
00:01:32,180 --> 00:01:36,020
可能一次性的通过read把这三个报文全部给读出来了

18
00:01:36,540 --> 00:01:37,560
但就算读出来了

19
00:01:37,820 --> 00:01:39,340
我也不知道该如何做风格

20
00:01:40,120 --> 00:01:41,400
这叫面向字节流

21
00:01:41,400 --> 00:01:44,480
就有点类似于你去超市买水

22
00:01:44,720 --> 00:01:45,760
那tcp

23
00:01:46,260 --> 00:01:47,040
是流逝的

24
00:01:47,280 --> 00:01:48,820
就说老板给了一个手笼头

25
00:01:49,080 --> 00:01:50,100
你打开手笼头

26
00:01:50,620 --> 00:01:51,900
不停的往外接水

27
00:01:52,160 --> 00:01:53,940
而udp是面向报文

28
00:01:54,200 --> 00:01:56,760
这好比是老板实用桶给你卖的

29
00:01:57,280 --> 00:01:59,320
那不管是udp还tcp吧

30
00:01:59,580 --> 00:02:02,900
我们在调用read时总是要传一个byte切片过来

31
00:02:03,160 --> 00:02:05,460
那这个byte切片它的长度

32
00:02:05,980 --> 00:02:07,520
对对取结果显然是有影响的

33
00:02:07,760 --> 00:02:09,560
假如说你这个长度太短的话

34
00:02:09,560 --> 00:02:13,140
有可能它连一个报文都容纳不下

35
00:02:13,660 --> 00:02:15,700
对于tcp来说这个没有问题

36
00:02:16,220 --> 00:02:20,060
因为一次读读不完你下一次还是接着读

37
00:02:20,560 --> 00:02:22,100
但是udp就很麻烦

38
00:02:23,120 --> 00:02:26,460
你这一次read没有把一个完整报文读完

39
00:02:26,960 --> 00:02:27,740
容纳不下了

40
00:02:28,240 --> 00:02:29,780
那么你下一次再调read

41
00:02:30,040 --> 00:02:32,080
上一次那个报文就读不到了

42
00:02:33,120 --> 00:02:34,640
而上一次你只读了一半

43
00:02:34,900 --> 00:02:36,960
你可能无法还原整体数据

44
00:02:37,200 --> 00:02:38,240
所以数据就丢失了

45
00:02:38,240 --> 00:02:40,280
所以使用udp时呢

46
00:02:40,540 --> 00:02:42,080
我们双方最好约定一下

47
00:02:42,340 --> 00:02:44,380
这个最大的报文长度是多少

48
00:02:44,640 --> 00:02:47,460
然后我们去开辟那个byte切片呢

49
00:02:47,960 --> 00:02:49,500
就直接开了大一些

50
00:02:50,280 --> 00:02:51,040
那tcp呢

51
00:02:52,060 --> 00:02:54,360
tcp的话每次发送数据长度

52
00:02:54,620 --> 00:02:55,900
它可能不是固定的

53
00:02:56,680 --> 00:02:58,460
那我怎么去做这个分隔呢

54
00:02:59,240 --> 00:03:00,760
这边有几种处理方式吧

55
00:03:01,020 --> 00:03:02,300
这种方式就是我刚才说的

56
00:03:02,560 --> 00:03:03,320
就是我们

57
00:03:03,840 --> 00:03:05,380
人类的约定一下

58
00:03:05,380 --> 00:03:10,240
强行把每个报文都搞成固定长度

59
00:03:11,260 --> 00:03:13,820
比方我们就说一个报文都是

60
00:03:14,600 --> 00:03:15,100
1k

61
00:03:15,359 --> 00:03:17,160
那假如说真的不到1k的话

62
00:03:17,420 --> 00:03:18,940
我们前面不是讲过那个padding吗

63
00:03:19,200 --> 00:03:19,720
填充吧

64
00:03:20,220 --> 00:03:21,500
pksc填充

65
00:03:22,020 --> 00:03:24,320
你强行把它填充够一起来的自己

66
00:03:25,340 --> 00:03:25,860
接触方

67
00:03:26,100 --> 00:03:28,660
在末尾的填充数据把这个删掉

68
00:03:29,440 --> 00:03:30,200
这是一种方案

69
00:03:30,720 --> 00:03:31,740
这种方案就是说

70
00:03:32,260 --> 00:03:33,540
双方约定好

71
00:03:33,780 --> 00:03:35,060
报文和报文之间

72
00:03:35,320 --> 00:03:37,120
用什么分隔符来进行分隔

73
00:03:37,880 --> 00:03:39,680
虽然说每个报文长度不一样

74
00:03:39,680 --> 00:03:40,200
但是呢

75
00:03:40,440 --> 00:03:41,980
当你从流里面

76
00:03:42,240 --> 00:03:43,520
读出这个分隔符的话

77
00:03:43,780 --> 00:03:44,540
你就知道

78
00:03:44,800 --> 00:03:46,340
sign的报文已经结束了

79
00:03:46,840 --> 00:03:48,120
下面该下一个报文

80
00:03:48,900 --> 00:03:49,920
第3个方案

81
00:03:50,680 --> 00:03:52,220
就是我们自定义

82
00:03:52,480 --> 00:03:53,760
报文的序列化方式

83
00:03:54,520 --> 00:03:56,320
因为报文是一个数据码

84
00:03:56,580 --> 00:03:58,120
可能是一个计算字符串

85
00:03:58,880 --> 00:04:02,980
那我们在把它转成by的切片转成流的时候

86
00:04:04,000 --> 00:04:06,320
我们可能需要在一开始的地方

87
00:04:06,560 --> 00:04:07,600
放一个数字

88
00:04:07,840 --> 00:04:08,360
这个数字呢

89
00:04:08,620 --> 00:04:11,440
就用来记录本条报文的总长度

90
00:04:11,680 --> 00:04:13,740
转成by的切片之后的总长度

91
00:04:14,240 --> 00:04:16,040
我首先把长度读出来

92
00:04:16,300 --> 00:04:16,800
然后呢

93
00:04:16,800 --> 00:04:19,120
我在往后读这么多个字节

94
00:04:19,620 --> 00:04:21,680
那刚好就是一个完整的报文

95
00:04:21,920 --> 00:04:23,200
每一个报文一上来

96
00:04:23,460 --> 00:04:25,760
都把该报文的总长度

97
00:04:26,020 --> 00:04:27,300
作为第一个字节给放了

98
00:04:28,120 --> 00:04:29,400
这是三种方案

99
00:04:30,160 --> 00:04:30,680
那么我们呢

100
00:04:30,940 --> 00:04:31,960
把中间那个方案

101
00:04:32,220 --> 00:04:32,980
就是自定义

102
00:04:33,240 --> 00:04:34,780
分隔符这个方案来实现一下

103
00:04:35,800 --> 00:04:38,100
我们先来把这个条理给理清楚啊

104
00:04:38,360 --> 00:04:39,120
自定义分隔符的话

105
00:04:39,380 --> 00:04:40,660
要面临各种情况

106
00:04:41,680 --> 00:04:42,720
TCP嘛

107
00:04:43,480 --> 00:04:45,520
我开辟好一个byl的切片

108
00:04:45,780 --> 00:04:47,580
来准备从连接里面

109
00:04:47,840 --> 00:04:49,620
去读出若干戈字节

110
00:04:50,400 --> 00:04:52,440
那每次读出来的这个内容呢

111
00:04:52,700 --> 00:04:53,200
就用

112
00:04:53,720 --> 00:04:54,240
一行

113
00:04:54,740 --> 00:04:55,260
来表示

114
00:04:56,020 --> 00:04:57,560
有五种情况

115
00:04:58,320 --> 00:04:59,100
看这种情况

116
00:05:00,120 --> 00:05:02,160
假如说我读出来的这行内容呢

117
00:05:03,200 --> 00:05:04,720
这个分隔符为中间

118
00:05:04,980 --> 00:05:07,040
这个分隔符可能不是一个字节啊

119
00:05:07,280 --> 00:05:08,060
大概率不是一个字节

120
00:05:08,560 --> 00:05:09,840
比方说我们定义好

121
00:05:10,100 --> 00:05:12,160
这个分隔符是三个字节

122
00:05:12,160 --> 00:05:13,180
就是

123
00:05:13,700 --> 00:05:15,240
八十九五十七一百零八

124
00:05:15,480 --> 00:05:16,760
你随便想三个字

125
00:05:17,020 --> 00:05:17,540
也就是说

126
00:05:17,800 --> 00:05:18,820
我们认为呀

127
00:05:19,080 --> 00:05:20,100
正常的

128
00:05:20,360 --> 00:05:21,120
数据里面

129
00:05:21,380 --> 00:05:22,400
不可能包含

130
00:05:22,660 --> 00:05:24,200
刚才说的那三个字节

131
00:05:24,700 --> 00:05:25,480
有那样概率

132
00:05:25,720 --> 00:05:26,500
确实太低了吗

133
00:05:27,000 --> 00:05:28,040
然后你就以这个

134
00:05:28,280 --> 00:05:28,800
作为分隔符

135
00:05:29,320 --> 00:05:31,620
实际当中这个分隔符可能会更长一些

136
00:05:31,880 --> 00:05:32,380
五个字节

137
00:05:32,380 --> 00:05:32,900
七个字节

138
00:05:33,140 --> 00:05:33,659
这样避免

139
00:05:33,920 --> 00:05:35,700
跟正数据发生冲突吗

140
00:05:36,740 --> 00:05:38,020
这是一种常过情况

141
00:05:38,260 --> 00:05:40,580
分隔符位于这个报文的中间某个位置

142
00:05:41,600 --> 00:05:42,360
第二种情况呢

143
00:05:42,620 --> 00:05:45,940
可能你每次接收到一个数据之后的话

144
00:05:46,700 --> 00:05:48,760
它里面包含多个分隔符

145
00:05:49,520 --> 00:05:51,320
你读出来

146
00:05:51,580 --> 00:05:52,080
一千个字节

147
00:05:52,600 --> 00:05:53,880
这一千个字节里面

148
00:05:54,380 --> 00:05:56,700
可能包含了三个或者四个分隔符

149
00:05:57,460 --> 00:06:00,020
而且有的分隔符它可能刚好位于末尾

150
00:06:00,280 --> 00:06:01,040
而且呢

151
00:06:01,300 --> 00:06:01,820
还没有完

152
00:06:02,580 --> 00:06:04,620
这个分隔符的一部分为

153
00:06:05,400 --> 00:06:06,680
这一次red的末尾

154
00:06:06,940 --> 00:06:08,720
而分隔符的后半部分

155
00:06:08,980 --> 00:06:10,780
为下一次red的七十位置

156
00:06:11,800 --> 00:06:12,820
那还有就是呢

157
00:06:13,080 --> 00:06:14,620
这个分隔符刚好位于

158
00:06:14,620 --> 00:06:17,700
这一次从流里面读出来的数据的末尾

159
00:06:18,720 --> 00:06:19,740
那还有就是

160
00:06:20,000 --> 00:06:23,060
你这一次读出来的数据里面完全没有分隔符

161
00:06:23,580 --> 00:06:24,600
还有就是

162
00:06:25,120 --> 00:06:25,620
分隔符呢

163
00:06:25,880 --> 00:06:26,900
刚好位于

164
00:06:27,420 --> 00:06:27,940
其实位置

165
00:06:28,960 --> 00:06:29,720
所以想一下

166
00:06:29,980 --> 00:06:31,780
我们该怎么去设计这个程序呢

167
00:06:32,800 --> 00:06:35,860
我们在程序大马里面的需要准备好一个buff

168
00:06:36,380 --> 00:06:37,140
一个缓冲区嘛

169
00:06:37,400 --> 00:06:40,220
说白了就是一个很长的一个by的切片

170
00:06:41,240 --> 00:06:42,780
那么这个by的切片

171
00:06:42,780 --> 00:06:45,600
和每次调略的函数使用的by的切片

172
00:06:45,860 --> 00:06:47,640
是两个不同的东西啊

173
00:06:48,660 --> 00:06:49,180
去开始的时候呢

174
00:06:49,440 --> 00:06:50,460
我们这个缓冲啊

175
00:06:50,460 --> 00:06:51,480
它是一个空的

176
00:06:51,740 --> 00:06:53,020
里面没有任何内容

177
00:06:54,300 --> 00:06:55,320
那比如第四对吧

178
00:06:55,580 --> 00:06:57,620
我把这么多字节给读出来了

179
00:06:58,140 --> 00:06:59,160
读出来之后的话

180
00:06:59,420 --> 00:07:03,000
我就去这一行内容里面的去找这个分隔符

181
00:07:03,780 --> 00:07:04,540
比如说

182
00:07:04,800 --> 00:07:06,340
我找到第1个分隔符呢

183
00:07:06,580 --> 00:07:07,620
它就出现在

184
00:07:08,380 --> 00:07:08,900
这个位置

185
00:07:09,660 --> 00:07:11,980
那么这个是往前的部分呢

186
00:07:13,000 --> 00:07:15,560
我就把它放到我那个缓冲区里面去

187
00:07:15,820 --> 00:07:16,580
放到buffer里面去

188
00:07:17,600 --> 00:07:18,120
然后呢

189
00:07:18,380 --> 00:07:21,440
把buffer里面内容整体进行输出

190
00:07:21,700 --> 00:07:22,220
这样的话

191
00:07:22,460 --> 00:07:26,060
buffer里面存放的就是一个完整的应用层的报纹

192
00:07:26,060 --> 00:07:27,600
啊

193
00:07:27,600 --> 00:07:29,900
你可以输出也可能是需要做一个什么

194
00:07:29,900 --> 00:07:30,660
计算反应序的话

195
00:07:30,920 --> 00:07:31,700
然后呢

196
00:07:31,940 --> 00:07:32,720
进行处理

197
00:07:33,480 --> 00:07:34,000
等等吧

198
00:07:34,000 --> 00:07:34,760
反正就是说

199
00:07:34,760 --> 00:07:35,540
此时此刻

200
00:07:35,780 --> 00:07:37,840
buffer里面就是一个完整的报纹

201
00:07:38,600 --> 00:07:41,680
然后我跳过这个分隔符对吧

202
00:07:42,700 --> 00:07:44,240
我从这个位置开始

203
00:07:45,260 --> 00:07:46,280
就上一次

204
00:07:46,540 --> 00:07:48,080
我把buffer里面给

205
00:07:48,340 --> 00:07:49,100
读出来了

206
00:07:49,620 --> 00:07:50,380
给使用了

207
00:07:50,640 --> 00:07:51,400
使用完之后

208
00:07:51,400 --> 00:07:52,680
我要把那个buffer呢

209
00:07:52,940 --> 00:07:53,460
给清空

210
00:07:53,980 --> 00:07:54,480
然后

211
00:07:54,740 --> 00:07:55,500
这边对吧

212
00:07:55,500 --> 00:07:58,060
我从这个位置往后的这部分

213
00:07:58,580 --> 00:08:00,120
再放到buffer里面去

214
00:08:01,140 --> 00:08:01,900
然后呢

215
00:08:02,160 --> 00:08:03,700
下一次又读出来第2号

216
00:08:03,960 --> 00:08:04,460
我发现

217
00:08:04,720 --> 00:08:05,740
我分个符在这个位置

218
00:08:06,520 --> 00:08:07,020
然后呢

219
00:08:07,280 --> 00:08:08,300
我把这一部分

220
00:08:08,560 --> 00:08:10,620
再追加到那个buffer里面去

221
00:08:10,620 --> 00:08:11,120
注意啊

222
00:08:11,380 --> 00:08:13,180
上一次buffer里面不是有

223
00:08:14,200 --> 00:08:14,960
这个内容吗

224
00:08:15,480 --> 00:08:16,240
现在我又把

225
00:08:16,760 --> 00:08:19,060
这两个内容也追到buffer里面去

226
00:08:19,320 --> 00:08:19,820
这样的话

227
00:08:19,820 --> 00:08:22,140
buffer里面存放的就是一个完整的报纹

228
00:08:22,380 --> 00:08:22,640
对吧

229
00:08:23,159 --> 00:08:24,680
我就可以把这个报纹给读出来

230
00:08:24,940 --> 00:08:25,460
去使用它

231
00:08:26,219 --> 00:08:26,740
使用完之后

232
00:08:27,000 --> 00:08:28,280
把这个buffer给清空

233
00:08:29,039 --> 00:08:29,560
我接着

234
00:08:29,800 --> 00:08:30,580
又是下一次

235
00:08:30,840 --> 00:08:31,860
又把这个玩意

236
00:08:32,880 --> 00:08:33,900
放到buffer里面去

237
00:08:34,159 --> 00:08:34,679
依次推

238
00:08:35,699 --> 00:08:36,199
那像这种

239
00:08:36,460 --> 00:08:36,980
如果没有

240
00:08:37,240 --> 00:08:38,260
预见分割符的话

241
00:08:38,520 --> 00:08:40,299
就把整体内容的整体

242
00:08:40,819 --> 00:08:42,600
追加到buffer的末尾就可以了

243
00:08:43,880 --> 00:08:44,660
直到下一次

244
00:08:44,920 --> 00:08:45,680
预见分割符

245
00:08:45,939 --> 00:08:47,220
我再把buffer里面内容

246
00:08:47,480 --> 00:08:48,240
全部给取出来

247
00:08:49,000 --> 00:08:50,800
当成一个完整的报纹来处理

248
00:08:51,560 --> 00:08:53,359
所以对应到代码这边这样子的

249
00:08:53,620 --> 00:08:55,400
我这边是搞了一个buffer

250
00:08:55,660 --> 00:08:56,939
它是buffer

251
00:08:57,959 --> 00:09:00,780
你可以不断的往这个buffer里面的去追加内容

252
00:09:01,040 --> 00:09:02,579
也可以把这个buffer给清空

253
00:09:03,859 --> 00:09:06,920
这个request还是用来去读取流里面的数据

254
00:09:07,680 --> 00:09:09,479
这边是一个for循环长连接

255
00:09:11,020 --> 00:09:15,620
好每次掉这个read都是把数据读到request里面去

256
00:09:16,400 --> 00:09:18,439
那么request的前n个字节

257
00:09:18,699 --> 00:09:20,240
这个data就是内行内容

258
00:09:21,260 --> 00:09:25,099
然后我要从这个data里面去找分割符

259
00:09:25,360 --> 00:09:27,660
这个分割符的话是我定义好的一个长链

260
00:09:27,660 --> 00:09:31,760
就是这样的一个长堆五的一个切片

261
00:09:33,040 --> 00:09:38,920
通过byteindex去一个byte的切片里面去找某一个特定的子切片

262
00:09:39,180 --> 00:09:39,939
找这个分割符

263
00:09:40,719 --> 00:09:42,760
返回分割符的其实位置

264
00:09:43,020 --> 00:09:45,319
如果这个位置大于0的话

265
00:09:45,319 --> 00:09:46,100
说明找到了

266
00:09:47,120 --> 00:09:48,900
再细分一下

267
00:09:48,900 --> 00:09:50,439
如果刚好等于0的话

268
00:09:50,439 --> 00:09:53,260
说明刚好在其实位置就是一个分割符

269
00:09:53,260 --> 00:09:58,120
那说明之前我们buff里面的内容呢已经可以

270
00:09:58,900 --> 00:10:00,939
直接读出来使用了对吧

271
00:10:01,200 --> 00:10:05,800
所以呢我先判断一下这个buff里面确实是有那种的长度大于0的

272
00:10:05,800 --> 00:10:06,319
然后呢

273
00:10:06,580 --> 00:10:11,700
我这边所谓的使用就是直接把它给进行一个打印输出就完事了

274
00:10:12,200 --> 00:10:14,000
在实际工作中可能是说

275
00:10:14,260 --> 00:10:16,040
我拿到这个内容之后的话

276
00:10:16,300 --> 00:10:18,340
它大概率是一个阶层格式啊

277
00:10:18,600 --> 00:10:19,620
进行一个阶层版式的话

278
00:10:19,880 --> 00:10:20,900
然后取出各个参数

279
00:10:20,900 --> 00:10:21,680
然后呢

280
00:10:21,680 --> 00:10:23,480
去生成你的response

281
00:10:23,720 --> 00:10:25,260
然后把response返回对方啊

282
00:10:25,520 --> 00:10:26,280
这样一个流程

283
00:10:27,320 --> 00:10:27,820
好

284
00:10:28,080 --> 00:10:29,620
那么你把这个数据啊

285
00:10:29,880 --> 00:10:30,640
试用完之后的话

286
00:10:30,900 --> 00:10:33,200
要及时的把buff把它给清空

287
00:10:33,460 --> 00:10:33,960
清空

288
00:10:35,240 --> 00:10:37,800
因为下次要帮助全新的报文嘛

289
00:10:38,840 --> 00:10:41,900
如果这个位置大于0就说明这个分割符啊

290
00:10:42,160 --> 00:10:43,440
它是在这个

291
00:10:44,460 --> 00:10:46,260
data的中间某个位置

292
00:10:47,280 --> 00:10:49,320
那么我就把这个position

293
00:10:49,840 --> 00:10:50,860
前的内容

294
00:10:51,620 --> 00:10:54,960
把它追加到这个buffer里面去通过调这个right

295
00:10:55,460 --> 00:10:57,000
是追加到buffer里面去

296
00:10:57,780 --> 00:10:59,820
然后因为已经碰见分割符了嘛

297
00:11:00,080 --> 00:11:04,680
所以buffer里面那种也是一个完整报文也可以打印出来直接使用了

298
00:11:04,940 --> 00:11:06,740
那每次使用完之后的话

299
00:11:06,980 --> 00:11:10,580
都记得把buffer给清空通过调reset来清空

300
00:11:11,100 --> 00:11:11,600
好

301
00:11:11,860 --> 00:11:15,700
这个data里面它可能包含多个分割符

302
00:11:15,960 --> 00:11:17,740
刚才只是处理了一个分割符

303
00:11:18,780 --> 00:11:25,680
所以我要把这个data这个游标往后走实际上是通过一个切片的截取来实现

304
00:11:26,200 --> 00:11:26,700
你看

305
00:11:26,960 --> 00:11:28,240
这个切片截取的话

306
00:11:28,240 --> 00:11:29,520
其实位置呢

307
00:11:29,780 --> 00:11:31,560
是通过这个position

308
00:11:31,820 --> 00:11:33,360
position表示分割符的位置

309
00:11:33,880 --> 00:11:34,380
其实位置

310
00:11:34,900 --> 00:11:37,460
再加上这个分割符本身的长度

311
00:11:38,740 --> 00:11:41,800
从这个位置开始就跳过这个分割符

312
00:11:41,800 --> 00:11:42,320
是吧

313
00:11:42,560 --> 00:11:46,160
从下一个位置开始继续的进入下一轮的for循环

314
00:11:47,440 --> 00:11:51,020
这样的话data就变成了data的一个紫切片嘛

315
00:11:51,280 --> 00:11:55,360
下一轮for循环我再从data里面去找这个分割符

316
00:11:55,880 --> 00:11:56,400
好

317
00:11:56,400 --> 00:11:58,180
直到说找不到

318
00:11:58,180 --> 00:12:00,740
找不到的话这个position会等于-1

319
00:12:01,000 --> 00:12:01,520
小二零

320
00:12:01,520 --> 00:12:03,560
小二零的话走到else里面来

321
00:12:05,100 --> 00:12:05,600
break

322
00:12:05,600 --> 00:12:06,380
退出后循环

323
00:12:06,380 --> 00:12:08,680
就表示这个data我已经处理完了

324
00:12:09,700 --> 00:12:10,220
但是呢

325
00:12:10,220 --> 00:12:11,500
在break之前

326
00:12:12,260 --> 00:12:16,880
你还是需要把data里面剩余的内容的追加到buff里面去

327
00:12:17,640 --> 00:12:19,940
因为他可能还剩余这么多内容吧

328
00:12:20,720 --> 00:12:22,260
就好比是这边啊

329
00:12:22,500 --> 00:12:25,580
最后还剩两个空白格把这两个空白格呢

330
00:12:25,840 --> 00:12:27,880
需要追加到buff里面去

331
00:12:28,900 --> 00:12:29,940
那么最后

332
00:12:30,180 --> 00:12:33,780
当我在这边读的时候发现遇到了EOF

333
00:12:33,780 --> 00:12:37,360
对TCP来说如果扣端主动把链接关闭了

334
00:12:37,360 --> 00:12:38,380
那么复端呢

335
00:12:38,380 --> 00:12:40,940
这个read就会报EOF

336
00:12:41,700 --> 00:12:42,980
当发生EOF的话

337
00:12:42,980 --> 00:12:44,780
我还是最终啊兜底来看一看

338
00:12:45,040 --> 00:12:46,320
buff里面有没有内容

339
00:12:46,820 --> 00:12:48,100
如果有内容的话呢

340
00:12:48,100 --> 00:12:50,160
对buff整体进行一个输出

341
00:12:50,920 --> 00:12:51,700
所以这样的话

342
00:12:51,700 --> 00:12:53,740
如果按照分割服进行分割

343
00:12:53,740 --> 00:12:59,620
那么我们就可以大胆的在这个长连接之前大胆的去休息一个五秒钟啊

344
00:12:59,880 --> 00:13:05,000
就算在这五秒钟之内客户端给我连续发过来的好几条出去

345
00:13:05,260 --> 00:13:07,060
我也不害怕不担心啊

346
00:13:07,060 --> 00:13:10,900
我可以通过分割服顺利的把每一个报文给抽取出来

347
00:13:10,900 --> 00:13:18,580
看一下客户端这边客户端这边的话我们每次发送数据呢就不能简单发送一个字

348
00:13:18,840 --> 00:13:21,140
得把这个分割服给加上啊

349
00:13:21,140 --> 00:13:26,260
每次这个报文呢我们都把分割服加上作为后缀

350
00:13:27,020 --> 00:13:28,060
发给付端

351
00:13:28,819 --> 00:13:31,900
然后我们连接创建好之后啊不休息

352
00:13:31,900 --> 00:13:35,220
脑不停蹄的立刻给付端去发送多个报文

353
00:13:35,220 --> 00:13:38,040
看看付端能不能把它们给分割出来

354
00:13:38,540 --> 00:13:42,380
好我们先把TCP的这个付端起一下

355
00:13:43,140 --> 00:13:46,740
然后呢紧接着启动一下这个客户端

356
00:13:48,260 --> 00:13:48,780
好

357
00:13:49,040 --> 00:13:51,340
客户端这边每次发出去10个字节

358
00:13:51,340 --> 00:13:52,620
因为hello是5个字节

359
00:13:52,620 --> 00:13:54,160
然后5个字节的n个符嘛

360
00:13:54,160 --> 00:13:55,180
总共是10个字节

361
00:13:55,700 --> 00:13:56,980
看一下服务在这边

362
00:13:58,260 --> 00:13:59,020
服务在这边呢

363
00:13:59,020 --> 00:14:01,580
他说他收到这样一条数据啊

364
00:14:01,580 --> 00:14:02,600
就是我们

365
00:14:03,620 --> 00:14:05,420
通过掉read

366
00:14:05,939 --> 00:14:07,460
不是读出来n个字节吗

367
00:14:07,460 --> 00:14:10,800
我们把这个request起n个字节打出来

368
00:14:10,800 --> 00:14:12,840
就是这个样子啊

369
00:14:12,840 --> 00:14:14,120
连续的多个hello

370
00:14:14,640 --> 00:14:16,440
这是我们第85框打出来的

371
00:14:17,200 --> 00:14:17,960
然后后面的话

372
00:14:17,960 --> 00:14:19,760
我们不是把这个报文进行分割吗

373
00:14:19,760 --> 00:14:22,580
那确实能够把三个报文给分割出来

