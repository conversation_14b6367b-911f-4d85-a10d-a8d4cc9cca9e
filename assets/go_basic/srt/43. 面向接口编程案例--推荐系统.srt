1
00:00:00,700 --> 00:00:03,710
初学者经常会感觉接口很抽象

2
00:00:03,710 --> 00:00:04,820
这个主要是因为

3
00:00:04,820 --> 00:00:07,352
他没有实际的项目开发经验

4
00:00:07,352 --> 00:00:10,640
他找不到一个使用接口的合理场景

5
00:00:10,640 --> 00:00:12,390
现实中也确实是这样

6
00:00:12,390 --> 00:00:13,820
我们开发很多项目

7
00:00:13,820 --> 00:00:15,635
可能根本就不需要使用接口

8
00:00:15,635 --> 00:00:17,840
一般你也不会主动的去使用接口

9
00:00:17,840 --> 00:00:19,960
而仅仅是在看别人代码的时候

10
00:00:19,960 --> 00:00:22,062
看到了有接口这个东西

11
00:00:22,062 --> 00:00:25,110
那么今天我们举一个稍微复杂的业务场景

12
00:00:25,110 --> 00:00:27,832
看看接口到底是如何发挥作用的

13
00:00:27,832 --> 00:00:30,260
这个案例就是电商推荐系统

14
00:00:30,260 --> 00:00:32,100
但只是一个简单的框架

15
00:00:32,100 --> 00:00:33,657
一个小 demo 而已

16
00:00:33,657 --> 00:00:36,030
先简单介绍一下推荐流程

17
00:00:36,030 --> 00:00:37,770
大致上分为三步

18
00:00:37,770 --> 00:00:40,067
召回、排序和过滤

19
00:00:40,067 --> 00:00:41,280
那么召回呢

20
00:00:41,280 --> 00:00:45,720
就是从全部的这个带推荐的物品里面呢

21
00:00:45,720 --> 00:00:49,202
先挑选出一些比较小的集合

22
00:00:49,202 --> 00:00:50,990
这个集合里面的元素呢

23
00:00:50,990 --> 00:00:53,030
是用户可能比较感兴趣

24
00:00:53,030 --> 00:00:54,160
只是可能

25
00:00:54,160 --> 00:00:55,450
而排序呢

26
00:00:55,450 --> 00:00:58,025
则是考虑很多因素

27
00:00:58,025 --> 00:01:01,530
对召回都说集合进行一个金

28
00:01:01,530 --> 00:01:03,032
更精细的排序

29
00:01:03,032 --> 00:01:06,740
那么我们认为排斥结果是绝对可靠的

30
00:01:06,740 --> 00:01:09,810
排在前面的就一定是用户最喜欢的

31
00:01:09,810 --> 00:01:11,800
所以呢，就把排在最前面的

32
00:01:11,800 --> 00:01:12,800
比方前几十个

33
00:01:12,800 --> 00:01:15,177
前一两百个返回给前端

34
00:01:15,177 --> 00:01:18,030
在返回之前再过一次过滤

35
00:01:18,030 --> 00:01:20,250
这个过滤呢，就是一些人工

36
00:01:20,250 --> 00:01:23,550
跟业务相关的规则会比较简单

37
00:01:23,550 --> 00:01:25,890
比如说用户已经看过的

38
00:01:25,890 --> 00:01:26,870
可能需要过滤掉

39
00:01:26,870 --> 00:01:28,450
用户已经购买过的

40
00:01:28,450 --> 00:01:30,050
可能需要过滤掉啊

41
00:01:30,050 --> 00:01:32,410
用户可能曾经点过不喜欢的啊

42
00:01:32,410 --> 00:01:33,490
需要过滤掉

43
00:01:33,490 --> 00:01:36,485
就类似于这样的过滤规则会比较多

44
00:01:36,485 --> 00:01:38,230
那么每一个过滤规则呢

45
00:01:38,230 --> 00:01:40,590
咱们就顺序的去执行就可以了

46
00:01:40,590 --> 00:01:42,190
每过一个过滤规则

47
00:01:42,190 --> 00:01:44,630
这个推荐集合就会少一部分

48
00:01:44,630 --> 00:01:45,407
数据嘛

49
00:01:45,407 --> 00:01:47,220
比较需要特意说明的就是

50
00:01:47,220 --> 00:01:49,087
前面的召回和排序

51
00:01:49,087 --> 00:01:51,040
这两块会比较复杂一点

52
00:01:51,040 --> 00:01:53,950
其实召回和排序都是在做推荐

53
00:01:53,950 --> 00:01:57,045
只不过他们考虑的这个力度不一样

54
00:01:57,045 --> 00:01:58,250
那召回呢

55
00:01:58,250 --> 00:01:59,950
会比较粗糙一点

56
00:01:59,950 --> 00:02:01,590
他考虑的特征比较少

57
00:02:01,590 --> 00:02:03,240
我举一个最简单的例子

58
00:02:03,240 --> 00:02:05,560
比如说我是一个视频网站

59
00:02:05,560 --> 00:02:07,450
那么我给你推荐什么内容呢

60
00:02:07,450 --> 00:02:08,509
啊，最简单

61
00:02:08,509 --> 00:02:10,916
我就把整个数据库里面

62
00:02:10,916 --> 00:02:13,880
播放量最高的推荐给你，对吧

63
00:02:13,880 --> 00:02:16,800
因为播放量最高就意味着这个最热门，对吧

64
00:02:16,800 --> 00:02:19,710
我把最热门的前几个推荐给你

65
00:02:19,710 --> 00:02:22,160
但这显然是一种很粗糙的推荐策略

66
00:02:22,160 --> 00:02:24,342
因为它不是个性化的吗

67
00:02:24,342 --> 00:02:26,390
他只说整体上大家都喜欢

68
00:02:26,390 --> 00:02:27,830
但对于某一个个体来说

69
00:02:27,830 --> 00:02:29,260
他不一定喜欢，对吧

70
00:02:29,260 --> 00:02:31,045
但是没关系啊，没关系

71
00:02:31,045 --> 00:02:33,710
这个依然可以作为一种召回策略

72
00:02:33,710 --> 00:02:35,620
因为大家都喜欢的

73
00:02:35,620 --> 00:02:37,730
其中总有那么几部

74
00:02:37,730 --> 00:02:39,850
是你当前这个户也喜欢的

75
00:02:39,850 --> 00:02:40,560
对吧

76
00:02:40,560 --> 00:02:43,650
所以这个会产生一个小的集合啊

77
00:02:43,650 --> 00:02:46,870
假如说库里面有100万个物品

78
00:02:46,870 --> 00:02:47,890
100万个视频

79
00:02:47,890 --> 00:02:49,470
那通过这种策略，诶

80
00:02:49,470 --> 00:02:51,170
拿到了200个视频

81
00:02:51,170 --> 00:02:53,745
200个全站最热门的视频

82
00:02:53,745 --> 00:02:55,430
那我们可以把这个账号策略

83
00:02:55,430 --> 00:02:57,130
考虑的再更精细一点

84
00:02:57,130 --> 00:02:58,990
比如说他判断出，哦

85
00:02:58,990 --> 00:03:00,867
你的 IP 来自于北京

86
00:03:00,867 --> 00:03:03,080
那北京的用户喜欢东西

87
00:03:03,080 --> 00:03:06,510
和上海的用户喜欢的东西可能也不一样啊

88
00:03:06,510 --> 00:03:10,390
尤其是一些跟北京当地相关的一些新闻呐

89
00:03:10,390 --> 00:03:10,910
视频呐

90
00:03:10,910 --> 00:03:11,150
是吧

91
00:03:11,150 --> 00:03:12,787
北京人可能更喜欢看

92
00:03:12,787 --> 00:03:13,540
所以呢

93
00:03:13,540 --> 00:03:16,400
我再把这个热门考虑的更精细一点，对吧

94
00:03:16,400 --> 00:03:18,290
我按城市去统计

95
00:03:18,290 --> 00:03:21,887
看看每个城市哪些视频最是最热门的

96
00:03:21,887 --> 00:03:23,690
好，你这个 IP 来自北京

97
00:03:23,690 --> 00:03:26,950
我就把北京最热门的前200个拿出来

98
00:03:26,950 --> 00:03:29,097
又产生一个召回集合

99
00:03:29,097 --> 00:03:32,460
所以第二种召回集合和第一种召回集合

100
00:03:32,460 --> 00:03:35,200
它们可能会存在相同的交集

101
00:03:35,200 --> 00:03:38,035
但也可能又存在着不同的内容，对吧

102
00:03:38,035 --> 00:03:40,440
所以最终呢，各种召回策略

103
00:03:40,440 --> 00:03:41,520
他们这个结果呀

104
00:03:41,520 --> 00:03:43,420
需要合在一起排重

105
00:03:43,420 --> 00:03:47,060
好，刚才举的这两个都是基于热度吧

106
00:03:47,060 --> 00:03:49,280
再来举一个稍微复杂点的

107
00:03:49,280 --> 00:03:50,680
比方说这个用户啊

108
00:03:50,680 --> 00:03:54,200
他曾经点击过或者购买过哪个商品

109
00:03:54,200 --> 00:03:58,940
然后我去找出跟这些商品比较相似的商品

110
00:03:58,940 --> 00:04:02,302
比方说你曾经购买过一本购员的书

111
00:04:02,302 --> 00:04:03,250
那下次呢

112
00:04:03,250 --> 00:04:05,410
我给你推荐另外一本勾圆的书

113
00:04:05,410 --> 00:04:07,210
那么在实际的工作当中啊

114
00:04:07,210 --> 00:04:08,410
这种召回策

115
00:04:08,410 --> 00:04:10,070
比我刚才举的这三个例子

116
00:04:10,070 --> 00:04:11,470
要更加复杂一点

117
00:04:11,470 --> 00:04:12,992
非常多、非常多

118
00:04:12,992 --> 00:04:15,980
这个就需要靠算法工程师去开动脑筋

119
00:04:15,980 --> 00:04:17,780
结合他们的具体业务

120
00:04:17,780 --> 00:04:20,488
去开发更多的召回策略

121
00:04:20,488 --> 00:04:21,820
然后讲排序

122
00:04:21,820 --> 00:04:24,010
那么排序相对召回来说

123
00:04:24,010 --> 00:04:26,010
他考虑的因素就特别多

124
00:04:26,010 --> 00:04:26,690
就非常多了

125
00:04:26,690 --> 00:04:31,045
因为排序是最终的那个决策的这个环节嘛

126
00:04:31,045 --> 00:04:34,060
召回的结果并不一定都会推荐给用户

127
00:04:34,060 --> 00:04:35,810
到底要不要给用户展示

128
00:04:35,810 --> 00:04:37,762
完全取决于排序这一步

129
00:04:37,762 --> 00:04:40,720
而且排序呢，它只有一个策略

130
00:04:40,720 --> 00:04:42,420
通常是一个模型吧

131
00:04:42,420 --> 00:04:43,980
什么决策树模型啊

132
00:04:43,980 --> 00:04:45,902
什么深度学习模型啊

133
00:04:45,902 --> 00:04:47,670
所以呢，这个模型里

134
00:04:47,670 --> 00:04:49,690
他要考虑的因素就特别多

135
00:04:49,690 --> 00:04:50,290
特别多啊

136
00:04:50,290 --> 00:04:53,567
他需要把能考虑的因素全部考虑进来

137
00:04:53,567 --> 00:04:55,850
那么它对谁排序呢

138
00:04:55,850 --> 00:04:58,560
就对前面各个召回策略

139
00:04:58,560 --> 00:05:01,375
他们召回的这个并集进行一个排序

140
00:05:01,375 --> 00:05:02,620
我们假设一下

141
00:05:02,620 --> 00:05:05,570
数据库里面总共有100万个商品

142
00:05:05,570 --> 00:05:08,170
那么这么多的召回策略

143
00:05:08,170 --> 00:05:09,250
他们召回结果

144
00:05:09,250 --> 00:05:13,460
合并在一起是1000个商品是吧

145
00:05:13,460 --> 00:05:16,330
从100万就整为了1000个

146
00:05:16,330 --> 00:05:19,130
我把这1000个扔给我的排序模型

147
00:05:19,130 --> 00:05:20,890
那排序模型对这1000个

148
00:05:20,890 --> 00:05:23,610
再进行一个精细化排序啊

149
00:05:23,610 --> 00:05:25,830
因为这个排序结果我就认为是用户

150
00:05:25,830 --> 00:05:27,490
是当前这个用户啊

151
00:05:27,490 --> 00:05:29,110
他最喜欢的排在最前

152
00:05:29,110 --> 00:05:30,550
个性化排序嘛

153
00:05:30,550 --> 00:05:32,600
那么对这1000个排好序之后

154
00:05:32,600 --> 00:05:34,650
我截取前200个

155
00:05:34,650 --> 00:05:36,530
最靠前的前200个啊

156
00:05:36,530 --> 00:05:39,110
扔给这个过滤环节

157
00:05:39,110 --> 00:05:41,840
那过滤完之后可能还剩下150个

158
00:05:41,840 --> 00:05:44,635
好，我就把这150个展示给用户

159
00:05:44,635 --> 00:05:47,360
那么由于这个排序它用到模型嘛

160
00:05:47,360 --> 00:05:50,172
大模型会非常消耗 CPU 

161
00:05:50,172 --> 00:05:52,350
计算速度会非常慢

162
00:05:52,350 --> 00:05:56,475
所以呢，带着排序的集合通常要很小

163
00:05:56,475 --> 00:05:58,950
你不能说我对这个数据库里面的

164
00:05:58,950 --> 00:06:00,430
这100万个商品啊

165
00:06:00,430 --> 00:06:04,100
全部跟当前这个用户过一下我的排序模型

166
00:06:04,100 --> 00:06:04,840
算一下，哎

167
00:06:04,840 --> 00:06:06,760
我这个用户对每一个商品的一个

168
00:06:06,760 --> 00:06:08,395
感兴趣程度是多少

169
00:06:08,395 --> 00:06:11,380
这样的话，100万个商品你算完啊

170
00:06:11,380 --> 00:06:12,240
都几个小时了

171
00:06:12,240 --> 00:06:12,930
很慢

172
00:06:12,930 --> 00:06:15,992
所以呢，前面需要加一个召回嘛

173
00:06:15,992 --> 00:06:17,670
召回是一种粗的策略

174
00:06:17,670 --> 00:06:20,770
他先把那些大概率用户不喜欢的

175
00:06:20,770 --> 00:06:22,112
先排除在外面

176
00:06:22,112 --> 00:06:24,492
先产生一个小的集合

177
00:06:24,492 --> 00:06:26,350
所以各个招考策略

178
00:06:26,350 --> 00:06:28,370
他们是可以并行执行的啊

179
00:06:28,370 --> 00:06:29,730
并行执行完之后的话

180
00:06:29,730 --> 00:06:31,650
再求一个并集嘛

181
00:06:31,650 --> 00:06:33,310
扔给一个排序模型

182
00:06:33,310 --> 00:06:34,510
那排序模型呢

183
00:06:34,510 --> 00:06:36,250
后面过多个过滤规则

184
00:06:36,250 --> 00:06:38,610
那每一个过滤规则他们得串行执行

185
00:06:38,610 --> 00:06:40,030
因为过滤嘛，这个没办法搞

186
00:06:40,030 --> 00:06:42,430
并行排序之后有200个

187
00:06:42,430 --> 00:06:44,170
经过这个过滤规则一

188
00:06:44,170 --> 00:06:45,450
还剩下190个

189
00:06:45,450 --> 00:06:46,850
再经过过滤规则二

190
00:06:46,850 --> 00:06:48,785
可能还剩下160个

191
00:06:48,785 --> 00:06:49,990
电商推荐嘛

192
00:06:49,990 --> 00:06:52,030
那么我们首先需要定义好

193
00:06:52,030 --> 00:06:53,430
一个最基本的结构体

194
00:06:53,430 --> 00:06:54,910
就是商品

195
00:06:54,910 --> 00:06:59,777
商品本身包含 id 和名称等等各种属性

196
00:06:59,777 --> 00:07:02,880
然后呢，这边写了一个 RECOMMENDER 啊

197
00:07:02,880 --> 00:07:05,180
他是负责推荐的这样一个结构体

198
00:07:05,180 --> 00:07:07,782
看下它里面包含哪些成员

199
00:07:07,782 --> 00:07:10,935
三个召回、排序和过滤

200
00:07:10,935 --> 00:07:14,050
这里面不管是 recorder 、 salter 还是 filter 

201
00:07:14,050 --> 00:07:17,685
他们实际上都是我自己写的一个接口

202
00:07:17,685 --> 00:07:19,850
比如说召回啊

203
00:07:19,850 --> 00:07:20,770
传一个数字 N 

204
00:07:20,770 --> 00:07:22,290
我要召回 N 个结果

205
00:07:22,290 --> 00:07:25,320
那这个结果呢，自然是一个切片

206
00:07:25,320 --> 00:07:26,900
再来看排序

207
00:07:26,900 --> 00:07:29,580
排序的话就说你扔给我一个集合切片

208
00:07:29,580 --> 00:07:32,247
我返回排序之后的这个切片

209
00:07:32,247 --> 00:07:33,850
再来看过滤

210
00:07:33,850 --> 00:07:36,200
过滤的话我拿到一个切片

211
00:07:36,200 --> 00:07:37,310
拿到一个集合

212
00:07:37,310 --> 00:07:38,180
然后过滤

213
00:07:38,180 --> 00:07:41,280
过滤之后的话是一个更小的集合

214
00:07:41,280 --> 00:07:44,560
所以参数和返回值都是商品构成的

215
00:07:44,560 --> 00:07:46,870
欺骗这个地方全部指针了

216
00:07:46,870 --> 00:07:48,110
那么指针的好处在于

217
00:07:48,110 --> 00:07:51,602
它可以减小结构体拷贝的开销

218
00:07:51,602 --> 00:07:53,000
这里面有三个目录

219
00:07:53,000 --> 00:07:54,950
filter 、 record 和 salt 

220
00:07:54,950 --> 00:07:55,960
每个目录下

221
00:07:55,960 --> 00:07:58,980
除了定义刚才所展示的这个接口之外

222
00:07:58,980 --> 00:08:04,710
还实现了若干种结构体来实现相应的接口

223
00:08:04,710 --> 00:08:08,205
我们来看这个推荐主的框架代码

224
00:08:08,205 --> 00:08:09,250
rock banner 

225
00:08:09,250 --> 00:08:11,630
还有一个最核心函数就是 rank 

226
00:08:11,630 --> 00:08:13,000
产生推荐结果

227
00:08:13,000 --> 00:08:15,695
首先搞了一个 map 

228
00:08:15,695 --> 00:08:19,030
来容纳多路召回的召回结果

229
00:08:19,030 --> 00:08:23,260
便利每一路召回就是便利这个 recorders 嘛

230
00:08:23,260 --> 00:08:28,140
每一个元素呢，都是一个 recorder 接口的具体实现

231
00:08:28,140 --> 00:08:31,270
核心是调用对应的 record 方法

232
00:08:31,270 --> 00:08:33,000
来得到一个召回结果

233
00:08:33,000 --> 00:08:34,770
然后把这个召回结果呢

234
00:08:34,770 --> 00:08:36,260
切片嘛，变成切片

235
00:08:36,260 --> 00:08:38,400
把它放到这个 map 里面去

236
00:08:38,400 --> 00:08:39,679
放到 map 里面去

237
00:08:39,679 --> 00:08:42,030
map 的 key 呢，是商品的 id 

238
00:08:42,030 --> 00:08:43,870
因为不同的召回策略

239
00:08:43,870 --> 00:08:46,010
它可能会返回相同的商品

240
00:08:46,010 --> 00:08:47,840
我需要通过一个 map 

241
00:08:47,840 --> 00:08:50,240
按照 id 来进行排除

242
00:08:50,240 --> 00:08:52,280
然后呢，打印个日志啊

243
00:08:52,280 --> 00:08:54,160
说这个某一个账号策略

244
00:08:54,160 --> 00:08:55,720
他一共花了多少时间

245
00:08:55,720 --> 00:08:59,085
他一共召回了几件商品

246
00:08:59,085 --> 00:09:01,780
最后呢，把所有的召回都走完之后

247
00:09:01,780 --> 00:09:03,640
再打一个总的日志啊

248
00:09:03,640 --> 00:09:06,725
排成之后总共召回了几件商品

249
00:09:06,725 --> 00:09:09,840
然后就开始去调我们的排序

250
00:09:09,840 --> 00:09:11,860
而排序的话需要输入个切片嘛

251
00:09:11,860 --> 00:09:15,900
所以呢，需要先把这个 map 先转成一个切片

252
00:09:15,900 --> 00:09:20,320
便利 Mac 逐一的 append 到这个切片里面去

253
00:09:20,320 --> 00:09:22,260
丢轨 salt 排序

254
00:09:22,260 --> 00:09:24,040
然后打开日志是吧

255
00:09:24,040 --> 00:09:27,575
这个排序呢，总共消耗了多少时间

256
00:09:27,575 --> 00:09:32,020
接下来就是去依次的遍历每一条过滤规则

257
00:09:32,020 --> 00:09:33,260
本来排序之后

258
00:09:33,260 --> 00:09:35,420
这个集合长度假设是100

259
00:09:35,420 --> 00:09:37,660
那么经过第一个过滤规则之后

260
00:09:37,660 --> 00:09:39,040
还剩下90个结果

261
00:09:39,040 --> 00:09:40,620
再经过第二个过滤规则

262
00:09:40,620 --> 00:09:42,300
还剩下80个结果

263
00:09:42,300 --> 00:09:45,707
那最终剩下来的就是最终的这个推荐结果

264
00:09:45,707 --> 00:09:47,550
所以对于每一个过滤规则而言

265
00:09:47,550 --> 00:09:49,810
我也会打印一下这个过滤规则

266
00:09:49,810 --> 00:09:51,210
它耗时有多少

267
00:09:51,210 --> 00:09:53,467
它过滤掉了几件商品

268
00:09:53,467 --> 00:09:56,300
好，从目前的这个代码里面来看

269
00:09:56,300 --> 00:09:57,140
我们发现呢

270
00:09:57,140 --> 00:10:00,720
它整体上就是在面向接口编程啊

271
00:10:00,720 --> 00:10:05,290
这里面的 recorders 、 salter 和 filters 全部是接口

272
00:10:05,290 --> 00:10:07,530
我根本就没有关心这个接口的

273
00:10:07,530 --> 00:10:08,910
具体实现是什么

274
00:10:08,910 --> 00:10:11,875
也没有关心这个切片里面有几个元素

275
00:10:11,875 --> 00:10:14,230
这就是架构师需要干的事情

276
00:10:14,230 --> 00:10:18,852
架构师就只负责去把控整体的逻辑架构

277
00:10:18,852 --> 00:10:21,640
而具体实现呢，可以交给不同的人

278
00:10:21,640 --> 00:10:24,215
不同的小弟小妹分头去实现

279
00:10:24,215 --> 00:10:27,880
比如说这里面有好几个召唤策略

280
00:10:27,880 --> 00:10:29,760
张三负责两三个

281
00:10:29,760 --> 00:10:32,007
李四负责223个

282
00:10:32,007 --> 00:10:33,950
他们完全可以并行执行

283
00:10:33,950 --> 00:10:35,020
互不干扰

284
00:10:35,020 --> 00:10:39,590
它们的代码也是放在不同的 go 文件里面的

285
00:10:39,590 --> 00:10:41,975
维护起来也更加清晰

286
00:10:41,975 --> 00:10:44,100
不会互相产生影响

287
00:10:44,100 --> 00:10:46,230
同时呢，每一个召回策略

288
00:10:46,230 --> 00:10:48,250
他只需要关注自己的这个

289
00:10:48,250 --> 00:10:50,160
召回逻辑本身就可以了

290
00:10:50,160 --> 00:10:52,770
你像这种打印每一个账户策略

291
00:10:52,770 --> 00:10:53,910
账号了几件商品

292
00:10:53,910 --> 00:10:55,530
这种通用性代码

293
00:10:55,530 --> 00:10:59,140
完全可以放在框架层面去执行

294
00:10:59,140 --> 00:11:02,590
如果放在每个召回结构体里面

295
00:11:02,590 --> 00:11:04,080
去打这个字的话

296
00:11:04,080 --> 00:11:06,090
大家还需要事先做好约定

297
00:11:06,090 --> 00:11:08,690
这个痣到底长成什么样子

298
00:11:08,690 --> 00:11:11,030
因为将来我要去通过正则表达式

299
00:11:11,030 --> 00:11:12,270
解析这个日志嘛

300
00:11:12,270 --> 00:11:15,310
那你必须保持格式统一

301
00:11:15,310 --> 00:11:18,750
而放在框架层面就解决了这个问题

302
00:11:18,750 --> 00:11:22,130
看看，最终我要去构建 RECOMMANDER 一个实例

303
00:11:22,130 --> 00:11:25,870
然后呢，通过调它的这个 react 来产生推荐结果

304
00:11:25,870 --> 00:11:27,790
在构造这个结构体的时候

305
00:11:27,790 --> 00:11:30,510
核心是要给他三个成员变量赋值吗

306
00:11:30,510 --> 00:11:33,325
records 、 SER 和 filters 

307
00:11:33,325 --> 00:11:35,210
那么它是一个切片

308
00:11:35,210 --> 00:11:37,050
切片里面的每一个元素呢

309
00:11:37,050 --> 00:11:40,205
就是一个 recorder 接口的具体实现

310
00:11:40,205 --> 00:11:41,910
你可以说今天啊

311
00:11:41,910 --> 00:11:44,130
我开发了两种账号策略

312
00:11:44,130 --> 00:11:47,795
把这两个直接放在签约里面就可以了

313
00:11:47,795 --> 00:11:49,850
产品上线之后一周啊

314
00:11:49,850 --> 00:11:51,750
这个产品经理有一个新的想法

315
00:11:51,750 --> 00:11:53,730
哎，又开发了一个新的账货策略

316
00:11:53,730 --> 00:11:57,570
那直接在对应的目录下面是吧

317
00:11:57,570 --> 00:11:58,690
在这 call 目录下面

318
00:11:58,690 --> 00:12:01,070
再加一个新的构文件就可以了

319
00:12:01,070 --> 00:12:03,130
然后呢，把那个结构体是吧

320
00:12:03,130 --> 00:12:05,480
再放到这个切片里面来

321
00:12:05,480 --> 00:12:07,720
切片里面有三个元素就可以了

322
00:12:07,720 --> 00:12:09,010
可能过了段时间

323
00:12:09,010 --> 00:12:11,020
某种带货策略需要下线

324
00:12:11,020 --> 00:12:11,950
下划线的话

325
00:12:11,950 --> 00:12:14,110
对应这边的 go 代码不用动是吧

326
00:12:14,110 --> 00:12:15,530
还放着不用删掉

327
00:12:15,530 --> 00:12:17,230
直接在这个地方

328
00:12:17,230 --> 00:12:20,577
把对应的 recorder 注释掉就可以了

329
00:12:20,577 --> 00:12:22,000
可能过了段时间

330
00:12:22,000 --> 00:12:23,460
产品经理又反复

331
00:12:23,460 --> 00:12:24,860
他说要不再加回来吧

332
00:12:24,860 --> 00:12:27,197
那你再把注释打开就可以了

333
00:12:27,197 --> 00:12:29,110
所以代码改动非常清晰

334
00:12:29,110 --> 00:12:30,542
非常方便

335
00:12:30,542 --> 00:12:33,040
如果说你没有采用这种架构

336
00:12:33,040 --> 00:12:35,360
比方说你把所有的账号策略

337
00:12:35,360 --> 00:12:38,442
全部放在了一个 go 代码里面

338
00:12:38,442 --> 00:12:40,370
所有的逻辑混在一

339
00:12:40,370 --> 00:12:41,980
本身就不便阅读

340
00:12:41,980 --> 00:12:46,250
更并不是说将来某些策略需要反复的上线

341
00:12:46,250 --> 00:12:46,610
下线

342
00:12:46,610 --> 00:12:49,500
你需要反复的注视再去注释

343
00:12:49,500 --> 00:12:51,500
好没出错

344
00:12:51,500 --> 00:12:55,037
所以这是一个使用接口的典型应用场景

345
00:12:55,037 --> 00:12:56,550
当某一个步

346
00:12:56,550 --> 00:12:58,400
它有多种实现方案时

347
00:12:58,400 --> 00:12:59,640
可以把这个步骤呢

348
00:12:59,640 --> 00:13:01,972
用接口来进行抽象

349
00:13:01,972 --> 00:13:04,290
各种不同的实践方案都来实现

350
00:13:04,290 --> 00:13:07,080
这个接口就是一个单独的结构体

351
00:13:07,080 --> 00:13:08,570
代码更好维护
