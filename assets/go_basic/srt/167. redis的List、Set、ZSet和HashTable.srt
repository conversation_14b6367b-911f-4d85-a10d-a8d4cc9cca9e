1
00:00:00,120 --> 00:00:03,600
再来看 REDIS 支持的一些比较复杂的数据类型

2
00:00:03,600 --> 00:00:05,520
那第一个呢，是 list 啊

3
00:00:05,520 --> 00:00:07,220
这个 value 呢，可以是 list 

4
00:00:07,220 --> 00:00:09,085
好，这边搞了一个 K 

5
00:00:09,085 --> 00:00:11,440
然后呢，这边创建了一个切片

6
00:00:11,440 --> 00:00:12,620
那这个切片的话

7
00:00:12,620 --> 00:00:14,720
里面存放的元素类型呢

8
00:00:14,720 --> 00:00:15,700
有的是整数

9
00:00:15,700 --> 00:00:16,880
有的是字符串

10
00:00:16,880 --> 00:00:18,930
因为这个是 any 嘛啊

11
00:00:18,930 --> 00:00:19,990
任意类型都可以

12
00:00:19,990 --> 00:00:22,467
而且里面会存在一些重复的元素

13
00:00:22,467 --> 00:00:25,890
我要把这些个切片里面的元素呢

14
00:00:25,890 --> 00:00:29,120
放到 REDIS 的一个 list 数组里面去

15
00:00:29,120 --> 00:00:30,300
看看怎么放啊

16
00:00:30,300 --> 00:00:34,160
这边啊，是通过调用 r push ， push 嘛

17
00:00:34,160 --> 00:00:36,517
就表示 V 8追加对吧

18
00:00:36,517 --> 00:00:40,470
那可以想象它应该是一个集合 key 传进来

19
00:00:40,470 --> 00:00:41,470
然后 value 的话呢

20
00:00:41,470 --> 00:00:43,950
这边是把这个切片加上三个点

21
00:00:43,950 --> 00:00:45,350
转成不定长参数

22
00:00:45,350 --> 00:00:46,870
因为这个 r push 啊

23
00:00:46,870 --> 00:00:51,350
它本身最后一个参数就是由空接口构成的

24
00:00:51,350 --> 00:00:52,730
不定长参数吗

25
00:00:52,730 --> 00:00:56,440
好，这是往 list 往数组里面去追加

26
00:00:56,440 --> 00:00:58,710
那是往尾部追加吗

27
00:00:58,710 --> 00:01:00,390
还是往头部去追加呢

28
00:01:00,390 --> 00:01:02,950
取决于这个 RR 嘛

29
00:01:02,950 --> 00:01:07,610
表示 right 就右侧 a one 链表的右侧追加

30
00:01:07,610 --> 00:01:10,067
所以是向是往尾部去追加

31
00:01:10,067 --> 00:01:12,550
如果你换成 L 的话

32
00:01:12,550 --> 00:01:14,210
left 就是往左侧啊

33
00:01:14,210 --> 00:01:16,540
往表的首部去追加

34
00:01:16,540 --> 00:01:20,970
如果说这个 K 它事先不存在

35
00:01:20,970 --> 00:01:22,770
那么你这个 push 啊

36
00:01:22,770 --> 00:01:25,730
它会先去创建这个 K 啊

37
00:01:25,730 --> 00:01:27,570
先去创建一个空的数组

38
00:01:27,570 --> 00:01:30,460
然后呢，再往里面去追加新元素

39
00:01:30,460 --> 00:01:33,550
好，你通过连续多次的调用 push 

40
00:01:33,550 --> 00:01:34,830
就可以把列表里面呢

41
00:01:34,830 --> 00:01:36,410
去添加很多元素

42
00:01:36,410 --> 00:01:38,090
然后的话就开始读取了嘛

43
00:01:38,090 --> 00:01:42,550
那读取的话，它允许就是读取某一个区间段

44
00:01:42,550 --> 00:01:45,910
通过 range 函数 L 这个 L 啊

45
00:01:45,910 --> 00:01:50,337
此时这个 L 啊跟这个 l push l 就是两个含义了

46
00:01:50,337 --> 00:01:53,050
这个 L 表示是 left 左侧

47
00:01:53,050 --> 00:01:56,630
而这个 L 呢，它表示的是 list 啊

48
00:01:56,630 --> 00:01:59,030
我要去便利一个 list 

49
00:01:59,030 --> 00:02:00,770
range 嘛，通常表示遍历

50
00:02:00,770 --> 00:02:02,110
遍历一个链表

51
00:02:02,110 --> 00:02:03,790
那么把 K 传进来

52
00:02:03,790 --> 00:02:07,030
你要指定遍历这个列表的哪一段啊

53
00:02:07,030 --> 00:02:09,550
把开始位置和结束位置传过来

54
00:02:09,550 --> 00:02:11,810
开始位置这边指的是零啊

55
00:02:11,810 --> 00:02:15,280
从第0号元素结束位置的指定是一

56
00:02:15,280 --> 00:02:18,082
一的话就表示最后一个元素

57
00:02:18,082 --> 00:02:19,920
需要特别注意的是

58
00:02:19,920 --> 00:02:22,830
它这个一啊，是一个闭区间

59
00:02:22,830 --> 00:02:25,990
就说前 B 后面也是 B 啊

60
00:02:25,990 --> 00:02:29,212
双闭区间就包含最后一个元素

61
00:02:29,212 --> 00:02:32,160
那 for 的话表示倒数第二个元素嘛

62
00:02:32,160 --> 00:02:38,365
然后通过调 result 把这个集合呢，转成 string 切片

63
00:02:38,365 --> 00:02:41,980
所以 VR 里面每一个元素都是一个字符串

64
00:02:41,980 --> 00:02:44,610
哪怕这些一、三、四

65
00:02:44,610 --> 00:02:46,220
它们全部是字符串

66
00:02:46,220 --> 00:02:47,970
如果你想转成 int 

67
00:02:47,970 --> 00:02:51,415
你还得自己通过 STRUCV 来进行转换

68
00:02:51,415 --> 00:02:54,770
那这个 value 除了是普通的 list 之外

69
00:02:54,770 --> 00:02:55,910
还可以是 set 

70
00:02:55,910 --> 00:02:58,760
其实 set 跟这个 list 很类似啦

71
00:02:58,760 --> 00:02:59,860
都是一个集合嘛

72
00:02:59,860 --> 00:03:01,990
只不过是说 set 里面呢

73
00:03:01,990 --> 00:03:04,150
它不允许出现重复的元素

74
00:03:04,150 --> 00:03:06,495
而且 set 也不保证顺序

75
00:03:06,495 --> 00:03:09,070
list 当初是用的 push 函数

76
00:03:09,070 --> 00:03:10,970
而现在改成 set 之后呢

77
00:03:10,970 --> 00:03:13,050
应该调用 ADD 啊

78
00:03:13,050 --> 00:03:17,487
这个 S 表示 set 就往 set 里面去添加元素

79
00:03:17,487 --> 00:03:19,500
依然呢，传的是不定长参数

80
00:03:19,500 --> 00:03:21,740
我们直接把这个切片呢

81
00:03:21,740 --> 00:03:23,380
转成不定长参数

82
00:03:23,380 --> 00:03:26,000
依然是支持各种不同的数据类型

83
00:03:26,000 --> 00:03:27,450
因为不管什么数据选

84
00:03:27,450 --> 00:03:30,010
你最后都是转成了字符串嘛

85
00:03:30,010 --> 00:03:32,290
本来呢，这里面是存在重复元素的

86
00:03:32,290 --> 00:03:34,590
但是的话写进去之后

87
00:03:34,590 --> 00:03:36,692
它会自动的进行排除

88
00:03:36,692 --> 00:03:39,850
里面就只有一个三、一个一

89
00:03:39,850 --> 00:03:41,660
对 set 来说

90
00:03:41,660 --> 00:03:43,460
我们可以判断某一个元素

91
00:03:43,460 --> 00:03:45,200
是否在这个集合里面

92
00:03:45,200 --> 00:03:48,580
调用的是这个 s is member 

93
00:03:48,580 --> 00:03:50,870
S 表示 set is member 

94
00:03:50,870 --> 00:03:54,960
判断它是否是某个集合的一个成员

95
00:03:54,960 --> 00:03:56,510
把 key 传进来

96
00:03:56,510 --> 00:04:00,990
然后你要判断的那个元素呢，就是 value 啊

97
00:04:00,990 --> 00:04:03,230
这个 value 得转成空接口吧

98
00:04:03,230 --> 00:04:05,790
animation 。比方说我要判断一下，诶

99
00:04:05,790 --> 00:04:06,750
这个一呀

100
00:04:06,750 --> 00:04:09,557
它在不在这个集合里面

101
00:04:09,557 --> 00:04:10,320
注意啊

102
00:04:10,320 --> 00:04:12,420
在 REDIS 里

103
00:04:12,420 --> 00:04:14,840
这些数字全部是字符串

104
00:04:14,840 --> 00:04:18,730
那现在你拿了一个数字来进行判断

105
00:04:18,730 --> 00:04:21,920
你觉得这个数字在这个集合里面吗

106
00:04:21,920 --> 00:04:23,287
在啊，在

107
00:04:23,287 --> 00:04:26,350
其实啊，就是当我们你在 go 代码里面

108
00:04:26,350 --> 00:04:27,290
你感觉是数字

109
00:04:27,290 --> 00:04:32,350
但实际上当你把这个查询命令传给 REDIS 时啊

110
00:04:32,350 --> 00:04:34,670
他已经把这个一呢转成字符串了

111
00:04:34,670 --> 00:04:37,510
它实际上是拿着字符串一询问 REDIS 

112
00:04:37,510 --> 00:04:39,070
那 REDIS 自然返回 true 嘛

113
00:04:39,070 --> 00:04:40,590
啊，告诉你它在里面

114
00:04:40,590 --> 00:04:41,130
当然了

115
00:04:41,130 --> 00:04:43,750
如果你直接拿的就是字符串一的话

116
00:04:43,750 --> 00:04:45,295
那当然也在了，对吧

117
00:04:45,295 --> 00:04:46,930
二的话，二应该不在啊

118
00:04:46,930 --> 00:04:48,780
因为刚才我发现没有二

119
00:04:48,780 --> 00:04:53,500
对于 set 也可以便利直接使用 members 啊

120
00:04:53,500 --> 00:04:54,870
获得所有的成员

121
00:04:54,870 --> 00:04:56,390
把 key 呢传进来

122
00:04:56,390 --> 00:05:00,050
这是获得 set 里面的所有成员

123
00:05:00,050 --> 00:05:01,750
然后对于 set 来说

124
00:05:01,750 --> 00:05:03,150
它还可以什么呢

125
00:05:03,150 --> 00:05:06,667
它还可以进行那个求差几求交集

126
00:05:06,667 --> 00:05:10,880
比方说我在打算搞第二个 site key 呢

127
00:05:10,880 --> 00:05:11,880
稍微变一变

128
00:05:11,880 --> 00:05:14,980
它里面的元素呢，是这四个元素

129
00:05:14,980 --> 00:05:17,855
把它加到 set 这个集合里面去

130
00:05:17,855 --> 00:05:20,217
然后就有两个 set ，对吧

131
00:05:20,217 --> 00:05:21,490
两个 set 呢

132
00:05:21,490 --> 00:05:23,290
它们可以求差几啊

133
00:05:23,290 --> 00:05:26,150
就是指定第一个 K 和第二个 K 

134
00:05:26,150 --> 00:05:28,270
然后呢，他们求这个 SDF 啊

135
00:05:28,270 --> 00:05:29,360
求 DF 

136
00:05:29,360 --> 00:05:30,050
当然了

137
00:05:30,050 --> 00:05:32,270
这个低复位的话是有前后顺序的

138
00:05:32,270 --> 00:05:37,170
你是 K 减 K 2呢还是 K 2减 K 

139
00:05:37,170 --> 00:05:38,250
这个是不一样的

140
00:05:38,250 --> 00:05:41,247
你 K 减 K 2的话就表示是说

141
00:05:41,247 --> 00:05:42,580
那么这个集合呢

142
00:05:42,580 --> 00:05:46,270
它仅在 K 所对应的集合里面有

143
00:05:46,270 --> 00:05:47,730
在第二个集合里面没有

144
00:05:47,730 --> 00:05:49,310
如果反过来的话

145
00:05:49,310 --> 00:05:52,030
表示仅在第二个集合里面有

146
00:05:52,030 --> 00:05:53,340
第一个集合里面没有

147
00:05:53,340 --> 00:05:56,120
这是求两个集合的差

148
00:05:56,120 --> 00:05:57,850
还可以求交集了啊

149
00:05:57,850 --> 00:06:01,510
调这个英特尔求两个集合的交集

150
00:06:01,510 --> 00:06:04,820
所以我们感觉一下什么情况下适合使用 list 

151
00:06:04,820 --> 00:06:06,520
什么情况下适合使用 start 

152
00:06:06,520 --> 00:06:07,990
就如果说

153
00:06:07,990 --> 00:06:11,782
呃，你希望这个集合里面元素啊，互不重复

154
00:06:11,782 --> 00:06:14,290
但是呢，你又不想自己去判断啊

155
00:06:14,290 --> 00:06:16,410
这个元素是否已经存在过了，对吧

156
00:06:16,410 --> 00:06:18,830
你就直接交给 REDIS 啊

157
00:06:18,830 --> 00:06:20,870
直接使用 set 这种数据式

158
00:06:20,870 --> 00:06:24,280
你就拼命的通过调用 ADD 

159
00:06:24,280 --> 00:06:25,540
往里面添加就可以了

160
00:06:25,540 --> 00:06:28,140
它内部会自动的帮你完成排除

161
00:06:28,140 --> 00:06:31,055
这是适合使用 sans 的第一个场景

162
00:06:31,055 --> 00:06:32,420
第二个场景就是说

163
00:06:32,420 --> 00:06:34,600
假如说你将来啊，有这样的需求

164
00:06:34,600 --> 00:06:37,750
打算去进行一个求差集呀

165
00:06:37,750 --> 00:06:38,770
求交集啊

166
00:06:38,770 --> 00:06:40,130
有这样的需求的话

167
00:06:40,130 --> 00:06:41,910
也可以使用 site 

168
00:06:41,910 --> 00:06:44,160
而使用 list 的好处在于

169
00:06:44,160 --> 00:06:46,520
它可以天然的帮你保持顺序吗

170
00:06:46,520 --> 00:06:48,600
你当初是怎么往里面插入的

171
00:06:48,600 --> 00:06:50,310
到时候你便利的话

172
00:06:50,310 --> 00:06:52,170
取出来还是什么样的顺序

173
00:06:52,170 --> 00:06:54,532
但 set 就不能够保证顺序了

174
00:06:54,532 --> 00:06:57,180
然后还有一种叫做 z set 

175
00:06:57,180 --> 00:07:01,202
那这个 z set 实际上是一种有序的 set 

176
00:07:01,202 --> 00:07:03,390
它既可以保证元素不重复

177
00:07:03,390 --> 00:07:05,230
同时呢，又可以保证顺序

178
00:07:05,230 --> 00:07:07,880
而这个顺序是怎样保证的

179
00:07:07,880 --> 00:07:11,940
它不是简单的按照 value 的元素来进行排序

180
00:07:11,940 --> 00:07:15,402
而是需要有一个特定的字段来标识

181
00:07:15,402 --> 00:07:19,190
比如啊，这边呢，你去构建一个切片

182
00:07:19,190 --> 00:07:19,950
一个集合

183
00:07:19,950 --> 00:07:21,150
那这个切片的话

184
00:07:21,150 --> 00:07:24,020
它必须是 REDIS 点 Z 这种类型

185
00:07:24,020 --> 00:07:25,900
而点 Z 这个类型呢

186
00:07:25,900 --> 00:07:28,360
它包含一个 member 和一个 score 

187
00:07:28,360 --> 00:07:30,940
这个 member 的话可以是任意类型

188
00:07:30,940 --> 00:07:34,420
就是你实际想存储的数据本身就是 member 啊

189
00:07:34,420 --> 00:07:35,140
字符串也好

190
00:07:35,140 --> 00:07:36,537
整形也好，都可以

191
00:07:36,537 --> 00:07:39,290
但是呢，你得提供一个额外的的 score 

192
00:07:39,290 --> 00:07:42,090
到时候啊，他就是按照 score 来进行排序的

193
00:07:42,090 --> 00:07:43,470
比如张三是70

194
00:07:43,470 --> 00:07:44,610
李四是100

195
00:07:44,610 --> 00:07:45,850
王五是80

196
00:07:45,850 --> 00:07:47,950
那实际上排序之后的话

197
00:07:47,950 --> 00:07:50,250
应该是先张三后王五

198
00:07:50,250 --> 00:07:52,132
最后呢，是李四

199
00:07:52,132 --> 00:07:54,420
通过调用 ZI 的啊

200
00:07:54,420 --> 00:07:55,700
刚才是 SI 的

201
00:07:55,700 --> 00:07:57,120
现在是 ZI 的

202
00:07:57,120 --> 00:07:59,760
把知识元素呢添加进去

203
00:07:59,760 --> 00:08:01,230
它内部执行一个排序

204
00:08:01,230 --> 00:08:02,510
然后的话我们进行遍历

205
00:08:02,510 --> 00:08:05,667
遍历的话使用的是 z range 

206
00:08:05,667 --> 00:08:08,280
跟遍历 list 有点类似，对吧

207
00:08:08,280 --> 00:08:11,710
把这个开始和结束位置呢，传进来啊

208
00:08:11,710 --> 00:08:12,990
零表示最开始的

209
00:08:12,990 --> 00:08:15,230
一表示最后一个双倍区间嘛

210
00:08:15,230 --> 00:08:17,812
我们把这个案例打出来看看

211
00:08:17,812 --> 00:08:19,920
跑一下单侧打出来呢

212
00:08:19,920 --> 00:08:21,780
是张三、王五、李四，对吧

213
00:08:21,780 --> 00:08:23,100
跟刚才分析一样的

214
00:08:23,100 --> 00:08:26,760
他是按照 score 升序排列之后再给你返回的

215
00:08:26,760 --> 00:08:27,940
前面我们讲过

216
00:08:27,940 --> 00:08:32,020
如果是一个结构体想存到 REDIS 的 value 里面的

217
00:08:32,020 --> 00:08:35,940
你可以先把这个结构体通过 JSON 转成字符串

218
00:08:35,940 --> 00:08:37,161
再存进去

219
00:08:37,161 --> 00:08:39,570
那其实还有一种更简单的方式

220
00:08:39,570 --> 00:08:40,429
就是直接啊

221
00:08:40,429 --> 00:08:44,580
你拿这个 map 来作为 register value 

222
00:08:44,580 --> 00:08:49,257
因为 REDIS 整体上你可以把它看成是一个大的 map 

223
00:08:49,257 --> 00:08:50,420
K 是字符串

224
00:08:50,420 --> 00:08:54,350
而 value 呢， value 又可以是一个小的 map 

225
00:08:54,350 --> 00:08:55,620
我们看下这个地方啊

226
00:08:55,620 --> 00:08:57,607
这边搞了一个 map 

227
00:08:57,607 --> 00:08:58,930
key 呢是字符串

228
00:08:58,930 --> 00:09:00,810
value 呢，是一个任意类型

229
00:09:00,810 --> 00:09:03,760
包含什么姓名啊、年龄啊、身高啊

230
00:09:03,760 --> 00:09:06,020
它实际上就是一个结构体嘛

231
00:09:06,020 --> 00:09:10,142
那么我通过 h m site 

232
00:09:10,142 --> 00:09:12,460
H 表示是一种哈希表结构啊

233
00:09:12,460 --> 00:09:13,982
M 表示 man 嘛

234
00:09:13,982 --> 00:09:15,930
K 啊， K 是一个字符串

235
00:09:15,930 --> 00:09:18,690
因为所有的 REDK 都是字符串嘛

236
00:09:18,690 --> 00:09:20,730
K 而 value 的话，哎

237
00:09:20,730 --> 00:09:22,020
是一个 map 

238
00:09:22,020 --> 00:09:24,270
这边又搞了第二个学生啊

239
00:09:24,270 --> 00:09:26,450
他也是这样一个 map 

240
00:09:26,450 --> 00:09:30,280
也通过调这个 h m site 

241
00:09:30,280 --> 00:09:32,750
把这个学生二也放进去了

242
00:09:32,750 --> 00:09:34,780
好，既然这个 value 啊

243
00:09:34,780 --> 00:09:37,340
它都是一个 map 对吧

244
00:09:37,340 --> 00:09:41,115
那么我怎么获得这个 map 里面特定的字段呢

245
00:09:41,115 --> 00:09:43,640
比如我想获得学生二

246
00:09:43,640 --> 00:09:46,650
它对应的年龄到底是多少

247
00:09:46,650 --> 00:09:50,340
一般情况我们是通过调用 get 来获得 key 

248
00:09:50,340 --> 00:09:51,140
对应的 value 

249
00:09:51,140 --> 00:09:51,420
对吧

250
00:09:51,420 --> 00:09:53,480
但现在的话，我的 value 是一个 map 

251
00:09:53,480 --> 00:09:56,900
那么呢，你应该改为 h get ，好

252
00:09:56,900 --> 00:09:59,620
h get 把 K 传进来

253
00:09:59,620 --> 00:10:03,640
然后呢，再把这个小 map 里面对应的 K 再传进来

254
00:10:03,640 --> 00:10:04,820
还把 A 级传进来

255
00:10:04,820 --> 00:10:06,130
这样的话获得 value 

256
00:10:06,130 --> 00:10:07,110
一般情况下

257
00:10:07,110 --> 00:10:08,870
value 都是字符串码啊

258
00:10:08,870 --> 00:10:11,200
除非你显示指定 int 

259
00:10:11,200 --> 00:10:13,020
那这样的话，获得的这个 A 级呢

260
00:10:13,020 --> 00:10:15,080
它就是一个整形

261
00:10:15,080 --> 00:10:19,580
或者说你也可以直接把学生二对应的整个 map 

262
00:10:19,580 --> 00:10:21,530
一次性全部拿到

263
00:10:21,530 --> 00:10:25,580
比如我通过调用这个 h get wall 啊

264
00:10:25,580 --> 00:10:26,790
多了一个 wall 嘛

265
00:10:26,790 --> 00:10:28,820
把原始的 key 传进来

266
00:10:28,820 --> 00:10:32,530
这样的话我获得的就是整个 map 

267
00:10:32,530 --> 00:10:35,030
那我呢，我通过这个 for range 对吧

268
00:10:35,030 --> 00:10:37,940
for range 可以去便利这个 map 

269
00:10:37,940 --> 00:10:39,240
那注意此时啊

270
00:10:39,240 --> 00:10:42,890
这个 map 里面不管是 key 还是 value 

271
00:10:42,890 --> 00:10:44,730
他给你返回的都是字符串

272
00:10:44,730 --> 00:10:47,270
虽然说我们当初在写入的时候

273
00:10:47,270 --> 00:10:50,600
这个 value 并没有说死出一定的字符串

274
00:10:50,600 --> 00:10:51,910
也可能存在整形

275
00:10:51,910 --> 00:10:54,350
但是呢，你现在读出来这个 value 

276
00:10:54,350 --> 00:10:55,610
它就都是字符

277
00:10:55,610 --> 00:10:56,860
单再跑一下

278
00:10:56,860 --> 00:10:59,830
能够拿到学生二所对应的这个 edge 

279
00:10:59,830 --> 00:11:01,070
edge 等于20啊

280
00:11:01,070 --> 00:11:01,910
这边是等于20

281
00:11:01,910 --> 00:11:05,130
然后的话是便利学生一的这个 map 

282
00:11:05,130 --> 00:11:08,240
他拿到的就是 A 姐18

283
00:11:08,240 --> 00:11:10,520
然后呢，身高173.5

284
00:11:10,520 --> 00:11:12,760
然后这个 name 呢，是张三
