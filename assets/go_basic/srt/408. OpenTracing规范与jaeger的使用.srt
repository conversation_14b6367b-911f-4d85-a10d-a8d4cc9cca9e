1
00:00:00,820 --> 00:00:02,260
上一节课我们讲

2
00:00:02,260 --> 00:00:04,540
如何在微服务框架之下

3
00:00:04,540 --> 00:00:07,602
跨进程的去传递追踪信息

4
00:00:07,602 --> 00:00:08,330
但是呢

5
00:00:08,330 --> 00:00:12,180
这并没有完全解决分布式链路追踪问题

6
00:00:12,180 --> 00:00:13,210
大家想象一下

7
00:00:13,210 --> 00:00:16,329
比方说这个图是一个微幅的框架啊

8
00:00:16,329 --> 00:00:18,510
里面每一个方框代表是一个微负

9
00:00:18,510 --> 00:00:19,770
那每一个微负呢

10
00:00:19,770 --> 00:00:21,560
是由不同的人去维护的

11
00:00:21,560 --> 00:00:23,917
甚至是由不同的组去维护的

12
00:00:23,917 --> 00:00:25,570
用户的一次请求是吧

13
00:00:25,570 --> 00:00:28,110
会触发多个微服务的调用

14
00:00:28,110 --> 00:00:31,650
假如说某个用户他的数据出问题了

15
00:00:31,650 --> 00:00:33,430
那么呢，在入口这边

16
00:00:33,430 --> 00:00:34,730
入口这个为负值

17
00:00:34,730 --> 00:00:38,610
他根据用户 id 和事故发生的时间去查知

18
00:00:38,610 --> 00:00:40,312
找到对应的 trc id 

19
00:00:40,312 --> 00:00:41,300
然后怎么办

20
00:00:41,300 --> 00:00:44,680
然后他要把这个 trace id 告诉其他微服务

21
00:00:44,680 --> 00:00:46,780
让其他人也去自己的日志里面

22
00:00:46,780 --> 00:00:47,960
查一下日志吗

23
00:00:47,960 --> 00:00:50,040
这显然是效率很低啊

24
00:00:50,040 --> 00:00:52,462
别人不一定随时可以配合你

25
00:00:52,462 --> 00:00:56,790
所以呢，每一个微服他们打的这个日志信息啊

26
00:00:56,790 --> 00:00:59,610
需要记录到某一个公共的地方去啊

27
00:00:59,610 --> 00:01:02,080
比方说存到某一个数据库里面去

28
00:01:02,080 --> 00:01:04,230
当我们需要查问题的时候

29
00:01:04,230 --> 00:01:05,590
直接去读那个数据库

30
00:01:05,590 --> 00:01:07,950
就可以把整个调用链路里面

31
00:01:07,950 --> 00:01:11,427
每一步的详细信息给查出来

32
00:01:11,427 --> 00:01:13,100
好，那所以这里面呢

33
00:01:13,100 --> 00:01:15,460
就涉及到一个规范问题

34
00:01:15,460 --> 00:01:19,210
因为每一个微服他打的这个信息呢

35
00:01:19,210 --> 00:01:21,090
大家需要保持结构统一

36
00:01:21,090 --> 00:01:23,510
这样的话我们汇总到一个地方的时候

37
00:01:23,510 --> 00:01:26,147
才方便结构化查询嘛

38
00:01:26,147 --> 00:01:26,940
另外

39
00:01:26,940 --> 00:01:31,450
那每一个微服务它是由不同的语言编写的

40
00:01:31,450 --> 00:01:34,330
不管是 web 接口还是加 PC 接口

41
00:01:34,330 --> 00:01:36,897
本身都支持多种语言嘛

42
00:01:36,897 --> 00:01:38,520
所以说这个规范呢

43
00:01:38,520 --> 00:01:40,820
它需要是跟语言无关的

44
00:01:40,820 --> 00:01:42,500
它它是一个通用的规范

45
00:01:42,500 --> 00:01:42,980
好

46
00:01:42,980 --> 00:01:46,620
那么 open tracing 就是这样一个标准规范

47
00:01:46,620 --> 00:01:50,020
它就是解决分布式链路追踪里面

48
00:01:50,020 --> 00:01:52,680
那的数据怎么记啊

49
00:01:52,680 --> 00:01:53,260
好

50
00:01:53,260 --> 00:01:54,870
那既然是一个标

51
00:01:54,870 --> 00:01:56,570
语言无关的这样一个规范嘛

52
00:01:56,570 --> 00:01:58,550
但它实际上要想落地的话

53
00:01:58,550 --> 00:02:01,030
还是需要转成对应的语言啊

54
00:02:01,030 --> 00:02:04,100
通过语言代码的形式来表达

55
00:02:04,100 --> 00:02:07,337
这个普通的这个文本信息嘛

56
00:02:07,337 --> 00:02:09,410
那落到 go 代码这边呢

57
00:02:09,410 --> 00:02:13,050
那么这个标准就转成了这样的一个啊

58
00:02:13,050 --> 00:02:15,360
open tracing go 这样一个项目

59
00:02:15,360 --> 00:02:16,830
在这个项目里面的话

60
00:02:16,830 --> 00:02:20,950
其实主要就定义了一堆 interface 接口啊

61
00:02:20,950 --> 00:02:23,095
通过接口来进行约束

62
00:02:23,095 --> 00:02:24,370
那这个接口里面呢

63
00:02:24,370 --> 00:02:26,370
有三个很重要的接口

64
00:02:26,370 --> 00:02:27,510
一个是 span 

65
00:02:27,510 --> 00:02:29,170
一个是 span context 

66
00:02:29,170 --> 00:02:30,632
一个是 tracer 

67
00:02:30,632 --> 00:02:32,870
好，那什么是 span 呢

68
00:02:32,870 --> 00:02:35,200
这个大家比方以这个图为例吧

69
00:02:35,200 --> 00:02:36,680
这里每一个方框啊

70
00:02:36,680 --> 00:02:38,840
你可以理解为每一个方框就是个 span ， span 

71
00:02:38,840 --> 00:02:39,880
这是个过程嘛

72
00:02:39,880 --> 00:02:41,460
一个函数是一个 span 

73
00:02:41,460 --> 00:02:43,280
你也可以理解为某一次

74
00:02:43,280 --> 00:02:45,390
第一个微夫也是个 SPA 

75
00:02:45,390 --> 00:02:47,050
那微服之间对吧

76
00:02:47,050 --> 00:02:48,670
形成这种调用关系嘛

77
00:02:48,670 --> 00:02:50,970
就是这种箭头指示的就是调用关系

78
00:02:50,970 --> 00:02:52,080
谁调用了谁

79
00:02:52,080 --> 00:02:52,530
好

80
00:02:52,530 --> 00:02:53,050
这里面呢

81
00:02:53,050 --> 00:02:56,080
他们说上面的称之为父子班啊

82
00:02:56,080 --> 00:02:57,240
这个属于子丝瓣，对吧

83
00:02:57,240 --> 00:02:59,560
在这样一个调用关系里面

84
00:02:59,560 --> 00:03:00,620
它是父子班

85
00:03:00,620 --> 00:03:02,445
他是呢，是子子班

86
00:03:02,445 --> 00:03:03,230
好

87
00:03:03,230 --> 00:03:05,260
那同样是父子关系啊

88
00:03:05,260 --> 00:03:07,260
也存在两种类型

89
00:03:07,260 --> 00:03:09,280
一种称之为 child of 

90
00:03:09,280 --> 00:03:11,740
一种称之为 follow 字 from 啊

91
00:03:11,740 --> 00:03:14,080
因为这里面呢，它用这个 reference 啊

92
00:03:14,080 --> 00:03:17,440
引用来表示我们通常所理解的调用

93
00:03:17,440 --> 00:03:17,960
好

94
00:03:17,960 --> 00:03:19,940
那所以说引关系分为两种嘛

95
00:03:19,940 --> 00:03:22,240
这两种啊有什么区别呢

96
00:03:22,240 --> 00:03:24,040
虽然都是副字对吧

97
00:03:24,040 --> 00:03:24,660
都是副词

98
00:03:24,660 --> 00:03:27,590
但是呢，如果说这个副词 span 

99
00:03:27,590 --> 00:03:31,810
它完全不依赖于子 span 的返回结尾的话

100
00:03:31,810 --> 00:03:34,890
那么这种引用关系我们称之为 follow this pro 

101
00:03:34,890 --> 00:03:37,640
如果说负 span 在某种程度上

102
00:03:37,640 --> 00:03:40,180
它依赖自 span 的返回结果

103
00:03:40,180 --> 00:03:43,670
那么这种引用关系称之为 child of 

104
00:03:43,670 --> 00:03:45,710
好，这是 span 的概念啊

105
00:03:45,710 --> 00:03:47,415
我们看一下这个 spa

106
00:03:47,415 --> 00:03:49,530
interface 里面有一些方法

107
00:03:49,530 --> 00:03:52,370
包括 finish 是这个 span ，它开始

108
00:03:52,370 --> 00:03:53,650
然后执行一些工作

109
00:03:53,650 --> 00:03:55,857
最后呢，需要 finish 结束嘛

110
00:03:55,857 --> 00:03:58,360
然后这个 span 会对应一个 context 

111
00:03:58,360 --> 00:04:01,080
这个 context 返回的就是一个 span 

112
00:04:01,080 --> 00:04:04,500
context 啊，会设置一个什么呢

113
00:04:04,500 --> 00:04:06,020
operation name 啊

114
00:04:06,020 --> 00:04:08,180
每一个 span 会有自己的一个名字啊

115
00:04:08,180 --> 00:04:10,630
这个名字我们称之为 operation name 

116
00:04:10,630 --> 00:04:12,347
操作名称吧

117
00:04:12,347 --> 00:04:13,460
哪个 SPA 呢

118
00:04:13,460 --> 00:04:14,660
还会有一些 tag 啊

119
00:04:14,660 --> 00:04:16,060
我们可以给它设置一些 tag 

120
00:04:16,060 --> 00:04:18,300
这个 tag 就是一些 KV 

121
00:04:18,300 --> 00:04:21,060
KV 6这些属性嘛

122
00:04:21,060 --> 00:04:22,340
包括这个 field 啊

123
00:04:22,340 --> 00:04:24,000
这个 field 跟 TX 很类似

124
00:04:24,000 --> 00:04:24,940
也是一些属性

125
00:04:24,940 --> 00:04:26,320
包括这个 KV 啊

126
00:04:26,320 --> 00:04:27,332
其实

127
00:04:27,332 --> 00:04:28,790
其实这四个啊

128
00:04:28,790 --> 00:04:32,750
其实这四个包括这个 by baggage item 

129
00:04:32,750 --> 00:04:35,180
这四个他们通通都是 KV 

130
00:04:35,180 --> 00:04:37,890
都是一些这个 span 上面的一些属性

131
00:04:37,890 --> 00:04:39,130
只不过这些属性

132
00:04:39,130 --> 00:04:41,670
他们从使用方法上面来讲呢

133
00:04:41,670 --> 00:04:42,770
会有一些区别

134
00:04:42,770 --> 00:04:44,557
待会我们再来讲

135
00:04:44,557 --> 00:04:46,020
好啊

136
00:04:46,020 --> 00:04:47,080
而且我们注意到啊

137
00:04:47,080 --> 00:04:49,990
其实你看这个 set tag 对吧

138
00:04:49,990 --> 00:04:52,790
它返回的又是返回一个 span 啊

139
00:04:52,790 --> 00:04:55,430
这包括这个 set baggage item 

140
00:04:55,430 --> 00:04:56,710
返回的也是这个 span 

141
00:04:56,710 --> 00:04:58,070
这样的好处在于什么

142
00:04:58,070 --> 00:04:59,160
这样的好处在于说

143
00:04:59,160 --> 00:05:01,780
当我调用 set tag 之后

144
00:05:01,780 --> 00:05:04,720
我会紧接着点，哎，再去调用什么

145
00:05:04,720 --> 00:05:06,240
调用这个 rock fills 

146
00:05:06,240 --> 00:05:07,605
因为你是这

147
00:05:07,605 --> 00:05:09,480
相当于是用链式调用嘛，对吧

148
00:05:09,480 --> 00:05:11,360
函数本身又返回到自己

149
00:05:11,360 --> 00:05:12,680
那么我接受自己的话

150
00:05:12,680 --> 00:05:15,505
又可以执行这么多的方法

151
00:05:15,505 --> 00:05:18,460
然后这边通过 set 对吧

152
00:05:18,460 --> 00:05:19,640
这边呢可以读取啊

153
00:05:19,640 --> 00:05:21,380
你把这个对应的 key 传进来

154
00:05:21,380 --> 00:05:23,825
可以返回对应的这个 value 

155
00:05:23,825 --> 00:05:25,480
然后是一个 tracer 

156
00:05:25,480 --> 00:05:26,680
我们说 tracer 是干嘛的

157
00:05:26,680 --> 00:05:29,520
tracer 主要是用来启动这个 spend 

158
00:05:29,520 --> 00:05:32,930
所以呢，你可以获得这个 tracer 当初是由哪个，呃

159
00:05:32,930 --> 00:05:36,492
这个 span 当初使用哪个 tracer 把我启动的

160
00:05:36,492 --> 00:05:40,280
那我们看到这个呃 span context 里面呢

161
00:05:40,280 --> 00:05:42,020
它这个接口主要就是什么

162
00:05:42,020 --> 00:05:46,850
主要就是提供获取 bgage item 这样一个方法

163
00:05:46,850 --> 00:05:48,930
而且是便利包一起便利

164
00:05:50,120 --> 00:05:51,400
吹散呢主要是三个

165
00:05:51,400 --> 00:05:54,000
一个是它可以通过 start span 去启动

166
00:05:54,000 --> 00:05:56,960
一个 span 里面它需要指定这个 operation name 对吧

167
00:05:56,960 --> 00:05:59,320
每一个 span 都有一个 operation name 啊

168
00:05:59,320 --> 00:06:02,040
还可以指定一些附加选项

169
00:06:02,040 --> 00:06:04,500
其实你通过这些附加选项

170
00:06:04,500 --> 00:06:06,340
实际上可以指定

171
00:06:06,340 --> 00:06:07,930
比方说 tag 呀

172
00:06:07,930 --> 00:06:09,430
来个 get em 吗

173
00:06:09,430 --> 00:06:12,540
还有一个 inject 和这个 extract 啊

174
00:06:12,540 --> 00:06:13,260
我们待会儿讲

175
00:06:13,260 --> 00:06:15,930
我们先来说一说这个啊

176
00:06:15,930 --> 00:06:19,250
这几个 k value 属性有什么区别啊

177
00:06:20,790 --> 00:06:26,190
好， tag log 和这个 bgage item 都是 KV 形式的属性啊

178
00:06:26,190 --> 00:06:28,017
都是属于这个 span 的属性

179
00:06:28,017 --> 00:06:29,100
这个 tag 呢

180
00:06:29,100 --> 00:06:32,560
它主要是用于检索的啊

181
00:06:32,560 --> 00:06:33,500
你可以想象一下

182
00:06:33,500 --> 00:06:36,280
我们把一些被动信息

183
00:06:36,280 --> 00:06:39,000
比方说写到了某一个数据库里面去

184
00:06:39,000 --> 00:06:41,100
然后我们要进行一些查询，对吧

185
00:06:41,100 --> 00:06:41,900
查询的时候

186
00:06:41,900 --> 00:06:44,480
我们当然是根据什么索引进行查询

187
00:06:44,480 --> 00:06:46,570
你可以理解为这个 tag 

188
00:06:46,570 --> 00:06:49,170
这个 key 上面建了个索引

189
00:06:49,170 --> 00:06:52,010
到时候你可以根据这个 key 去查询

190
00:06:52,010 --> 00:06:54,102
查询出对应的 span 

191
00:06:54,102 --> 00:06:55,500
而 log 的话呢

192
00:06:55,500 --> 00:06:57,740
它虽然也是 k value 形式

193
00:06:57,740 --> 00:06:58,660
但是它这个 K 呢

194
00:06:58,660 --> 00:06:59,900
一一般不用于查询

195
00:06:59,900 --> 00:07:02,845
它这个 K 仅仅是一些，呃

196
00:07:02,845 --> 00:07:06,440
就是随着这个 span 附带一同展示出来的

197
00:07:06,440 --> 00:07:07,120
一些信息吧

198
00:07:07,120 --> 00:07:08,712
就是它上面没有建索引

199
00:07:08,712 --> 00:07:12,030
而这个 bgage item 虽然说它也是 KY 6

200
00:07:12,030 --> 00:07:13,590
但是这个信息呢

201
00:07:13,590 --> 00:07:15,530
它主要强的是什么

202
00:07:15,530 --> 00:07:17,820
是继承传递啊

203
00:07:17,820 --> 00:07:21,760
比方说在啊，在这样一个继承链条里面对吧

204
00:07:21,760 --> 00:07:25,010
假如说这个登 span 啊

205
00:07:25,010 --> 00:07:27,010
这个 visitor 这个 span 里面

206
00:07:27,010 --> 00:07:30,090
假如说放置了一些 bgage item 的话

207
00:07:30,090 --> 00:07:31,050
那这个 BGAGE 

208
00:07:31,050 --> 00:07:37,380
item 它会默认的自动的传给这个 get recommend 

209
00:07:37,380 --> 00:07:40,780
然后会默认自动的去传给 get you the road 

210
00:07:40,780 --> 00:07:41,780
也就说 BGAGE 

211
00:07:41,780 --> 00:07:45,690
item 它是可以自动的往后代进行传递的

212
00:07:45,690 --> 00:07:50,190
而我们刚才说的像 tag 、 a log 这些属性

213
00:07:50,190 --> 00:07:53,060
它就属于当前这个子弹本身啊

214
00:07:53,060 --> 00:07:54,820
它不会往后传递

215
00:07:56,200 --> 00:07:57,200
所以我们说

216
00:07:57,200 --> 00:08:01,470
哎，我们刚才看到这个 span context 对吧

217
00:08:01,470 --> 00:08:02,280
假如说啊

218
00:08:02,280 --> 00:08:04,540
你把这个白给加特姆

219
00:08:04,540 --> 00:08:06,880
传给了这个孙子这一代，对吧

220
00:08:06,880 --> 00:08:08,160
那在孙子这一代

221
00:08:08,160 --> 00:08:10,400
通过他的那个 context 

222
00:08:10,400 --> 00:08:13,840
你可以获得一个 span context 

223
00:08:13,840 --> 00:08:15,340
然后通过这个 span context 

224
00:08:15,340 --> 00:08:16,400
你可以去什么

225
00:08:16,400 --> 00:08:18,720
可以去便利这个白给 atom 

226
00:08:18,720 --> 00:08:19,860
这个时候你发现诶

227
00:08:19,860 --> 00:08:21,180
他可以把他父亲的

228
00:08:21,180 --> 00:08:25,120
他爷爷背的白给 atom 全部给读过来

229
00:08:26,380 --> 00:08:30,180
好，然后我们再来说这个 TRA 里面那两个函数啊

230
00:08:30,180 --> 00:08:32,980
这里面是说我们看通过这个 TR

231
00:08:32,980 --> 00:08:36,780
可以去启动一个 span a 指定 operation name 啊

232
00:08:36,780 --> 00:08:37,860
后面还有一些选项

233
00:08:37,860 --> 00:08:41,321
选项一边可以指定什么继承关系

234
00:08:41,321 --> 00:08:42,970
这个是引用 laser 啊

235
00:08:42,970 --> 00:08:44,290
你可以是 child of 

236
00:08:44,290 --> 00:08:46,020
也可以是 blows from 

237
00:08:46,020 --> 00:08:48,490
可以给这个 span 设置一些 tag 啊

238
00:08:48,490 --> 00:08:51,367
可以指定这个 span 的开始时间等等吧

239
00:08:51,367 --> 00:08:53,060
这个开始时间一般可以不设啊

240
00:08:53,060 --> 00:08:53,820
因为你不指定的话

241
00:08:53,820 --> 00:08:55,657
它默认就是当前时间

242
00:08:55,657 --> 00:08:56,270
好

243
00:08:56,270 --> 00:08:57,930
终点我们是看这个 tracer 里面

244
00:08:57,930 --> 00:09:01,225
有一个 inject 和一个 extract 

245
00:09:01,225 --> 00:09:02,570
这两个是干嘛使的

246
00:09:02,570 --> 00:09:05,997
这两个其实主要是实现序列化和反序化

247
00:09:05,997 --> 00:09:07,760
就是说你可以想象

248
00:09:07,760 --> 00:09:11,340
成为我们的这个分布式链路追踪作系统

249
00:09:11,340 --> 00:09:13,800
是他们需要有一个地方去存储

250
00:09:13,800 --> 00:09:15,900
这些追踪信息嘛

251
00:09:15,900 --> 00:09:17,700
你可以把它理解为就是个税库

252
00:09:17,700 --> 00:09:18,350
好

253
00:09:18,350 --> 00:09:21,030
那问题是说这个追踪信息啊

254
00:09:21,030 --> 00:09:22,900
你可以理解它是个结构体嘛，对吧

255
00:09:22,900 --> 00:09:26,390
这个追踪信息你需要通过网络传输

256
00:09:26,390 --> 00:09:28,110
发送给我们的那个数据库

257
00:09:28,110 --> 00:09:30,420
数据库显示一个第三方调

258
00:09:30,420 --> 00:09:32,320
他不可能跟你的这个，呃

259
00:09:32,320 --> 00:09:34,380
微服务是附在同样台机器上面的

260
00:09:34,380 --> 00:09:37,700
它需要是一个跟所有的微服务都独立的一

261
00:09:37,700 --> 00:09:39,060
另外一个公共地方

262
00:09:39,060 --> 00:09:39,560
好

263
00:09:39,560 --> 00:09:41,140
它需要消毒网、多网络

264
00:09:41,140 --> 00:09:42,700
那么一旦走网络的话

265
00:09:42,700 --> 00:09:45,540
就涉及到你这个追踪信息这么多字段

266
00:09:45,540 --> 00:09:48,360
如何进行序列化和反序列化

267
00:09:48,360 --> 00:09:49,230
这里面呢

268
00:09:49,230 --> 00:09:52,530
inject 和 extract 就解决这个问题

269
00:09:53,540 --> 00:09:57,110
好，你看这个 inject 这个函数的这个参数啊

270
00:09:57,110 --> 00:09:59,600
需要传一个 span 空 text 进来

271
00:09:59,600 --> 00:10:02,180
这边呢还需要传一个 carry 

272
00:10:02,180 --> 00:10:04,082
这边还有一个 format 

273
00:10:04,082 --> 00:10:08,190
其实这个 format 主要是指定格式吗

274
00:10:08,190 --> 00:10:08,970
什么格式呢

275
00:10:08,970 --> 00:10:10,530
就是这个数据的格式

276
00:10:10,530 --> 00:10:14,002
就是你打算采用哪一种序列化方法

277
00:10:14,002 --> 00:10:14,940
他的意思就是说

278
00:10:14,940 --> 00:10:18,620
我要把这个 span context 里面的信息啊

279
00:10:18,620 --> 00:10:22,090
其实主要就是那个 bgage item 的信息

280
00:10:22,090 --> 00:10:24,760
把这个 span context 里面的信息呢

281
00:10:24,760 --> 00:10:27,960
我要把它注入到这个 carrier 

282
00:10:27,960 --> 00:10:31,910
carrier 直译过来就是那个什么运输工具嘛，对吧

283
00:10:31,910 --> 00:10:34,680
我要把它放到这个 carrier 里面去

284
00:10:34,680 --> 00:10:35,950
放进去的时候

285
00:10:35,950 --> 00:10:38,070
以一种什么样的形式放进去呢

286
00:10:38,070 --> 00:10:39,570
转成什么样的数据格式呢

287
00:10:39,570 --> 00:10:42,470
通过 format 来指定啊

288
00:10:42,470 --> 00:10:43,150
这个是注入

289
00:10:43,150 --> 00:10:43,650
好

290
00:10:43,650 --> 00:10:45,590
当你放进 carry 里面去之后

291
00:10:45,590 --> 00:10:49,070
哎，这个 carry 就负责帮你把这个信息呢

292
00:10:49,070 --> 00:10:51,770
发送给那个第三方的那个数据库

293
00:10:51,770 --> 00:10:53,262
把它给存起来

294
00:10:53,262 --> 00:10:53,940
好

295
00:10:53,940 --> 00:10:57,220
然后你还要从这个第三方数据库里面

296
00:10:57,220 --> 00:11:00,480
把这个 span context 把它给读出来，对吧

297
00:11:00,480 --> 00:11:01,250
好

298
00:11:01,250 --> 00:11:02,220
最开始啊

299
00:11:02,220 --> 00:11:05,120
你从这个数据库里面读出了一段信息

300
00:11:05,120 --> 00:11:08,505
但这个信息呢，比方说它是一个二进制对吧

301
00:11:08,505 --> 00:11:10,420
你是不是代表它反序列化吧

302
00:11:10,420 --> 00:11:11,680
好，反序列化啊

303
00:11:11,680 --> 00:11:13,940
就是说你读出来的是一个 carry 

304
00:11:13,940 --> 00:11:16,490
然后你要需要把这个 carry 里面的信息呢

305
00:11:16,490 --> 00:11:17,530
反序列化

306
00:11:17,530 --> 00:11:20,955
反序列化成为一个 span context 

307
00:11:20,955 --> 00:11:22,470
在反虚化的时候呢

308
00:11:22,470 --> 00:11:24,750
你需要指定这个 format 啊

309
00:11:24,750 --> 00:11:26,500
指定数据格式啊

310
00:11:26,500 --> 00:11:29,852
所以这是 inject 和 extract 

311
00:11:29,852 --> 00:11:32,950
在 open tracing 这样一个标准规范里面呢

312
00:11:32,950 --> 00:11:34,330
关于这个 formula 

313
00:11:34,330 --> 00:11:35,670
它规定了三种

314
00:11:35,670 --> 00:11:38,760
据说那你至少需要实现这三种

315
00:11:38,760 --> 00:11:40,300
第一种呢，是 binary 

316
00:11:40,300 --> 00:11:43,070
那就是我这个 spanton context 

317
00:11:43,070 --> 00:11:44,310
它最终序列化之后

318
00:11:44,310 --> 00:11:46,510
转成的就是一个完全不透明的

319
00:11:46,510 --> 00:11:47,920
一个二进制格式

320
00:11:47,920 --> 00:11:49,900
好，如果这种格式的话

321
00:11:49,900 --> 00:11:52,860
那将来你的这个 carry ，这个 carrier 

322
00:11:52,860 --> 00:11:54,980
你看这 carry 他实际上也是一个接口啊

323
00:11:54,980 --> 00:11:57,330
你需要有一个具体的实践

324
00:11:57,330 --> 00:12:00,960
那如果你是这种类型的序列化的话

325
00:12:00,960 --> 00:12:04,720
将来你在这个 inject 里面使用的这个 carry 

326
00:12:04,720 --> 00:12:07,600
就需要是一种 i writer 

327
00:12:07,600 --> 00:12:08,240
注意啊

328
00:12:08,240 --> 00:12:10,460
这个 l writer 在构院里面本身是

329
00:12:10,460 --> 00:12:11,180
它也是一个什么

330
00:12:11,180 --> 00:12:12,260
也是个接口啊

331
00:12:12,260 --> 00:12:14,480
你需要是他的某个具体时间

332
00:12:14,480 --> 00:12:16,650
那么你在反虚化的时候呢

333
00:12:16,650 --> 00:12:19,960
需要使用的是一种 IO reader 

334
00:12:19,960 --> 00:12:24,070
它的一种实现来作为这里面的 carrier 

335
00:12:24,070 --> 00:12:25,125
好

336
00:12:25,125 --> 00:12:26,440
除了是二进制之外

337
00:12:26,440 --> 00:12:28,840
那么你这个虚化之后还可以是一种文本

338
00:12:28,840 --> 00:12:29,960
那这个文本里面呢

339
00:12:29,960 --> 00:12:31,600
是一种 KV 的形式

340
00:12:31,600 --> 00:12:33,840
所以呢，是 text map 

341
00:12:33,840 --> 00:12:36,080
map 表示是一种 KEV 形式

342
00:12:36,080 --> 00:12:39,290
text 表示就是个普通的文本字符串

343
00:12:39,290 --> 00:12:42,662
好，你的 K 和 V 可以是任意的字符串

344
00:12:42,662 --> 00:12:43,740
那这样的话

345
00:12:43,740 --> 00:12:45,820
那么对应的这个 carrier 呢

346
00:12:45,820 --> 00:12:47,180
在注入的时候

347
00:12:47,180 --> 00:12:51,900
这个 carry 他需要实现 text app carry 这个接口

348
00:12:51,900 --> 00:12:53,372
writer 这个接口

349
00:12:53,372 --> 00:12:55,930
那对应的这个 extract 的时候呢

350
00:12:55,930 --> 00:12:58,630
使用的 carrier 需要实现 text np reader 

351
00:12:58,630 --> 00:13:01,620
这个接口跟 text np 很类似的

352
00:13:01,620 --> 00:13:04,720
还有一种格式是这个 HTTP header 啊

353
00:13:04,720 --> 00:13:08,020
这个 header 跟这个 text map 都是什么

354
00:13:08,020 --> 00:13:09,580
都是 KV 形式

355
00:13:09,580 --> 00:13:11,540
并且 K 和 V 呢都是字符串

356
00:13:11,540 --> 00:13:12,530
只不过啊

357
00:13:12,530 --> 00:13:13,830
它既然叫什么

358
00:13:13,830 --> 00:13:14,790
既然叫 header 

359
00:13:14,790 --> 00:13:17,730
那我们知道在 htp header 里

360
00:13:17,730 --> 00:13:19,230
它是一种 KV 形式嘛

361
00:13:19,230 --> 00:13:21,550
但这里面的 K 、 V 它是有要求的啊

362
00:13:21,550 --> 00:13:22,810
它不能是中文对吧

363
00:13:22,810 --> 00:13:26,480
必须是英文或者是少量的几个英文贝塔符号

364
00:13:26,480 --> 00:13:30,130
那它必须经过 URLL 转移之后的等等吧

365
00:13:30,130 --> 00:13:33,405
有一系列的这个形式上的约定

366
00:13:33,405 --> 00:13:34,360
好

367
00:13:34,360 --> 00:13:36,720
那实际上呢，在 open tron 啊

368
00:13:36,720 --> 00:13:39,300
刚刚才给大家看的那个 open tron go 那个项目里面

369
00:13:39,300 --> 00:13:43,020
他已经实现了几种具体的 carrier 

370
00:13:43,020 --> 00:13:46,280
比方说这个 text app carrier 啊

371
00:13:46,280 --> 00:13:47,180
这个 carrier 呢

372
00:13:47,180 --> 00:13:52,300
它实际上它同时实现了这个 writer 和这个 rider 啊

373
00:13:52,300 --> 00:13:53,502
这两个接口

374
00:13:53,502 --> 00:13:55,710
包括跟这个 HTP 对应的

375
00:13:55,710 --> 00:13:58,930
它也实现了一个 HTTP header carrier 啊

376
00:13:58,930 --> 00:14:03,720
它也是实现了这两个接口啊

377
00:14:03,720 --> 00:14:05,540
这样的话就把这个 open 推荐

378
00:14:05,540 --> 00:14:08,250
这个标准规范给讲完了

379
00:14:08,250 --> 00:14:09,510
非常的抽象对吧

380
00:14:09,510 --> 00:14:12,150
待会我们会马上上一个具体例子

381
00:14:12,150 --> 00:14:13,147
给大家看一下

382
00:14:13,147 --> 00:14:14,360
上例子之前

383
00:14:14,360 --> 00:14:17,280
我们还是需要简单的讲一下这个 JAGGER 啊

384
00:14:17,280 --> 00:14:20,440
我们既然说 open tracing 它是一个标准规范嘛

385
00:14:20,440 --> 00:14:22,580
那总得有一个具体的实现对吧

386
00:14:22,580 --> 00:14:25,250
那么 jar 就是一个具体实现啊

387
00:14:25,250 --> 00:14:27,170
那么你从这个网址上对吧

388
00:14:27,170 --> 00:14:29,450
去下载 windows 版本的 jar 

389
00:14:29,450 --> 00:14:30,660
然后解压啊

390
00:14:30,660 --> 00:14:32,100
解压之后就可以直用了啊

391
00:14:32,100 --> 00:14:34,830
解压之后的话会有一些可执行文件

392
00:14:34,830 --> 00:14:36,750
这里面呢，我们是单机版啊

393
00:14:36,750 --> 00:14:39,470
就直接运行这个 jagger all in one 

394
00:14:39,470 --> 00:14:41,580
就可以把这个接口呢给记起来

395
00:14:41,580 --> 00:14:43,530
这个 JAGGER 你就可以把它理解为

396
00:14:43,530 --> 00:14:45,010
就是它这个是一个数据库啊

397
00:14:45,010 --> 00:14:47,600
就是用来存放各个微服务里面

398
00:14:47,600 --> 00:14:49,480
打的那个追踪信息的

399
00:14:49,480 --> 00:14:50,220
只不过呢

400
00:14:50,220 --> 00:14:51,500
它基于这个数据库

401
00:14:51,500 --> 00:14:53,720
他帮你做了一个查询界面啊

402
00:14:53,720 --> 00:14:55,040
帮你去做一些

403
00:14:56,440 --> 00:14:58,060
链路的对比啊

404
00:14:58,060 --> 00:15:00,220
做一些附附加功能吧

405
00:15:00,220 --> 00:15:02,040
好，我们本地啊

406
00:15:02,040 --> 00:15:05,542
先把这个 JER 进行一个启动是吧

407
00:15:05,542 --> 00:15:07,470
Jagger wall 

408
00:15:08,590 --> 00:15:11,720
in one 。好，先记起

409
00:15:11,720 --> 00:15:13,140
那积在之后啊

410
00:15:13,140 --> 00:15:15,180
然后我们看一看啊

411
00:15:15,180 --> 00:15:18,580
他说你可以通过访问本机的这个

412
00:15:18,580 --> 00:15:20,100
16686啊

413
00:15:20,100 --> 00:15:23,020
这个端口号就会看到这样一个界面啊

414
00:15:24,700 --> 00:15:27,480
呃，那我们看一下这个代码啊

415
00:15:27,480 --> 00:15:30,380
代码里面我们如何去把这个 open tration 呢

416
00:15:30,380 --> 00:15:32,280
把它给用起来

417
00:15:32,280 --> 00:15:33,020
好

418
00:15:33,020 --> 00:15:34,260
那这个代码呢

419
00:15:34,260 --> 00:15:37,862
还是以刚才在 PPT 里面演示

420
00:15:37,862 --> 00:15:38,770
这个啊

421
00:15:38,770 --> 00:15:42,030
以这个调用关系为例进行一些演示

422
00:15:42,030 --> 00:15:43,400
在上一节课里面

423
00:15:43,400 --> 00:15:46,920
我们是通过在每一个函数内部去打质，对吧

424
00:15:46,920 --> 00:15:49,490
把那个垂死 id 和对应的函数耗时

425
00:15:49,490 --> 00:15:50,510
进行一个打印

426
00:15:50,510 --> 00:15:52,510
那今天呢，我们就不打日志了

427
00:15:52,510 --> 00:15:56,802
而我们通过创建 span 来完成同样的功能

428
00:15:56,802 --> 00:15:59,160
好，在我的 main 函数里面呢

429
00:15:59,160 --> 00:16:00,220
我需要什么

430
00:16:00,220 --> 00:16:02,980
我需要去创建一个 JER 啊

431
00:16:02,980 --> 00:16:06,020
因为你需要往 JER 里面去写入数据嘛，对吧

432
00:16:06,020 --> 00:16:08,310
你把 JER 想象成是一个数据库

433
00:16:08,310 --> 00:16:12,070
好，这里面呢，去创建这个 JAGGER 啊

434
00:16:12,070 --> 00:16:12,950
大家看一下创

435
00:16:12,950 --> 00:16:13,990
这个时候我传了一个

436
00:16:13,990 --> 00:16:15,670
这个是那个 service 的名称

437
00:16:15,670 --> 00:16:17,810
这个是杰哥啊

438
00:16:17,810 --> 00:16:19,910
在我本地的那个 server 

439
00:16:19,910 --> 00:16:23,077
启动的端口号是6831

440
00:16:23,077 --> 00:16:25,260
那么我在创建这个结果的时候呢

441
00:16:25,260 --> 00:16:27,640
你看一下指定哪些参数啊

442
00:16:27,640 --> 00:16:30,560
比方说有一个跟采样相关的参数

443
00:16:30,560 --> 00:16:31,815
也就是说呀

444
00:16:31,815 --> 00:16:32,400
呃

445
00:16:32,400 --> 00:16:35,910
你没必要每一次的数据

446
00:16:35,910 --> 00:16:37,750
都真正的往数据库里去写

447
00:16:37,750 --> 00:16:39,490
你可以按照一定的参数比例去写

448
00:16:39,490 --> 00:16:44,060
这里面呢，我是说一就100%全部要入库

449
00:16:44,060 --> 00:16:47,760
还可以指定这个杰克是吧

450
00:16:47,760 --> 00:16:49,280
你在上报数据的时候啊

451
00:16:49,280 --> 00:16:53,350
你的一些跟缓存相关刷新周期呀

452
00:16:53,350 --> 00:16:54,690
包括 jar 的 host 

453
00:16:54,690 --> 00:16:56,630
就是我们从外部传进来的啊

454
00:16:56,630 --> 00:16:57,650
那个 jack 的 host 

455
00:16:57,650 --> 00:17:00,815
包括 service 名称也是从外部传进来的

456
00:17:00,815 --> 00:17:03,990
这里面还可以指定你接个本身的日志

457
00:17:03,990 --> 00:17:04,849
需不需输出

458
00:17:04,849 --> 00:17:06,780
以及输出到什么地方

459
00:17:06,780 --> 00:17:07,609
好，最终呢

460
00:17:07,609 --> 00:17:08,630
这里面来了一个啊

461
00:17:08,630 --> 00:17:09,710
他要设置一个什么

462
00:17:09,710 --> 00:17:11,750
设置一个全局的 tracer 啊

463
00:17:11,750 --> 00:17:13,510
通过 open tron 这个 open tracing 

464
00:17:13,510 --> 00:17:15,640
就是用刚才给大家看到的那个

465
00:17:15,640 --> 00:17:17,510
标准规范和 open creation 

466
00:17:17,510 --> 00:17:21,471
这里面呢，去设置了一个全局的 tracer 啊

467
00:17:21,471 --> 00:17:23,819
好，设置完全局 TRATOR 之后

468
00:17:23,819 --> 00:17:26,400
下面我们就可以去用这个全局的 tron 

469
00:17:26,400 --> 00:17:28,430
比方说你看在 home 代码里面的

470
00:17:28,430 --> 00:17:30,750
我们都是通过什么 global tracer 对吧

471
00:17:30,750 --> 00:17:31,190
这样的话

472
00:17:31,190 --> 00:17:32,740
你实际上获取到的

473
00:17:32,740 --> 00:17:36,580
就是刚才设置的这个全局的 tracer 

474
00:17:38,390 --> 00:17:39,970
比方说这是入口这对吧

475
00:17:39,970 --> 00:17:42,910
入口这啊，你连上这个接口之后啊

476
00:17:42,910 --> 00:17:43,670
跟往常一样

477
00:17:43,670 --> 00:17:46,050
还是去调用这些个函数嘛

478
00:17:46,050 --> 00:17:48,740
首先调用 visit offset 

479
00:17:48,740 --> 00:17:51,210
好，在 visit of set 里面呢

480
00:17:51,210 --> 00:17:52,660
本来我们在这是吧

481
00:17:52,660 --> 00:17:55,440
搞了一个什么第一份 funk 去上报耗时嘛，对吧

482
00:17:55,440 --> 00:17:57,272
这个地方它是什么

483
00:17:57,272 --> 00:17:58,970
仅仅是创建了一个 span 

484
00:17:58,970 --> 00:18:03,212
你看它通过这个全局的 tracer 

485
00:18:03,212 --> 00:18:07,020
调 tracer 的 start span 来创建了一个 span 

486
00:18:07,020 --> 00:18:09,000
然后在函数零推出的时候呢

487
00:18:09,000 --> 00:18:11,480
去把这个 span 给 finish 掉

488
00:18:11,480 --> 00:18:14,880
其实你在这个执行 FINNISH 操作的时候啊

489
00:18:14,880 --> 00:18:17,040
因为我们是使用了这个 jar 

490
00:18:17,040 --> 00:18:21,057
这个具体的 open tron 的实现嘛，对吧

491
00:18:21,057 --> 00:18:22,090
标准里面

492
00:18:22,090 --> 00:18:24,810
它并没有规定说 finish 里面一定要做什么

493
00:18:24,810 --> 00:18:26,230
但是在结构里面呢

494
00:18:26,230 --> 00:18:27,930
你调用 finish 的时候

495
00:18:27,930 --> 00:18:30,940
它实际会把这个 spend 各种信息

496
00:18:30,940 --> 00:18:34,340
全部的写到 JER 的数据库里面去啊

497
00:18:34,340 --> 00:18:36,600
这是这个 finish 的作用哈

498
00:18:36,600 --> 00:18:38,640
然后呢，休息了一段时间

499
00:18:38,640 --> 00:18:39,500
然后，诶

500
00:18:39,500 --> 00:18:42,020
你看他开始往这个 span 里面去

501
00:18:42,020 --> 00:18:43,600
添加一下属性啊

502
00:18:43,600 --> 00:18:46,640
包括以 tag 的形式啊

503
00:18:46,640 --> 00:18:50,200
以 log 的形式以及以白给键 item 形式

504
00:18:50,200 --> 00:18:51,600
都是 KY 6对吧

505
00:18:51,600 --> 00:18:52,490
都是 KY 6

506
00:18:52,490 --> 00:18:54,130
只不过呢，将来啊

507
00:18:54,130 --> 00:18:56,790
你可以按照访问时间

508
00:18:56,790 --> 00:18:59,320
等于上午这样一个 where 条件

509
00:18:59,320 --> 00:19:02,665
去把这个 span 把它给查出来

510
00:19:02,665 --> 00:19:03,970
而这个 log 呢

511
00:19:03,970 --> 00:19:05,590
就是不能够进行查询啊

512
00:19:05,590 --> 00:19:07,900
它只是一些附加的信息而已

513
00:19:07,900 --> 00:19:12,460
这个是 K ，这个是 value by gage item 的话

514
00:19:12,460 --> 00:19:15,080
它的作用是可以往后代传递啊

515
00:19:15,080 --> 00:19:17,850
比方说你看你在这个入口这是吧

516
00:19:17,850 --> 00:19:19,410
设置了一些 BGJIN 

517
00:19:19,410 --> 00:19:23,470
然后我们到 get user role 里面啊

518
00:19:23,470 --> 00:19:25,490
通过你看这里面也创建了一个 span 

519
00:19:25,490 --> 00:19:28,010
通过这个 span 你拿到他的 context 

520
00:19:28,010 --> 00:19:30,197
然后通过这个 span contex

521
00:19:30,197 --> 00:19:33,060
可以去遍历每一个 BGITAL 

522
00:19:33,060 --> 00:19:33,900
把他们给打出来

523
00:19:33,900 --> 00:19:35,820
你会发现当初啊

524
00:19:35,820 --> 00:19:39,970
在最上层设置的这两个白给丫头

525
00:19:39,970 --> 00:19:42,840
是可以获取到的

526
00:19:42,840 --> 00:19:44,570
在入口函数这边

527
00:19:44,570 --> 00:19:48,680
他去调用了 record uv 和 get recommend 这两个函数

528
00:19:48,680 --> 00:19:50,910
那么在 record uv 里面呢

529
00:19:50,910 --> 00:19:52,370
他创建 span 的时候

530
00:19:52,370 --> 00:19:55,550
这个地方它除了指定 operation name 之外

531
00:19:55,550 --> 00:19:59,040
还指定了，哎，我是从这个 span 是

532
00:19:59,040 --> 00:20:00,180
继承过来

533
00:20:00,180 --> 00:20:01,300
他的子 span 

534
00:20:01,300 --> 00:20:05,100
并且呢，这个应用关系是 follows from 

535
00:20:05,100 --> 00:20:07,000
因为在雕的时候呢

536
00:20:07,000 --> 00:20:08,620
他是把 span 是吧

537
00:20:08,620 --> 00:20:10,360
自身的这个 span 传进去的嘛

538
00:20:10,360 --> 00:20:13,780
包括这个地这边也是把自己 span 给传进去的

539
00:20:13,780 --> 00:20:16,000
所以在 get recommend 里面呢

540
00:20:16,000 --> 00:20:18,520
这个 span 相当于是他的父亲嘛，是吧

541
00:20:18,520 --> 00:20:19,920
把父子 span 传过来

542
00:20:19,920 --> 00:20:22,800
而这边的引用关系是 child off 

543
00:20:22,800 --> 00:20:26,060
也就是说我们的这个入口函数啊

544
00:20:26,060 --> 00:20:27,100
这个 vs 函数

545
00:20:27,100 --> 00:20:31,480
它实际上不依赖于 record uv 的任何返回值

546
00:20:31,480 --> 00:20:34,422
而依赖于 get recommend 的返回值

547
00:20:34,422 --> 00:20:37,510
因为很明显你看到它确实依赖这个返回值吗

548
00:20:37,510 --> 00:20:41,170
好，另外就是我们之前的代码里面的这个地方

549
00:20:41,170 --> 00:20:44,090
由于它不依赖 record uv 返回值嘛

550
00:20:44,090 --> 00:20:45,880
所以之前是通过

551
00:20:45,880 --> 00:20:49,080
呃，异步协程去做质检工作

552
00:20:49,080 --> 00:20:52,412
但这个地方为什么没有通过异步去搞呢

553
00:20:52,412 --> 00:20:55,670
如果说我通过异步的方式去搞的话

554
00:20:55,670 --> 00:20:56,430
那这个是吧

555
00:20:56,430 --> 00:20:57,430
我这个函数啊

556
00:20:57,430 --> 00:21:00,550
我要把比方说要往 CLI 里 cos 里面去打一条

557
00:21:00,550 --> 00:21:01,650
买一点数据

558
00:21:01,650 --> 00:21:04,010
那这个操作如果说它比较慢

559
00:21:04,010 --> 00:21:05,630
我们假如说极端情况下

560
00:21:05,630 --> 00:21:07,490
可能需要五秒钟才做完

561
00:21:07,490 --> 00:21:09,210
好，五秒钟做完

562
00:21:09,210 --> 00:21:10,750
如果你 E 不搞的话

563
00:21:10,750 --> 00:21:14,640
那可能我的这个 visit 函数早就已经返回了，对吧

564
00:21:14,640 --> 00:21:15,360
早就返回了

565
00:21:15,360 --> 00:21:18,775
但是呢，你这个函数还没有执行完

566
00:21:18,775 --> 00:21:21,220
那么我 VZ 的函数返回之后

567
00:21:21,220 --> 00:21:23,340
我会去执行这个 finish ，对吧

568
00:21:23,340 --> 00:21:24,350
这个 finish 的话

569
00:21:24,350 --> 00:21:28,620
或者把这个 spend 的各种信息打到接口里面去

570
00:21:28,620 --> 00:21:32,020
而由于这个 record u v 还没有执行完

571
00:21:32,020 --> 00:21:36,880
所以呢， record u v 里面的这个 finish 就没有执行

572
00:21:36,880 --> 00:21:40,980
所以你会发现 record uv 它对应的 span 信息呢

573
00:21:40,980 --> 00:21:43,150
就没有达到这个里面去

574
00:21:43,150 --> 00:21:46,530
到时候你就发现在这条调用链里面

575
00:21:46,530 --> 00:21:51,347
你找不到这个 record v 的任何信息

576
00:21:51,347 --> 00:21:53,920
你会误以为没有调这个函数

577
00:21:53,920 --> 00:21:54,840
实际上是掉了

578
00:21:54,840 --> 00:21:57,220
只不过没有等他执行完毕

579
00:21:57,220 --> 00:21:59,507
就已经把所有的这个

580
00:21:59,507 --> 00:22:02,210
呃，答案的信息呢，给上报了啊

581
00:22:02,210 --> 00:22:02,850
抛下来

582
00:22:03,890 --> 00:22:05,050
抛下之后的话

583
00:22:05,050 --> 00:22:07,500
实际上这里面每一次的 span finish 啊

584
00:22:07,500 --> 00:22:10,120
都会把这个 span 里面的所有信息呢

585
00:22:10,120 --> 00:22:11,480
打到 J 格里面去

586
00:22:11,480 --> 00:22:14,630
那我们打开我们的 jar ui 来看一看啊

587
00:22:14,630 --> 00:22:15,510
好， my service 

588
00:22:15,510 --> 00:22:18,790
这个 my service 就是我们在代码里面

589
00:22:18,790 --> 00:22:20,330
代码里面我们在连接

590
00:22:20,330 --> 00:22:20,950
这个时候呢

591
00:22:20,950 --> 00:22:23,310
这边传的是这个 service name ，是 my service 

592
00:22:23,310 --> 00:22:26,082
所以呢，这边就是 my service 

593
00:22:26,082 --> 00:22:27,960
我直接查

594
00:22:27,960 --> 00:22:29,520
好，这边出来一个啊

595
00:22:29,520 --> 00:22:31,220
上面是 my service 对吧

596
00:22:31,220 --> 00:22:32,380
是他的 service 名称

597
00:22:32,380 --> 00:22:35,760
然后这边你看这个 vista Upset record uv 对吧

598
00:22:35,760 --> 00:22:38,300
这个是我们每创建一个 span 的话

599
00:22:38,300 --> 00:22:41,630
给这个 span 打的一个 operation name 啊

600
00:22:41,630 --> 00:22:43,710
他会把这个他们的调用关系啊

601
00:22:43,710 --> 00:22:44,650
生命周期啊

602
00:22:44,650 --> 00:22:47,110
比较清晰的展示在这个区域

603
00:22:47,110 --> 00:22:48,755
然后我们点第一个

604
00:22:48,755 --> 00:22:52,450
他会把第一个 span 的相关信息给展示出来

605
00:22:52,450 --> 00:22:55,420
比方说第一个 span 里面有一些 tag 对吧

606
00:22:55,420 --> 00:22:56,280
那这个 tag 呢

607
00:22:56,280 --> 00:22:58,400
这个 JAGGER 会默认的啊

608
00:22:58,400 --> 00:23:00,960
会自动的帮我们去上报一些 tag 

609
00:23:00,960 --> 00:23:02,202
包括什么

610
00:23:02,202 --> 00:23:04,920
format 等于 PROTO ，额

611
00:23:04,920 --> 00:23:06,030
采样的信息

612
00:23:06,030 --> 00:23:07,910
包括这个类型

613
00:23:07,910 --> 00:23:10,310
哎，这个访问时段等于上午这个数

614
00:23:10,310 --> 00:23:11,750
在代码里面显示

615
00:23:11,750 --> 00:23:13,635
加了这样一个 tag 

616
00:23:13,635 --> 00:23:15,070
process 啊

617
00:23:15,070 --> 00:23:17,550
里面有一些它默认的会上报一些什么

618
00:23:17,550 --> 00:23:20,510
kind 的 UID 、 host 、 name 、 IP 

619
00:23:20,510 --> 00:23:23,520
以及这个 jar 的 version locks 

620
00:23:23,520 --> 00:23:25,525
我们展开 logs 看看啊

621
00:23:25,525 --> 00:23:26,760
logs 里面呢

622
00:23:26,760 --> 00:23:27,700
第一条啊

623
00:23:27,700 --> 00:23:29,480
user visit 等于 by visit 

624
00:23:29,480 --> 00:23:30,420
TPAGE 等于 home 

625
00:23:30,420 --> 00:23:33,010
这个是我们在代码里面自己显示的

626
00:23:33,010 --> 00:23:35,130
去给他加了这样一个 log 

627
00:23:35,130 --> 00:23:36,390
就是在这个地方对吧

628
00:23:36,390 --> 00:23:38,310
我们自己加的 log 吧

629
00:23:38,310 --> 00:23:39,410
那此外呢

630
00:23:39,410 --> 00:23:42,020
它下面还有两个时间

631
00:23:42,020 --> 00:23:43,090
一个是 trace id 

632
00:23:43,090 --> 00:23:43,910
一个是 uzi id 

633
00:23:43,910 --> 00:23:46,370
这两个是我们在代码里面

634
00:23:46,370 --> 00:23:49,980
把它们加到这个 bgage item 里面去的

635
00:23:49,980 --> 00:23:50,960
那么这样的话呢

636
00:23:50,960 --> 00:23:54,680
它会默认的自动的去生成一个事件 event 

637
00:23:54,680 --> 00:23:56,880
并且事件类型就是这个 baggage 

638
00:23:56,880 --> 00:23:59,790
也一并呢放到 locks 里面去

639
00:24:00,910 --> 00:24:03,670
好看。下面一个啊，这个 record uv 

640
00:24:03,670 --> 00:24:05,830
这个 record uv 的话啊

641
00:24:05,830 --> 00:24:07,250
这里面有一个 reference 对吧

642
00:24:07,250 --> 00:24:08,830
引用啊，展开引用

643
00:24:08,830 --> 00:24:12,890
他说他是引用自这个 visit or Upset 

644
00:24:12,890 --> 00:24:15,570
而且引类型的是这个 follows from 

645
00:24:15,570 --> 00:24:17,190
因为它比较特别嘛

646
00:24:17,190 --> 00:24:18,690
别人都是 child of 

647
00:24:18,690 --> 00:24:20,080
只有他是 follows from 

648
00:24:20,080 --> 00:24:22,570
所以会特别的展示一下

649
00:24:22,570 --> 00:24:24,780
而且这边注意到我们的这个

650
00:24:24,780 --> 00:24:26,690
我们说这个 tag 里

651
00:24:26,690 --> 00:24:27,870
访问时段等于上网

652
00:24:27,870 --> 00:24:30,030
这个 tag 是可以用来查询的

653
00:24:30,030 --> 00:24:31,310
那这个 logs 呢

654
00:24:31,310 --> 00:24:33,670
它主要是记录事件的发生时间

655
00:24:33,670 --> 00:24:35,790
比方说这边每一条 log 对吧

656
00:24:35,790 --> 00:24:37,080
都会有一个时间

657
00:24:37,080 --> 00:24:39,490
比方说这个9.888 ms 

658
00:24:39,490 --> 00:24:42,490
不是说这个事件持续了这么长时间

659
00:24:42,490 --> 00:24:43,980
这个9.88 ms 

660
00:24:43,980 --> 00:24:46,580
它指示的是一个开始时间啊

661
00:24:46,580 --> 00:24:48,960
这个时间呢，你看它下面有个解释啊

662
00:24:48,960 --> 00:24:53,730
这个时间是相对于整个 trace 的开始时间的

663
00:24:53,730 --> 00:24:54,770
一个偏移量啊

664
00:24:54,770 --> 00:24:56,210
所以这个时间是这个时刻啊

665
00:24:56,210 --> 00:24:57,260
它不是一个时段

666
00:24:57,260 --> 00:24:59,590
然后返回到我们这个查询界面

667
00:24:59,590 --> 00:25:01,390
这边有一个 text 对吧

668
00:25:01,390 --> 00:25:02,940
你看它有一个例子啊

669
00:25:02,940 --> 00:25:04,780
诶，什么什么等于什么什么对吧

670
00:25:04,780 --> 00:25:06,220
我们也可以输入

671
00:25:06,220 --> 00:25:11,000
比方说访问时段等于上午

672
00:25:11,000 --> 00:25:12,530
我们要查询一下

673
00:25:12,530 --> 00:25:14,250
就会命中这一个 trace 
