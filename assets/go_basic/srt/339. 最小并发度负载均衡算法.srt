1
00:00:00,019 --> 00:00:02,900
基于最小并发度的复杂均衡算法

2
00:00:02,900 --> 00:00:05,260
那这个算法呢，它就比较取巧啊

3
00:00:05,260 --> 00:00:08,080
他也不关心服务器的具体配置上

4
00:00:08,080 --> 00:00:09,380
有什么差异啊

5
00:00:09,380 --> 00:00:12,560
直接根据服务器的当前负载来决

6
00:00:12,560 --> 00:00:14,160
要选择哪台服务器

7
00:00:14,160 --> 00:00:15,060
谁负载低

8
00:00:15,060 --> 00:00:17,225
我就把请求发给谁

9
00:00:17,225 --> 00:00:19,240
那么基本的原理是这样子的

10
00:00:19,240 --> 00:00:22,500
如果一台服务器它的负载比较高啊

11
00:00:22,500 --> 00:00:25,740
为了不管是由于你的请求太多了

12
00:00:25,740 --> 00:00:26,640
导致负载高

13
00:00:26,640 --> 00:00:30,150
还是由于你的资源配置本身比较少

14
00:00:30,150 --> 00:00:31,360
导致负载比较高

15
00:00:31,360 --> 00:00:33,140
反正你负载比较高，对吧

16
00:00:33,140 --> 00:00:34,520
你负载比较高的话

17
00:00:34,520 --> 00:00:36,280
就会造成一个什么结果呢

18
00:00:36,280 --> 00:00:40,390
结果就是说你上面阻塞的请求比较多啊

19
00:00:40,390 --> 00:00:42,070
好多请求都在你这

20
00:00:42,070 --> 00:00:43,310
你都没有处理完吧

21
00:00:43,310 --> 00:00:44,480
都堵在你这儿了

22
00:00:44,480 --> 00:00:48,060
好，如果说你的阻塞的请求比较多

23
00:00:48,060 --> 00:00:51,440
那么我就认为我就反推你的负责比较高

24
00:00:51,440 --> 00:00:54,080
那么呢，我就不把请求发给你

25
00:00:54,080 --> 00:00:57,330
那么谁上面注册的请求数最少

26
00:00:57,330 --> 00:01:00,180
我就把请求的发给谁啊

27
00:01:00,180 --> 00:01:03,580
这是基于最小并发度的这样一个复杂

28
00:01:03,580 --> 00:01:04,739
均衡策略

29
00:01:04,739 --> 00:01:05,615
好

30
00:01:05,615 --> 00:01:08,560
那这里面需要考虑一个比较极端的情况

31
00:01:08,560 --> 00:01:12,240
就是当我总的请求本身很低的时候啊

32
00:01:12,240 --> 00:01:13,340
很低的时候啊

33
00:01:13,340 --> 00:01:16,580
由于我这个每次都要便利每一台服务器

34
00:01:16,580 --> 00:01:19,427
去找到那个并发度最小的嘛

35
00:01:19,427 --> 00:01:23,182
那假如说负载请求数一直很低很少

36
00:01:23,182 --> 00:01:24,020
那么这个时候呢

37
00:01:24,020 --> 00:01:26,700
你会发现每次新来一个新请求

38
00:01:26,700 --> 00:01:28,560
你会发现每台服务器上面

39
00:01:28,560 --> 00:01:31,092
这个等待的请数都为零

40
00:01:31,092 --> 00:01:32,030
那这样的话呢

41
00:01:32,030 --> 00:01:35,205
你可能会每次都选中 D 台服务器

42
00:01:35,205 --> 00:01:36,590
这样的话，虽然说

43
00:01:36,590 --> 00:01:38,910
额滴台服务器它也不忙，对吧

44
00:01:38,910 --> 00:01:41,150
但毕竟感觉不够均衡，对吧

45
00:01:41,150 --> 00:01:43,562
后面的服务器始终没有被选中

46
00:01:43,562 --> 00:01:45,500
所以对于这种极端情况

47
00:01:45,500 --> 00:01:47,520
我们简单的做一个处理啊

48
00:01:47,520 --> 00:01:49,890
就说我每次啊

49
00:01:49,890 --> 00:01:51,850
每次你不是要便利服务器

50
00:01:51,850 --> 00:01:54,480
去找那个并发度最少的嘛

51
00:01:54,480 --> 00:01:55,840
在变质的时候

52
00:01:55,840 --> 00:01:57,500
我们这个起始位置

53
00:01:57,500 --> 00:01:59,950
不要从第一台服务器开始

54
00:01:59,950 --> 00:02:02,810
我们随机的选一个起始位置啊

55
00:02:02,810 --> 00:02:04,572
从它开始去便利

56
00:02:04,572 --> 00:02:05,860
这个就跟什么呢

57
00:02:05,860 --> 00:02:06,980
这个就相当于在说

58
00:02:06,980 --> 00:02:09,979
你把所有服务器当成了一个环

59
00:02:09,979 --> 00:02:13,260
你每次从环上随机的选择一个起始位置

60
00:02:13,260 --> 00:02:15,650
绕这个环转了一圈去便利啊

61
00:02:15,650 --> 00:02:19,760
这个就跟那个 go 源里面的 map 思想是一样的

62
00:02:19,760 --> 00:02:23,640
为什么你每次通过 for range 去辨 d map 

63
00:02:23,640 --> 00:02:26,070
你发现那个 key 的顺序都不一样啊

64
00:02:26,070 --> 00:02:27,670
其实它是有规律的

65
00:02:27,670 --> 00:02:30,080
就是说我们说 hot map 底层

66
00:02:30,080 --> 00:02:32,040
它是用了什么一个数组嘛

67
00:02:32,040 --> 00:02:33,380
来充当各种槽位

68
00:02:33,380 --> 00:02:37,300
它只不过是每次都是从那个数组的

69
00:02:37,300 --> 00:02:39,890
随机选哪个位置开始便利而已

70
00:02:39,890 --> 00:02:41,550
如果你把它看成一个环的话

71
00:02:41,550 --> 00:02:43,570
你会发现每次 for range 

72
00:02:43,570 --> 00:02:46,337
他那个相对顺序其实都是一致的

73
00:02:46,337 --> 00:02:47,430
好

74
00:02:47,430 --> 00:02:50,585
我们看一下代码层面怎么写

75
00:02:50,585 --> 00:02:54,780
基于最小并发度的负载均衡算法 main concurrency 

76
00:02:54,780 --> 00:02:58,520
这边呢，一个数据结构是我的这个 and points 

77
00:02:58,520 --> 00:03:01,720
就是说我要存储我的每一个服务器的

78
00:03:01,720 --> 00:03:04,207
IP 地址和端口和 and points 

79
00:03:04,207 --> 00:03:07,970
然后呢，我要记录每台服务器它的这个当前啊

80
00:03:07,970 --> 00:03:09,530
被左侧的请求数

81
00:03:09,530 --> 00:03:13,207
即是当它的并发数是多少

82
00:03:13,207 --> 00:03:15,740
好，这边是一个构造函数啊

83
00:03:15,740 --> 00:03:16,060
很简单

84
00:03:16,060 --> 00:03:19,540
就是对这两个切片进行一个初始化嘛

85
00:03:19,540 --> 00:03:20,650
啊，这个没什么

86
00:03:20,650 --> 00:03:21,740
我们过来看看啊

87
00:03:21,740 --> 00:03:24,140
就是我们假如说啊

88
00:03:24,140 --> 00:03:27,290
假如说我要去从 and points 啊

89
00:03:27,290 --> 00:03:29,550
从这么多台服务器里面

90
00:03:29,550 --> 00:03:31,090
我要去选择一台

91
00:03:31,090 --> 00:03:32,080
我选谁呢

92
00:03:32,080 --> 00:03:34,310
就通过这个 tick 函数啊

93
00:03:34,310 --> 00:03:36,677
take 函数它返回的是那个

94
00:03:36,677 --> 00:03:39,760
呃， in 服务器的 index 下标啊

95
00:03:39,760 --> 00:03:42,010
就是从这个 and points 里面

96
00:03:42,010 --> 00:03:43,780
拿出对应的那个下

97
00:03:43,780 --> 00:03:45,260
服务器就可以了

98
00:03:45,260 --> 00:03:46,740
好，怎么选呢

99
00:03:46,740 --> 00:03:47,520
一上来啊

100
00:03:47,520 --> 00:03:50,120
先进行一些边界条件的判断啊

101
00:03:50,120 --> 00:03:53,510
先判断一下我的服务器有没有啊

102
00:03:53,510 --> 00:03:54,310
如果就没有的话

103
00:03:54,310 --> 00:03:54,950
长度零

104
00:03:54,950 --> 00:03:56,390
那返回一嘛

105
00:03:56,390 --> 00:03:57,620
没有服务器可以用

106
00:03:57,620 --> 00:03:58,260
好

107
00:03:58,260 --> 00:03:59,210
然后判断一下

108
00:03:59,210 --> 00:04:01,150
如果服务器只有一台的话

109
00:04:01,150 --> 00:04:04,460
那也就直接返回这台服务器就可以了，对吧

110
00:04:04,460 --> 00:04:06,760
好，如果服务器有多台诶

111
00:04:06,760 --> 00:04:07,800
我们就要去什么

112
00:04:07,800 --> 00:04:11,000
我们就去便利那个 concurrency 啊

113
00:04:11,000 --> 00:04:12,300
去遍历这个数组

114
00:04:12,300 --> 00:04:13,910
去找到最小的那元素

115
00:04:13,910 --> 00:04:14,440
好

116
00:04:14,440 --> 00:04:16,300
如何从一个切片里面

117
00:04:16,300 --> 00:04:17,959
找到最小的那个元素呢

118
00:04:17,959 --> 00:04:20,820
这个也是一个很固定的一个套路啊

119
00:04:20,820 --> 00:04:22,390
就是最最开，最开始呢

120
00:04:22,390 --> 00:04:24,840
因为你要找那个最小者啊

121
00:04:24,840 --> 00:04:28,540
你先令这个 min 最小者等于什么呢

122
00:04:28,540 --> 00:04:32,040
等于最大的整数啊

123
00:04:32,040 --> 00:04:32,840
最大整数

124
00:04:32,840 --> 00:04:35,400
然后呢，你利用你找到的那个目标

125
00:04:35,400 --> 00:04:37,270
那个 index 等于一

126
00:04:37,270 --> 00:04:38,447
好

127
00:04:38,447 --> 00:04:40,670
然后呢，你就要开始什么

128
00:04:40,670 --> 00:04:43,170
开始去遍历这个数组嘛

129
00:04:43,170 --> 00:04:45,232
但是我们说，呃

130
00:04:45,232 --> 00:04:47,160
我们那个起始的位

131
00:04:47,160 --> 00:04:49,120
每次需要随机选对吧

132
00:04:49,120 --> 00:04:51,640
所以呢，哎，我们通过这个 random 啊

133
00:04:51,640 --> 00:04:54,852
随机的选择一个起始的位置

134
00:04:54,852 --> 00:04:55,490
好

135
00:04:55,490 --> 00:04:57,310
然后呢，我们就从

136
00:04:57,310 --> 00:04:58,150
哎，你看啊

137
00:04:58,150 --> 00:04:59,770
我们是从 begin 

138
00:04:59,770 --> 00:05:02,750
你看关键看这一行代码啊

139
00:05:02,750 --> 00:05:04,110
我们第38行

140
00:05:04,110 --> 00:05:06,530
这个 for 循环 I 虽然是从零开始的

141
00:05:06,530 --> 00:05:09,050
但是这个 I 它并不是什么

142
00:05:09,050 --> 00:05:13,670
它并不是那个我们对应到这个 and points 的下标

143
00:05:13,670 --> 00:05:17,090
我们对应到 and points 的下标

144
00:05:17,090 --> 00:05:20,110
或者说 concurrency 的下标是这个 IDX 

145
00:05:20,110 --> 00:05:22,170
而这个 IDX 是怎么的

146
00:05:22,170 --> 00:05:26,040
这个 IDX 是让这个 I 加上这个 begin 啊

147
00:05:26,040 --> 00:05:27,700
begin 是你的起始位置吗

148
00:05:27,700 --> 00:05:28,440
加上 begin 

149
00:05:28,440 --> 00:05:32,000
然后呢，他加上 begin 之后可能会超出数组总长度

150
00:05:32,000 --> 00:05:34,780
所以还需要对数组总长度取一个模

151
00:05:34,780 --> 00:05:39,100
唉，这是我们真正的当前那个数组里面的下标

152
00:05:39,100 --> 00:05:42,360
好，然后我们去看一眼这个下标啊

153
00:05:42,360 --> 00:05:43,900
这个并发度是几

154
00:05:43,900 --> 00:05:47,810
如果说啊，他比我们这个 mean 还要小的话

155
00:05:47,810 --> 00:05:50,380
我们立刻去更新这个 mean 啊

156
00:05:50,380 --> 00:05:54,170
因 min 使用维护那个当前找到的

157
00:05:54,170 --> 00:05:55,750
便利过的最小者嘛

158
00:05:55,750 --> 00:05:56,490
付给 me 

159
00:05:56,490 --> 00:05:58,770
同时呢，我们的 index 也要更新

160
00:05:58,770 --> 00:06:00,432
更新为这个 IDX 

161
00:06:00,432 --> 00:06:02,000
好，我们的第40行

162
00:06:02,000 --> 00:06:04,560
为什么我们去读这个并发图的话

163
00:06:04,560 --> 00:06:05,440
没有直接读

164
00:06:05,440 --> 00:06:07,160
而通过这个 TOMIC 呢

165
00:06:07,160 --> 00:06:09,180
主要是因为啊，在实际当中

166
00:06:09,180 --> 00:06:11,360
那么我们的这个复杂均衡算法

167
00:06:11,360 --> 00:06:14,300
这个 take 函数啊，会被并发的调用啊

168
00:06:14,300 --> 00:06:16,730
所以呢，你要通过这种并发的啊

169
00:06:16,730 --> 00:06:19,430
这种啊，叫做什么原子操作嘛，对吧

170
00:06:19,430 --> 00:06:20,730
来读取这个数值

171
00:06:22,290 --> 00:06:24,330
当我们啊，已经决定

172
00:06:24,330 --> 00:06:26,690
我们最终找到这个 index ，对吧

173
00:06:26,690 --> 00:06:29,720
index 是我们最终要返回的那个结果

174
00:06:29,720 --> 00:06:30,450
同时呢

175
00:06:30,450 --> 00:06:32,630
我们要把这个 inde

176
00:06:32,630 --> 00:06:35,030
它所对应的服务器的 IP 啊

177
00:06:35,030 --> 00:06:35,670
端口号啊

178
00:06:35,670 --> 00:06:37,720
进行一个返回啊

179
00:06:37,720 --> 00:06:39,470
啊，因为你选中它了嘛

180
00:06:39,470 --> 00:06:40,390
那选中它了

181
00:06:40,390 --> 00:06:42,870
那么它的这个并发度呢，就要加一啊

182
00:06:42,870 --> 00:06:47,150
通过 atomic 原子操作对应的那个并发度加一

183
00:06:47,150 --> 00:06:48,730
那么什么时候减一呢

184
00:06:48,730 --> 00:06:51,750
就说你要选一台服务器

185
00:06:51,750 --> 00:06:55,260
你要去把你的这个 RPC 请求发给他

186
00:06:55,260 --> 00:06:58,060
那么当这个服务器给你返回结果了

187
00:06:58,060 --> 00:06:59,200
返回结果了

188
00:06:59,200 --> 00:07:03,097
你要立刻对这个 concurrency 执行一次减一

189
00:07:03,097 --> 00:07:05,280
也就是说你把请求给他

190
00:07:05,280 --> 00:07:07,140
他在返回给你结果之前

191
00:07:07,140 --> 00:07:09,100
这段时间可能就几10 ms 

192
00:07:09,100 --> 00:07:10,020
通常只有几10 ms 

193
00:07:10,020 --> 00:07:11,470
在这段时间之内

194
00:07:11,470 --> 00:07:14,660
他的这个并发度是维持着这个加一这个状态

195
00:07:14,660 --> 00:07:15,580
一旦达到结果了

196
00:07:15,580 --> 00:07:17,632
那并发度立刻就要减一

197
00:07:17,632 --> 00:07:20,640
好，这是啊，去选择某一台服务器

198
00:07:20,640 --> 00:07:21,830
那么如果说

199
00:07:21,830 --> 00:07:24,250
呃，他服务器给你返回结果了

200
00:07:24,250 --> 00:07:28,440
你要令他呃，并发数减一嘛

201
00:07:28,440 --> 00:07:31,010
我们称之为这个 return 啊

202
00:07:31,010 --> 00:07:32,280
啊

203
00:07:32,280 --> 00:07:34,300
负载均衡选择的时候

204
00:07:34,300 --> 00:07:36,080
他不是返回给你个 index 吗

205
00:07:36,080 --> 00:07:37,540
那么你让它减一的话

206
00:07:37,540 --> 00:07:39,420
你把 index 再传回来

207
00:07:39,420 --> 00:07:40,880
好，我通过这个 index 呢

208
00:07:40,880 --> 00:07:42,420
找到对应的 concurrency 

209
00:07:42,420 --> 00:07:45,850
通过原子操作令他什么加一个一

210
00:07:45,850 --> 00:07:46,850
相当于减一嘛

211
00:07:46,850 --> 00:07:47,770
减一就可以了

212
00:07:47,770 --> 00:07:48,590
只不过呢

213
00:07:48,590 --> 00:07:51,830
会先对这个 index 进行一个合法性的校验啊

214
00:07:51,830 --> 00:07:54,270
这个 index 总不能超过数据的边界吧

215
00:07:54,270 --> 00:07:56,135
否则就越界了嘛，对吧

216
00:07:56,135 --> 00:07:56,940
好

217
00:07:56,940 --> 00:07:58,930
所以整体实现啊，也很简单

218
00:07:58,930 --> 00:08:01,962
我们对应的单次函数看一

219
00:08:01,962 --> 00:08:04,120
这边我的 and points 

220
00:08:04,120 --> 00:08:04,920
我的服务端

221
00:08:04,920 --> 00:08:07,060
然假设是这三个啊

222
00:08:07,060 --> 00:08:08,240
是 IP 一样

223
00:08:08,240 --> 00:08:09,340
但是端口号不一样

224
00:08:09,340 --> 00:08:11,360
假设是我们的起了个夫

225
00:08:11,360 --> 00:08:13,730
他又是在三台服务器上

226
00:08:13,730 --> 00:08:15,250
或者在三个端口号上面

227
00:08:15,250 --> 00:08:16,590
把这个服务给记起来了

228
00:08:16,590 --> 00:08:17,850
好，然后呢

229
00:08:17,850 --> 00:08:21,130
我们这个 use count 是打算去记录啊

230
00:08:21,130 --> 00:08:23,070
我们不是要做负载均衡测试吗

231
00:08:23,070 --> 00:08:26,060
我们叫测很多次啊

232
00:08:26,060 --> 00:08:27,780
几千万次，很多次啊

233
00:08:27,780 --> 00:08:29,780
去记录下来这三台服务器

234
00:08:29,780 --> 00:08:32,440
每台服务器被命中了几次

235
00:08:32,440 --> 00:08:35,479
会存到这个 user count 里面去

236
00:08:35,479 --> 00:08:36,179
好

237
00:08:36,179 --> 00:08:39,460
即便是初始化了一个啊

238
00:08:39,460 --> 00:08:42,350
mean currency 这样一个 BALANCER 啊

239
00:08:42,350 --> 00:08:43,429
负载均衡器

240
00:08:43,429 --> 00:08:46,770
然后呢，我这边是打算开100个并发

241
00:08:46,770 --> 00:08:47,570
开100个并发

242
00:08:47,570 --> 00:08:48,730
100个携程

243
00:08:48,730 --> 00:08:49,190
好

244
00:08:49,190 --> 00:08:51,510
那对于每一个系统而言呢

245
00:08:51,510 --> 00:08:54,350
他要去 for 循环100次啊

246
00:08:54,350 --> 00:08:56,265
总共是100×100，1万次对吧

247
00:08:56,265 --> 00:08:57,610
那每一次进来呢

248
00:08:57,610 --> 00:08:59,330
就是调这个 take 函数啊

249
00:08:59,330 --> 00:09:02,290
我就随机的去选择某一台服务器啊

250
00:09:02,290 --> 00:09:04,040
我把这个 index 给取出来

251
00:09:04,040 --> 00:09:08,390
然后呢，那么 d index 这个服务器 and point 被选中了

252
00:09:08,390 --> 00:09:09,750
那么我对应的这个吧

253
00:09:09,750 --> 00:09:10,790
这个 use count 是吧

254
00:09:10,790 --> 00:09:13,447
需要加一啊，加一

255
00:09:13,447 --> 00:09:15,720
然后我这边有一个 sleep 

256
00:09:15,720 --> 00:09:17,880
这个 sleep 就是在模拟什么

257
00:09:17,880 --> 00:09:20,320
就模拟说我既然选中了这台服务器

258
00:09:20,320 --> 00:09:22,800
那我要把我请求打给这台服务器

259
00:09:22,800 --> 00:09:23,740
那这台服务器呢

260
00:09:23,740 --> 00:09:26,660
需要经过一段时间才会可给我返回结果

261
00:09:26,660 --> 00:09:27,890
那经过这段时间

262
00:09:27,890 --> 00:09:30,820
我通过一个 time slip 来进行模拟啊

263
00:09:30,820 --> 00:09:34,610
模拟这边还是用一个随机数啊

264
00:09:34,610 --> 00:09:37,320
随机数来模拟这样一个延迟

265
00:09:37,320 --> 00:09:39,120
好，他给我返回结果之后

266
00:09:39,120 --> 00:09:41,480
我要令对应的并发度减一嘛

267
00:09:41,480 --> 00:09:43,800
哎，我把这个给归还回去

268
00:09:43,800 --> 00:09:45,722
令对应的并发度减一

269
00:09:45,722 --> 00:09:47,690
那最终下来的话呢

270
00:09:47,690 --> 00:09:50,310
由于所有的服务器被调用的次数

271
00:09:50,310 --> 00:09:52,990
都存在了这个 use count 里面嘛，对吧

272
00:09:52,990 --> 00:09:55,350
我对 use count 再进行一次打印

273
00:09:55,350 --> 00:09:56,630
按理来说啊

274
00:09:56,630 --> 00:09:58,160
那么每台服务器

275
00:09:58,160 --> 00:10:00,150
因为这跟逻辑是一模一样的嘛

276
00:10:00,150 --> 00:10:02,050
所以每台服务器它的命中的次数

277
00:10:02,050 --> 00:10:04,440
应该是非常接近

278
00:10:04,440 --> 00:10:05,800
才符合逻辑啊

279
00:10:05,800 --> 00:10:08,200
你可以看看它是不是非常接近
