1
00:00:00,480 --> 00:00:04,070
这节课我们来实现一个 GRPC 的连接池

2
00:00:04,070 --> 00:00:05,800
那么首先我们要思考一个问题

3
00:00:05,800 --> 00:00:09,105
就是我们为什么需要加 PC 的拼接时

4
00:00:09,105 --> 00:00:10,480
说到连接池呢

5
00:00:10,480 --> 00:00:13,000
我们可以听到其他很多各种池

6
00:00:13,000 --> 00:00:17,097
线程池、协程池、 MYSQL 的连接池

7
00:00:17,097 --> 00:00:20,130
一般情况下，我们凡是需要使用

8
00:00:20,130 --> 00:00:22,420
都是为了提高吞吐

9
00:00:22,420 --> 00:00:26,250
比方说有很多工作需要同时做

10
00:00:26,250 --> 00:00:29,690
那么我们如果已经准备好了很多个线程

11
00:00:29,690 --> 00:00:31,950
每一个线程分配一个工作

12
00:00:31,950 --> 00:00:34,890
大家就可以快速的开始行动，对吧

13
00:00:34,890 --> 00:00:37,100
快速的把这工作给做完

14
00:00:37,100 --> 00:00:40,640
因为不需要去重新创建线程嘛

15
00:00:40,640 --> 00:00:42,610
那连接池也是一样的

16
00:00:42,610 --> 00:00:46,280
假如说我也事先准备好了很多个 MYSQL 连接

17
00:00:46,280 --> 00:00:51,627
那么我可以并行的去处理多个读写请求

18
00:00:51,627 --> 00:00:54,660
但是对于加 PC 连接而言

19
00:00:54,660 --> 00:00:56,630
那一个加 PC 连接

20
00:00:56,630 --> 00:01:00,540
它已经可以并行的去处理多个请求了

21
00:01:00,540 --> 00:01:04,912
它可以同时去承载多个 request 或者 response 

22
00:01:04,912 --> 00:01:11,462
我们在这个文件就是这个 micro service 加 pc client 

23
00:01:11,462 --> 00:01:14,260
加 p c vs HTTP test 点 go 

24
00:01:14,260 --> 00:01:15,800
在这个 go 文件里

25
00:01:15,800 --> 00:01:17,700
我们写过一个测试

26
00:01:17,700 --> 00:01:20,440
就是我们测了一下一个加 PC 

27
00:01:20,440 --> 00:01:24,520
如果我们真正的去并行发起多个请求

28
00:01:24,520 --> 00:01:29,077
它这个性能上跟串行到底有没有区别啊

29
00:01:29,077 --> 00:01:30,630
简单给大家看一下

30
00:01:30,630 --> 00:01:37,280
这个代码是在这个 benchmark 加 PC 这个函数里面

31
00:01:37,280 --> 00:01:40,687
我们这边啊，其创建了一个 client 

32
00:01:40,687 --> 00:01:42,840
然后第103行

33
00:01:42,840 --> 00:01:44,480
这个是由于写了个 BT 

34
00:01:44,480 --> 00:01:46,220
Mark 是小于 B 点 N 

35
00:01:46,220 --> 00:01:48,520
是一个循环很多次

36
00:01:48,520 --> 00:01:51,000
那核心是第107行

37
00:01:51,000 --> 00:01:53,355
这个并

38
00:01:53,355 --> 00:01:56,840
我们是打算开十个线程啊

39
00:01:56,840 --> 00:02:01,350
这十个携程呢，他们去公用这一个 client 啊

40
00:02:01,350 --> 00:02:02,730
去公用这一个 client 

41
00:02:02,730 --> 00:02:06,510
一个 client 被十个继承并发的去使用啊

42
00:02:06,510 --> 00:02:08,792
去发起这个 JIPC 请求

43
00:02:08,792 --> 00:02:11,760
那么我们可以把这个 P 从十改为一嘛

44
00:02:11,760 --> 00:02:13,180
从并形改为串行

45
00:02:13,180 --> 00:02:14,260
我们对比了一下

46
00:02:14,260 --> 00:02:17,360
对比结果呢，在下边我们看啊

47
00:02:17,360 --> 00:02:19,540
如果是开十个并发的话

48
00:02:19,540 --> 00:02:21,472
那么对于 GIPC 而言

49
00:02:21,472 --> 00:02:26,345
它的每勾 P 是差不多是52万纳米

50
00:02:26,345 --> 00:02:28,170
如果是开一个印发的话

51
00:02:28,170 --> 00:02:29,950
他也是51.8

52
00:02:29,950 --> 00:02:31,690
差不多也是52万纳秒

53
00:02:31,690 --> 00:02:33,590
而这个时间几乎是一样的

54
00:02:33,590 --> 00:02:34,780
说明呢

55
00:02:34,780 --> 00:02:36,670
我一个加 PC 连接

56
00:02:36,670 --> 00:02:39,810
本来就可以同时发送多个请求嘛

57
00:02:39,810 --> 00:02:42,470
所以你发送一个跟发送十个

58
00:02:42,470 --> 00:02:43,930
这个耗时是一样的

59
00:02:43,930 --> 00:02:47,570
但是对于传统的 HTTP 而言就不一样

60
00:02:47,570 --> 00:02:48,830
你开一个的话

61
00:02:48,830 --> 00:02:51,350
那么这个速度显然是很快的

62
00:02:51,350 --> 00:02:53,010
但如果是开十个的话

63
00:02:53,010 --> 00:02:55,260
这个耗时啊就长了很多

64
00:02:55,260 --> 00:02:58,320
因为它本身底层不支持并发嘛

65
00:02:58,320 --> 00:03:01,410
你上层并发的去调用它

66
00:03:01,410 --> 00:03:03,630
底层只能去顺序排队嘛

67
00:03:03,630 --> 00:03:07,630
好，那既然一个加 PC 连接已经支持并发了

68
00:03:07,630 --> 00:03:09,590
那么这个吞吐量已经足够了啊

69
00:03:09,590 --> 00:03:10,110
通常来讲

70
00:03:10,110 --> 00:03:12,930
我们在 go 圆里面的一个加 PC 连接

71
00:03:12,930 --> 00:03:16,592
可以支持最多100个并发请求

72
00:03:16,592 --> 00:03:18,580
对于绝大多数的业务场景而言

73
00:03:18,580 --> 00:03:20,420
100个并发这个已经足够了啊

74
00:03:20,420 --> 00:03:22,720
不需要开多个连接

75
00:03:22,720 --> 00:03:27,200
那加 PC 连接池还有投资的必要吗

76
00:03:27,200 --> 00:03:29,450
好，为了回答这个问题啊

77
00:03:29,450 --> 00:03:31,590
其实我们来看这样一个图啊

78
00:03:31,590 --> 00:03:35,905
加 PC 连接池主要是为了解决负载均衡的问题

79
00:03:35,905 --> 00:03:38,050
因为我们正常情况下

80
00:03:38,050 --> 00:03:40,290
公司里面一个加 p server 

81
00:03:40,290 --> 00:03:43,460
它一般是布在多台服务器上面

82
00:03:43,460 --> 00:03:45,200
这里面是三台服务器

83
00:03:45,200 --> 00:03:46,817
S 1到 S 3

84
00:03:46,817 --> 00:03:48,730
然后前面挂一个代理

85
00:03:48,730 --> 00:03:51,070
那这代理呢，我们在讲构表的时候

86
00:03:51,070 --> 00:03:53,320
一般都会讲 EEDCD 是吧

87
00:03:53,320 --> 00:03:54,980
用来做服务的注册与发现

88
00:03:54,980 --> 00:03:56,900
包括服务的均衡等等

89
00:03:56,900 --> 00:03:58,780
但实际上在公司里面呢

90
00:03:58,780 --> 00:04:00,540
大部分还是用的 NINX 

91
00:04:00,540 --> 00:04:04,020
因为 NGINX 这个本来就是需要有的

92
00:04:04,020 --> 00:04:05,600
只要你有前端和后端

93
00:04:05,600 --> 00:04:07,160
总是要走 HTTP 嘛

94
00:04:07,160 --> 00:04:10,695
那 HTTP 的话肯定是用的 NTX 30

95
00:04:10,695 --> 00:04:12,360
所以既然已经 NGINX 了

96
00:04:12,360 --> 00:04:15,460
而且 index 现在也支持加 PC 了

97
00:04:15,460 --> 00:04:19,019
所以不如直接就使用这个词来作为加 PC 

98
00:04:19,019 --> 00:04:19,420
代理

99
00:04:19,420 --> 00:04:21,120
上面是一个 content 

100
00:04:21,120 --> 00:04:23,200
我们以一个 cat 为例来讲解

101
00:04:23,200 --> 00:04:28,200
那这个 client 它想事先建立好多个连接

102
00:04:28,200 --> 00:04:31,480
他呢，肯定是去询问代理 proxy 

103
00:04:31,480 --> 00:04:35,460
比方说这 cat 想事先建立好五个链接

104
00:04:35,460 --> 00:04:37,020
放到一个池子里面去啊

105
00:04:37,020 --> 00:04:38,460
这个池子你可以想象成

106
00:04:38,460 --> 00:04:40,432
就是一个数组或者一个切片

107
00:04:40,432 --> 00:04:42,610
第一次他询问 proxy 啊

108
00:04:42,610 --> 00:04:45,180
我应该跟谁建立连接

109
00:04:45,180 --> 00:04:46,340
那这个 proxy 呢

110
00:04:46,340 --> 00:04:50,270
内部做了一系列的负载均衡算法之后

111
00:04:50,270 --> 00:04:53,740
它返回 cat 说你应该跟 S 2建立连接

112
00:04:53,740 --> 00:04:56,410
第二次 cat 再去询问 proxy 

113
00:04:56,410 --> 00:04:59,590
这次呢是跟 S 3接连接

114
00:04:59,590 --> 00:05:02,882
后面意思是 S 3、 S 1、 S 3

115
00:05:02,882 --> 00:05:04,900
好，准备好了五个链接

116
00:05:04,900 --> 00:05:06,570
从这个情况里面来看

117
00:05:06,570 --> 00:05:09,850
我们发现 S 3这样三个连接

118
00:05:09,850 --> 00:05:11,870
S 1跟 S 2呢，只有一个连接

119
00:05:11,870 --> 00:05:13,690
这个负载不均衡

120
00:05:13,690 --> 00:05:15,037
但是没关系

121
00:05:15,037 --> 00:05:16,950
这只是一个 client 

122
00:05:16,950 --> 00:05:19,600
现实情况下是有好多个 client 

123
00:05:19,600 --> 00:05:20,780
所以总体而言

124
00:05:20,780 --> 00:05:24,650
那每一个 server 上面的负载是基本均衡的

125
00:05:24,650 --> 00:05:26,610
有了这样一个连接池之后

126
00:05:26,610 --> 00:05:29,240
那么 client 每次发送请求

127
00:05:29,240 --> 00:05:32,960
他就从这个池子里面随机的去选择一个

128
00:05:32,960 --> 00:05:34,505
那这个怎么随机

129
00:05:34,505 --> 00:05:37,240
这个算法由 client 自己去控制

130
00:05:37,240 --> 00:05:38,580
它可以轮询

131
00:05:38,580 --> 00:05:39,990
也可以随机

132
00:05:39,990 --> 00:05:43,670
也可以使用更复杂的选择策略

133
00:05:43,670 --> 00:05:44,680
所以注意一下

134
00:05:44,680 --> 00:05:47,840
这个地方是由两个负载均衡

135
00:05:47,840 --> 00:05:50,480
一个是代理 proxy 

136
00:05:50,480 --> 00:05:53,390
它内部需要内置一个服务的监控策略

137
00:05:53,390 --> 00:05:55,560
另外一个是 client 里边

138
00:05:55,560 --> 00:05:58,260
它从铺里面去取连接

139
00:05:58,260 --> 00:06:01,600
也需要有某种服务的权衡策略

140
00:06:01,600 --> 00:06:05,190
所以这个地方池子的作用就显现出来了

141
00:06:05,190 --> 00:06:09,702
假如说整个生产环境下只有一个 client 

142
00:06:09,702 --> 00:06:12,010
那这一个 client 通过连接池

143
00:06:12,010 --> 00:06:15,110
就有可能跟每一个 server 都发生交互

144
00:06:15,110 --> 00:06:18,192
每一个 server 都能分担一定压力

145
00:06:18,192 --> 00:06:19,930
而如果没有连接池

146
00:06:19,930 --> 00:06:22,820
一个 cat 上面只有一个链接

147
00:06:22,820 --> 00:06:26,140
他肯定是跟某一台 server 建立连接

148
00:06:26,140 --> 00:06:29,685
而另外两台 server 压根就收不到任何请求

149
00:06:29,685 --> 00:06:31,960
所以负载严重的不均衡

150
00:06:31,960 --> 00:06:33,520
看第二个情况

151
00:06:33,520 --> 00:06:36,282
那假如说 server 2挂了

152
00:06:36,282 --> 00:06:38,910
此时我们的连接池里面

153
00:06:38,910 --> 00:06:41,550
有一个指向 server 2的连接

154
00:06:41,550 --> 00:06:44,250
那这个连接就不可用了

155
00:06:44,250 --> 00:06:48,370
一旦发现池子里面某一个连接不可用的话

156
00:06:48,370 --> 00:06:52,330
我们的坎普就要重新向 proxy 

157
00:06:52,330 --> 00:06:54,150
去申请一个新的链接

158
00:06:54,150 --> 00:06:57,090
那此时 proxy 告诉 cat 

159
00:06:57,090 --> 00:07:00,645
你需要跟 S 1或者是跟 S 3进行连接

160
00:07:00,645 --> 00:07:04,630
此时 proxy 已经把 S 2给剔除在外了啊

161
00:07:04,630 --> 00:07:05,530
假如说呢

162
00:07:05,530 --> 00:07:09,010
这次 client 是跟 S 1建立了连接

163
00:07:09,010 --> 00:07:12,450
那么 S 1就顶替了原先 S 2的位置

164
00:07:12,450 --> 00:07:14,525
再看一下第三种情况

165
00:07:14,525 --> 00:07:17,715
就是我们新增了一台 S 4

166
00:07:17,715 --> 00:07:20,210
原线连接都是可用的

167
00:07:20,210 --> 00:07:24,515
那么新这代 server 似乎永远用不上

168
00:07:24,515 --> 00:07:27,970
因为 S 4它不在这个连接池里面

169
00:07:27,970 --> 00:07:30,810
所以这个时候我们要引用一个新的策略

170
00:07:30,810 --> 00:07:34,820
就是呢，池子里面每一个连接虽然都是可用的

171
00:07:34,820 --> 00:07:37,390
但是我们给他设置一个倒计时间

172
00:07:37,390 --> 00:07:39,172
比方说10分钟

173
00:07:39,172 --> 00:07:43,460
那么10分钟之后强制的把链接给关闭掉

174
00:07:43,460 --> 00:07:47,227
然后重新向 proxy 申请一个新的连接

175
00:07:47,227 --> 00:07:48,730
那这个时候

176
00:07:48,730 --> 00:07:53,050
proxy 就有可能会把新增的这台 S 4

177
00:07:53,050 --> 00:07:54,237
告诉 client 

178
00:07:54,237 --> 00:07:55,180
那 IS 4呢

179
00:07:55,180 --> 00:07:58,070
就有可能进入到这个连接池里面去

180
00:07:58,070 --> 00:08:00,290
同时啊，在这个地方有一个小技巧

181
00:08:00,290 --> 00:08:03,870
就是我们让每个连接的过期时间

182
00:08:03,870 --> 00:08:05,290
产生一点差异

183
00:08:05,290 --> 00:08:09,370
大家不要都是在10分钟那个点上同时失效

184
00:08:09,370 --> 00:08:13,520
这样的话会导致某一个瞬间没有可用连接

185
00:08:13,520 --> 00:08:16,200
所以呢，给它一个随机扰动

186
00:08:16,200 --> 00:08:17,680
有人是10分钟

187
00:08:17,680 --> 00:08:18,940
有人是11分钟

188
00:08:18,940 --> 00:08:20,080
有人是10分钟

189
00:08:20,080 --> 00:08:20,527
半

190
00:08:20,527 --> 00:08:22,250
补充一个小的知识点

191
00:08:22,250 --> 00:08:23,890
有同学呢，会有疑问

192
00:08:23,890 --> 00:08:25,490
说一个 client 

193
00:08:25,490 --> 00:08:26,920
比方说第一张图

194
00:08:26,920 --> 00:08:27,950
一个 client 

195
00:08:27,950 --> 00:08:31,540
它怎么可能和 S 3建立三个连接呢

196
00:08:31,540 --> 00:08:32,590
这个是可以的

197
00:08:32,590 --> 00:08:35,450
无非是使用三个不同的端口号嘛

198
00:08:35,450 --> 00:08:38,594
我们在讲 TCP socket 编程的时候

199
00:08:38,594 --> 00:08:39,700
我说过

200
00:08:39,700 --> 00:08:42,310
作为 TCP 的 zero 端

201
00:08:42,310 --> 00:08:44,210
他在一个端口号上面

202
00:08:44,210 --> 00:08:46,270
把这个 server 进程给起好了

203
00:08:46,270 --> 00:08:50,240
它可以同时监听来自多个 content 的连接

204
00:08:50,240 --> 00:08:52,830
那每一个 content 系统

205
00:08:52,830 --> 00:08:55,050
会给它随机分配一个端口号

206
00:08:55,050 --> 00:08:56,750
去跟 server 建立连接

207
00:08:56,750 --> 00:09:02,430
如果一台机器上想跟同一个 server 建立三个连接

208
00:09:02,430 --> 00:09:06,270
那操作系统呢，会给大家随机分配三个端口号

209
00:09:06,270 --> 00:09:08,840
我们在讲手如 RPC 的时候

210
00:09:08,840 --> 00:09:13,270
在这个卖 RPC 里面有一个 transport server TCP 

211
00:09:13,270 --> 00:09:15,115
打开这个 TCP 

212
00:09:15,115 --> 00:09:17,520
你看作为服务端的话

213
00:09:17,520 --> 00:09:21,320
他在这一个端口上面把服务给提起来

214
00:09:21,320 --> 00:09:24,760
它可以接收多个客户端链接

215
00:09:24,760 --> 00:09:29,030
然后呢，在这个连接上面调用 remote address 

216
00:09:29,030 --> 00:09:32,890
可以把客户端的 IP 和端口号给打印出来

217
00:09:32,890 --> 00:09:35,980
所以有可能是来自于同一个 IP 

218
00:09:35,980 --> 00:09:38,710
但是呢，多个不同的端口号

219
00:09:38,710 --> 00:09:41,010
那 GRPC 连接池的完整代码

220
00:09:41,010 --> 00:09:44,847
是在这个 GRPCCOMO 这个目录下

221
00:09:44,847 --> 00:09:48,360
我们下一节课把代码的实现细节讲一下
