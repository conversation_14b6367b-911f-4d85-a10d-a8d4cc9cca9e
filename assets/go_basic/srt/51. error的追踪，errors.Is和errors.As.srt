1
00:00:00,360 --> 00:00:03,540
上一节课末尾，我们提到了 eroes as 

2
00:00:03,540 --> 00:00:06,140
那我们先来看看 eros as 的语法是什么

3
00:00:06,140 --> 00:00:08,119
然后来讲一讲这个语法

4
00:00:08,119 --> 00:00:09,970
它产生的背景是什么

5
00:00:09,970 --> 00:00:12,130
最后讲一讲 eros as 

6
00:00:12,130 --> 00:00:13,590
看第11行

7
00:00:13,590 --> 00:00:16,090
A ， B 呢，是一个普通的 error 

8
00:00:16,090 --> 00:00:20,460
BB 呢，是通过 F 、 M 、 T 、 L 、 F 这种方式

9
00:00:20,460 --> 00:00:22,370
它也构造了一个 error 

10
00:00:22,370 --> 00:00:24,825
只不过我们注意一下

11
00:00:24,825 --> 00:00:28,160
这个格式化人用的是百分号 W 啊

12
00:00:28,160 --> 00:00:30,690
这个 W 实际上是英文单

13
00:00:30,690 --> 00:00:32,360
rap 的首字母

14
00:00:32,360 --> 00:00:33,200
就包装嘛

15
00:00:33,200 --> 00:00:36,200
封装它可不是一个简单的格式化

16
00:00:36,200 --> 00:00:37,800
跟我们之前讲的什么百分 S 啊

17
00:00:37,800 --> 00:00:38,440
百分号 D 啊

18
00:00:38,440 --> 00:00:39,637
还不完全一样

19
00:00:39,637 --> 00:00:42,250
C 变量也是通过 L 、 O 、 F 

20
00:00:42,250 --> 00:00:45,215
然后也是用到了百分号 W 

21
00:00:45,215 --> 00:00:48,430
那跟这个百分号 W 相对应的是什么呢

22
00:00:48,430 --> 00:00:50,070
应该是什么数据类型呢

23
00:00:50,070 --> 00:00:51,190
应该是一个 error 

24
00:00:51,190 --> 00:00:53,510
你看这个 A 变量本身是一个 error 

25
00:00:53,510 --> 00:00:54,702
然后呢

26
00:00:54,702 --> 00:00:56,140
这边这个 B 变量是吧

27
00:00:56,140 --> 00:00:57,680
它也是一个 L 

28
00:00:57,680 --> 00:01:01,400
所以是 L 跟这个百分号 W 进行一个对应

29
00:01:01,400 --> 00:01:05,372
那 B 和 A 是什么关系呢

30
00:01:05,372 --> 00:01:07,960
C 和 B 又是什么关系呢

31
00:01:07,960 --> 00:01:13,242
你可以理解为 A 是那个最内核的、最核心的 LR 

32
00:01:13,242 --> 00:01:14,872
然后 B 呢

33
00:01:14,872 --> 00:01:16,680
rap 包装嘛，对吧

34
00:01:16,680 --> 00:01:19,790
B 相当于在 A 的外面套了个壳子

35
00:01:19,790 --> 00:01:21,672
对 A 进行了一个包装

36
00:01:21,672 --> 00:01:22,980
然后 C 呢

37
00:01:22,980 --> 00:01:25,420
又在 B 的外面又套了一层壳子

38
00:01:25,420 --> 00:01:26,560
又进行了一个包装

39
00:01:26,560 --> 00:01:28,390
而这次包装呢

40
00:01:28,390 --> 00:01:34,010
他把一二、三前缀和 A 、 B 、 C 后缀加了进来

41
00:01:34,010 --> 00:01:36,880
就是我们在一层层套壳子的时候

42
00:01:36,880 --> 00:01:40,180
可以追加一些额外的附属信息

43
00:01:40,180 --> 00:01:41,460
但是你也可以不理解

44
00:01:41,460 --> 00:01:44,362
但一般的话都会去追加额外信息

45
00:01:44,362 --> 00:01:47,570
好，所以是 C 套着 BB ，套着 A 

46
00:01:47,570 --> 00:01:51,040
然后的话这个 errose is 是吧

47
00:01:51,040 --> 00:01:55,780
ca 就判断 C 是否把 A 给套住了

48
00:01:55,780 --> 00:01:58,900
这 C 是否是 A 的一个包装啊

49
00:01:58,900 --> 00:02:00,267
封装之后的结果

50
00:02:00,267 --> 00:02:01,450
包括下面这个

51
00:02:01,450 --> 00:02:05,190
他判断 C 呢是否是 B 包装之后的结果

52
00:02:05,190 --> 00:02:06,960
当然都是了啊

53
00:02:06,960 --> 00:02:08,650
这两个都是返回 true 的

54
00:02:08,650 --> 00:02:12,470
所以 else is 返回的是一个布尔变量

55
00:02:12,470 --> 00:02:16,005
那布尔变量应该是用百分号 T 来进行对应

56
00:02:16,005 --> 00:02:20,030
所以说 errose is 它还不是严格的等号判断是吧

57
00:02:20,030 --> 00:02:22,430
你说这个 C 跟 B 相等吗

58
00:02:22,430 --> 00:02:23,210
它不相等

59
00:02:23,210 --> 00:02:24,750
它确实是两个不同的 IO 

60
00:02:24,750 --> 00:02:28,540
但是呢， C 是由 B 包装而来的

61
00:02:28,540 --> 00:02:30,490
那么什么样的业务场景

62
00:02:30,490 --> 00:02:32,597
需要用到这个包装呢

63
00:02:32,597 --> 00:02:35,650
我们来看下面这个函数调用链啊

64
00:02:35,650 --> 00:02:41,317
我们是在 handler 函数里面调用了 A 函数

65
00:02:41,317 --> 00:02:44,960
然后呢，在 A 函数里面去调用了 B 函数

66
00:02:44,960 --> 00:02:48,240
在 B 函数里面调用了 C 函数是吧

67
00:02:48,240 --> 00:02:50,297
函数的层层调用

68
00:02:50,297 --> 00:02:52,850
所以 C 函数作为最下游

69
00:02:52,850 --> 00:02:55,037
最底层的这样一个函数呢

70
00:02:55,037 --> 00:02:56,840
假如说啊， C 函数里面

71
00:02:56,840 --> 00:03:01,452
他打算把一个字符串转成一个数字

72
00:03:01,452 --> 00:03:04,050
如果这个字符串里面不是纯数字

73
00:03:04,050 --> 00:03:05,510
包含英文字母的话

74
00:03:05,510 --> 00:03:06,610
就会转失败是吧

75
00:03:06,610 --> 00:03:08,662
这个 L 呢，就不为空

76
00:03:08,662 --> 00:03:10,660
如果发现 L 不为空的话

77
00:03:10,660 --> 00:03:13,020
那这边他打算把这个 L 呢

78
00:03:13,020 --> 00:03:14,540
往上游去抛啊

79
00:03:14,540 --> 00:03:16,645
抛给上游，返回给上游吗

80
00:03:16,645 --> 00:03:17,700
所以这边的话

81
00:03:17,700 --> 00:03:19,340
如果我们只是简单的

82
00:03:19,340 --> 00:03:20,090
比如说

83
00:03:20,090 --> 00:03:23,337
好，这样原封不动进行返回的话

84
00:03:23,337 --> 00:03:24,710
我们在每一个地方

85
00:03:24,710 --> 00:03:26,330
假如都是这样

86
00:03:26,330 --> 00:03:28,882
直接返回原始的 error 

87
00:03:28,882 --> 00:03:30,180
在 A 函数这边呢

88
00:03:30,180 --> 00:03:33,290
也是直接返回原始的 error 

89
00:03:33,290 --> 00:03:37,432
然后到 handler 这边去调用 A 嘛，对吧

90
00:03:37,432 --> 00:03:39,070
发现 error 不为空

91
00:03:39,070 --> 00:03:40,850
把 ERRO 呢进行一个打印

92
00:03:40,850 --> 00:03:44,230
那我们看看打印出来这个 error 是什么

93
00:03:44,230 --> 00:03:47,990
我们传一个0 A 3吧

94
00:03:47,990 --> 00:03:49,410
好，跑一下

95
00:03:51,430 --> 00:03:55,780
呃，这两个 true 是那个 errose is 这边打出来的啊

96
00:03:55,780 --> 00:03:56,890
那中间是吧

97
00:03:56,890 --> 00:04:01,510
这一行信息就是我们的 D 52行打出来的

98
00:04:01,510 --> 00:04:02,880
大家注意这个信息啊

99
00:04:02,880 --> 00:04:04,420
说这个 a to i 对吧

100
00:04:04,420 --> 00:04:08,350
在解析0 A 3的时候发生了异常啊

101
00:04:08,350 --> 00:04:10,070
这是一个非法的语法

102
00:04:10,070 --> 00:04:13,160
就说你不能把这个0 A 3转成数字嘛

103
00:04:13,160 --> 00:04:15,340
但我们现在把代码通读了一遍

104
00:04:15,340 --> 00:04:16,490
我们知道啊

105
00:04:16,490 --> 00:04:19,190
是由于它最终在最下游

106
00:04:19,190 --> 00:04:22,800
在 C 函数内部应该是这一行发生的问题

107
00:04:22,800 --> 00:04:24,390
但是在实际工作中啊

108
00:04:24,390 --> 00:04:26,970
当你的这个函数调用链特别长的时候

109
00:04:26,970 --> 00:04:28,690
你的代码量特别多的时候

110
00:04:28,690 --> 00:04:30,590
你其实看到一个信息

111
00:04:30,590 --> 00:04:35,290
你很难判断出他是从什么地方抛出来的

112
00:04:35,290 --> 00:04:38,440
这个问题到底是 A 函数还是 B 函数

113
00:04:38,440 --> 00:04:39,040
还是奇函数

114
00:04:39,040 --> 00:04:40,450
你根本就不知道

115
00:04:40,450 --> 00:04:44,235
就是说这个异常的追踪很难

116
00:04:44,235 --> 00:04:47,380
所以呢，我们在返回 ERROSE 的时候啊

117
00:04:47,380 --> 00:04:49,440
最好给它包装一层

118
00:04:49,440 --> 00:04:50,740
就是像这样啊

119
00:04:50,740 --> 00:04:52,072
在 C 函数里面

120
00:04:52,072 --> 00:04:54,410
假如说我想去返回这个 error 

121
00:04:54,410 --> 00:04:58,720
那么呢，我在 error 的基础上通过百分号 W 是吧

122
00:04:58,720 --> 00:05:01,390
IWTLOF 百分号 W 

123
00:05:01,390 --> 00:05:02,070
通过这种方式

124
00:05:02,070 --> 00:05:04,362
我对原始代 L 呢进行一次包装

125
00:05:04,362 --> 00:05:06,080
而在这个曝光的时候

126
00:05:06,080 --> 00:05:08,620
我就会把一些额外信息加进来

127
00:05:08,620 --> 00:05:11,140
诶，你看我写了一个 C 冒号 cast 

128
00:05:11,140 --> 00:05:14,220
就表示我是在 C 函数内部

129
00:05:14,220 --> 00:05:15,600
在执行 cast 

130
00:05:15,600 --> 00:05:17,970
就执行这个类型转换的时

131
00:05:17,970 --> 00:05:19,860
发生了 error 啊

132
00:05:19,860 --> 00:05:20,840
就像这样的话

133
00:05:20,840 --> 00:05:23,000
这个 L 信息就很明确嘛

134
00:05:23,000 --> 00:05:25,020
啊，知道是从哪爆出来的

135
00:05:25,020 --> 00:05:27,500
再比方说在 B 函数里边啊

136
00:05:27,500 --> 00:05:29,460
他打算去返回 L ， A 的话

137
00:05:29,460 --> 00:05:31,150
它也对这个 L 呢

138
00:05:31,150 --> 00:05:32,470
也包装一下，对吧

139
00:05:32,470 --> 00:05:33,670
通过摆放 W 啊

140
00:05:33,670 --> 00:05:35,960
对这个 L 包装一下指示书啊

141
00:05:35,960 --> 00:05:37,920
它是从 B 函数里面爆出来的

142
00:05:37,920 --> 00:05:38,560
当然了

143
00:05:38,560 --> 00:05:39,660
他这个地方的话

144
00:05:39,660 --> 00:05:43,260
我发现其实你可以同时包装多个 error 

145
00:05:43,260 --> 00:05:45,320
就好比是一个大箱子里面

146
00:05:45,320 --> 00:05:47,140
除了可以套一个小箱子之外

147
00:05:47,140 --> 00:05:48,960
你甚至可以套两个小箱子

148
00:05:48,960 --> 00:05:49,320
当然了

149
00:05:49,320 --> 00:05:51,922
这两个小箱子它们是并列关系

150
00:05:51,922 --> 00:05:54,450
这两个 error 是并列关系

151
00:05:54,450 --> 00:05:56,900
在它们两个外面又套了一层

152
00:05:56,900 --> 00:05:59,320
就是这个 L 

153
00:05:59,320 --> 00:06:01,630
然后在 A 函数里面呢

154
00:06:01,630 --> 00:06:04,075
它也包装一层

155
00:06:04,075 --> 00:06:05,990
加一个附加信息啊

156
00:06:05,990 --> 00:06:06,610
告诉别人

157
00:06:06,610 --> 00:06:09,160
他是从 A 函数里面抛出来的这样一个 L 

158
00:06:09,160 --> 00:06:11,782
好，我们再来运行一下

159
00:06:11,782 --> 00:06:12,800
大家看啊

160
00:06:12,800 --> 00:06:15,810
这个信息呢，就变成了这个样子，对吧

161
00:06:15,810 --> 00:06:16,560
他说

162
00:06:16,560 --> 00:06:19,180
哦，我在 A 函数里面呢，发生了一个 L 

163
00:06:19,180 --> 00:06:20,400
然后呢，发现，诶

164
00:06:20,400 --> 00:06:23,127
它这个 L 呢，又是从 B 函数里面爆出来的

165
00:06:23,127 --> 00:06:24,050
然后发现，哦

166
00:06:24,050 --> 00:06:26,690
原来是从 C 函数的 cost 里面爆出来的

167
00:06:26,690 --> 00:06:29,715
我们刚才在这个 C 函数里面啊

168
00:06:29,715 --> 00:06:32,310
它有两个地方都可能会反馈 ERO 

169
00:06:32,310 --> 00:06:34,070
一个是说在这个地方

170
00:06:34,070 --> 00:06:35,940
一个是说在这个地方

171
00:06:35,940 --> 00:06:38,200
那既然两个地方都可能返回 VALERO 

172
00:06:38,200 --> 00:06:41,250
如果你只是写一个简单的 C 卡的话

173
00:06:41,250 --> 00:06:42,330
就不能够明确

174
00:06:42,330 --> 00:06:45,340
到底是从细看是哪个地方爆出来的

175
00:06:45,340 --> 00:06:48,420
所以呢，你还可以再加一点额外信息啊

176
00:06:48,420 --> 00:06:50,960
他是说在类型转换这发生的

177
00:06:50,960 --> 00:06:54,520
还是说在执行这个除法这发生的，对吧

178
00:06:54,520 --> 00:06:59,235
目前来看是在 C 函数的 cast 这一步发生了 error 

179
00:06:59,235 --> 00:07:00,900
那具体的 L 信息呢

180
00:07:00,900 --> 00:07:04,317
就是这些个信息

181
00:07:04,317 --> 00:07:07,810
所以啊，我当前选中的这个内容

182
00:07:07,810 --> 00:07:11,702
实际上对应的就是第40行代码的这个地方

183
00:07:11,702 --> 00:07:14,620
那为什么还会有这些信息呢

184
00:07:14,620 --> 00:07:15,960
什么 not found error 

185
00:07:15,960 --> 00:07:17,550
这个是从哪爆出来的

186
00:07:17,550 --> 00:07:20,560
这个呀，我们根据这个小括号的嵌套关系

187
00:07:20,560 --> 00:07:24,550
你会发现它实际上是在 B 函数这边来看

188
00:07:24,550 --> 00:07:25,900
在 B 函数这里

189
00:07:25,900 --> 00:07:28,400
我不是包装了两个 error 吗

190
00:07:28,400 --> 00:07:30,960
所以呢，这个 not found i 

191
00:07:30,960 --> 00:07:34,585
它实际上指的是这边的第二个百分号 W 

192
00:07:34,585 --> 00:07:36,960
好，返回，返回给了 A 对吧

193
00:07:36,960 --> 00:07:39,660
A 的话，这是 A 这一部分对吧

194
00:07:39,660 --> 00:07:40,320
这一部分

195
00:07:40,320 --> 00:07:42,557
然后的话，整个这一部分呢

196
00:07:42,557 --> 00:07:45,882
相当于这里面的这个摆放 W 

197
00:07:45,882 --> 00:07:47,080
那这样的话

198
00:07:47,080 --> 00:07:49,640
这个 L 的追踪啊，就比较清晰了

199
00:07:49,640 --> 00:07:51,540
我们就知道他是一步一步

200
00:07:51,540 --> 00:07:53,872
源头是从哪爆出来的

201
00:07:53,872 --> 00:07:55,790
我们再来看第54行

202
00:07:55,790 --> 00:07:58,310
那么我拿到这样一个 L 的话

203
00:07:58,310 --> 00:08:00,430
我通过等号去判断一下

204
00:08:00,430 --> 00:08:03,750
这个 L 是否是等于这个 l not found 的

205
00:08:03,750 --> 00:08:04,750
它显然不等于

206
00:08:04,750 --> 00:08:09,440
因为这个 error 它是从 A 函数抛出来的，对吧

207
00:08:09,440 --> 00:08:13,300
A 函数抛出来的是这样一个 L 啊

208
00:08:13,300 --> 00:08:17,750
它显然不等于这个 error not found 的

209
00:08:17,750 --> 00:08:21,680
但是呢，它实际上 A 卡就抛出的 error 

210
00:08:21,680 --> 00:08:23,100
它实际上里面啊

211
00:08:23,100 --> 00:08:26,260
它包装了这个 error not found 

212
00:08:26,260 --> 00:08:27,640
为什么呢

213
00:08:27,640 --> 00:08:29,740
因为我们在 B 函数这边

214
00:08:29,740 --> 00:08:32,020
你看这不就已经把这个 error 

215
00:08:32,020 --> 00:08:34,048
not found 给包进来了吗

216
00:08:34,048 --> 00:08:37,679
所以说如果我们的54行

217
00:08:37,679 --> 00:08:39,980
如果换成第55

218
00:08:39,980 --> 00:08:41,760
这种写法的话是吧

219
00:08:41,760 --> 00:08:46,890
errose is 就判断一下这个 error 它是否里面包含了啊

220
00:08:46,890 --> 00:08:49,372
包装了这个 error 它是满足的

221
00:08:49,372 --> 00:08:50,400
我们再来运行一下

222
00:08:50,400 --> 00:08:52,480
你就会发现它能够返回400

223
00:08:53,780 --> 00:08:55,170
运行一下

224
00:08:55,170 --> 00:08:58,360
你看这次就是返回了400，对吧

225
00:08:58,360 --> 00:08:59,570
400

226
00:08:59,570 --> 00:09:02,242
然后我们再来看 iOS as 

227
00:09:02,242 --> 00:09:04,150
当我去调一个函数

228
00:09:04,150 --> 00:09:05,830
拿到一个 L 之后

229
00:09:05,830 --> 00:09:07,372
L 不为空

230
00:09:07,372 --> 00:09:09,960
那么我想看看这个 error 啊

231
00:09:09,960 --> 00:09:11,000
因为他是个接口嘛，对吧

232
00:09:11,000 --> 00:09:14,687
我想看看它是不是某一种具体的结构体

233
00:09:14,687 --> 00:09:16,510
那我的这个猜测目标呢

234
00:09:16,510 --> 00:09:20,130
先锚定了某种特定的 ERROMLRO 吧

235
00:09:20,130 --> 00:09:21,350
以 my l 为例

236
00:09:21,350 --> 00:09:23,470
error 是一个接口对吧

237
00:09:23,470 --> 00:09:26,570
通过什么这种类型断言啊

238
00:09:26,570 --> 00:09:29,500
我先断言它就是这种类型

239
00:09:29,500 --> 00:09:31,970
之前我们讲那个空接口的时候

240
00:09:31,970 --> 00:09:33,250
说这个空接口啊

241
00:09:33,250 --> 00:09:35,190
它可以通过类型断言

242
00:09:35,190 --> 00:09:36,870
把它断言为某种具体类型

243
00:09:36,870 --> 00:09:37,610
现在的话

244
00:09:37,610 --> 00:09:40,970
对于一个普通的接口类型啊

245
00:09:40,970 --> 00:09:44,580
也可以通过断言把断言某种具体的结构体

246
00:09:44,580 --> 00:09:47,270
当然了，可能会断言失败，对吧

247
00:09:47,270 --> 00:09:49,430
OK 的话表示断言成功

248
00:09:49,430 --> 00:09:51,390
好，如果断言成功的话

249
00:09:51,390 --> 00:09:54,600
那么这个 E 就是一种具体的结构体

250
00:09:54,600 --> 00:09:57,320
我就可以拿到这个结构体里面的

251
00:09:57,320 --> 00:09:59,222
所有的成员变量

252
00:09:59,222 --> 00:10:03,847
比如说 name 、 code 和 disk 我都可以拿到

253
00:10:03,847 --> 00:10:06,240
但如果说只是一个 error 的话

254
00:10:06,240 --> 00:10:07,320
它是一个接口吗

255
00:10:07,320 --> 00:10:10,700
这个接口只有一个 error 方法可供调用

256
00:10:10,700 --> 00:10:15,362
我是拿不到这个 name 、 code 和 disco 

257
00:10:15,362 --> 00:10:17,480
所以这是断言的目的

258
00:10:17,480 --> 00:10:19,610
那为了实现这个目的

259
00:10:19,610 --> 00:10:21,470
除了可以使用类型断言之外

260
00:10:21,470 --> 00:10:24,302
也可以使用 iOS as s 

261
00:10:24,302 --> 00:10:25,550
来看一下啊

262
00:10:25,550 --> 00:10:27,960
这边是先声明了一个 MAL 哦

263
00:10:27,960 --> 00:10:30,150
某种特定的 L 类型

264
00:10:30,150 --> 00:10:32,412
它是一、二这两个变量

265
00:10:32,412 --> 00:10:34,230
然后通过 eros as 呢

266
00:10:34,230 --> 00:10:35,870
还是把这个 error 啊

267
00:10:35,870 --> 00:10:36,990
它是一个接口嘛

268
00:10:36,990 --> 00:10:38,910
把这个作为第一个参数传进来

269
00:10:38,910 --> 00:10:39,770
第二个参数呢

270
00:10:39,770 --> 00:10:42,890
是把刚才搞的这个空结构体啊

271
00:10:42,890 --> 00:10:44,290
一、二是一个空结构体啊

272
00:10:44,290 --> 00:10:46,470
里面的成员变量都还没有赋值

273
00:10:46,470 --> 00:10:47,732
都是零时

274
00:10:47,732 --> 00:10:50,820
那么我打算通过 eros as 

275
00:10:50,820 --> 00:10:54,060
去给这个空结构体赋值是吧

276
00:10:54,060 --> 00:10:57,860
你打算在一个函数里面去修改一个结构体

277
00:10:57,860 --> 00:11:00,487
是不是得传它对应的指针呢

278
00:11:00,487 --> 00:11:02,370
否则要发生拷贝嘛

279
00:11:02,370 --> 00:11:03,890
你记过的是备份

280
00:11:03,890 --> 00:11:06,110
那我们想对外面的这个一二

281
00:11:06,110 --> 00:11:07,770
产生实际的影响

282
00:11:07,770 --> 00:11:11,550
所以的话必须要加一个取值符号传指针

283
00:11:11,550 --> 00:11:12,770
但问题是说

284
00:11:12,770 --> 00:11:14,710
一旦这个 error 这个接

285
00:11:14,710 --> 00:11:17,670
它不是 ml 、 RO 这种类型的话

286
00:11:17,670 --> 00:11:21,930
那么你这种企图显然是不能得逞的

287
00:11:21,930 --> 00:11:24,300
因为这个结构体类型都不一样

288
00:11:24,300 --> 00:11:26,700
但你不可能给它复制复过去嘛

289
00:11:26,700 --> 00:11:31,320
而如果说类型确实是 ml 类型的话

290
00:11:31,320 --> 00:11:33,590
那么返回值 OK 就是 true 

291
00:11:33,590 --> 00:11:37,000
那这样的话你就成功的给一二进行了赋值

292
00:11:37,000 --> 00:11:39,560
那么你就可以去使用 E 2里面的

293
00:11:39,560 --> 00:11:40,742
各种成员变量

294
00:11:40,742 --> 00:11:42,330
所以最后总结一下

295
00:11:42,330 --> 00:11:45,910
errose is 呢，它是判断一下第一个参数

296
00:11:45,910 --> 00:11:49,480
它是否从第二个参数包装而来的

297
00:11:49,480 --> 00:11:51,230
而 errose as 呢

298
00:11:51,230 --> 00:11:54,080
主要是想把一个接口的 error 

299
00:11:54,080 --> 00:11:56,380
把它转为某种具体的 error 

300
00:11:56,380 --> 00:11:59,780
这样的话我就可以去获得这个具体的结构体

301
00:11:59,780 --> 00:12:01,942
里面的各种成员变量了

302
00:12:01,942 --> 00:12:03,470
而且这个 else as 

303
00:12:03,470 --> 00:12:06,650
实际上可以通过我们传统的类型断言

304
00:12:06,650 --> 00:12:07,970
来完成同样的目的
