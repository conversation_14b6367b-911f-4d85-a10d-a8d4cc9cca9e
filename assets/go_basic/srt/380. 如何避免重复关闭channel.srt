1
00:00:00,679 --> 00:00:03,940
这节课我们主要讲如何优雅的去关闭 channel 

2
00:00:03,940 --> 00:00:05,780
而难点在于一个 channe

3
00:00:05,780 --> 00:00:08,235
它不能够被第二次关闭

4
00:00:08,235 --> 00:00:12,040
OK ，还是这样一个生产方和消费方的场景

5
00:00:12,040 --> 00:00:15,010
而且呢，我们的生产方有1000个

6
00:00:15,010 --> 00:00:16,980
消费方呢，有十个，对吧

7
00:00:16,980 --> 00:00:18,510
多生产方、多消费方

8
00:00:18,510 --> 00:00:19,400
它们之间呢

9
00:00:19,400 --> 00:00:24,220
还是通过一个 data channel 来进行通信、传输数据

10
00:00:24,220 --> 00:00:24,930
好

11
00:00:24,930 --> 00:00:27,620
那我会起1000个子继承

12
00:00:27,620 --> 00:00:28,660
他们作为生产方

13
00:00:28,660 --> 00:00:30,820
起十个子集成作为消费方

14
00:00:30,820 --> 00:00:32,280
那么我的主线程呢

15
00:00:32,280 --> 00:00:33,240
要等生产

16
00:00:33,240 --> 00:00:34,780
然后接触方全部结束

17
00:00:34,780 --> 00:00:37,040
所以呢，使用了 weight group 啊

18
00:00:37,040 --> 00:00:39,427
开辟了两个 weight group 

19
00:00:39,427 --> 00:00:41,610
不管是生产方还是消费方

20
00:00:41,610 --> 00:00:45,050
他们都有可能会发出终止信号

21
00:00:45,050 --> 00:00:47,330
比方说我要发出一个终止信号

22
00:00:47,330 --> 00:00:48,810
这样的话呢，所有生产方

23
00:00:48,810 --> 00:00:49,670
所有交易方

24
00:00:49,670 --> 00:00:50,850
大家全部终止

25
00:00:50,850 --> 00:00:53,290
然后我整个函数退出啊

26
00:00:53,290 --> 00:00:55,010
不管是 standard 还是 receiver 

27
00:00:55,010 --> 00:00:56,890
都可能会发出这样一个信号

28
00:00:56,890 --> 00:00:57,930
那这个信号呢

29
00:00:57,930 --> 00:01:00,410
会发送到这个 stop channel 啊

30
00:01:00,410 --> 00:01:01,570
这个管道里面去

31
00:01:02,660 --> 00:01:03,500
那最后呢

32
00:01:03,500 --> 00:01:06,340
我们会通过一个全局变量来记录下来

33
00:01:06,340 --> 00:01:09,180
那那个终止信号到底是谁发出来的啊

34
00:01:09,180 --> 00:01:10,240
是哪个生产方

35
00:01:10,240 --> 00:01:12,470
是哪一个消费方发出来的

36
00:01:12,470 --> 00:01:14,880
OK ，这边呢，是是吧

37
00:01:14,880 --> 00:01:17,380
起了1000个子吸尘

38
00:01:17,380 --> 00:01:20,520
它们来充当生产房。 sender 

39
00:01:20,520 --> 00:01:22,340
我为了标记，呃

40
00:01:22,340 --> 00:01:24,680
为了给每一个 sender 有一个标记呢

41
00:01:24,680 --> 00:01:27,620
这边他是把这个 for 循环的这个 I 呢，给传进来

42
00:01:27,620 --> 00:01:29,600
你看 I 从这儿啊，传进

43
00:01:29,600 --> 00:01:31,360
作为字符串传进来了

44
00:01:31,360 --> 00:01:32,760
好，传上之后的话

45
00:01:32,760 --> 00:01:36,712
相当于是给每个集成起了个 id 编号吧

46
00:01:36,712 --> 00:01:39,510
好，这个 id 的话会在后面会用到啊

47
00:01:39,510 --> 00:01:43,045
大家看，对于每一个 sender 而言

48
00:01:43,045 --> 00:01:43,690
当然了

49
00:01:43,690 --> 00:01:47,090
这个集成在推出之前会去调那个 down 是吧

50
00:01:47,090 --> 00:01:48,780
with group down 一下减一嘛

51
00:01:48,780 --> 00:01:50,630
好，对每一个 standard 而言

52
00:01:50,630 --> 00:01:54,720
它的核心工作就是这样一个无限的循环

53
00:01:54,720 --> 00:01:55,710
它干嘛呢

54
00:01:55,710 --> 00:01:59,670
它的主要任务是要往这个 data 里面去

55
00:01:59,670 --> 00:02:01,280
放入一个数据

56
00:02:01,280 --> 00:02:02,430
你作为生产房

57
00:02:02,430 --> 00:02:05,910
你的核心任务就是往这个 channel 里面去

58
00:02:05,910 --> 00:02:06,830
生产数据嘛

59
00:02:06,830 --> 00:02:07,980
那作为消费房

60
00:02:07,980 --> 00:02:11,130
核心任务就是去读这个 channel 来消费数据

61
00:02:11,130 --> 00:02:13,760
好，这个 value 是怎么来的

62
00:02:13,760 --> 00:02:14,840
按照数据是怎么来的

63
00:02:14,840 --> 00:02:18,407
它实际上是通过一个简单的随机数来生成的

64
00:02:18,407 --> 00:02:22,190
OK ，这是他的第43行和第59行

65
00:02:22,190 --> 00:02:24,457
是作业三段的核心工作

66
00:02:24,457 --> 00:02:25,980
中间这一部分代码

67
00:02:25,980 --> 00:02:29,220
实际上是极少情况下会命中啊

68
00:02:29,220 --> 00:02:31,780
比方说第44行这个 value 对吧

69
00:02:31,780 --> 00:02:33,580
因为这个 value 是个随机数嘛

70
00:02:33,580 --> 00:02:34,970
它刚好等于零

71
00:02:34,970 --> 00:02:37,530
这个很少情况下会命中这种这种情况对吧

72
00:02:37,530 --> 00:02:39,370
一旦命中这种情况的话

73
00:02:39,370 --> 00:02:42,950
那 standard 认为这个时候该停止了啊

74
00:02:42,950 --> 00:02:46,310
就告诉他的所有 sender ，包括 receiver 

75
00:02:46,310 --> 00:02:47,900
大家可以终止了

76
00:02:47,900 --> 00:02:50,030
OK ，他首先啊

77
00:02:50,030 --> 00:02:52,430
他把这个自己的这个

78
00:02:52,430 --> 00:02:53,790
他身份标识

79
00:02:53,790 --> 00:02:54,730
我是一个 sender 

80
00:02:54,730 --> 00:02:55,710
我的 id 是多少

81
00:02:55,710 --> 00:02:57,110
把这个身份标识呢

82
00:02:57,110 --> 00:02:59,010
输给这个全局变量啊

83
00:02:59,010 --> 00:03:01,065
stop by 被谁终止的

84
00:03:01,065 --> 00:03:04,550
然后，诶，他要去关闭这个 stop channel 

85
00:03:04,550 --> 00:03:06,990
你看，关闭这个 stop channel 

86
00:03:06,990 --> 00:03:08,730
因为这个 channel 1旦被关闭的话

87
00:03:08,730 --> 00:03:13,130
那它上面所有的读操作都会立刻解除素材

88
00:03:13,130 --> 00:03:14,170
去关闭这个 channel 

89
00:03:14,170 --> 00:03:17,450
然后呢， return ， return 是返回函数

90
00:03:17,450 --> 00:03:20,500
离这个 return 最近的函数是它

91
00:03:20,500 --> 00:03:23,700
也就是说整个西城就退出了

92
00:03:23,700 --> 00:03:25,480
这是说其中某一个 thunder 

93
00:03:25,480 --> 00:03:28,040
这个西城就退出了啊

94
00:03:28,040 --> 00:03:30,200
这是说如果命中这个条件的话

95
00:03:30,200 --> 00:03:31,200
才会执行

96
00:03:31,200 --> 00:03:32,670
在大部分情况之

97
00:03:32,670 --> 00:03:34,110
不会命中这个条件

98
00:03:34,110 --> 00:03:37,370
所以呢，大部分情况下都会走到这个 select 里面来

99
00:03:37,370 --> 00:03:38,935
那这个 select 

100
00:03:38,935 --> 00:03:43,230
大部分情况也是在往这个 data 里面去放入元素

101
00:03:43,230 --> 00:03:46,420
但是呢，他也会去监听这个 stop channel 啊

102
00:03:46,420 --> 00:03:48,140
因为可能其他三个对吧

103
00:03:48,140 --> 00:03:50,680
发出了这样一个啊 stop 信号

104
00:03:50,680 --> 00:03:52,360
把这个 channel 给关闭了

105
00:03:52,360 --> 00:03:53,340
于是乎呢

106
00:03:53,340 --> 00:03:55,880
它这个 case 啊，就可以解除阻塞

107
00:03:55,880 --> 00:03:57,880
这个 case 1旦解除阻塞

108
00:03:57,880 --> 00:03:59,520
那么就 return 啊

109
00:03:59,520 --> 00:04:02,005
就是结束整个携程

110
00:04:02,005 --> 00:04:02,780
好

111
00:04:02,780 --> 00:04:03,940
所以我们看到啊

112
00:04:03,940 --> 00:04:05,220
作为 sender 啊

113
00:04:05,220 --> 00:04:06,100
一方面呢

114
00:04:06,100 --> 00:04:11,210
他要去时刻观察有没有满足这个 stop 条件

115
00:04:11,210 --> 00:04:12,210
如果满足的话

116
00:04:12,210 --> 00:04:14,170
他就去关闭这个 stop channel 

117
00:04:14,170 --> 00:04:15,050
那同时呢

118
00:04:15,050 --> 00:04:16,950
它也可能作为这个 stove chin

119
00:04:16,950 --> 00:04:18,190
一个接收方，对吧

120
00:04:18,190 --> 00:04:20,440
所以每次呢，他要去监听这个 stop 

121
00:04:20,440 --> 00:04:22,200
TL 里面是不是已经被关闭了

122
00:04:22,200 --> 00:04:23,060
如果被关闭了

123
00:04:23,060 --> 00:04:24,815
那么呢我就立刻结束

124
00:04:24,815 --> 00:04:26,060
这是作为 standard 

125
00:04:26,060 --> 00:04:26,540
好

126
00:04:26,540 --> 00:04:27,580
好，我们看一下 receiver 

127
00:04:27,580 --> 00:04:30,837
这个 receiver 跟 thunder 几乎类似啊

128
00:04:30,837 --> 00:04:33,710
基本呢是起了很多个 receiver 啊

129
00:04:33,710 --> 00:04:36,750
为每一个 receiver 单独开辟一个携程

130
00:04:36,750 --> 00:04:39,605
那每一个 receiver 也会有自己的一个 id 

131
00:04:39,605 --> 00:04:42,910
好，然后每次 C 成零退出之前呢

132
00:04:42,910 --> 00:04:44,280
去调一下这个刀

133
00:04:44,280 --> 00:04:46,970
好，它也是一个无限的循环

134
00:04:46,970 --> 00:04:50,830
这个循环的核心工作就是要取出

135
00:04:50,830 --> 00:04:53,935
从这个 data 里面取出数据来进行消费

136
00:04:53,935 --> 00:04:55,670
同时呢，作为 CEIVER 

137
00:04:55,670 --> 00:04:57,390
他也要去每一步啊

138
00:04:57,390 --> 00:04:59,610
每一次都要去监听这个 stop channel 

139
00:04:59,610 --> 00:05:01,030
看看它是否被关闭了

140
00:05:01,030 --> 00:05:01,990
如果被关闭了

141
00:05:01,990 --> 00:05:03,550
那这个 case 解素材

142
00:05:03,550 --> 00:05:05,190
然后立即退出

143
00:05:05,190 --> 00:05:06,590
大部分情况之下

144
00:05:06,590 --> 00:05:07,950
它是没有被关闭的

145
00:05:07,950 --> 00:05:09,250
所以绝大部分情况下

146
00:05:09,250 --> 00:05:11,790
它是从这个里面取出元素啊

147
00:05:11,790 --> 00:05:13,072
进行一些消费

148
00:05:13,072 --> 00:05:15,660
但是呢，他也有作为 receiver 

149
00:05:15,660 --> 00:05:18,235
它也可以发出这个终止信号

150
00:05:18,235 --> 00:05:22,400
他认为当这个 value 刚好等于 max 减一的时候

151
00:05:22,400 --> 00:05:24,360
唉，他去发出这个终止信号

152
00:05:24,360 --> 00:05:26,200
你看我们的 receiver 啊

153
00:05:26,200 --> 00:05:28,980
本质上这个 value 都是一个随机数嘛

154
00:05:28,980 --> 00:05:31,920
啊，零到 max 减一这样一个随机数

155
00:05:31,920 --> 00:05:33,380
我们的三点是说

156
00:05:33,380 --> 00:05:35,910
如果你是最小的那个值零值的话

157
00:05:35,910 --> 00:05:37,050
那么呢，就结束

158
00:05:37,050 --> 00:05:38,610
而我们的 receiver 是说

159
00:05:38,610 --> 00:05:41,060
如果你是最大的那个值 max 减一的话

160
00:05:41,060 --> 00:05:41,900
那么呢，就结束

161
00:05:41,900 --> 00:05:42,900
所以我们会发现

162
00:05:42,900 --> 00:05:44,580
不管是 standard 还是 receiver 

163
00:05:44,580 --> 00:05:47,860
它们发出终止信号的概率是一样的，对吧

164
00:05:47,860 --> 00:05:49,300
都是 max 分之一

165
00:05:49,300 --> 00:05:50,130
ok 

166
00:05:50,130 --> 00:05:50,987
好

167
00:05:50,987 --> 00:05:53,320
当他发现这个条件满足之后的话

168
00:05:53,320 --> 00:05:55,440
他去关闭这个 stop channel 

169
00:05:55,440 --> 00:05:57,920
那同时呢，给全局变量赋值是吧

170
00:05:57,920 --> 00:06:01,080
这个 stop 最最初是由谁发起的

171
00:06:01,080 --> 00:06:03,290
好，他关闭这个 chin 之后

172
00:06:03,290 --> 00:06:05,960
同时呢，他自己也立即退出

173
00:06:05,960 --> 00:06:07,030
那当然啦

174
00:06:07,030 --> 00:06:09,690
他也会去监听这个 channel ，对吧

175
00:06:09,690 --> 00:06:13,830
在主星里面呢，去等待两个携程退出

176
00:06:13,830 --> 00:06:15,890
最后呢，去打印一个 stop b

177
00:06:15,890 --> 00:06:18,390
到底是被谁来结束的

178
00:06:18,390 --> 00:06:20,490
好，我们把这个程序呢

179
00:06:20,490 --> 00:06:21,410
运行一下啊

180
00:06:21,410 --> 00:06:23,350
这个是封装到一个函数里面

181
00:06:23,350 --> 00:06:24,390
我们在 main 里面呢

182
00:06:24,390 --> 00:06:26,460
去反复的调这个函数

183
00:06:26,460 --> 00:06:27,040
为什么呢

184
00:06:27,040 --> 00:06:28,120
因为你调一次啊

185
00:06:28,120 --> 00:06:29,040
可能没有问题啊

186
00:06:29,040 --> 00:06:30,320
大家可以正常的

187
00:06:30,320 --> 00:06:32,860
可以正常的来到第92行结束

188
00:06:32,860 --> 00:06:34,260
但是如果你调多次的话

189
00:06:34,260 --> 00:06:35,380
可能就会出问题

190
00:06:35,380 --> 00:06:37,685
哎，我们运行一下看看

191
00:06:37,685 --> 00:06:39,960
好，你看前面对吧

192
00:06:39,960 --> 00:06:41,900
这么多次啊，都没有问题

193
00:06:41,900 --> 00:06:42,460
都很顺利

194
00:06:42,460 --> 00:06:43,800
但是呢，突然有一次看

195
00:06:43,800 --> 00:06:45,710
突然有一次发生了 panic 

196
00:06:45,710 --> 00:06:47,420
close of closed channel 

197
00:06:47,420 --> 00:06:48,200
就是你去

198
00:06:48,200 --> 00:06:51,560
你试图去关闭一个已经被关闭的 channel 

199
00:06:51,560 --> 00:06:53,180
看一下是从哪一行报出来的

200
00:06:53,180 --> 00:06:56,525
是从第51行是从这儿报出来的

201
00:06:56,525 --> 00:06:58,610
也就是说在51行

202
00:06:58,610 --> 00:07:01,010
我试图去关闭一个 stop channel 

203
00:07:01,010 --> 00:07:03,180
而这个 stop 前头已经被关闭了

204
00:07:03,180 --> 00:07:05,317
那他是被谁关闭的呢

205
00:07:05,317 --> 00:07:09,180
显然不是被他当前集成自己关闭的

206
00:07:09,180 --> 00:07:11,150
因为如果是被我自己关闭的话

207
00:07:11,150 --> 00:07:13,320
我第一次关闭之后已经退出了

208
00:07:13,320 --> 00:07:14,840
所以我同一个集

209
00:07:14,840 --> 00:07:16,480
我不可能关闭它两次

210
00:07:16,480 --> 00:07:19,230
那自然是被其他线程所关闭的

211
00:07:19,230 --> 00:07:21,065
那我们可以分析一下

212
00:07:21,065 --> 00:07:23,380
这行代码是在 standard 里面

213
00:07:23,380 --> 00:07:27,402
由于我们的 standard 是有1000个 standard 对吧

214
00:07:27,402 --> 00:07:29,400
有1000个 sender ，好

215
00:07:29,400 --> 00:07:30,510
1000个 sender 

216
00:07:30,510 --> 00:07:32,990
那每一个 sender 从概率上讲

217
00:07:32,990 --> 00:07:34,650
他们都可能会去什么

218
00:07:34,650 --> 00:07:36,750
都可能会命中这个 if 条件

219
00:07:36,750 --> 00:07:39,600
都可能会去执行这个 close 操作

220
00:07:39,600 --> 00:07:41,300
所以呢，极端情况下

221
00:07:41,300 --> 00:07:43,460
假如说两个学生他们挨得很近

222
00:07:43,460 --> 00:07:44,360
时间挨得很近

223
00:07:44,360 --> 00:07:45,930
都去执行这个扣操作

224
00:07:45,930 --> 00:07:48,235
那么第二个就会触发 panic 

225
00:07:48,235 --> 00:07:50,710
那也可能不是两个 sender 

226
00:07:50,710 --> 00:07:54,710
可能是一个 sender 和另外一个 receiver 

227
00:07:54,710 --> 00:07:56,370
你看 receiver 这边也会去关闭

228
00:07:56,370 --> 00:07:59,450
他们两个在很临近的一个时间内

229
00:07:59,450 --> 00:08:01,670
都去执行这个 close 操作啊

230
00:08:01,670 --> 00:08:03,660
也会触发一个一个 close 

231
00:08:03,660 --> 00:08:05,510
一个 channel 被 close 两次啊

232
00:08:05,510 --> 00:08:06,707
触发一个 panic 

233
00:08:06,707 --> 00:08:07,690
好

234
00:08:07,690 --> 00:08:09,730
那这个问题如何解决呢

235
00:08:09,730 --> 00:08:10,320
对吧

236
00:08:10,320 --> 00:08:11,842
如何解决这个问题

237
00:08:11,842 --> 00:08:13,710
既然说一个 channel 啊

238
00:08:13,710 --> 00:08:15,910
它不能够被关闭两次

239
00:08:15,910 --> 00:08:20,407
那我们就得想办法把这个 close 操作

240
00:08:20,407 --> 00:08:22,820
把它归到一个地方去

241
00:08:22,820 --> 00:08:25,300
只让它关闭一次

242
00:08:25,300 --> 00:08:27,382
哎，这个怎么办呢

243
00:08:27,382 --> 00:08:30,710
那这个我们自然就需要引出另外的一个

244
00:08:30,710 --> 00:08:31,630
切成啊

245
00:08:31,630 --> 00:08:33,030
只在那个性里面

246
00:08:33,030 --> 00:08:36,890
只让它有权利去关闭这个 stop channel 

247
00:08:36,890 --> 00:08:39,000
去执行这个 close 指的操作

248
00:08:39,000 --> 00:08:40,090
我们需要我们

249
00:08:40,090 --> 00:08:42,250
我们需要把这一行代码把它给移出去啊

250
00:08:42,250 --> 00:08:44,600
移到一个单独的扔里面去

251
00:08:44,600 --> 00:08:45,620
哎，所以呢

252
00:08:45,620 --> 00:08:48,060
我们就把它移到了你看这边

253
00:08:49,300 --> 00:08:51,560
好，我单独开辟了一个集成

254
00:08:51,560 --> 00:08:52,960
就在这个星里面

255
00:08:52,960 --> 00:08:56,720
他才可以有权利去执行这个 close stop 

256
00:08:56,720 --> 00:08:58,490
好，那这样的话

257
00:08:58,490 --> 00:09:00,290
那作为 sender 和 receiver 

258
00:09:00,290 --> 00:09:02,010
他们如何去通知我们

259
00:09:02,010 --> 00:09:05,160
把这个继承称之为主持人继承啊

260
00:09:05,160 --> 00:09:09,240
那作为本来那个 standard 和 receiver 都可以执行 close 

261
00:09:09,240 --> 00:09:11,050
现在他们不能执行了啊

262
00:09:11,050 --> 00:09:12,130
不能执行啊

263
00:09:12,130 --> 00:09:13,370
你不让我执行了

264
00:09:13,370 --> 00:09:16,692
你把这个 close 权限给我剥夺了

265
00:09:16,692 --> 00:09:20,370
但是我要去通知这个主持人

266
00:09:20,370 --> 00:09:21,300
我要通知他

267
00:09:21,300 --> 00:09:24,140
因为我现在想发起这个 stop 请求

268
00:09:24,140 --> 00:09:25,180
我得通知主持人

269
00:09:25,180 --> 00:09:27,820
让主持人帮我去执行这个客户操作

270
00:09:27,820 --> 00:09:29,770
那我怎么去通知主持人呢

271
00:09:29,770 --> 00:09:32,360
唉，自然还是通过 channel 

272
00:09:32,360 --> 00:09:34,390
你看协程之间啊

273
00:09:34,390 --> 00:09:37,630
传递信号、传递数据都是通过 channel 

274
00:09:37,630 --> 00:09:38,810
于是乎呢

275
00:09:38,810 --> 00:09:42,770
为了让这个 sender 和 receiver 跟主持人之间

276
00:09:42,770 --> 00:09:44,310
有一个通信的渠道

277
00:09:44,310 --> 00:09:47,250
所以呢，单独给他们看这28行

278
00:09:47,250 --> 00:09:50,842
28行单独给他们开辟了一个 channel 

279
00:09:50,842 --> 00:09:52,100
好，这样的话呢

280
00:09:52,100 --> 00:09:53,920
由于有了这个 channel 

281
00:09:53,920 --> 00:09:55,740
那么比方说这个 sender 对吧

282
00:09:55,740 --> 00:09:57,610
他想终止怎么办呢

283
00:09:57,610 --> 00:09:58,540
他这样

284
00:09:59,760 --> 00:10:00,400
好

285
00:10:01,480 --> 00:10:03,320
如果这个三个想终止的话呢

286
00:10:03,320 --> 00:10:06,860
那么他就给这个往这个 to stop 里面

287
00:10:06,860 --> 00:10:08,470
去发送一个信号

288
00:10:08,470 --> 00:10:11,060
你看第47行是吧

289
00:10:11,060 --> 00:10:13,760
哎，他向这个 to stop 广告里面呢

290
00:10:13,760 --> 00:10:15,855
去发送一个信号

291
00:10:15,855 --> 00:10:17,080
这个还注释掉啊

292
00:10:17,080 --> 00:10:17,780
不要

293
00:10:17,780 --> 00:10:18,960
那同样道理

294
00:10:18,960 --> 00:10:20,860
当 receiver 想关闭的时候

295
00:10:20,860 --> 00:10:23,850
他也是往这个 to stop 里面啊

296
00:10:23,850 --> 00:10:26,550
也是往这个 to stop 里面去发送一个信号

297
00:10:26,550 --> 00:10:27,350
这样的话呢

298
00:10:27,350 --> 00:10:28,460
我这个主持人

299
00:10:28,460 --> 00:10:32,430
但凡能从 to stop 里面能够取出数据的话

300
00:10:32,430 --> 00:10:35,840
那么呢，我再去调这个 close 、 stop 、 channe

301
00:10:35,840 --> 00:10:36,880
这三者而言

302
00:10:36,880 --> 00:10:39,020
他发现这个 if 满足之后

303
00:10:39,020 --> 00:10:41,040
他是想尽快退出的

304
00:10:41,040 --> 00:10:45,750
所以呢，他不希望被第47行所阻塞啊

305
00:10:45,750 --> 00:10:47,670
他想尽快的 return 

306
00:10:47,670 --> 00:10:50,250
假如说这个 to stop 它满了

307
00:10:50,250 --> 00:10:52,690
那么这个血管和操作叫阻塞

308
00:10:52,690 --> 00:10:53,490
叫等待，对吧

309
00:10:53,490 --> 00:10:54,720
他不想等待

310
00:10:54,720 --> 00:10:56,210
那不相等待的话呢

311
00:10:56,210 --> 00:10:58,730
我们第一种方法可以把这个 to stop 

312
00:10:58,730 --> 00:11:01,530
怎么设置的容量很大啊

313
00:11:01,530 --> 00:11:04,570
比方说不是所有的这个 sender 和 receiver 

314
00:11:04,570 --> 00:11:07,800
都有可能往这个 to stop 里面写数据嘛

315
00:11:07,800 --> 00:11:09,080
那我把这个 to stop 

316
00:11:09,080 --> 00:11:10,920
把它容量设置的大一点

317
00:11:10,920 --> 00:11:16,230
比方说就是所有的 receiver 好

318
00:11:16,230 --> 00:11:18,770
加上所有的 numbers 

319
00:11:19,880 --> 00:11:21,720
好，容量很大是吧

320
00:11:21,720 --> 00:11:22,220
足够大

321
00:11:22,220 --> 00:11:23,090
这样的话呢

322
00:11:23,090 --> 00:11:24,930
不管是 sender 还是 receiver 

323
00:11:24,930 --> 00:11:27,050
那这个协管道操作是吧

324
00:11:27,050 --> 00:11:29,430
这个协管道操作都不会阻塞

325
00:11:29,430 --> 00:11:31,320
他们都会 return 

326
00:11:31,320 --> 00:11:33,990
但这样的话就有一点点不太好

327
00:11:33,990 --> 00:11:35,450
就是作为主持人

328
00:11:35,450 --> 00:11:38,740
我还得知道有多少个 sender 和 receiver 

329
00:11:38,740 --> 00:11:39,940
那有些情况下

330
00:11:39,940 --> 00:11:41,560
它实际上他是不知道的

331
00:11:41,560 --> 00:11:45,280
他不知道到底有多少个 send 和 receiver 

332
00:11:45,280 --> 00:11:46,000
所以的话呢

333
00:11:46,000 --> 00:11:48,420
那这个地方容量就不好设

334
00:11:48,420 --> 00:11:49,740
那就不好设

335
00:11:50,910 --> 00:11:52,970
好，如果我设为一的话

336
00:11:52,970 --> 00:11:54,310
有没有什么办法

337
00:11:54,310 --> 00:11:58,350
可以不让这一行代码阻塞呢

338
00:11:58,350 --> 00:12:02,730
哎，就是给它外面套一层 select 就不说了

339
00:12:02,730 --> 00:12:03,410
为什么呢

340
00:12:03,410 --> 00:12:04,515
大家分析一下啊

341
00:12:04,515 --> 00:12:08,190
好，那这个 select 有一个 kiss 和一个 default 

342
00:12:08,190 --> 00:12:10,610
我们说 select 是多路监听嘛

343
00:12:10,610 --> 00:12:13,030
只要有一路不阻塞

344
00:12:13,030 --> 00:12:15,720
那么这个 select 很快就可以过去

345
00:12:15,720 --> 00:12:18,880
那我们可以看到这个 default 是这个 default 

346
00:12:18,880 --> 00:12:21,182
它总是不阻塞嘛，对吧

347
00:12:21,182 --> 00:12:22,910
既然 default 总是不走色

348
00:12:22,910 --> 00:12:25,270
假如说四七行阻塞

349
00:12:25,270 --> 00:12:26,670
但是我 default 不阻塞

350
00:12:26,670 --> 00:12:29,530
所以呢，这个 select 可以很快过去

351
00:12:29,530 --> 00:12:32,387
然后我很快就可以执行这个为

352
00:12:32,387 --> 00:12:33,920
所以即使这个地方

353
00:12:33,920 --> 00:12:35,880
我把它容量设置为一的话

354
00:12:35,880 --> 00:12:36,760
也没有影响
