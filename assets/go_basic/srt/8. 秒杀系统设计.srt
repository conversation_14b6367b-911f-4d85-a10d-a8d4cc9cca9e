1
00:00:00,520 --> 00:00:02,680
秒杀只是一个笼统的概念

2
00:00:02,680 --> 00:00:05,360
那么它对应的 U 形态可以很多啦

3
00:00:05,360 --> 00:00:07,490
比如说像春晚当天

4
00:00:07,490 --> 00:00:09,520
微信红包、支付宝红包是吧

5
00:00:09,520 --> 00:00:10,660
他要发红包嘛

6
00:00:10,660 --> 00:00:13,380
那么这种活动的话就是参与的人特别多

7
00:00:13,380 --> 00:00:15,760
然后每个人都可以中奖

8
00:00:15,760 --> 00:00:17,420
那中奖就是现金嘛

9
00:00:17,420 --> 00:00:19,460
也没有后续的什么事情了

10
00:00:19,460 --> 00:00:21,720
还有就是像这种双11啊

11
00:00:21,720 --> 00:00:24,240
我们一个很低的促销价格嘛

12
00:00:24,240 --> 00:00:25,150
大家都来抢

13
00:00:25,150 --> 00:00:28,087
那只有一部分人他能够抢购

14
00:00:28,087 --> 00:00:29,690
那抢中之后的话

15
00:00:29,690 --> 00:00:32,210
他毕竟还是要完成后面的这个支付

16
00:00:32,210 --> 00:00:34,640
如果你超时没有支付一

17
00:00:34,640 --> 00:00:36,800
就放弃了这个低价优惠嘛

18
00:00:36,800 --> 00:00:38,100
那么如果你放弃了

19
00:00:38,100 --> 00:00:40,620
那么这个库存量就会再加回去

20
00:00:40,620 --> 00:00:41,140
当然了

21
00:00:41,140 --> 00:00:43,380
如果说你已经抢中了

22
00:00:43,380 --> 00:00:44,400
但是还没有支付

23
00:00:44,400 --> 00:00:47,820
那这个时候这件商品是处于锁定状态

24
00:00:47,820 --> 00:00:50,960
就是我们会假设这库存已经没了

25
00:00:50,960 --> 00:00:54,010
或者说呢，你在时间之内完成支付

26
00:00:54,010 --> 00:00:55,940
那么这个库存也就没了嘛

27
00:00:55,940 --> 00:00:59,870
但是的话，我们说这个抢购这个过程啊

28
00:00:59,870 --> 00:01:01,550
参与的人特别多

29
00:01:01,550 --> 00:01:03,795
而且呢，时间很短

30
00:01:03,795 --> 00:01:05,750
那第一说你抢中以后

31
00:01:05,750 --> 00:01:06,890
你这个支付是吧

32
00:01:06,890 --> 00:01:08,270
可以往后延嘛

33
00:01:08,270 --> 00:01:12,980
在后续的一天、两天、一周之内完成支付都可以

34
00:01:12,980 --> 00:01:15,450
而且本身参与支付的人也很少

35
00:01:15,450 --> 00:01:17,260
因为抢购人就会少嘛

36
00:01:17,260 --> 00:01:20,650
所以说关键是在抢购这一步啊

37
00:01:20,650 --> 00:01:22,390
短时间人很多

38
00:01:22,390 --> 00:01:24,030
这一步的话， QPS 很高

39
00:01:24,030 --> 00:01:25,230
而后面的话

40
00:01:25,230 --> 00:01:26,150
参与的人少

41
00:01:26,150 --> 00:01:27,810
而且持续的时间比较长

42
00:01:27,810 --> 00:01:30,010
所以呢， QPS 就很低

43
00:01:30,010 --> 00:01:31,250
那作为课程

44
00:01:31,250 --> 00:01:32,490
作为我们学技术

45
00:01:32,490 --> 00:01:34,770
我们主要想教大家的就是

46
00:01:34,770 --> 00:01:37,410
如何应对高并发的这个场景

47
00:01:37,410 --> 00:01:38,530
那么我们的课程呢

48
00:01:38,530 --> 00:01:41,290
是设计了一个简单的 demo 啊

49
00:01:41,290 --> 00:01:42,572
第一步的话

50
00:01:42,572 --> 00:01:46,110
我们假设有很多人来参与这个

51
00:01:46,110 --> 00:01:48,352
大转盘的抽奖活动

52
00:01:48,352 --> 00:01:50,650
这里面呢，有一个谢谢参与

53
00:01:50,650 --> 00:01:54,330
就是大概率上你是抽不中任何奖品的

54
00:01:54,330 --> 00:01:56,702
都是这个谢谢参与嘛

55
00:01:56,702 --> 00:01:59,060
那可能只有1%的人啊

56
00:01:59,060 --> 00:02:00,420
能够抽中某个奖品

57
00:02:00,420 --> 00:02:02,510
会进入到下一个页面

58
00:02:02,510 --> 00:02:04,900
这个页面就告诉他你抽中了哪个奖品

59
00:02:04,900 --> 00:02:08,660
它的目前一个优惠的价格是多少钱啊

60
00:02:08,660 --> 00:02:09,847
你可以选择支付

61
00:02:09,847 --> 00:02:11,330
或者呢，选择放弃

62
00:02:11,330 --> 00:02:12,250
那你放弃的话

63
00:02:12,250 --> 00:02:15,225
就把这个库存就归还回去了嘛

64
00:02:15,225 --> 00:02:18,110
而且这个页面是有一个时间限制的

65
00:02:18,110 --> 00:02:19,190
超值之后的

66
00:02:19,190 --> 00:02:21,200
就视为你自动放弃了

67
00:02:21,200 --> 00:02:22,770
所以从高并发角度

68
00:02:22,770 --> 00:02:24,470
我们重点考虑第一个页面

69
00:02:24,470 --> 00:02:26,647
如何能够抗住高并发

70
00:02:26,647 --> 00:02:29,730
来看一下这个程序设计的流程

71
00:02:29,730 --> 00:02:32,240
第一步呢，是用户开始参与抽奖

72
00:02:32,240 --> 00:02:34,085
开始那个大转盘的游戏

73
00:02:34,085 --> 00:02:36,360
那可能抽中也可能没有抽中

74
00:02:36,360 --> 00:02:41,070
大部分人是否未抽中直接结束

75
00:02:41,070 --> 00:02:42,860
少部分人抽中了

76
00:02:42,860 --> 00:02:44,140
那抽中之后的话

77
00:02:44,140 --> 00:02:46,925
从代码层面我们要做三件事情

78
00:02:46,925 --> 00:02:50,980
第一件事情要立刻对该商品减库存

79
00:02:50,980 --> 00:02:52,342
库存减一

80
00:02:52,342 --> 00:02:53,330
第二步呢

81
00:02:53,330 --> 00:02:55,250
我们要生成一个临时订单

82
00:02:55,250 --> 00:02:56,790
就是这个用户 id 是吧

83
00:02:56,790 --> 00:03:00,785
我们假设他已经买走了这个商品 id 

84
00:03:00,785 --> 00:03:04,660
第三步，引导用户进入到这个支付页面

85
00:03:04,660 --> 00:03:07,260
好，那么他来到支付页面啊

86
00:03:07,260 --> 00:03:10,307
他可能点了那个放弃支付按钮，对吧

87
00:03:10,307 --> 00:03:11,420
放弃支付了

88
00:03:11,420 --> 00:03:12,500
那这样的话呢

89
00:03:12,500 --> 00:03:14,020
库存又再加回去

90
00:03:14,020 --> 00:03:15,820
因为你钱不把库存扣一了嘛

91
00:03:15,820 --> 00:03:18,650
现在得把库存呢，加一加回去

92
00:03:18,650 --> 00:03:21,950
同时的话，删除他那个零式订单

93
00:03:21,950 --> 00:03:23,340
然后就结束了

94
00:03:23,340 --> 00:03:24,800
如果你没有放弃支付啊

95
00:03:24,800 --> 00:03:25,930
你打算支付了

96
00:03:25,930 --> 00:03:27,492
那么你支付的话

97
00:03:27,492 --> 00:03:30,460
可能啊，那个时候已经超时了

98
00:03:30,460 --> 00:03:32,680
那这样的话也算是失败了嘛

99
00:03:32,680 --> 00:03:35,450
所以呢，也需要把库存加一再加回去

100
00:03:35,450 --> 00:03:38,627
同时呢，删除那个临时订单

101
00:03:38,627 --> 00:03:39,560
所以就是说

102
00:03:39,560 --> 00:03:41,320
不管你是主动放弃也好

103
00:03:41,320 --> 00:03:42,760
还是由于超时也好

104
00:03:42,760 --> 00:03:45,550
我们都会删除这个临时订单

105
00:03:45,550 --> 00:03:47,470
那么临时订单一删除的话

106
00:03:47,470 --> 00:03:50,965
你如果下一次再进入那个支付页面

107
00:03:50,965 --> 00:03:53,470
或者说提交支付接口的话

108
00:03:53,470 --> 00:03:54,580
我们发现，诶

109
00:03:54,580 --> 00:03:56,010
零时订单没有了

110
00:03:56,010 --> 00:03:57,670
就不允许你支付吗

111
00:03:57,670 --> 00:04:00,175
好，那如果一切正常的话

112
00:04:00,175 --> 00:04:01,490
成功支付了

113
00:04:01,490 --> 00:04:04,270
那么呢，我们生成一个正式订单

114
00:04:04,270 --> 00:04:07,510
同时呢，把零式订单把它给删掉

115
00:04:07,510 --> 00:04:09,170
删除这些制订单

116
00:04:09,170 --> 00:04:12,322
主要是为了防止他第二次重复支付

117
00:04:12,322 --> 00:04:15,300
那么这里面关于这个零是订单啊

118
00:04:15,300 --> 00:04:17,540
我们打算不存到 MYSQL 里面

119
00:04:17,540 --> 00:04:19,570
而存到 REDIS 里面

120
00:04:19,570 --> 00:04:23,370
因为 REDIS 它的这个可靠性比不上 MYSQL 嘛

121
00:04:23,370 --> 00:04:27,110
所以这种需要长久存储的一般是用 MYSQL 

122
00:04:27,110 --> 00:04:29,610
而 REDIS 的话可靠性会低一些

123
00:04:29,610 --> 00:04:31,370
那反正这个临时订单

124
00:04:31,370 --> 00:04:33,872
它存活的生命周期也不需要很长

125
00:04:33,872 --> 00:04:36,500
那么我们认为在这么短的一个时间之内

126
00:04:36,500 --> 00:04:38,660
二粒子大概率也不会出什么问题

127
00:04:38,660 --> 00:04:40,300
所以呢，对于连数据啊

128
00:04:40,300 --> 00:04:43,260
是比较适合使用 REDIS 来做出的

129
00:04:43,260 --> 00:04:43,760
当然了

130
00:04:43,760 --> 00:04:45,550
这只是 REDIS 的一个应用场景了

131
00:04:45,550 --> 00:04:46,540
然后我们想一下

132
00:04:46,540 --> 00:04:49,655
这个支付超时这一块该怎么设计

133
00:04:49,655 --> 00:04:52,250
那我们怎么去监控他超时了呢

134
00:04:52,250 --> 00:04:55,777
我们自己在构造大法里面写一个定时任务吗

135
00:04:55,777 --> 00:04:57,040
但这样的话

136
00:04:57,040 --> 00:04:59,540
用户每抢中一

137
00:04:59,540 --> 00:05:01,570
就会增加一个定时任务啊

138
00:05:01,570 --> 00:05:02,770
定时任务会比较多啊

139
00:05:02,770 --> 00:05:05,730
第二个呢，就是说如果中途程序挂了

140
00:05:05,730 --> 00:05:08,490
那么他在重启之前的所有定时任务

141
00:05:08,490 --> 00:05:09,890
就全部没了

142
00:05:09,890 --> 00:05:12,810
所以啊，我们打算借助 rocket mq 

143
00:05:12,810 --> 00:05:15,310
它的这个延时消息来完成

144
00:05:15,310 --> 00:05:17,947
就是说在这一步的时候

145
00:05:17,947 --> 00:05:19,960
库存减一之后的话

146
00:05:19,960 --> 00:05:22,820
会给 rocket mq 发一个延时消息啊

147
00:05:22,820 --> 00:05:25,540
你希望它24小时内完成支付

148
00:05:25,540 --> 00:05:29,000
那么呢，这个延时消息就是24小时之后

149
00:05:29,000 --> 00:05:30,475
对下游可见

150
00:05:30,475 --> 00:05:33,180
那么当下游收到这个消息以后啊

151
00:05:33,180 --> 00:05:36,687
他会去看一下这个临时订单是否还在

152
00:05:36,687 --> 00:05:37,790
如果还在

153
00:05:37,790 --> 00:05:40,910
就意味着用户没有真正的完成支付

154
00:05:40,910 --> 00:05:42,450
因为完成支付的话

155
00:05:42,450 --> 00:05:45,507
他会删除这个临时订单吗

156
00:05:45,507 --> 00:05:46,800
是不是你还没有支付

157
00:05:46,800 --> 00:05:47,840
那没有支付的话

158
00:05:47,840 --> 00:05:49,100
同时现在已经超时了

159
00:05:49,100 --> 00:05:51,140
那么呢，我把零是订单删掉

160
00:05:51,140 --> 00:05:52,662
同时库存加一

161
00:05:52,662 --> 00:05:54,970
还有一个难点在于这个抽奖

162
00:05:54,970 --> 00:05:57,987
这边我们的抽奖算法怎么设计

163
00:05:57,987 --> 00:06:00,040
比如像这样一个大转盘

164
00:06:00,040 --> 00:06:01,610
里面很多奖品

165
00:06:01,610 --> 00:06:02,870
那每一个奖品啊

166
00:06:02,870 --> 00:06:05,030
其实其价值是不一样的

167
00:06:05,030 --> 00:06:08,240
所以呢，他被抽中的概率也是不一样的

168
00:06:08,240 --> 00:06:10,580
价格越高的概率越低嘛

169
00:06:10,580 --> 00:06:11,920
那我们是这样设计的

170
00:06:11,920 --> 00:06:14,280
就是说我们给每一个奖品呢

171
00:06:14,280 --> 00:06:16,395
都给他一个库存量

172
00:06:16,395 --> 00:06:18,020
我们也不考虑价值了

173
00:06:18,020 --> 00:06:19,920
反正库存量越多

174
00:06:19,920 --> 00:06:21,930
那么你被抽中的概率就越高

175
00:06:21,930 --> 00:06:24,440
比如说这个，谢谢参与

176
00:06:24,440 --> 00:06:27,000
他的初始库存量就特别高

177
00:06:27,000 --> 00:06:29,327
我们希望它被抽中概率很高嘛

178
00:06:29,327 --> 00:06:31,650
所以这个抽奖算法呀，怎么搞呢

179
00:06:31,650 --> 00:06:33,710
就是按比例抽样嘛

180
00:06:33,710 --> 00:06:36,050
啊，其实我们在讲数据结构的时候

181
00:06:36,050 --> 00:06:38,410
讲过一种按比例抽样法

182
00:06:38,410 --> 00:06:41,150
就是那个二分区间查找

183
00:06:41,150 --> 00:06:43,440
那比如说我有三件奖品

184
00:06:43,440 --> 00:06:46,807
库存量分别是五件、两件和四件

185
00:06:46,807 --> 00:06:49,370
那么我可以算出他们被抽中的概率

186
00:06:49,370 --> 00:06:52,430
分别是0.45、0.18和0.36

187
00:06:52,430 --> 00:06:55,130
然后呢，我算一个累加值

188
00:06:55,130 --> 00:06:57,870
就是最开始只有0.45嘛

189
00:06:57,870 --> 00:06:58,940
0.45

190
00:06:58,940 --> 00:07:03,830
第二个呢，是0.45+0.18累加吗

191
00:07:03,830 --> 00:07:07,350
那最后一个就是前面的三个全部累加起来

192
00:07:07,350 --> 00:07:08,862
刚好是1.0嘛

193
00:07:08,862 --> 00:07:11,700
好，这样的话，这三个数值啊

194
00:07:11,700 --> 00:07:15,970
就把0~1这条线段切分成了三段

195
00:07:15,970 --> 00:07:20,140
那现在我就随机的在0~1上面

196
00:07:20,140 --> 00:07:21,540
选择一个小数是吧

197
00:07:21,540 --> 00:07:24,680
这个小数它落在这三段的哪一段上

198
00:07:24,680 --> 00:07:26,855
我就抽中那个奖品就可以了

199
00:07:26,855 --> 00:07:31,292
比如说中间这个奖品库存量只有二嘛

200
00:07:31,292 --> 00:07:33,190
那么它对应的这个区间段

201
00:07:33,190 --> 00:07:35,990
就是0.45到0.64是吧

202
00:07:35,990 --> 00:07:38,050
这个区间段比较短嘛

203
00:07:38,050 --> 00:07:40,710
他的抽中概率这就比较低啊

204
00:07:40,710 --> 00:07:43,982
所以这就是一个所谓的二分区间查找

205
00:07:43,982 --> 00:07:45,100
那我发现啊

206
00:07:45,100 --> 00:07:47,360
在这个抽奖算法里面有一个问题

207
00:07:47,360 --> 00:07:50,660
就是每当我抽中一个奖品之后的话

208
00:07:50,660 --> 00:07:53,217
它对应的库存量是不是应该减一

209
00:07:53,217 --> 00:07:55,370
而库存量一旦减一的话

210
00:07:55,370 --> 00:07:58,790
整个算法中间的所有参数得重新再来

211
00:07:58,790 --> 00:07:59,850
就从最开始的话

212
00:07:59,850 --> 00:08:01,990
你这个概率就要变嘛

213
00:08:01,990 --> 00:08:05,400
就意味着每一次抽奖之初啊

214
00:08:05,400 --> 00:08:09,140
我都要去重新获得每一件商品

215
00:08:09,140 --> 00:08:10,850
最新的库存量

216
00:08:10,850 --> 00:08:12,840
那这个库存量我存在哪呢

217
00:08:12,840 --> 00:08:14,930
存在 MYSQL 里面吗

218
00:08:14,930 --> 00:08:16,160
那这样的话

219
00:08:16,160 --> 00:08:18,560
我这个高并发的抽抽奖算法

220
00:08:18,560 --> 00:08:21,765
我就要高并发的去读我的 MYSQL 库

221
00:08:21,765 --> 00:08:22,880
一旦抽中

222
00:08:22,880 --> 00:08:24,240
我还得去修改 MYSQL 

223
00:08:24,240 --> 00:08:25,140
把库存减一

224
00:08:25,140 --> 00:08:25,640
当然了

225
00:08:25,640 --> 00:08:27,320
这个修改的 QPS 比较低

226
00:08:27,320 --> 00:08:28,920
因为抽中的概率比较低嘛

227
00:08:28,920 --> 00:08:32,210
关键是这个查询的 QPS 很高

228
00:08:32,210 --> 00:08:35,360
所以啊，我想把这个商品的库存量呢

229
00:08:35,360 --> 00:08:38,610
直接把它存到 REDIS 里面去啊

230
00:08:38,610 --> 00:08:41,390
你可以认为 REDIS 它能抗住的这个批发量

231
00:08:41,390 --> 00:08:43,900
大概是 MYSQL 的100倍

232
00:08:43,900 --> 00:08:45,520
大概是这样的量级

233
00:08:45,520 --> 00:08:49,520
那么呢，我在系统刚刚启动时

234
00:08:49,520 --> 00:08:53,370
我就把 MYSQL 里面存储的商品的库存量

235
00:08:53,370 --> 00:08:56,050
直接全量的导到 REDIS 里面去

236
00:08:56,050 --> 00:08:58,150
因为这个商品总数很少嘛

237
00:08:58,150 --> 00:09:01,430
啊，虽然说参与这个活动用户很多

238
00:09:01,430 --> 00:09:02,270
QPS 很高

239
00:09:02,270 --> 00:09:05,210
但是商品总数很少啊

240
00:09:05,210 --> 00:09:06,760
可以全部导入 release 

241
00:09:06,760 --> 00:09:08,980
那么每次抽奖之初

242
00:09:08,980 --> 00:09:11,460
我都是从 REDIS 上去获得

243
00:09:11,460 --> 00:09:13,820
当前实时的一个库存量啊

244
00:09:13,820 --> 00:09:16,950
每次减一的话也只是去减 REDIS 

245
00:09:16,950 --> 00:09:18,040
MYSQL 不动

246
00:09:18,040 --> 00:09:22,132
MYSQL 里面存储的始终都是最初的库存量

247
00:09:22,132 --> 00:09:25,010
设计一下我们后端需要完成哪些接口

248
00:09:25,010 --> 00:09:25,940
那第一步

249
00:09:25,940 --> 00:09:30,020
用户来到这个抽奖大转盘这个页面，对吧

250
00:09:30,020 --> 00:09:31,750
当当打开这个页面时

251
00:09:31,750 --> 00:09:33,370
那么我们需要提供一个接口

252
00:09:33,370 --> 00:09:36,470
就告诉前端现在一共有哪些奖品

253
00:09:36,470 --> 00:09:39,520
每一个奖品它对应的这个图片是什么

254
00:09:39,520 --> 00:09:43,530
所以这个就是我们的所谓的这个 gifts 这个接口

255
00:09:43,530 --> 00:09:44,790
那第二个接口呢

256
00:09:44,790 --> 00:09:46,690
用户点了那个抽奖按钮

257
00:09:46,690 --> 00:09:47,950
它开始抽奖了

258
00:09:47,950 --> 00:09:49,230
他开始抽奖了

259
00:09:49,230 --> 00:09:52,850
那后端就要运行这个抽奖算法对吧

260
00:09:52,850 --> 00:09:54,670
把抽奖结果告诉前端

261
00:09:54,670 --> 00:09:58,620
那这个呢，我们通过 lucky 这个接口来完成

262
00:09:58,620 --> 00:10:01,140
然后就是他抽奖成功之后

263
00:10:01,140 --> 00:10:02,660
这边对应的两个按钮是吧

264
00:10:02,660 --> 00:10:03,860
放弃和支付

265
00:10:03,860 --> 00:10:05,040
那这两个按钮呢

266
00:10:05,040 --> 00:10:08,400
也分别对应两个客户端接口

267
00:10:08,400 --> 00:10:13,140
那还有就是他要来到这个支付页面

268
00:10:13,140 --> 00:10:16,690
这个页面上面我们需要展示这个奖品名称

269
00:10:16,690 --> 00:10:17,070
是吧

270
00:10:17,070 --> 00:10:18,190
支付价格是吧

271
00:10:18,190 --> 00:10:21,610
这个页面本身也需要对应一个接口

272
00:10:21,610 --> 00:10:23,307
就是这个 result 

273
00:10:23,307 --> 00:10:25,440
然后关于那个抽奖大转盘

274
00:10:25,440 --> 00:10:29,280
我们是直接使用了这个 large canvas 这个插件
