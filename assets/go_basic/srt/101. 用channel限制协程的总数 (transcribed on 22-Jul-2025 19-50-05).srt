1
00:00:00,000 --> 00:00:05,500
但结合我们限制了一个函数的瞬间最高并发请求量

2
00:00:05,500 --> 00:00:06,700
这个还比较简单

3
00:00:06,700 --> 00:00:11,000
我们只需要在函数的入口处加一个阻塞机制就可以了

4
00:00:11,000 --> 00:00:18,000
那假如说你想限制整个 gol 进程里面所有协程的一个最高数目

5
00:00:18,000 --> 00:00:21,600
比方说虽然说这个 gol 协程非常轻量级

6
00:00:21,600 --> 00:00:24,000
可以轻松的创建成千三万个

7
00:00:24,000 --> 00:00:26,000
而不至于是系统经济枯竭

8
00:00:26,000 --> 00:00:28,300
但是呢在有些情况之下

9
00:00:28,300 --> 00:00:32,200
你可能一不留神就会创建几十万几百万个协程

10
00:00:32,200 --> 00:00:36,200
如果你想控制一下协程的总数目该怎么办呢

11
00:00:36,200 --> 00:00:39,740
这个时候可不可以控制某一个单独的函数

12
00:00:39,740 --> 00:00:41,640
是任意的协程

13
00:00:41,640 --> 00:00:43,560
那这样的话呢

14
00:00:43,560 --> 00:00:45,980
其实我们就需要做一个风光

15
00:00:45,980 --> 00:00:50,820
就是被所有的协程创建走一个统一的入口

16
00:00:50,820 --> 00:00:54,180
好 我先看一个原始的例子

17
00:00:54,180 --> 00:00:56,519
比方说在我的命里面呢

18
00:00:56,519 --> 00:00:57,460
这个地方

19
00:00:57,460 --> 00:01:00,360
我for循环了一万次

20
00:01:00,360 --> 00:01:03,360
那每次呢都会去开辟一个系统

21
00:01:03,360 --> 00:01:04,360
这个work啊

22
00:01:04,360 --> 00:01:07,360
这个work它实际上是一个函数变量

23
00:01:07,360 --> 00:01:09,900
你看这里面我定义了一个函数嘛

24
00:01:09,900 --> 00:01:10,900
对吧 命名函数

25
00:01:10,900 --> 00:01:11,860
把这个函数呢

26
00:01:11,860 --> 00:01:12,760
复给了work

27
00:01:12,760 --> 00:01:13,360
到这个地方呢

28
00:01:13,360 --> 00:01:15,360
再去调用这个work

29
00:01:15,360 --> 00:01:18,860
就好比说我选中的这部分是什么

30
00:01:18,860 --> 00:01:21,360
这部分是定义了一个函数

31
00:01:21,360 --> 00:01:22,360
work

32
00:01:22,360 --> 00:01:25,160
然后呢这个work后面加个括号

33
00:01:25,160 --> 00:01:28,060
表示什么 表示我在调用这个函数

34
00:01:28,060 --> 00:01:29,060
啊

35
00:01:29,060 --> 00:01:32,560
注意 比方说这个函数它可能会有参数

36
00:01:32,560 --> 00:01:34,759
对吧 可能会有入参 参数

37
00:01:34,759 --> 00:01:36,560
那对的话那你这个地方

38
00:01:36,560 --> 00:01:38,500
你要去调用这个函数嘛

39
00:01:38,500 --> 00:01:40,060
你就需要给它传参数啊

40
00:01:40,060 --> 00:01:43,060
传一个数字作为参数

41
00:01:43,060 --> 00:01:44,060
对吧 ok

42
00:01:44,060 --> 00:01:46,660
就要明白它的本质是什么含义啊

43
00:01:46,660 --> 00:01:50,460
好 这里面呢是开辟了一万个是吧

44
00:01:50,460 --> 00:01:53,660
一万个子系层去执行这个work函数

45
00:01:53,660 --> 00:01:55,960
这work函数呢会休息个十秒钟

46
00:01:55,960 --> 00:01:57,660
还是很长的一段时间啊

47
00:01:57,660 --> 00:02:01,060
然后呢命里面呢会也去休息十秒钟

48
00:02:01,060 --> 00:02:03,760
好 那这样的话我们可想而知啊

49
00:02:03,760 --> 00:02:07,060
可想当这一万个系层都起来之后啊

50
00:02:07,060 --> 00:02:08,960
在我们的整个购物竞争里面呢

51
00:02:08,960 --> 00:02:11,760
就会存在一万个子系层

52
00:02:11,760 --> 00:02:12,560
当然啦

53
00:02:12,560 --> 00:02:15,060
你没有考虑到我们还有这个min系层

54
00:02:15,060 --> 00:02:16,760
对吧 是一万零一个

55
00:02:16,760 --> 00:02:20,760
再考虑到这边还有一个是一万零二个子系层

56
00:02:20,760 --> 00:02:24,560
好 那么看看这段函数是想干嘛啊

57
00:02:24,560 --> 00:02:25,360
它实际上呢

58
00:02:25,360 --> 00:02:28,160
它是想每个一秒钟去输出一次

59
00:02:28,160 --> 00:02:30,860
当前还存活的携程数目

60
00:02:30,860 --> 00:02:31,460
那么呢

61
00:02:31,460 --> 00:02:33,360
它是通过这个runtime

62
00:02:33,360 --> 00:02:34,160
number

63
00:02:34,160 --> 00:02:36,960
go routines来获得这个值

64
00:02:36,960 --> 00:02:40,260
那如何实现每个一秒钟执行一件事情呢

65
00:02:40,260 --> 00:02:42,560
这里面是借助于了ticker

66
00:02:42,560 --> 00:02:43,460
ticker

67
00:02:43,460 --> 00:02:45,660
周期性的去执行嘛

68
00:02:45,660 --> 00:02:48,360
你只要指定这个间隔时间就可以了

69
00:02:48,360 --> 00:02:51,560
这个time.new ticker

70
00:02:51,560 --> 00:02:52,260
指定什么

71
00:02:52,260 --> 00:02:53,760
指定间隔的时间

72
00:02:53,760 --> 00:02:55,760
每个一秒钟就执行一次

73
00:02:55,760 --> 00:02:56,360
好

74
00:02:56,360 --> 00:02:57,960
这个ticker怎么使用呢

75
00:02:58,960 --> 00:02:59,760
这个ticker啊

76
00:02:59,760 --> 00:03:00,960
它有一个点c

77
00:03:00,960 --> 00:03:01,760
这个点c呢

78
00:03:01,760 --> 00:03:02,760
还是一个channel

79
00:03:02,760 --> 00:03:03,760
有个channel

80
00:03:03,760 --> 00:03:06,360
那你既然这边指定了一秒钟

81
00:03:06,360 --> 00:03:07,060
那么呢

82
00:03:07,060 --> 00:03:09,460
它背后就会每个一秒钟啊

83
00:03:09,460 --> 00:03:10,760
往这个点c

84
00:03:10,760 --> 00:03:11,960
往这个channel里面呢

85
00:03:11,960 --> 00:03:13,760
去放入一个元素

86
00:03:14,260 --> 00:03:15,060
这样的话呢

87
00:03:15,060 --> 00:03:16,560
你这边试图去读嘛

88
00:03:16,560 --> 00:03:16,860
对吧

89
00:03:16,860 --> 00:03:17,760
那显示说

90
00:03:18,160 --> 00:03:20,060
一秒钟之后才能读到元素嘛

91
00:03:20,060 --> 00:03:21,160
那这一秒钟之内

92
00:03:21,160 --> 00:03:22,060
它会阻塞

93
00:03:22,060 --> 00:03:22,660
好

94
00:03:22,660 --> 00:03:23,660
过了一秒钟啊

95
00:03:23,660 --> 00:03:25,360
终于可以解除阻塞了

96
00:03:25,360 --> 00:03:25,760
然后呢

97
00:03:25,760 --> 00:03:28,060
我就打印这样一行消息

98
00:03:28,060 --> 00:03:28,760
往上呢

99
00:03:28,760 --> 00:03:30,260
就一个无信的for循环

100
00:03:30,260 --> 00:03:31,060
相当使用

101
00:03:31,060 --> 00:03:32,860
相当是每隔一秒钟

102
00:03:32,860 --> 00:03:33,060
哎

103
00:03:33,060 --> 00:03:35,260
我去执行这样一个打印

104
00:03:35,260 --> 00:03:36,060
好

105
00:03:36,060 --> 00:03:39,460
这个跟我们通常所使用的这个time.new ticker

106
00:03:39,460 --> 00:03:39,660
是吧

107
00:03:39,660 --> 00:03:41,260
一秒钟功能是一样的

108
00:03:41,260 --> 00:03:43,660
只不过语义会更加清晰一点啊

109
00:03:43,660 --> 00:03:44,260
那注意

110
00:03:44,260 --> 00:03:45,060
这个地方

111
00:03:45,060 --> 00:03:48,460
我们是把它单独的放在了一个系绳里面去执行

112
00:03:48,460 --> 00:03:50,060
如果你不放在系绳里面的话

113
00:03:50,060 --> 00:03:51,360
你这个for循环对吧

114
00:03:51,360 --> 00:03:52,560
会一直的循环下去

115
00:03:52,560 --> 00:03:53,660
那下面的代码呢

116
00:03:53,660 --> 00:03:55,060
没有机会执行了

117
00:03:55,060 --> 00:03:55,860
好

118
00:03:55,860 --> 00:03:57,360
那对于这个ticker而言呢

119
00:03:57,360 --> 00:03:58,160
还要注意点

120
00:03:58,160 --> 00:03:59,460
预防我们用完之后

121
00:03:59,460 --> 00:04:01,260
记得把它给关掉啊

122
00:04:01,260 --> 00:04:02,460
释放相关的资源

123
00:04:02,460 --> 00:04:03,560
好

124
00:04:03,560 --> 00:04:04,360
我们来分析一下

125
00:04:04,360 --> 00:04:07,960
那每隔一秒钟输出的这个系绳数目

126
00:04:07,960 --> 00:04:10,560
应该是一万或者比一万多一两个

127
00:04:10,560 --> 00:04:10,960
对吧

128
00:04:10,960 --> 00:04:12,460
我们来运行一下

129
00:04:12,460 --> 00:04:15,060
每隔一秒钟输出一次

130
00:04:15,060 --> 00:04:16,060
好

131
00:04:16,060 --> 00:04:17,060
这是一万零二个啊

132
00:04:17,060 --> 00:04:17,560
一万零二个

133
00:04:17,560 --> 00:04:18,160
一万零二个

134
00:04:18,160 --> 00:04:20,860
因为除了这一万个之外

135
00:04:20,860 --> 00:04:21,760
还有一个min

136
00:04:21,760 --> 00:04:23,260
还有一个这个字

137
00:04:23,260 --> 00:04:23,659
系绳

138
00:04:23,659 --> 00:04:24,260
OK

139
00:04:24,260 --> 00:04:26,659
那我如何去限制

140
00:04:26,659 --> 00:04:27,960
比方说我想限制一下

141
00:04:27,960 --> 00:04:30,159
总的系绳数目不能超过一百

142
00:04:30,159 --> 00:04:30,659
啊

143
00:04:30,659 --> 00:04:31,560
这个怎么做限制呢

144
00:04:31,560 --> 00:04:32,260
啊

145
00:04:32,260 --> 00:04:32,760
这样的话

146
00:04:32,760 --> 00:04:36,060
我们需要把所有创建系绳的地方呢

147
00:04:36,060 --> 00:04:37,659
走一个统一的路口

148
00:04:37,659 --> 00:04:40,260
在那个路口那进行一个数量的管控

149
00:04:40,260 --> 00:04:41,260
啊

150
00:04:41,260 --> 00:04:42,460
那这个就说这个地方啊

151
00:04:42,460 --> 00:04:43,560
我们需要管控一下啊

152
00:04:43,560 --> 00:04:45,659
不能直接这样去开辟一个字系绳

153
00:04:45,659 --> 00:04:46,260
怎么做呢

154
00:04:46,260 --> 00:04:49,760
我们这里面封装一个结构体啊

155
00:04:49,760 --> 00:04:52,460
叫做routine limiter

156
00:04:52,460 --> 00:04:53,060
啊

157
00:04:53,060 --> 00:04:54,159
那这个结构体里面呢

158
00:04:54,159 --> 00:04:57,360
核心要指定一个你需要限制的数目啊

159
00:04:57,360 --> 00:04:58,159
Limit

160
00:04:58,159 --> 00:04:59,360
是个数字

161
00:04:59,960 --> 00:05:00,560
然后呢

162
00:05:00,560 --> 00:05:02,560
还需要借助一个管道

163
00:05:06,159 --> 00:05:06,760
OK

164
00:05:06,760 --> 00:05:09,159
那借助这个管道就跟我们上次和讲的那个

165
00:05:09,159 --> 00:05:10,060
其实很类似啊

166
00:05:10,060 --> 00:05:12,659
都是它那种阻塞机制来实践的

167
00:05:13,460 --> 00:05:15,960
那我们先给它创建一个构造函数

168
00:05:17,160 --> 00:05:19,460
你需要把这个Limit啊

169
00:05:20,060 --> 00:05:22,760
限定的行政数目给传过来

170
00:05:22,760 --> 00:05:25,460
返回一个它的一个指针吧

171
00:05:25,460 --> 00:05:25,860
啊

172
00:05:25,860 --> 00:05:27,760
就两个这个成员嘛

173
00:05:27,760 --> 00:05:28,560
一个Limit

174
00:05:28,860 --> 00:05:29,260
然后呢

175
00:05:29,260 --> 00:05:32,960
还需要给这个Switch进行一个初始化

176
00:05:33,860 --> 00:05:34,360
好

177
00:05:34,360 --> 00:05:36,260
那这个Channel容量多少呢

178
00:05:36,260 --> 00:05:38,360
容量就是这个Limit

179
00:05:38,360 --> 00:05:38,860
n

180
00:05:38,860 --> 00:05:39,460
n

181
00:05:39,960 --> 00:05:40,160
好

182
00:05:40,160 --> 00:05:41,660
跟上次这个思路一模一样啊

183
00:05:41,660 --> 00:05:44,260
你想限制为这个B方做多少

184
00:05:44,260 --> 00:05:47,760
你就把这个管道容量设置多少就可以了

185
00:05:48,460 --> 00:05:48,960
好

186
00:05:49,160 --> 00:05:50,260
关键核心啊

187
00:05:50,260 --> 00:05:52,960
核心我们需要给这个RoutineLimit

188
00:05:52,960 --> 00:05:54,760
给它一个方法

189
00:05:54,760 --> 00:05:57,260
比方说给它一个Run方法

190
00:05:58,260 --> 00:05:59,160
Run什么呢

191
00:06:00,060 --> 00:06:00,660
这个Run呢

192
00:06:00,660 --> 00:06:03,760
我们希望传进来的是一个函数

193
00:06:03,760 --> 00:06:04,760
是函数

194
00:06:05,860 --> 00:06:06,360
Funk

195
00:06:08,560 --> 00:06:09,260
这样的话呢

196
00:06:09,260 --> 00:06:10,560
我们到时候跑什么

197
00:06:10,560 --> 00:06:11,660
就跑这个函数

198
00:06:11,660 --> 00:06:12,660
它是个任意的函数

199
00:06:12,660 --> 00:06:13,160
对吧

200
00:06:13,160 --> 00:06:15,060
你函数里面指定什么内容

201
00:06:15,060 --> 00:06:15,960
那就跑什么内容

202
00:06:16,260 --> 00:06:16,760
但是呢

203
00:06:16,760 --> 00:06:19,660
我需要统一的走这个Run方法

204
00:06:19,960 --> 00:06:20,360
OK

205
00:06:21,060 --> 00:06:25,260
那我们要限制这个当前正在Run的函数的个数嘛

206
00:06:25,260 --> 00:06:25,860
所以呢

207
00:06:25,860 --> 00:06:26,960
你就要搞什么

208
00:06:26,960 --> 00:06:28,260
搞那个形成

209
00:06:28,260 --> 00:06:31,560
往这个星里面去写入一个元素

210
00:06:31,560 --> 00:06:31,760
好

211
00:06:31,760 --> 00:06:34,260
往这个Stage里面去写入一个元素

212
00:06:34,560 --> 00:06:38,160
那需要借助于一个C

213
00:06:39,460 --> 00:06:40,160
指针

214
00:06:40,560 --> 00:06:43,960
G.Ch

215
00:06:43,960 --> 00:06:44,560
好

216
00:06:44,560 --> 00:06:45,560
跟上一这个一样啊

217
00:06:45,560 --> 00:06:47,660
还是往里面去添加元素

218
00:06:48,860 --> 00:06:49,160
好

219
00:06:49,160 --> 00:06:49,860
这样的话呢

220
00:06:49,860 --> 00:06:51,360
那么比方说你这个N是10

221
00:06:51,360 --> 00:06:53,960
那么前10个可以顺利的添加进去

222
00:06:53,960 --> 00:06:55,060
添加进去之后

223
00:06:55,060 --> 00:06:57,160
就会顺利的去执行

224
00:06:57,160 --> 00:07:00,160
可以顺利的去执行这个F函数了

225
00:07:00,160 --> 00:07:01,160
那只不过呢

226
00:07:01,160 --> 00:07:04,560
我们需要把它放到一个斜称里面去执行

227
00:07:06,460 --> 00:07:06,760
好

228
00:07:06,760 --> 00:07:07,160
Go

229
00:07:07,660 --> 00:07:08,060
放开

230
00:07:11,560 --> 00:07:13,060
把这个F呢

231
00:07:13,060 --> 00:07:15,760
放到一个字称里面去执行

232
00:07:16,960 --> 00:07:17,360
好

233
00:07:17,360 --> 00:07:18,360
那执行完之后

234
00:07:18,360 --> 00:07:18,560
对吧

235
00:07:18,560 --> 00:07:19,160
执行完之后

236
00:07:19,160 --> 00:07:22,860
记得要把这个元素从管道里面呢

237
00:07:22,860 --> 00:07:23,660
把它给取走

238
00:07:23,960 --> 00:07:25,160
这样腾出未来

239
00:07:25,160 --> 00:07:26,860
给后门的人去使用

240
00:07:27,760 --> 00:07:28,160
取走

241
00:07:28,960 --> 00:07:29,560
这样的话呢

242
00:07:29,560 --> 00:07:30,660
那我们相应的啊

243
00:07:30,660 --> 00:07:32,060
这个地方就需要改造一下

244
00:07:32,460 --> 00:07:36,160
我们就不能通过直接去创建这个形成了

245
00:07:36,160 --> 00:07:37,760
我们得限制一下啊

246
00:07:37,760 --> 00:07:39,360
我们这边

247
00:07:39,960 --> 00:07:40,660
Limiter

248
00:07:42,160 --> 00:07:43,760
等于我们new一个啊

249
00:07:44,460 --> 00:07:45,960
掉这个构造还说new一个吧

250
00:07:47,360 --> 00:07:49,160
好

251
00:07:49,160 --> 00:07:52,360
需要传参与就是你需要把它设定为多少

252
00:07:52,360 --> 00:07:54,460
我们说限制100个吧

253
00:07:54,460 --> 00:07:56,160
最高限制100个

254
00:07:56,460 --> 00:07:56,860
系统

255
00:07:57,660 --> 00:07:59,160
然后通过这个Limiter

256
00:08:00,060 --> 00:08:02,560
通过它的这个run方法

257
00:08:02,860 --> 00:08:05,760
去执行刚才的这个work

258
00:08:07,060 --> 00:08:07,560
OK

259
00:08:07,560 --> 00:08:08,660
就改成成这个样子了

260
00:08:09,560 --> 00:08:09,960
好

261
00:08:09,960 --> 00:08:12,960
本来是直接起系统去执行work

262
00:08:12,960 --> 00:08:15,160
去执行这里面的逻辑

263
00:08:15,160 --> 00:08:15,860
现在呢

264
00:08:15,860 --> 00:08:19,660
变成了通过统一走Limiter的run方法

265
00:08:19,660 --> 00:08:21,060
来执行这个work

266
00:08:21,460 --> 00:08:22,660
那我在run里面呢

267
00:08:22,660 --> 00:08:23,660
在这个地方对吧

268
00:08:23,660 --> 00:08:25,560
就进行了一个限制嘛

269
00:08:26,560 --> 00:08:31,460
所以就是说那上这颗我们是限定某一个特定函数的并发量

270
00:08:31,460 --> 00:08:31,860
所以呢

271
00:08:31,860 --> 00:08:34,960
我们可以在那个函数的入口里去加这个阻塞

272
00:08:34,960 --> 00:08:35,960
现在呢

273
00:08:35,960 --> 00:08:39,460
你既然是要设定所有的形成数目

274
00:08:39,460 --> 00:08:43,160
那么你必然要把所有系统和它归集到一起嘛

275
00:08:43,160 --> 00:08:43,760
对吧

276
00:08:43,760 --> 00:08:45,160
就一个统一入口

277
00:08:45,160 --> 00:08:45,660
这样的话呢

278
00:08:45,660 --> 00:08:48,860
我们在统一入口这去做这个阻塞

279
00:08:49,660 --> 00:08:50,160
好

280
00:08:50,160 --> 00:08:51,460
我们可以把这个代码呢

281
00:08:51,460 --> 00:08:52,660
再运行起来

282
00:08:52,660 --> 00:08:57,860
看这个每一个瞬间的一个存活的形成数目是102个

283
00:08:57,860 --> 00:08:58,160
对吧

284
00:08:58,160 --> 00:09:01,560
因为我们还有这个ticker和这个密码

285
00:09:01,560 --> 00:09:02,760
所以一共是102个

