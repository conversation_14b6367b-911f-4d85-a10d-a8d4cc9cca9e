1
00:00:00,600 --> 00:00:01,720
上一节课我们讲

2
00:00:01,720 --> 00:00:03,160
把这个用户 id 呢

3
00:00:03,160 --> 00:00:04,800
直接存到 cookie 里

4
00:00:04,800 --> 00:00:07,112
来保持我的登录状态

5
00:00:07,112 --> 00:00:08,390
请求每一个接口

6
00:00:08,390 --> 00:00:10,330
都会把这个用户 id 传给后端吗

7
00:00:10,330 --> 00:00:11,650
后端知道你是谁

8
00:00:11,650 --> 00:00:12,822
已经登录了

9
00:00:12,822 --> 00:00:15,640
但问题是这种方法就很不安全

10
00:00:15,640 --> 00:00:17,517
比如说一个黑客

11
00:00:17,517 --> 00:00:19,710
他要去请求一个敏感操作

12
00:00:19,710 --> 00:00:22,120
还要去请求这个转账接口吧

13
00:00:22,120 --> 00:00:24,120
要请求转账接口

14
00:00:24,120 --> 00:00:26,490
他可以任意的去伪造一个 cookie 

15
00:00:26,490 --> 00:00:27,310
在 cookie 里面呢

16
00:00:27,310 --> 00:00:29,810
它可以任意的去伪造这个用户 id 

17
00:00:29,810 --> 00:00:31,897
这个用户 id 他可以随便设

18
00:00:31,897 --> 00:00:34,180
他把这样一个 cookie 提交给了后端

19
00:00:34,180 --> 00:00:35,020
那么后端呢

20
00:00:35,020 --> 00:00:37,787
就从这个用户的账上把钱给扣走了

21
00:00:37,787 --> 00:00:41,890
所以呢， cookie 里面肯定存放的不是原始的

22
00:00:41,890 --> 00:00:43,855
这么简单的一个用户 id 

23
00:00:43,855 --> 00:00:45,870
那么我们现在讲一个方法

24
00:00:45,870 --> 00:00:47,542
就是借 W 

25
00:00:47,542 --> 00:00:50,100
它是一种生成 token 的方法

26
00:00:50,100 --> 00:00:52,540
当然这个 token 里面也是包含了用户 id 

27
00:00:52,540 --> 00:00:54,620
只不过呢，它可以保证这个 token 

28
00:00:54,620 --> 00:00:57,195
不是说任何人都可以随意伪造的

29
00:00:57,195 --> 00:01:01,157
那么 GWT 全称是 JSON web token 

30
00:01:01,157 --> 00:01:03,370
这个 token 里面核心存储的

31
00:01:03,370 --> 00:01:04,930
就是我上一节课讲的

32
00:01:04,930 --> 00:01:06,170
就类似于 UID 

33
00:01:06,170 --> 00:01:08,110
这样的一些业务信息

34
00:01:08,110 --> 00:01:09,370
那这个 token 呢

35
00:01:09,370 --> 00:01:12,370
整体上看好像是一个随机的字符串啊

36
00:01:12,370 --> 00:01:13,450
实际上不随机

37
00:01:13,450 --> 00:01:17,330
他呢，通过这个点分割成了三部分

38
00:01:17,330 --> 00:01:18,930
那么第一部分怎么来的

39
00:01:18,930 --> 00:01:22,930
第一部分啊，实际上是由这样的一个 JSON 字符串

40
00:01:22,930 --> 00:01:24,370
本质上是一个字符串嘛

41
00:01:24,370 --> 00:01:25,410
那这个字符串呢

42
00:01:25,410 --> 00:01:29,130
通过一个 base 64 u l l encode 编码

43
00:01:29,130 --> 00:01:30,775
会转成这样的形式

44
00:01:30,775 --> 00:01:33,790
先说这个 JSON 里面本身包含的信息啊

45
00:01:33,790 --> 00:01:36,027
基本上都是完全一样的

46
00:01:36,027 --> 00:01:37,680
不同公司、不同业务场景

47
00:01:37,680 --> 00:01:40,810
基本上使用的都是这个数据

48
00:01:40,810 --> 00:01:43,212
所以第一部分其实意义不大啊

49
00:01:43,212 --> 00:01:44,930
然后再说这个贝斯洛斯

50
00:01:44,930 --> 00:01:46,090
那贝斯洛斯本身

51
00:01:46,090 --> 00:01:48,510
它是一种透明的编码机制吗

52
00:01:48,510 --> 00:01:50,770
它并不是一种什么加密算法

53
00:01:50,770 --> 00:01:51,980
它可以编码过去

54
00:01:51,980 --> 00:01:53,620
也可以解码回来

55
00:01:53,620 --> 00:01:55,140
就有点类似于说

56
00:01:55,140 --> 00:01:56,780
你可以把一个中文啊

57
00:01:56,780 --> 00:01:58,080
编码成英文

58
00:01:58,080 --> 00:02:01,180
但你也可以再从英文再返回成中文啊

59
00:02:01,180 --> 00:02:03,075
它并不具有任何的保密性

60
00:02:03,075 --> 00:02:05,360
只是一种形式转换

61
00:02:05,360 --> 00:02:09,539
比方说我的原始输入是这样一个文本啊

62
00:02:09,539 --> 00:02:13,970
通过 base 64 encode 编码一下

63
00:02:13,970 --> 00:02:16,100
生成一个所谓的 CPF 

64
00:02:16,100 --> 00:02:21,220
然后呢，我把这个 CPF 再扔给这个贝斯六四的 decode 

65
00:02:21,220 --> 00:02:22,427
反解一下

66
00:02:22,427 --> 00:02:23,410
你发现呢

67
00:02:23,410 --> 00:02:28,570
这个 BS 它跟原始的这个 text 是完全相等的

68
00:02:28,570 --> 00:02:31,392
所以这个数据实际上是明文

69
00:02:31,392 --> 00:02:32,800
再来看第二部分

70
00:02:32,800 --> 00:02:33,960
那第二部分的话

71
00:02:33,960 --> 00:02:37,022
这个 JSON 就跟你的业务强度相关了

72
00:02:37,022 --> 00:02:40,900
你右侧需要在这个 token 里面存储哪些数据

73
00:02:40,900 --> 00:02:44,010
都可以把它们放到这个第三里面去

74
00:02:44,010 --> 00:02:48,020
当然了，还是不建议存放一些特别敏感的信息

75
00:02:48,020 --> 00:02:52,685
因为第二部分毕竟是通过 base 64

76
00:02:52,685 --> 00:02:54,430
放到 token 里面去的

77
00:02:54,430 --> 00:02:55,350
这个 token 呢

78
00:02:55,350 --> 00:02:57,760
是在网络上进行传输的

79
00:02:57,760 --> 00:03:00,712
那一旦被别人截获了

80
00:03:00,712 --> 00:03:02,760
别人拿到这个贝索斯编码

81
00:03:02,760 --> 00:03:05,820
结果能够反解除原始的这个阶层

82
00:03:05,820 --> 00:03:08,730
所以如果你觉得第二部分

83
00:03:08,730 --> 00:03:10,330
这个阶层比较敏感的话

84
00:03:10,330 --> 00:03:10,850
怎么办

85
00:03:10,850 --> 00:03:14,270
哎，你可以结合着 HTPS 来使用

86
00:03:14,270 --> 00:03:18,965
因为 HTBS 会对整个应用层数据全部加密码

87
00:03:18,965 --> 00:03:22,190
那么将来我们就打算把这个 UID 啊

88
00:03:22,190 --> 00:03:25,010
就放到第二部分的这个阶层里面去

89
00:03:25,010 --> 00:03:27,220
好，关键是第三部分

90
00:03:27,220 --> 00:03:28,870
正是因为有了第三部分

91
00:03:28,870 --> 00:03:31,710
才能够解决我最开始提出的那个问题

92
00:03:31,710 --> 00:03:35,992
就是避免有人去随意的伪造这个 UID 

93
00:03:35,992 --> 00:03:38,360
那么第三部分是怎么生成的

94
00:03:38,360 --> 00:03:42,265
它是呢，把第一部分这个贝斯修斯的结果

95
00:03:42,265 --> 00:03:44,230
再加上一个底儿

96
00:03:44,230 --> 00:03:46,540
再加上第二部分被搜索结果

97
00:03:46,540 --> 00:03:49,110
实际上就是把前两部分

98
00:03:49,110 --> 00:03:51,260
再结合着我的一个密钥啊

99
00:03:51,260 --> 00:03:53,430
扔给一个加密算法

100
00:03:53,430 --> 00:03:55,770
当然这个加密算法一方面有加密

101
00:03:55,770 --> 00:03:57,390
另一方面呢，有这个哈希

102
00:03:57,390 --> 00:03:58,915
它是有混合的吧

103
00:03:58,915 --> 00:03:59,950
但总之呢

104
00:03:59,950 --> 00:04:03,130
这个算法它需要一个密钥作为输入

105
00:04:03,130 --> 00:04:07,570
而这个密钥只有我们的 web 服务端它才有

106
00:04:07,570 --> 00:04:08,680
黑客是没有的

107
00:04:08,680 --> 00:04:09,760
包括用户也没有

108
00:04:09,760 --> 00:04:10,220
好

109
00:04:10,220 --> 00:04:12,810
所以你可以把它生成的这个输出啊

110
00:04:12,810 --> 00:04:16,260
输出理解为是一个唯一的

111
00:04:16,260 --> 00:04:19,918
并且只有服务端才能够生成的这样一个输出

112
00:04:19,918 --> 00:04:21,251
作为第三分

113
00:04:21,251 --> 00:04:25,940
那么这三份合起来就是所谓的 jwt token 

114
00:04:25,940 --> 00:04:28,270
那么用户登录成功之后是吧

115
00:04:28,270 --> 00:04:30,770
生成一个用户 id 

116
00:04:30,770 --> 00:04:32,020
用户 id 呢

117
00:04:32,020 --> 00:04:34,450
存到第二部分这个计算里面去

118
00:04:34,450 --> 00:04:37,197
由服务端去生成这样一个 token 

119
00:04:37,197 --> 00:04:38,790
把这个 token 呢

120
00:04:38,790 --> 00:04:40,130
传给客户端

121
00:04:40,130 --> 00:04:42,660
种到浏览器的 cookie 里面去

122
00:04:42,660 --> 00:04:45,080
那以后请求任意接口

123
00:04:45,080 --> 00:04:48,010
浏览器都把这个 token 回传给服务端

124
00:04:48,010 --> 00:04:49,970
那么服务端拿到这个 token 

125
00:04:49,970 --> 00:04:52,890
它当然可以从第二分里面去反

126
00:04:52,890 --> 00:04:54,360
解除那个 UID 

127
00:04:54,360 --> 00:04:56,030
但同时呢

128
00:04:56,030 --> 00:05:00,110
它要根据前两部分重新来生成

129
00:05:00,110 --> 00:05:01,000
第三部分

130
00:05:01,000 --> 00:05:03,710
跟你传的 token 的第三部分对比一下

131
00:05:03,710 --> 00:05:05,527
看是否完全一样

132
00:05:05,527 --> 00:05:07,260
如果完全一样，诶

133
00:05:07,260 --> 00:05:10,680
证明这个 token 最开始就是我服务

134
00:05:10,680 --> 00:05:11,600
我生成的

135
00:05:11,600 --> 00:05:13,975
不受黑客随意伪造的

136
00:05:13,975 --> 00:05:17,460
从而证明这个 UID 就是浏览器里面

137
00:05:17,460 --> 00:05:18,605
真实的 UID 

138
00:05:18,605 --> 00:05:21,800
那么我在 YOUTUBE 包下面写了一个 JWT 

139
00:05:21,800 --> 00:05:23,790
算法的完整实现

140
00:05:23,790 --> 00:05:25,802
能快速过一下

141
00:05:25,802 --> 00:05:27,330
因为需要三部分嘛

142
00:05:27,330 --> 00:05:29,860
所以这个是第一部分称之为 header 

143
00:05:29,860 --> 00:05:31,610
包含这两个东西啊

144
00:05:31,610 --> 00:05:33,040
算法和类型

145
00:05:33,040 --> 00:05:36,520
第二部分就是这个所谓的 payload 

146
00:05:36,520 --> 00:05:38,110
那么这个算法本身呢

147
00:05:38,110 --> 00:05:40,440
它要求里面包含很多信息啊

148
00:05:40,440 --> 00:05:42,330
那么这里面并没有我们自己的 UI 

149
00:05:42,330 --> 00:05:44,850
D 是跟 U 相关的一些数据呢

150
00:05:44,850 --> 00:05:47,390
它是放到这个扩展字段里面

151
00:05:47,390 --> 00:05:49,660
这个 user defend ，他是个 map 啊

152
00:05:49,660 --> 00:05:51,780
你可以把这个 map 里面的任意添加

153
00:05:51,780 --> 00:05:54,160
你的跟右强相关的数据

154
00:05:54,160 --> 00:05:56,665
看一下如何去生成这个 token 

155
00:05:56,665 --> 00:05:57,920
对于第一部分

156
00:05:57,920 --> 00:06:00,275
先进行这个计算序列化

157
00:06:00,275 --> 00:06:01,460
序列化之后呢

158
00:06:01,460 --> 00:06:03,340
再按照 base 64啊

159
00:06:03,340 --> 00:06:05,740
这个 url encode 编码一下

160
00:06:05,740 --> 00:06:07,307
这样是第一部分

161
00:06:07,307 --> 00:06:09,887
同理啊，对于第二部分完全一样

162
00:06:09,887 --> 00:06:13,120
第二部分 payload 也先计算序列化

163
00:06:13,120 --> 00:06:15,330
然后再进行 base 编码

164
00:06:15,330 --> 00:06:18,277
好，拿到这两部分之后的话

165
00:06:18,277 --> 00:06:21,240
还需要有一个密钥 secret 

166
00:06:21,240 --> 00:06:24,905
那么这样呢，就能够生成我的第三部分

167
00:06:24,905 --> 00:06:26,270
signature 签名

168
00:06:26,270 --> 00:06:28,000
这服务端签名了

169
00:06:28,000 --> 00:06:32,890
第三部分通过点号拼起来就是 j w t token 

170
00:06:32,890 --> 00:06:34,910
看一下我如何去做验证

171
00:06:34,910 --> 00:06:38,130
就是将来浏览器把 token 回传到服务端

172
00:06:38,130 --> 00:06:41,442
服务端要验证一下这个 token 是否合法

173
00:06:41,442 --> 00:06:45,000
同时呢，要从 token 里面提取出我所需要的信息

174
00:06:45,000 --> 00:06:50,220
就是关键是说我要把第二部分给还原回来

175
00:06:50,220 --> 00:06:53,402
就是执行刚才的相反操作

176
00:06:53,402 --> 00:06:55,410
那么你把 token 传过来

177
00:06:55,410 --> 00:06:59,165
我按照点能够分隔出三部分，对吧

178
00:06:59,165 --> 00:07:02,310
那么根据前两部分以及我的 secret 

179
00:07:02,310 --> 00:07:05,100
我能够生成一个 signature 

180
00:07:05,100 --> 00:07:06,480
那么这个 signature 呢

181
00:07:06,480 --> 00:07:10,990
跟你传过来的这个第三部分

182
00:07:10,990 --> 00:07:12,650
进行一下对比是吧

183
00:07:12,650 --> 00:07:13,882
要保证一样吗

184
00:07:13,882 --> 00:07:15,900
好，我先确保一样啊

185
00:07:15,900 --> 00:07:17,680
先确保 token 是合法的

186
00:07:17,680 --> 00:07:19,420
然后呢，我就可以什么

187
00:07:19,420 --> 00:07:23,250
可以从第二部分里面通过 base 64啊

188
00:07:23,250 --> 00:07:25,450
decode 反解码是吧

189
00:07:25,450 --> 00:07:28,072
反解除那个右数据

190
00:07:28,072 --> 00:07:31,000
那我的 UID 就存在这个已有数据里面

191
00:07:31,000 --> 00:07:32,180
那所以这样的话

192
00:07:32,180 --> 00:07:34,892
我们的登录接口需要改造一下

193
00:07:34,892 --> 00:07:38,150
那么现在如果用户名密码都输对了是吧

194
00:07:38,150 --> 00:07:39,280
登录成功了

195
00:07:39,280 --> 00:07:40,530
登录成功的话

196
00:07:40,530 --> 00:07:43,655
我需要生成一个 JWT 啊

197
00:07:43,655 --> 00:07:45,730
就是先构造一个 header 

198
00:07:45,730 --> 00:07:47,410
再构造一个 payload 

199
00:07:47,410 --> 00:07:50,235
关键是这个 payload 里面啊

200
00:07:50,235 --> 00:07:51,290
这部分是吧

201
00:07:51,290 --> 00:07:53,130
user defend 就自由的

202
00:07:53,130 --> 00:07:53,880
这一部分呢

203
00:07:53,880 --> 00:07:56,030
我要把我的这个用户 id 

204
00:07:56,030 --> 00:07:57,810
放到这一部分里面去

205
00:07:57,810 --> 00:08:00,750
然后调我 U 求包下面的这个函数

206
00:08:00,750 --> 00:08:03,550
去生成一个 jwt token 

207
00:08:03,550 --> 00:08:05,250
然后呢，这个 token 呢

208
00:08:05,250 --> 00:08:08,660
你看它就作为 cookie 的 value ，哎

209
00:08:08,660 --> 00:08:10,730
种到浏览器的 cookie 里面去了

210
00:08:10,730 --> 00:08:13,262
然后再来看我们的修改密码接口

211
00:08:13,262 --> 00:08:14,980
那修改密码里面的话

212
00:08:14,980 --> 00:08:16,060
还是跟之前一样

213
00:08:16,060 --> 00:08:19,580
我还是要去便利你给我回传回来的所有 cookie 

214
00:08:19,580 --> 00:08:22,260
去找到我所需要的那个 cookie 

215
00:08:22,260 --> 00:08:23,820
它对应的 value 啊

216
00:08:23,820 --> 00:08:26,130
就是那个 gwt token 

217
00:08:26,130 --> 00:08:29,920
然后呢，我要拿到这个 token 之后啊

218
00:08:29,920 --> 00:08:31,360
我去调这个函数

219
00:08:31,360 --> 00:08:34,510
那这个函数啊，就是说拿到这个 token 之后

220
00:08:34,510 --> 00:08:37,429
调 YOUTUBE 包下面的这个 verify 啊

221
00:08:37,429 --> 00:08:41,640
这个 WIFI 呢，一方面是在验证 token 是合法的同时呢

222
00:08:41,640 --> 00:08:43,989
它会提取出 payload 

223
00:08:43,989 --> 00:08:48,550
然后我从 PLOAD 里面提取出那个用户 id 

224
00:08:48,550 --> 00:08:51,380
然后呢，提取用户 id 之后

225
00:08:51,380 --> 00:08:53,020
我会把这个用户 id 啊

226
00:08:53,020 --> 00:08:55,197
就如果用化 D 大于零的话

227
00:08:55,197 --> 00:08:56,370
可以把它呢

228
00:08:56,370 --> 00:08:59,920
放到这个 GI 的 context 里面去

229
00:08:59,920 --> 00:09:01,977
哎，这一步很关键

230
00:09:01,977 --> 00:09:05,180
就是我们说既然本身支持那个中间件嘛

231
00:09:05,180 --> 00:09:09,825
那所有的中间件他们都会共享同一个 context 

232
00:09:09,825 --> 00:09:12,720
那么如果我在前面的中心店里面

233
00:09:12,720 --> 00:09:16,050
往这个 context 里面去放入了 UID 

234
00:09:16,050 --> 00:09:18,050
那么我在后面的 handle 里面

235
00:09:18,050 --> 00:09:21,850
就能够从 context 里面把这个 UID 把它给取出来

236
00:09:21,850 --> 00:09:25,860
那具体来说，我们来到定义路由这边

237
00:09:25,860 --> 00:09:27,110
main 函数

238
00:09:27,110 --> 00:09:30,800
大家注意看这个修改密

239
00:09:30,800 --> 00:09:32,000
这个 handler 是吧

240
00:09:32,000 --> 00:09:33,940
本来我们是没有这部分的啊

241
00:09:33,940 --> 00:09:35,607
没加这个文件

242
00:09:35,607 --> 00:09:36,670
那现在呢

243
00:09:36,670 --> 00:09:39,310
你在正式的进入到修改密

244
00:09:39,310 --> 00:09:41,110
这个 handler 之前啊

245
00:09:41,110 --> 00:09:44,160
会先进到这个 worth 中间件里面来

246
00:09:44,160 --> 00:09:46,665
而在 vs 中间件里面呢

247
00:09:46,665 --> 00:09:49,560
会从 cookie 里面解析出 UI 

248
00:09:49,560 --> 00:09:52,445
D 把 UID 放到 context 里面去

249
00:09:52,445 --> 00:09:57,740
那这样的话，我们到真正的 update password 这个 header 里面

250
00:09:57,740 --> 00:10:01,280
我就可以直接从 JN 的 context 里面

251
00:10:01,280 --> 00:10:04,140
把这个 UID 直接给取出来啊

252
00:10:04,140 --> 00:10:05,807
如果没有取出来

253
00:10:05,807 --> 00:10:08,960
那就提示你请先登录嘛

254
00:10:08,960 --> 00:10:11,230
那么改造之后我们再来登录一下

255
00:10:11,230 --> 00:10:11,670
注意看

256
00:10:11,670 --> 00:10:13,870
目前 cookie 里面是没有那个

257
00:10:13,870 --> 00:10:16,220
UID 或者 JWT 的

258
00:10:16,220 --> 00:10:19,320
来登录一下用户名密码

259
00:10:19,320 --> 00:10:20,580
我点击登录

260
00:10:20,580 --> 00:10:21,940
注意看这个地方

261
00:10:21,940 --> 00:10:25,867
点登录好就出来一个 JWT 对吧

262
00:10:25,867 --> 00:10:27,830
那么为什么叫 GWT 呢

263
00:10:27,830 --> 00:10:31,735
这个是因为我在代码里面给 cookie 设置的名称

264
00:10:31,735 --> 00:10:33,930
就叫这个名字是吧

265
00:10:33,930 --> 00:10:35,990
设置 cookie ，那么这个 cookie 名称呢

266
00:10:35,990 --> 00:10:37,885
就叫 JWT 

267
00:10:37,885 --> 00:10:39,580
看一下它的值啊

268
00:10:39,580 --> 00:10:41,230
值的话是这个样子

269
00:10:41,230 --> 00:10:43,930
它其实就是由底分割成了三部分

270
00:10:43,930 --> 00:10:46,267
第一个点在这个位置

271
00:10:46,267 --> 00:10:48,557
第二个点呢，在这个位置

272
00:10:48,557 --> 00:10:50,650
如果我点这个退出的话

273
00:10:50,650 --> 00:10:53,030
那么这个 JWT 就没了

274
00:10:53,030 --> 00:10:55,270
点一下注意看是吧

275
00:10:55,270 --> 00:10:56,157
没了

276
00:10:56,157 --> 00:10:59,820
那本质上是因为他请求到了后端的

277
00:10:59,820 --> 00:11:02,180
这个 logo 的接口

278
00:11:02,180 --> 00:11:04,340
把生命周期改成了一嘛
