1
00:00:00,800 --> 00:00:03,260
WSL 和 docker 登完账之后

2
00:00:03,260 --> 00:00:05,880
终于我们可以来安装 ca 

3
00:00:05,880 --> 00:00:08,720
那官网上给出了多种安装方式

4
00:00:08,720 --> 00:00:11,460
包括从二进制文件安装

5
00:00:11,460 --> 00:00:13,500
从源码编译安装

6
00:00:13,500 --> 00:00:16,499
包括依赖于包关系来安装

7
00:00:16,499 --> 00:00:18,440
大家可以去官网上看一下

8
00:00:18,440 --> 00:00:20,455
它支持苹果和 windows 

9
00:00:20,455 --> 00:00:24,180
我们今天呢讲一种通过 go 来安装的方式

10
00:00:24,180 --> 00:00:26,130
是通过 go install 来安装

11
00:00:26,130 --> 00:00:27,700
那要使用 go install 

12
00:00:27,700 --> 00:00:29,797
前提是你得先装上 go 

13
00:00:29,797 --> 00:00:32,210
我们先进到 LINUX 这个子系统里面

14
00:00:32,210 --> 00:00:35,970
来使用 apt install 命令来安装

15
00:00:35,970 --> 00:00:37,040
够

16
00:00:37,040 --> 00:00:40,917
a apt get install go l 

17
00:00:40,917 --> 00:00:43,180
由于是 STOM ，需要输入密码

18
00:00:44,300 --> 00:00:46,360
OK ，这样的话我们的 go 就安装好了

19
00:00:46,360 --> 00:00:50,390
现在是1.2这个版本

20
00:00:50,390 --> 00:00:52,450
看一下 go in 

21
00:00:52,450 --> 00:00:54,867
这个是 go pass 

22
00:00:54,867 --> 00:00:56,820
这个是 proxy 

23
00:00:56,820 --> 00:00:58,900
这个是 go root 

24
00:00:58,900 --> 00:01:01,242
已经都配好了

25
00:01:01,242 --> 00:01:02,910
如何让你的 go pass 

26
00:01:02,910 --> 00:01:05,350
并放到环境变量 pass 里面去呢

27
00:01:05,350 --> 00:01:06,630
我们可以编辑一下

28
00:01:06,630 --> 00:01:09,240
当前用户下面的 by chrc 文件

29
00:01:09,240 --> 00:01:13,955
V 波浪线点 BH 2 C 

30
00:01:13,955 --> 00:01:17,000
在文件最后一行追加这样一个内容

31
00:01:17,000 --> 00:01:20,130
export pass 等于 pass 

32
00:01:20,130 --> 00:01:23,050
后面是你的 go pass 

33
00:01:23,050 --> 00:01:25,640
下面的这一集 B 目

34
00:01:25,640 --> 00:01:28,430
然后保存 WH 退出

35
00:01:29,710 --> 00:01:32,070
看一下我的 pass 环境变量

36
00:01:33,330 --> 00:01:36,570
最后这一项其实就是我的 go pass 下面的 bin 

37
00:01:36,570 --> 00:01:37,690
这个子目

38
00:01:37,690 --> 00:01:39,790
将来通过 going in store 

39
00:01:39,790 --> 00:01:42,880
按照命令会放到这个 bin 目录下

40
00:01:42,880 --> 00:01:45,460
而这个 bin 目录刚好在我的 pass 里面

41
00:01:45,460 --> 00:01:45,940
所以呢

42
00:01:45,940 --> 00:01:48,600
可以直接去使用这个目录下面的

43
00:01:48,600 --> 00:01:49,967
所有可执行文件

44
00:01:49,967 --> 00:01:55,890
然后可以直接通过这个 go install 命令来安装 kind 

45
00:01:55,890 --> 00:02:00,865
把命令拷过来粘贴回车报错

46
00:02:00,865 --> 00:02:05,240
它要求必须在一个 model 里面才可以运行 going in store 

47
00:02:05,240 --> 00:02:07,780
也就是说少一个 go 点 model 文件

48
00:02:07,780 --> 00:02:10,030
那我们先随便创建一个目录

49
00:02:10,030 --> 00:02:13,950
tap 进到这个目录里面来

50
00:02:13,950 --> 00:02:16,980
然后 go mod ini

51
00:02:17,980 --> 00:02:20,220
随便输个 mod 名称

52
00:02:21,700 --> 00:02:25,297
然后再来执行刚才那个 go in storm 命令

53
00:02:25,297 --> 00:02:26,410
还是报错

54
00:02:26,410 --> 00:02:30,525
他要求先通过 go get 下载这个代码

55
00:02:30,525 --> 00:02:33,410
那我们就先下载这个代码

56
00:02:33,410 --> 00:02:36,012
go get 一下回车

57
00:02:36,012 --> 00:02:38,240
然后再执行 going in store 

58
00:02:38,240 --> 00:02:40,477
go in store 是执行编译吗

59
00:02:40,477 --> 00:02:42,970
这样的话已经生成了可执行文件

60
00:02:42,970 --> 00:02:46,847
并且可执行文件放到了我的 go pass b 目录下

61
00:02:46,847 --> 00:02:48,890
那我的 go pass 是什么呢

62
00:02:48,890 --> 00:02:55,170
看一眼 go in go pass 是这个目录

63
00:02:55,170 --> 00:03:00,040
那我们看一看这个目录下面的 bin 目录

64
00:03:00,040 --> 00:03:02,020
DIN 回

65
00:03:02,020 --> 00:03:05,770
就生成了这个 can 的可执行文件

66
00:03:05,770 --> 00:03:09,827
于是乎呢，我就可以直接使用 can 这个命令

67
00:03:09,827 --> 00:03:14,710
我在任意目录下都可以使用 can 这个命令

68
00:03:16,050 --> 00:03:18,130
这样看的就安装成功了
