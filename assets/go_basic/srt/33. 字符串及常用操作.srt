1
00:00:00,600 --> 00:00:03,750
对字符串进行一个详细的讲解

2
00:00:03,750 --> 00:00:05,540
我们看这边 S 1啊

3
00:00:05,540 --> 00:00:08,725
用双引号引起来表示字符串

4
00:00:08,725 --> 00:00:12,260
那字符串里面其实是可以包含汉字的

5
00:00:12,260 --> 00:00:15,590
包括像这种特殊符号都可以包含啊

6
00:00:15,590 --> 00:00:18,932
包括中文和英文的变量符号都可以有

7
00:00:18,932 --> 00:00:21,030
这是一个简单的字符串

8
00:00:21,030 --> 00:00:22,430
再来看 S 2

9
00:00:22,430 --> 00:00:23,380
S 2的话

10
00:00:23,380 --> 00:00:24,300
这个字符串里面呢

11
00:00:24,300 --> 00:00:25,940
它包含一些斜杠，对吧

12
00:00:25,940 --> 00:00:27,030
你看斜杠

13
00:00:27,030 --> 00:00:27,920
为什么呢

14
00:00:27,920 --> 00:00:30,740
因为我们说本来是应该用双引号

15
00:00:30,740 --> 00:00:31,940
把字符串给引起来了

16
00:00:31,940 --> 00:00:34,020
但如果这个字符串内部

17
00:00:34,020 --> 00:00:37,910
它本身就想出现双引号怎么办啊

18
00:00:37,910 --> 00:00:39,650
你不能直接把双引号写这

19
00:00:39,650 --> 00:00:41,590
如果直接把双引号写这儿的话

20
00:00:41,590 --> 00:00:45,380
它会认为前面是一个字符串啊

21
00:00:45,380 --> 00:00:47,262
后面的是另外一个是吧

22
00:00:47,262 --> 00:00:49,970
所以呢，如果它本身是字符串一部分的话

23
00:00:49,970 --> 00:00:52,490
那么需要在前面加一个斜杠啊

24
00:00:52,490 --> 00:00:54,935
转移一下称之为转移

25
00:00:54,935 --> 00:00:59,320
包括说你如果想包含像这种制表符

26
00:00:59,320 --> 00:01:03,420
制表符嘛，就是你的 tab 键啊

27
00:01:03,420 --> 00:01:04,430
四个空格吗

28
00:01:04,430 --> 00:01:06,740
包括你像你的换行符的话

29
00:01:06,740 --> 00:01:10,250
那么需要通过斜杠 N 表示换行符是吧

30
00:01:10,250 --> 00:01:13,485
斜杠 T 表示一个制表符

31
00:01:13,485 --> 00:01:15,930
那么如果里面包含转移的话

32
00:01:15,930 --> 00:01:21,060
这个 S 2通过 print 打印出来长成什么样子呢

33
00:01:21,060 --> 00:01:23,317
我们来把代码运行一下看看

34
00:01:23,317 --> 00:01:24,560
你们看一下

35
00:01:24,560 --> 00:01:26,940
S 1答案出来就长成这个样子

36
00:01:26,940 --> 00:01:28,500
而这个 S 2呢

37
00:01:28,500 --> 00:01:29,805
S 2的话

38
00:01:29,805 --> 00:01:32,820
C 冒号后面应该是有一个双引号

39
00:01:32,820 --> 00:01:34,010
所以这边呢

40
00:01:34,010 --> 00:01:35,780
确实是有双引号，对吧

41
00:01:35,780 --> 00:01:40,540
然后这个 fine 点后面双引号

42
00:01:40,540 --> 00:01:42,180
然后是换行符

43
00:01:42,180 --> 00:01:45,260
fine 后面确实是一个换行符

44
00:01:45,260 --> 00:01:48,847
然后这边是两个斜杠

45
00:01:48,847 --> 00:01:52,240
然后就反应到这边呢，是一个斜杠

46
00:01:52,240 --> 00:01:55,390
就说那你这个斜杠是一个特殊含义吗

47
00:01:55,390 --> 00:01:56,710
它表是要转移

48
00:01:56,710 --> 00:01:59,910
但如果我不想让这个斜杠就有特殊含义

49
00:01:59,910 --> 00:02:02,785
我就想出现一个原生的斜杠咋办

50
00:02:02,785 --> 00:02:04,250
原生的斜杠的话

51
00:02:04,250 --> 00:02:05,290
不能用一个斜杠

52
00:02:05,290 --> 00:02:06,750
得用两个斜杠啊

53
00:02:06,750 --> 00:02:08,970
两个斜杠表示一个原生的斜杠

54
00:02:08,970 --> 00:02:09,830
所以这边的话呢

55
00:02:09,830 --> 00:02:11,210
是只有一个斜杠啊

56
00:02:11,210 --> 00:02:14,920
后面 sat 后面是一个制表符

57
00:02:14,920 --> 00:02:16,000
然后是 U 是吧

58
00:02:16,000 --> 00:02:17,660
就说 SP 和 U 中间啊

59
00:02:17,660 --> 00:02:18,740
它不是一个空格

60
00:02:18,740 --> 00:02:20,120
而是一个制表符

61
00:02:20,120 --> 00:02:20,800
好吧

62
00:02:20,800 --> 00:02:22,485
又是一个斜杠

63
00:02:22,485 --> 00:02:24,640
再来看这个 S 3

64
00:02:24,640 --> 00:02:27,320
S 3的话，他不是使用的双引号

65
00:02:27,320 --> 00:02:30,160
而是使用的这个反引号啊

66
00:02:30,160 --> 00:02:31,605
反引号引起来了

67
00:02:31,605 --> 00:02:33,850
那反应好，其实问题也比较多啊

68
00:02:33,850 --> 00:02:38,190
就当我们想原封不动的去生成一个字符串

69
00:02:38,190 --> 00:02:40,750
比如说我感觉我还得写这个什么

70
00:02:40,750 --> 00:02:43,030
写个 N 来表示换行太麻烦了，对吧

71
00:02:43,030 --> 00:02:45,590
我直接在这里面换行

72
00:02:45,590 --> 00:02:47,190
多好多直接对吧

73
00:02:47,190 --> 00:02:48,810
包括我想，你看啊

74
00:02:48,810 --> 00:02:50,670
其实这个地方是有一个什么设

75
00:02:50,670 --> 00:02:52,185
有一个制表符的

76
00:02:52,185 --> 00:02:54,550
我不想写这个斜杠 T ，对吧

77
00:02:54,550 --> 00:02:56,130
你就可以原生的啊

78
00:02:56,130 --> 00:02:57,230
该怎么写怎么写

79
00:02:57,230 --> 00:03:00,810
然后呢，把它整体放到一个反引号里面

80
00:03:00,810 --> 00:03:03,570
那最终把这个 IS 3打印出来

81
00:03:03,570 --> 00:03:05,450
就长成这个样子

82
00:03:05,450 --> 00:03:07,140
我们来看一下第一行呢

83
00:03:07,140 --> 00:03:09,030
是这边第一行啊

84
00:03:09,030 --> 00:03:11,742
它后面应该是有一个制表符的啊

85
00:03:11,742 --> 00:03:13,740
然后呢，这边有一个空行

86
00:03:13,740 --> 00:03:15,860
哎，他这是也原封不动

87
00:03:15,860 --> 00:03:17,425
有一个空行

88
00:03:17,425 --> 00:03:19,240
第三行是这个玩意儿

89
00:03:19,240 --> 00:03:21,020
那为什么这边这个第三行

90
00:03:21,020 --> 00:03:23,970
它这个左边有这么多空白呢

91
00:03:23,970 --> 00:03:28,680
啊，它为什么没有顶格写的这个呀

92
00:03:28,680 --> 00:03:30,810
主要是由于注意一下啊

93
00:03:30,810 --> 00:03:34,100
我们的这个反引号里面

94
00:03:34,100 --> 00:03:37,440
第三行它前面是不是还有一个制表符啊

95
00:03:37,440 --> 00:03:38,550
注意看是吧

96
00:03:38,550 --> 00:03:40,370
代码这边确实是有制表符

97
00:03:40,370 --> 00:03:41,290
结果导致呢

98
00:03:41,290 --> 00:03:43,990
你这边也会有制表符啊

99
00:03:43,990 --> 00:03:46,180
有这么多空白

100
00:03:46,180 --> 00:03:50,965
所以啊，千万不要被这个代码缩进所迷惑啊

101
00:03:50,965 --> 00:03:52,530
然后再注意一下

102
00:03:52,530 --> 00:03:56,490
那其实第三行后面它是有一个换行符的

103
00:03:56,490 --> 00:04:00,390
换行之后这边才来了一个番号啊

104
00:04:00,390 --> 00:04:02,420
才表示这个字符串结束了

105
00:04:02,420 --> 00:04:05,550
这个字符串最末尾是有一个换行符的

106
00:04:05,550 --> 00:04:07,950
所以你这边答案完之后

107
00:04:07,950 --> 00:04:10,275
这边有一个空行

108
00:04:10,275 --> 00:04:13,520
这是字符串的三种表示形式

109
00:04:13,520 --> 00:04:17,122
然后我们再来看一看字符串的一些本质吧

110
00:04:17,122 --> 00:04:18,100
看这边啊

111
00:04:18,100 --> 00:04:19,060
S 1是这样一个

112
00:04:19,060 --> 00:04:22,467
包含中文和英文的混合的一个字符串

113
00:04:22,467 --> 00:04:23,550
那字符串的话

114
00:04:23,550 --> 00:04:27,110
我们可以强行的把它转成 byte 切片啊

115
00:04:27,110 --> 00:04:29,960
这是那个强制的类型转换

116
00:04:29,960 --> 00:04:32,460
就是你想转成什么类型

117
00:04:32,460 --> 00:04:33,860
直接把类型呢放这

118
00:04:33,860 --> 00:04:37,467
然后一个小括号说这是你要转换的对象

119
00:04:37,467 --> 00:04:40,947
转完之后 AR 就是一个 by 的切片

120
00:04:40,947 --> 00:04:43,260
那除了可以转成 by 的切片之外

121
00:04:43,260 --> 00:04:46,600
也可以把字符串转成 rain 切片啊

122
00:04:46,600 --> 00:04:47,000
注意啊

123
00:04:47,000 --> 00:04:49,950
这个是构圆里面一种特殊的数据类型

124
00:04:49,950 --> 00:04:50,960
叫做 RU 

125
00:04:50,960 --> 00:04:52,890
怎么理解呢

126
00:04:52,890 --> 00:04:55,360
就对于这种汉字啊

127
00:04:55,360 --> 00:04:59,087
我们说他是用的 U 、 T 、 L 8字符集嘛

128
00:04:59,087 --> 00:05:01,040
英文的符号不管是字母也好

129
00:05:01,040 --> 00:05:01,840
被加符号也好

130
00:05:01,840 --> 00:05:04,350
它转成 U 、 T 、 I 、 F 字符集啊

131
00:05:04,350 --> 00:05:06,530
它里面的话用一个 beat 就可以表示

132
00:05:06,530 --> 00:05:10,030
但汉字的话呢，需要用三个 BT 才可以表示

133
00:05:10,030 --> 00:05:11,980
所以在 AR 里面

134
00:05:11,980 --> 00:05:15,710
每一个汉字它占的是三个元素

135
00:05:15,710 --> 00:05:16,750
三个字节

136
00:05:16,750 --> 00:05:18,840
但是在这个 B 2里面呢

137
00:05:18,840 --> 00:05:21,850
一个汉字它占的就是一个 re 啊

138
00:05:21,850 --> 00:05:23,025
就一个元素

139
00:05:23,025 --> 00:05:26,810
比如说这边我们把 AR 和 BR 的长度呢

140
00:05:26,810 --> 00:05:27,910
分别打出来

141
00:05:27,910 --> 00:05:30,670
你会发现 AR 更长一些

142
00:05:30,670 --> 00:05:32,490
而 BR 呢，更短一些

143
00:05:32,490 --> 00:05:33,565
就这边啊

144
00:05:33,565 --> 00:05:35,490
AR 是那个20

145
00:05:35,490 --> 00:05:37,597
BR 呢是14

146
00:05:37,597 --> 00:05:42,845
那我如果直接取得这个字符串本身的长度呢

147
00:05:42,845 --> 00:05:44,080
其实在这个地方我发现

148
00:05:44,080 --> 00:05:46,680
其实他把这个字符串当成一个切片来使用

149
00:05:46,680 --> 00:05:47,160
你看

150
00:05:47,160 --> 00:05:50,330
切片可以通过这个 line 来获得切叶片长度嘛

151
00:05:50,330 --> 00:05:52,860
这边他也把它当成一个切片来使用了

152
00:05:52,860 --> 00:05:54,462
那注意

153
00:05:54,462 --> 00:05:58,280
字符串的长度到底是 AR 的长度呢

154
00:05:58,280 --> 00:06:00,010
还是 BR 的长度呢

155
00:06:00,010 --> 00:06:01,390
他这个地方啊

156
00:06:01,390 --> 00:06:03,760
实际上是 AR 的长度啊

157
00:06:03,760 --> 00:06:04,040
也就是说

158
00:06:04,040 --> 00:06:06,900
他这个20跟这个20是保持一致的

159
00:06:06,900 --> 00:06:09,672
而不是这个润的长度啊

160
00:06:09,672 --> 00:06:11,650
来看第32行

161
00:06:11,650 --> 00:06:16,150
这边呢，是想输出 ARR 的最后一个元素

162
00:06:16,150 --> 00:06:21,410
我们说 A 2里面是很多个字节 bat ，对吧

163
00:06:21,410 --> 00:06:23,590
那么它的最后那三个字节

164
00:06:23,590 --> 00:06:26,550
来共同表示羊这样一个汉字

165
00:06:26,550 --> 00:06:29,430
而我只是取了最后一个 bat 

166
00:06:29,430 --> 00:06:32,427
最后一个 BT 呢是179

167
00:06:32,427 --> 00:06:33,820
那179

168
00:06:33,820 --> 00:06:37,240
如果我使用百分号 C 来进行输出呢

169
00:06:37,240 --> 00:06:39,842
百分号 C 表示 CHARA 字符

170
00:06:39,842 --> 00:06:41,710
那么179这个编号

171
00:06:41,710 --> 00:06:43,550
它表示的是哪一个字符呢

172
00:06:43,550 --> 00:06:44,290
肯定不是羊

173
00:06:44,290 --> 00:06:46,970
因为羊需要占三个字节吧

174
00:06:46,970 --> 00:06:49,930
179它表示字符啊

175
00:06:49,930 --> 00:06:52,010
是这样的一个符号啊

176
00:06:52,010 --> 00:06:53,940
一个小三这样符号

177
00:06:53,940 --> 00:06:56,540
这是 AR 字节切片

178
00:06:56,540 --> 00:06:58,860
再来看 BR 啊，润切片

179
00:06:58,860 --> 00:06:59,890
rain 切片的话

180
00:06:59,890 --> 00:07:03,930
那么这个 BR 里面它的最后一个元素就是阳

181
00:07:03,930 --> 00:07:05,460
这汉字本身

182
00:07:05,460 --> 00:07:08,467
这汉字如果你使用百分号 C 

183
00:07:08,467 --> 00:07:10,360
答案的话就是羊

184
00:07:10,360 --> 00:07:12,020
如果你非要使用百分号 

185
00:07:12,020 --> 00:07:13,420
用数字打印的话

186
00:07:13,420 --> 00:07:14,560
就是这么多啊

187
00:07:14,560 --> 00:07:17,520
它是由那个三个字节构成的啊

188
00:07:17,520 --> 00:07:19,560
比较大的一个整数

189
00:07:19,560 --> 00:07:24,540
再来看，我可以通过 for range 来便利这个字符串

190
00:07:24,540 --> 00:07:26,650
就如同便利切片一样

191
00:07:26,650 --> 00:07:28,720
而且这个下标我不需要

192
00:07:28,720 --> 00:07:31,120
所以呢，用下划线来代替

193
00:07:32,180 --> 00:07:35,220
那么我去遍历这个字符串

194
00:07:35,220 --> 00:07:38,820
我是遍历的 AR 呢还是 BR 啊

195
00:07:38,820 --> 00:07:40,020
实际上辨认的是 BR 

196
00:07:40,020 --> 00:07:43,260
也就是说你从这个字符串里面取出来的

197
00:07:43,260 --> 00:07:44,020
每一个元素

198
00:07:44,020 --> 00:07:46,190
它都是一个 R 啊

199
00:07:46,190 --> 00:07:51,300
这个汉字是一个最小的单元

200
00:07:51,300 --> 00:07:52,090
当然了

201
00:07:52,090 --> 00:07:53,310
每一个字符

202
00:07:53,310 --> 00:07:55,902
每一个字母也是一个单元是吧

203
00:07:55,902 --> 00:07:58,380
每一个字母，包括每一个空格都是单元

204
00:07:58,380 --> 00:08:01,237
包括每一个汉字也是一个独立的单元

205
00:08:01,237 --> 00:08:02,940
但如果说啊

206
00:08:02,940 --> 00:08:06,750
你要通过这种下标的形式的话

207
00:08:06,750 --> 00:08:08,645
S 本来是一个字符串

208
00:08:08,645 --> 00:08:12,490
通过下标去访问字符串里面的某一个元素

209
00:08:12,490 --> 00:08:13,370
这个时候呢

210
00:08:13,370 --> 00:08:16,670
它又把这个字符串当成是一个 bite 

211
00:08:16,670 --> 00:08:18,310
切片来使用了

212
00:08:18,310 --> 00:08:19,440
那这样的话

213
00:08:19,440 --> 00:08:21,440
汉字就会出现乱码

214
00:08:21,440 --> 00:08:25,445
因为他强行把一个汉字拆成了三个元素嘛

215
00:08:25,445 --> 00:08:28,920
所以这个时而把字符串当成是润切片

216
00:08:28,920 --> 00:08:31,220
时而把字符串当成是 bad 的切片

217
00:08:31,220 --> 00:08:32,820
确实很乱啊

218
00:08:32,820 --> 00:08:34,187
这个记不住

219
00:08:34,187 --> 00:08:36,210
但是有一点用的比较多

220
00:08:36,210 --> 00:08:39,669
就是我们在字符串后面加中括号啊

221
00:08:39,669 --> 00:08:42,370
根据下标来访问其中某个元素动画

222
00:08:42,370 --> 00:08:45,980
记住它实际上指的是 byte 切片

223
00:08:45,980 --> 00:08:48,640
好， AAR 是一个 BT 切片

224
00:08:48,640 --> 00:08:49,400
那既然切片嘛

225
00:08:49,400 --> 00:08:51,860
我就可以修改里面的任何一个元素

226
00:08:51,860 --> 00:08:54,450
但是呢，对字符串而言

227
00:08:54,450 --> 00:08:57,290
你不要企图去修改里面的某一元素

228
00:08:57,290 --> 00:08:59,590
字符串它是只读的啊

229
00:08:59,590 --> 00:09:00,680
不让修改

230
00:09:00,680 --> 00:09:02,360
我把这行代码打开

231
00:09:02,360 --> 00:09:05,270
你看打开之后会有红线报错啊

232
00:09:05,270 --> 00:09:07,327
字符串是不让修改的

233
00:09:07,327 --> 00:09:09,460
然后有一个现成的函数

234
00:09:09,460 --> 00:09:10,940
就是 UTF 8

235
00:09:10,940 --> 00:09:12,000
UTF 8的话

236
00:09:12,000 --> 00:09:15,747
是从贝隆库的这个包里面出来的

237
00:09:15,747 --> 00:09:17,670
这个包下面有一个函数

238
00:09:17,670 --> 00:09:19,410
rin count in rain 啊

239
00:09:19,410 --> 00:09:21,650
我可以计算一下这个字符串里面

240
00:09:21,650 --> 00:09:24,300
它包含几个 rain 啊

241
00:09:24,300 --> 00:09:27,182
三个汉字就是三个 run 

242
00:09:27,182 --> 00:09:28,670
这种统计方法

243
00:09:28,670 --> 00:09:32,510
实际上跟后面这种方法的结果是一样的

244
00:09:32,510 --> 00:09:35,940
就是我先把字符串转成润切片

245
00:09:35,940 --> 00:09:38,760
再来看一下这个切片的长度啊

246
00:09:38,760 --> 00:09:39,930
他俩应该是一样的

247
00:09:39,930 --> 00:09:42,340
结果呢都是14

248
00:09:43,340 --> 00:09:45,520
而如果是 bat 欺骗的话

249
00:09:45,520 --> 00:09:48,272
里面是有20个 bike 

250
00:09:48,272 --> 00:09:50,470
RIN 的话是14个 rain 

251
00:09:50,470 --> 00:09:52,070
一个字母是一个 rain 

252
00:09:52,070 --> 00:09:53,570
一个空格是一个 rain 

253
00:09:53,570 --> 00:09:55,725
一个汉字也是一个 RIN 

254
00:09:55,725 --> 00:09:58,030
再来看字符串的拼接

255
00:09:58,030 --> 00:10:00,370
这也是比较常用的一个功能

256
00:10:00,370 --> 00:10:02,970
现在我有若干个字符串啊

257
00:10:02,970 --> 00:10:04,037
四个字符串

258
00:10:04,037 --> 00:10:07,280
想把它们拼接成一个长的字符串

259
00:10:07,280 --> 00:10:10,870
可以直接通过加号来进行拼接

260
00:10:10,870 --> 00:10:15,680
每次拼接呢，中间我强行插入一个空格儿啊

261
00:10:15,680 --> 00:10:19,020
把这四个单词用三个空格呢给连起来

262
00:10:19,020 --> 00:10:20,660
就是 hello 

263
00:10:20,660 --> 00:10:21,480
how are you 啊

264
00:10:21,480 --> 00:10:22,592
这个形式

265
00:10:22,592 --> 00:10:24,130
这是第一种方法

266
00:10:24,130 --> 00:10:28,710
第二种方法呢，还可以通过 FMT 点 s print f 

267
00:10:28,710 --> 00:10:31,970
注意，之前我们用的都是 print 服务

268
00:10:31,970 --> 00:10:35,190
现在呢，在前面加了一个 s s print 符

269
00:10:35,190 --> 00:10:37,950
这样的话它就不是输出到终端

270
00:10:37,950 --> 00:10:41,530
而是去生成一个字符串

271
00:10:41,530 --> 00:10:43,950
里面你可以放四个百分号 S 

272
00:10:43,950 --> 00:10:46,900
那对应的后面就要放四个字符串

273
00:10:46,900 --> 00:10:48,670
然后这边的话

274
00:10:48,670 --> 00:10:52,317
四个百分 S 还用空格进行了分割

275
00:10:52,317 --> 00:10:56,120
实际上想达到跟刚才一样的效果嘛

276
00:10:56,120 --> 00:10:58,020
所以这个 merge 啊

277
00:10:58,020 --> 00:10:59,697
它是一个字符串

278
00:10:59,697 --> 00:11:02,275
长成这个样子

279
00:11:02,275 --> 00:11:02,970
Hello 

280
00:11:02,970 --> 00:11:03,850
how are you 

281
00:11:04,860 --> 00:11:06,460
再来看第三种方式

282
00:11:06,460 --> 00:11:08,040
就是通过 strength 

283
00:11:08,040 --> 00:11:10,225
这个 strength 啊，是一个包

284
00:11:10,225 --> 00:11:14,360
他也是从标准库下面自带的一个包啊

285
00:11:14,360 --> 00:11:16,347
string s 加个 S 啊

286
00:11:16,347 --> 00:11:19,430
这个包下面有一个函数叫做 joy 

287
00:11:19,430 --> 00:11:22,262
joy 嘛，有联合拼接，好吧

288
00:11:22,262 --> 00:11:25,400
那它传递的参数呢，是一个切片

289
00:11:25,400 --> 00:11:27,800
好，我得把这四个字符串呢

290
00:11:27,800 --> 00:11:31,295
把它构成一个字符串切片

291
00:11:31,295 --> 00:11:33,830
作为 join 的第一个参数

292
00:11:33,830 --> 00:11:36,980
第二个参数呢，需要指定你的分隔符啊

293
00:11:36,980 --> 00:11:41,330
我是按照空格来进行分割的啊

294
00:11:41,330 --> 00:11:45,227
所以这个 MERGLE 就长成这个样子

295
00:11:45,227 --> 00:11:46,780
再来看第四种方式

296
00:11:46,780 --> 00:11:49,100
实际上第四种方式是我们比较推崇的

297
00:11:49,100 --> 00:11:52,610
就是当你要拼接的字符串特别多的时候

298
00:11:52,610 --> 00:11:55,590
用第四种方式效率会更高一些

299
00:11:55,590 --> 00:11:56,130
当然了

300
00:11:56,130 --> 00:11:58,670
如果字符串啊，就只有三个、四个对吧

301
00:11:58,670 --> 00:11:59,890
十个以内

302
00:11:59,890 --> 00:12:02,180
这个用那种方式都可以

303
00:12:02,180 --> 00:12:03,780
没什么性差别

304
00:12:03,780 --> 00:12:04,390
当然了

305
00:12:04,390 --> 00:12:07,520
第四种方式会更麻烦一点啊

306
00:12:07,520 --> 00:12:10,240
我得先创建一个 strings 点 builder 

307
00:12:10,240 --> 00:12:10,720
好

308
00:12:10,720 --> 00:12:12,340
既然后面是一个大框

309
00:12:12,340 --> 00:12:15,060
那么我们可以推得出这个 builder 

310
00:12:15,060 --> 00:12:17,340
它应该就是一个结构体

311
00:12:17,340 --> 00:12:18,960
只不过这个结构体呢

312
00:12:18,960 --> 00:12:22,560
它是出自于 strings 这个包下面的一个结构体

313
00:12:22,560 --> 00:12:26,285
好，先构建这样一个空的结构体实例

314
00:12:26,285 --> 00:12:29,860
然后去调用这个结构体的一个成员方法

315
00:12:29,860 --> 00:12:31,547
就是 red string 

316
00:12:31,547 --> 00:12:32,910
我把谁加进来

317
00:12:32,910 --> 00:12:35,000
我先把 SC 给加进来

318
00:12:35,000 --> 00:12:37,460
然后我再把一个空格加进来

319
00:12:37,460 --> 00:12:39,500
然后我再把 i so 加进来，是吧

320
00:12:39,500 --> 00:12:40,860
就类似于 append 嘛啊

321
00:12:40,860 --> 00:12:42,957
不断的往里面去追加新内容

322
00:12:42,957 --> 00:12:44,220
最后的话

323
00:12:44,220 --> 00:12:49,150
通过调这个结构体的 string 这个方法啊

324
00:12:49,150 --> 00:12:50,607
最终呢

325
00:12:50,607 --> 00:12:53,740
他再返回一个字符串类型

326
00:12:53,740 --> 00:12:55,630
赋给这个 merge 

327
00:12:55,630 --> 00:12:57,840
好,这种方法虽然说麻烦

328
00:12:57,840 --> 00:13:00,620
但是呢,效率最高

329
00:13:00,620 --> 00:13:03,780
刚才我们已经见识了这个 strings 对吧

330
00:13:03,780 --> 00:13:06,180
它里面有一些函数啊

331
00:13:06,180 --> 00:13:07,310
有一些结构体

332
00:13:07,310 --> 00:13:09,220
其实这个 strings 包下面

333
00:13:09,220 --> 00:13:12,547
还有其他更多很方便的函数可以使用

334
00:13:12,547 --> 00:13:13,530
看这里啊

335
00:13:13,530 --> 00:13:16,430
搞了一个普通的字符串

336
00:13:16,430 --> 00:13:17,550
Born to win 

337
00:13:17,550 --> 00:13:18,950
Born to die 

338
00:13:18,950 --> 00:13:20,750
那么我要打印它的长度

339
00:13:20,750 --> 00:13:21,590
这个长度的话

340
00:13:21,590 --> 00:13:24,350
实际上是说它转成 bat 切片之后

341
00:13:24,350 --> 00:13:27,437
所对应的切片的长度

342
00:13:27,437 --> 00:13:30,500
那这个字符串长度自然是一啦

343
00:13:30,500 --> 00:13:32,712
里面只包含一个英文的

344
00:13:32,712 --> 00:13:35,990
如果这个字符串里面包含的是一个中文的话

345
00:13:35,990 --> 00:13:39,680
它长度应该是三占三个字节嘛

346
00:13:39,680 --> 00:13:42,130
好看这个 strings 点 split 

347
00:13:42,130 --> 00:13:43,830
split 表示分隔对吧

348
00:13:43,830 --> 00:13:45,470
S 是这样一个字符串

349
00:13:45,470 --> 00:13:48,490
我要把这个字符串分割成好几部分

350
00:13:48,490 --> 00:13:49,777
怎么分割呢

351
00:13:49,777 --> 00:13:53,570
诶，我这边指定了要按照空格进行分割

352
00:13:53,570 --> 00:13:56,020
这样的话，这里面像 BN 呢，是吧

353
00:13:56,020 --> 00:13:57,530
就是一个元素啊

354
00:13:57,530 --> 00:13:58,570
这个 to 是一个元素

355
00:13:58,570 --> 00:14:02,190
然后这个 when 和逗号它们两个合在一起

356
00:14:02,190 --> 00:14:02,950
是个元素

357
00:14:02,950 --> 00:14:05,570
这个带和这个句号合在一起

358
00:14:05,570 --> 00:14:06,770
它们是一个元素

359
00:14:06,770 --> 00:14:09,347
这是按照空格进行分隔

360
00:14:09,347 --> 00:14:11,520
然后有这个 strings 点 container 

361
00:14:11,520 --> 00:14:13,760
container 包含它返回一个布尔变量

362
00:14:13,760 --> 00:14:18,180
就是我判断一下这个 S 里面是否包含氮

363
00:14:18,180 --> 00:14:20,320
这样一个子串啊

364
00:14:20,320 --> 00:14:21,360
应该是包含对吧

365
00:14:21,360 --> 00:14:22,667
应该返回 true 吧

366
00:14:22,667 --> 00:14:24,090
返回布尔变量的话

367
00:14:24,090 --> 00:14:28,647
应该使用百分号 T 进行格式化输出

368
00:14:28,647 --> 00:14:30,900
这是 contains 包含

369
00:14:30,900 --> 00:14:32,440
还有一个是 index 

370
00:14:32,440 --> 00:14:35,165
就是返回那个位置

371
00:14:35,165 --> 00:14:39,390
我看看在 S 里面是否包含 B 

372
00:14:39,390 --> 00:14:40,860
如果包含的话

373
00:14:40,860 --> 00:14:44,795
返回这个 BN 它在 S 里面的位置

374
00:14:44,795 --> 00:14:47,820
那目前来看里面是包含两个 ball 啊

375
00:14:47,820 --> 00:14:48,700
两个 BN 的话

376
00:14:48,700 --> 00:14:51,060
它默认的是找到第一个 ball 

377
00:14:51,060 --> 00:14:52,360
那第一个 BN 

378
00:14:52,360 --> 00:14:55,680
它在整个 S 里面的位置是什么呢

379
00:14:55,680 --> 00:14:59,380
那实际上指的是这个 BB 的第一个字母

380
00:14:59,380 --> 00:15:00,340
就是这个 B 啊

381
00:15:00,340 --> 00:15:03,140
这个 B 它在 S 里面的位置就是零吧

382
00:15:03,140 --> 00:15:04,235
0号位置

383
00:15:04,235 --> 00:15:08,260
那如果我想找最后一个包呢

384
00:15:08,260 --> 00:15:12,120
就是使用的这个 last index 啊

385
00:15:12,120 --> 00:15:14,940
我从这个 S 里面找到 born 

386
00:15:14,940 --> 00:15:16,780
最后一次的出现位置

387
00:15:16,780 --> 00:15:18,720
返回那个位置的下标

388
00:15:18,720 --> 00:15:22,750
其实就是返回第二个 born 的这个 B 啊

389
00:15:22,750 --> 00:15:25,445
它在 S 里面的下标

390
00:15:25,445 --> 00:15:28,490
还有这个 strings 点 has prefix 

391
00:15:28,490 --> 00:15:34,147
判断一下 S 是否以 BD 来作为前缀

392
00:15:34,147 --> 00:15:36,410
strings 字典 has surface 

393
00:15:36,410 --> 00:15:40,680
判断一下 S 是否以这个带来作为后缀

394
00:15:40,680 --> 00:15:42,410
都是返回布尔变量

395
00:15:42,410 --> 00:15:45,890
所以呢，通过百分号 T 来进行承接
