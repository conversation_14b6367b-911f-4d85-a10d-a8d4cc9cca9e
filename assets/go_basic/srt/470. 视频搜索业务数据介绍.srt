1
00:00:00,379 --> 00:00:01,480
从这节课开始

2
00:00:01,480 --> 00:00:05,080
我们来正式开发一个具体的搜索业务

3
00:00:05,080 --> 00:00:08,400
把我们的这个 raid 搜索引擎给用起来

4
00:00:08,400 --> 00:00:11,420
我们就以一个简单的视频搜索为例

5
00:00:11,420 --> 00:00:14,520
先来看一下对应的代码目录结构

6
00:00:14,520 --> 00:00:16,900
这边呢，包括 hler handler 

7
00:00:16,900 --> 00:00:18,690
主要是后端接口

8
00:00:18,690 --> 00:00:22,410
就是你在前端页面上通过 JS 发起请求嘛

9
00:00:22,410 --> 00:00:25,230
那提交给某一个具体的后端接口

10
00:00:25,230 --> 00:00:28,320
那后端接口呢，来负责接收请求

11
00:00:28,320 --> 00:00:29,580
然后呢，返回结果

12
00:00:29,580 --> 00:00:33,120
但这里面啊，它并没有具体的搜索逻辑啊

13
00:00:33,120 --> 00:00:34,440
具体的搜索逻辑呢

14
00:00:34,440 --> 00:00:38,745
实际上是放到了这个 video search 这个目录下

15
00:00:38,745 --> 00:00:40,700
那么一个搜索

16
00:00:40,700 --> 00:00:43,500
它通常会要经历两个大的步骤吧

17
00:00:43,500 --> 00:00:46,060
第一个步骤我们称之为 record 召回

18
00:00:46,060 --> 00:00:47,947
就根据这些关键词啊

19
00:00:47,947 --> 00:00:49,610
作者呀这些条件

20
00:00:49,610 --> 00:00:51,350
哎，我去盗版索引上对吧

21
00:00:51,350 --> 00:00:53,470
去我们的 RADC 里面搜一把啊

22
00:00:53,470 --> 00:00:55,890
搜出一些结果后选集

23
00:00:55,890 --> 00:01:00,110
然后呢，右侧还需要经过一些更精细的过滤

24
00:01:00,110 --> 00:01:03,430
比方说按照视频播放量，对吧

25
00:01:03,430 --> 00:01:04,870
一个区间进行一些过滤

26
00:01:04,870 --> 00:01:06,970
好，然后呢，才是最终的这个结果

27
00:01:06,970 --> 00:01:10,360
那这个结果呢，就会返回给 hler handler 呢

28
00:01:10,360 --> 00:01:11,540
再返回给前端

29
00:01:11,540 --> 00:01:13,480
好，是这样一个关系啊

30
00:01:13,480 --> 00:01:15,430
那 mini 里面的主要就是我们的这个

31
00:01:15,430 --> 00:01:17,130
g i in 的这个 engine 

32
00:01:17,130 --> 00:01:17,410
对吧

33
00:01:17,410 --> 00:01:18,130
创建一个 engine 

34
00:01:18,130 --> 00:01:19,650
把这个 engine 呢给它跑起来

35
00:01:19,650 --> 00:01:23,550
是我们整个 observer 的程序的入口

36
00:01:23,550 --> 00:01:26,605
test 下面是一些单元测试

37
00:01:26,605 --> 00:01:27,440
views 

38
00:01:27,440 --> 00:01:31,010
views 主要是我们建开发一个 web server 嘛

39
00:01:31,010 --> 00:01:34,410
那么一些前端的 HTML 啊、 JSS 啊

40
00:01:34,410 --> 00:01:35,730
这个样式啊，对吧

41
00:01:35,730 --> 00:01:37,250
统统跟前端相关的吧

42
00:01:37,250 --> 00:01:39,182
全部在这个 views 下面

43
00:01:39,182 --> 00:01:41,920
好，这边还有一些公共的 go 代码

44
00:01:41,920 --> 00:01:44,960
我们重点关注一下这个 video 点。 PROTO 

45
00:01:44,960 --> 00:01:47,370
好，我们搜索这个实体

46
00:01:47,370 --> 00:01:48,350
它是个视频嘛

47
00:01:48,350 --> 00:01:50,930
那么视频里面包含哪些属性呢

48
00:01:50,930 --> 00:01:52,380
我们还来了解一下

49
00:01:52,380 --> 00:01:53,860
包括视频的 id 

50
00:01:53,860 --> 00:01:56,840
那我们说我们的这个 raid 搜索请对吧

51
00:01:56,840 --> 00:02:01,060
你 the document 文档总得有一个右侧 id 嘛

52
00:02:01,060 --> 00:02:03,792
那这边呢，我们是使用的字符串作为 id 

53
00:02:03,792 --> 00:02:05,750
视频的向标题啊

54
00:02:05,750 --> 00:02:07,035
发布时间

55
00:02:07,035 --> 00:02:09,190
发布时间它是一个时间戳嘛

56
00:02:09,190 --> 00:02:10,750
所以是用的 int 64啊

57
00:02:10,750 --> 00:02:11,870
包括作者

58
00:02:11,870 --> 00:02:17,110
还有视频的播放量、点赞量、投币量和这个收藏量

59
00:02:17,110 --> 00:02:18,810
以及分享的量啊

60
00:02:18,810 --> 00:02:21,550
包括说每个视频它会有一些标签

61
00:02:21,550 --> 00:02:22,430
关键词啊

62
00:02:22,430 --> 00:02:24,210
这是一个数组

63
00:02:24,210 --> 00:02:24,980
ok 

64
00:02:24,980 --> 00:02:28,670
那为什么我要把它定义在这样一个 PROTO 里面呢

65
00:02:28,670 --> 00:02:32,120
唉，是因为我们的 raid 搜索引擎

66
00:02:32,120 --> 00:02:35,100
它要求还记得那个 document 的定义吗

67
00:02:35,100 --> 00:02:36,910
它要求右侧

68
00:02:36,910 --> 00:02:40,440
你把你的这个具体得到后面的对吧

69
00:02:40,440 --> 00:02:43,060
这个实体你需要先把它序列化之后啊

70
00:02:43,060 --> 00:02:46,870
序列化之后再传给 RATIC 去进行存储

71
00:02:46,870 --> 00:02:50,490
那我如何把这个这样一个哔哩 video 

72
00:02:50,490 --> 00:02:52,470
这样一个结构体进行序列化呢

73
00:02:52,470 --> 00:02:54,630
哎，我采用的是这个 proto buffer 

74
00:02:54,630 --> 00:02:56,930
所以呢，这边就把它放在了，呃

75
00:02:56,930 --> 00:02:58,490
点 PROTO 文件里面啊

76
00:02:58,490 --> 00:03:01,357
这是生成的对应的那个 go 代码

77
00:03:01,357 --> 00:03:02,800
这里面还有一个 model 啊

78
00:03:02,800 --> 00:03:04,060
一般我们你说 model 的话

79
00:03:04,060 --> 00:03:06,240
都是一些基础的类的定义啊

80
00:03:06,240 --> 00:03:10,520
这里面是把那个搜索的 request 放在这里面了啊

81
00:03:10,520 --> 00:03:12,720
到时候我们那个前端浏览器啊

82
00:03:12,720 --> 00:03:15,950
它可能就要按照这样的一个参数吧

83
00:03:15,950 --> 00:03:18,360
啊，把它给传给后端

84
00:03:18,360 --> 00:03:20,560
这边还有一个 build index 

85
00:03:20,560 --> 00:03:24,220
这个 building index 主要是说我们最开始的时候啊

86
00:03:24,220 --> 00:03:26,220
我们最开始的时候你总得建立索引吧

87
00:03:26,220 --> 00:03:29,420
你的正牌刀排里面的数据是从哪来的

88
00:03:29,420 --> 00:03:30,542
那我们呢

89
00:03:30,542 --> 00:03:32,090
这个原始的数据文件

90
00:03:32,090 --> 00:03:35,110
实际上是一个 CSV 文件啊

91
00:03:35,110 --> 00:03:36,770
这个 CSV 文件实际上就是

92
00:03:36,770 --> 00:03:40,900
我们之前讲那个爬虫、反爬虫啊

93
00:03:40,900 --> 00:03:43,650
我们那个课程里面其实最核心啊

94
00:03:43,650 --> 00:03:46,610
最重要的一个任务就是去爬取是吧

95
00:03:46,610 --> 00:03:48,565
某个视频网站里面的视频

96
00:03:48,565 --> 00:03:50,070
而且爬取下来之后呢

97
00:03:50,070 --> 00:03:52,510
就是一个 CSV 文件

98
00:03:52,510 --> 00:03:56,010
我是直接把那个 CSV 文件拿过来啊

99
00:03:56,010 --> 00:04:00,460
用它来做我们这个 swim 的中级的数据源

100
00:04:00,460 --> 00:04:01,750
那所以这里面呢

101
00:04:01,750 --> 00:04:04,150
就写了一个函数啊

102
00:04:04,150 --> 00:04:04,830
写了一个函数

103
00:04:04,830 --> 00:04:07,850
就是它要读取那个原始的 CSC 文件

104
00:04:07,850 --> 00:04:11,020
然后呢，把它添加到我们的所有你

105
00:04:11,020 --> 00:04:11,740
所以你们去啊

106
00:04:11,740 --> 00:04:15,080
其实主要是去啊，构建正排索引

107
00:04:15,080 --> 00:04:18,540
就是放到我们的 badger 或者 boot 里面去

108
00:04:18,540 --> 00:04:19,387
好

109
00:04:19,387 --> 00:04:19,930
额

110
00:04:19,930 --> 00:04:23,602
所以这里面呢，我就直接使用的单机的模式啊

111
00:04:23,602 --> 00:04:26,460
大家可以看一下这一段代码

112
00:04:26,460 --> 00:04:28,720
好，传进了一个 CSV 文件

113
00:04:28,720 --> 00:04:31,145
我先把这个文件呢，进行一次打开

114
00:04:31,145 --> 00:04:32,860
好，打开之后的话啊

115
00:04:32,860 --> 00:04:33,540
打开之后的话

116
00:04:33,540 --> 00:04:34,860
这里面有一个 CSV 

117
00:04:34,860 --> 00:04:36,860
你看这是 go 标准库里面

118
00:04:36,860 --> 00:04:38,620
它自动的提供了一个

119
00:04:38,620 --> 00:04:41,050
解析 cs 文件的一个包吧

120
00:04:41,050 --> 00:04:43,470
我通过 CSV 啊， new reader 是吧

121
00:04:43,470 --> 00:04:45,830
把你刚刚打开的这个文件，哎

122
00:04:45,830 --> 00:04:47,570
传给这个 new reader 

123
00:04:47,570 --> 00:04:50,080
这样的话我就可以通过这个 reader 

124
00:04:50,080 --> 00:04:52,300
直接来读取这个 cs 的文件了啊

125
00:04:52,300 --> 00:04:53,580
其实就像逗号风格嘛

126
00:04:53,580 --> 00:04:54,420
按逗号风格

127
00:04:54,420 --> 00:04:55,105
好

128
00:04:55,105 --> 00:04:56,920
那么通过这个 read 函数啊

129
00:04:56,920 --> 00:04:59,640
它返回的这个 record 实际上是一个切片啊

130
00:04:59,640 --> 00:05:01,080
因为你一行嘛

131
00:05:01,080 --> 00:05:02,780
一行分割之后是有多列

132
00:05:02,780 --> 00:05:04,020
所以呢，它是一个切片

133
00:05:04,020 --> 00:05:05,780
好，我只需要从这个 record 里面

134
00:05:05,780 --> 00:05:06,710
比方说，哎

135
00:05:06,710 --> 00:05:09,150
读取什么 record 0123对吧

136
00:05:09,150 --> 00:05:11,470
那每一列对应的是什么意思啊

137
00:05:11,470 --> 00:05:13,017
把它给读出来

138
00:05:13,017 --> 00:05:16,000
大概就是包含了一些什么播放量啊

139
00:05:16,000 --> 00:05:18,640
把这个发布时间啊、作者呀、标题

140
00:05:18,640 --> 00:05:19,440
这样的一些字段嘛

141
00:05:19,440 --> 00:05:20,275
对吧

142
00:05:20,275 --> 00:05:22,740
呃，这里面需要稍微注意一点

143
00:05:22,740 --> 00:05:25,130
就是它的这个你看的首列对吧

144
00:05:25,130 --> 00:05:28,520
首列记录的是那个视频的 URL 地址

145
00:05:28,520 --> 00:05:31,282
那么我要从这个首列它最后面啊

146
00:05:31,282 --> 00:05:33,310
他这个 video 后面还有一集

147
00:05:33,310 --> 00:05:35,410
那一集就是那个视频的 id 

148
00:05:35,410 --> 00:05:37,980
好，把这个 id 呢给抠出来

149
00:05:37,980 --> 00:05:41,820
OK ，额，这里面还包括一个时间的解析吧

150
00:05:41,820 --> 00:05:43,740
所以这里面是用的这个 time 点 pass 

151
00:05:43,740 --> 00:05:46,530
那我们说一般都建议不要使用 time in 的 pass 

152
00:05:46,530 --> 00:05:51,740
而使用 pass in location 就是你要显示的指定这个时区

153
00:05:51,740 --> 00:05:53,240
否则你可能会发现

154
00:05:53,240 --> 00:05:55,780
那个时间跟真实时间差了八个小时

155
00:05:55,780 --> 00:05:57,590
就是因为我们是中华区嘛

156
00:05:57,590 --> 00:05:58,700
好

157
00:05:58,700 --> 00:06:00,440
把时间给解析过来

158
00:06:00,440 --> 00:06:03,140
然后最终是要去构造这样一个

159
00:06:03,140 --> 00:06:04,640
video 的结构体啊

160
00:06:04,640 --> 00:06:07,800
去构造这样一个结构体啊

161
00:06:07,800 --> 00:06:09,660
把这个结构体构造好之后的话

162
00:06:09,660 --> 00:06:13,680
你就可以去扔给我们的那个 index or 啊

163
00:06:13,680 --> 00:06:15,680
它里面包含了正派和倒排吧

164
00:06:15,680 --> 00:06:17,125
扔给 index 

165
00:06:17,125 --> 00:06:17,780
好

166
00:06:17,780 --> 00:06:18,900
扔个 index 的话

167
00:06:18,900 --> 00:06:20,340
我们说 index 里面核心

168
00:06:20,340 --> 00:06:23,100
你是要去构造这样一个 document 对吧

169
00:06:23,100 --> 00:06:24,020
构造 document 啊

170
00:06:24,020 --> 00:06:26,075
我们看这个 doc 里面

171
00:06:26,075 --> 00:06:28,280
第八十四行是给 id 赋值了

172
00:06:28,280 --> 00:06:30,400
然后你要给什么什么 BISS 复制啊

173
00:06:30,400 --> 00:06:31,640
这个 BS 是怎么来的

174
00:06:31,640 --> 00:06:34,755
这个 BAS 是我们通过，你看80哈

175
00:06:34,755 --> 00:06:39,680
我们是通过这个 PROTOBUFFER 这种方式进行的序列化

176
00:06:39,680 --> 00:06:43,300
因为 PROTOBUFFER 的序列化方式还是蛮高效

177
00:06:43,300 --> 00:06:45,180
还是挺快的啊

178
00:06:45,180 --> 00:06:48,720
好，这是这个 BS 这个字段

179
00:06:48,720 --> 00:06:50,280
我们再来看还有其他什么字段

180
00:06:50,280 --> 00:06:52,040
还包括我的 key words 啊

181
00:06:52,040 --> 00:06:52,760
key words 是吧

182
00:06:52,760 --> 00:06:55,340
你这个 CSV 文件里面就存储了

183
00:06:55,340 --> 00:06:56,982
原始的关键词嘛

184
00:06:56,982 --> 00:06:58,490
好，这边啊

185
00:06:58,490 --> 00:07:01,250
这边这个 beats feature 就比较难搞了啊

186
00:07:01,250 --> 00:07:04,070
确实这个需要根据你的业务具体结合起来

187
00:07:04,070 --> 00:07:05,362
我们看一下啊

188
00:07:05,362 --> 00:07:06,740
我是如何去

189
00:07:06,740 --> 00:07:09,620
呃，生成这个 bs features 的

190
00:07:09,620 --> 00:07:10,240
beat feature 

191
00:07:10,240 --> 00:07:13,500
看一下，我点到这个 get class bs feature 

192
00:07:13,500 --> 00:07:15,582
点到这个函数里面去

193
00:07:15,582 --> 00:07:16,760
好

194
00:07:16,760 --> 00:07:18,630
额，这个 beat feature 里面的

195
00:07:18,630 --> 00:07:22,690
我核心是想对这个视频的类别进行一个编码

196
00:07:22,690 --> 00:07:25,430
那我们发现我们从 CSV 文件里面统计了

197
00:07:25,430 --> 00:07:28,870
我们发现这个其实每一个视频

198
00:07:28,870 --> 00:07:31,870
他的那个关键词的最后两个是

199
00:07:31,870 --> 00:07:34,412
最后两个实际上就是它的类别

200
00:07:34,412 --> 00:07:37,560
比方资讯啊、什么编程啊、科技啊、类眼嘛

201
00:07:37,560 --> 00:07:39,980
那我们统计了一下所有的类别

202
00:07:39,980 --> 00:07:42,000
其实就是这些啊

203
00:07:42,000 --> 00:07:45,225
就这些包括什么资讯

204
00:07:45,225 --> 00:07:49,930
社会热点、生活等等是吧

205
00:07:49,930 --> 00:07:51,410
那每一个类别呢

206
00:07:51,410 --> 00:07:54,430
我都把它放到了这样一个枚举里面是吧

207
00:07:54,430 --> 00:07:57,110
那咨询这个类别它就是一嘛

208
00:07:57,110 --> 00:08:00,050
因为这个 IOTA 第一次出现是零

209
00:08:00,050 --> 00:08:03,437
那么一往左移零位还是一嘛

210
00:08:03,437 --> 00:08:05,460
那下面这个就是一

211
00:08:05,460 --> 00:08:06,840
左移一位变成二了

212
00:08:06,840 --> 00:08:07,940
然后下面这个是吧

213
00:08:07,940 --> 00:08:09,740
再左移变成四了嘛，对吧

214
00:08:09,740 --> 00:08:10,800
反正一是左移嘛

215
00:08:10,800 --> 00:08:12,700
就说它，额

216
00:08:12,700 --> 00:08:14,400
只有一位是一啊

217
00:08:14,400 --> 00:08:15,860
只不过这位呢，一直在变

218
00:08:15,860 --> 00:08:16,740
好

219
00:08:16,740 --> 00:08:17,740
这样的话呢

220
00:08:17,740 --> 00:08:19,480
那你来了一个 key words 

221
00:08:19,480 --> 00:08:21,600
我先从 key words 里面我先取出

222
00:08:21,600 --> 00:08:24,370
我看看啊，它里面是否包含资讯

223
00:08:24,370 --> 00:08:25,930
那如果包含资讯的话

224
00:08:25,930 --> 00:08:30,460
我就把对应的资讯这位置为一吗

225
00:08:30,460 --> 00:08:32,220
好，怎么把它置位置为一

226
00:08:32,220 --> 00:08:36,408
就是让你跟这个资讯进行一次货运转嘛

227
00:08:36,408 --> 00:08:39,380
本来这个 BS 里面全是零是吧

228
00:08:39,380 --> 00:08:40,809
你只要命中资讯了

229
00:08:40,809 --> 00:08:42,970
那么我就把对应的位置给你定值为一

230
00:08:42,970 --> 00:08:45,190
你只要命中这个环球了

231
00:08:45,190 --> 00:08:48,140
好，我就把对应的环球那位给你置为一

232
00:08:48,140 --> 00:08:51,310
因为一个视频他可能同时会属于多个类别

233
00:08:51,310 --> 00:08:52,450
好，这样的话呢

234
00:08:52,450 --> 00:08:54,330
我就把视频的所有类别

235
00:08:54,330 --> 00:08:56,440
全部编入到了这样一个 BS 里面去

236
00:08:56,440 --> 00:08:57,050
对吧

237
00:08:57,050 --> 00:08:59,570
这个地方演示的只是一个啊

238
00:08:59,570 --> 00:09:00,890
类别这样一个字段

239
00:09:00,890 --> 00:09:02,970
那实际当中除了类别之外

240
00:09:02,970 --> 00:09:04,930
你可能还想把其他的某个字段

241
00:09:04,930 --> 00:09:06,422
比方说

242
00:09:06,422 --> 00:09:11,180
呃，再举就说这个视频是否是付费视频

243
00:09:11,180 --> 00:09:14,040
就是我们是否需要特殊的进行推广，对吧

244
00:09:14,040 --> 00:09:15,120
这也是一个属性

245
00:09:15,120 --> 00:09:18,300
再比方说这个视频它是否是啊

246
00:09:18,300 --> 00:09:20,780
24小时内刚发布的新视频是吧

247
00:09:20,780 --> 00:09:21,320
等等

248
00:09:21,320 --> 00:09:24,300
你都可以编编码到这样一个 BS 里面去

249
00:09:24,300 --> 00:09:27,030
因为我们发现这个类别是吧

250
00:09:27,030 --> 00:09:30,630
他并没有把64位全部用完啊

251
00:09:30,630 --> 00:09:31,602
还很多

252
00:09:31,602 --> 00:09:34,620
你完全可以再把其他属性编码进来

253
00:09:34,620 --> 00:09:37,680
OK ，这样的话，生成这样一个 document 

254
00:09:37,680 --> 00:09:39,840
然后直接调我们的 index or 啊

255
00:09:39,840 --> 00:09:43,200
把 document 添加到正牌和倒排索引里面去
