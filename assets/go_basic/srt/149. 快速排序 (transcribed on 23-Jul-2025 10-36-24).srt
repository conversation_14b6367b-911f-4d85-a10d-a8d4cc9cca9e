1
00:00:00,000 --> 00:00:03,080
这几个我们来看一个非常基础

2
00:00:03,080 --> 00:00:04,960
出现频率非常高的算法

3
00:00:04,960 --> 00:00:05,920
快速排序

4
00:00:05,920 --> 00:00:08,400
先看我们左上方的这个数组

5
00:00:08,400 --> 00:00:10,200
6729这个数组

6
00:00:10,200 --> 00:00:13,260
下面是它这个下标index从0到8

7
00:00:13,260 --> 00:00:14,060
好

8
00:00:14,060 --> 00:00:17,120
本来呢这是一个完全无序的数组

9
00:00:17,120 --> 00:00:20,720
现在我要对它从小到大的排好序

10
00:00:20,720 --> 00:00:24,720
那快速排序的一个基本思路是什么呢

11
00:00:24,720 --> 00:00:28,320
比方说一个班级里面有30个学生

12
00:00:28,320 --> 00:00:31,940
那么呢我要把它们按照个头身高

13
00:00:31,940 --> 00:00:33,280
从低到高排好序

14
00:00:33,280 --> 00:00:34,500
怎么做呢

15
00:00:34,500 --> 00:00:36,720
我先随机的选择一个学生

16
00:00:36,720 --> 00:00:38,800
好比方说是张三

17
00:00:38,800 --> 00:00:42,080
剩下的29个人都去跟张三比一下身高

18
00:00:42,080 --> 00:00:46,040
凡是比张三矮的就到左边

19
00:00:46,040 --> 00:00:48,540
凡是比张三高的就到右边

20
00:00:48,540 --> 00:00:51,120
这样的话整个班级身高

21
00:00:51,120 --> 00:00:52,680
就从一个完全无序的状态

22
00:00:52,680 --> 00:00:54,680
变成了一个稍微有序

23
00:00:54,680 --> 00:00:55,080
对吧

24
00:00:55,080 --> 00:00:56,920
至少以张三为分解线

25
00:00:56,920 --> 00:00:58,600
左边的都比它低

26
00:00:58,600 --> 00:01:00,100
右边的都比它高

27
00:01:00,100 --> 00:01:00,980
好

28
00:01:00,980 --> 00:01:02,860
然后对于左边

29
00:01:02,860 --> 00:01:04,320
假如说左边10个人

30
00:01:04,320 --> 00:01:05,800
那左边这10个人

31
00:01:05,800 --> 00:01:08,000
我按照跟刚才一样的方法

32
00:01:08,000 --> 00:01:11,020
在随机的抽取一个人比方上李四

33
00:01:11,020 --> 00:01:15,080
那么剩下9个人都跟李四去进行身高比较

34
00:01:15,080 --> 00:01:17,640
比他矮的都站在他左边

35
00:01:17,640 --> 00:01:19,980
比他高的都站在他右边

36
00:01:19,980 --> 00:01:21,480
把意思类推

37
00:01:21,480 --> 00:01:22,820
这样的话呢

38
00:01:22,820 --> 00:01:27,540
我就通过地规的方式不断调用刚才这个操作

39
00:01:27,540 --> 00:01:31,360
最终如果说集合剩下两个元素了

40
00:01:31,360 --> 00:01:34,660
那么我随便从这两个里面选择一个

41
00:01:34,660 --> 00:01:37,660
剩下一个如果比他矮放到他左边

42
00:01:37,660 --> 00:01:39,100
如果比他高放到他右边

43
00:01:39,100 --> 00:01:42,660
这样的话就可以退出地规了

44
00:01:42,660 --> 00:01:43,560
好

45
00:01:43,560 --> 00:01:47,180
那我们具体到这个数组里面再来看看

46
00:01:47,180 --> 00:01:49,500
对于这样一个数组呢

47
00:01:49,500 --> 00:01:54,100
我一般都是取第一个作为基准

48
00:01:54,100 --> 00:01:54,820
好

49
00:01:54,820 --> 00:01:56,080
6作为基准

50
00:01:56,080 --> 00:01:57,160
我们称之为pivot

51
00:01:57,160 --> 00:01:58,380
然后呢

52
00:01:58,380 --> 00:01:59,440
搞两个指针

53
00:01:59,440 --> 00:02:00,940
或者说两个边量吧

54
00:02:00,940 --> 00:02:02,200
一个i一个j

55
00:02:02,200 --> 00:02:03,100
i呢

56
00:02:03,100 --> 00:02:04,040
从1开始

57
00:02:04,040 --> 00:02:06,920
就是i是从这个index等于1开始

58
00:02:06,920 --> 00:02:08,000
而j呢

59
00:02:08,000 --> 00:02:10,140
是从最大的那个index开始

60
00:02:10,140 --> 00:02:12,300
i逐步地往后走

61
00:02:12,300 --> 00:02:13,020
加加

62
00:02:13,020 --> 00:02:13,900
j呢

63
00:02:13,900 --> 00:02:14,820
逐步地往前走

64
00:02:14,820 --> 00:02:15,300
减减

65
00:02:15,300 --> 00:02:16,400
最开始的时候

66
00:02:16,400 --> 00:02:17,400
j在最后面

67
00:02:17,400 --> 00:02:19,740
那么我们首先来观察这个j

68
00:02:19,740 --> 00:02:23,020
当j指向的这个数组元素

69
00:02:23,020 --> 00:02:25,100
它比我们的这个pivot

70
00:02:25,100 --> 00:02:26,620
如果要小的话

71
00:02:26,620 --> 00:02:27,700
那么j呢

72
00:02:27,700 --> 00:02:28,640
就停下来

73
00:02:28,640 --> 00:02:29,420
不要动了

74
00:02:29,420 --> 00:02:30,820
而如果说

75
00:02:30,820 --> 00:02:33,300
它比这个pivot要大的话呢

76
00:02:33,300 --> 00:02:34,500
那j就要往前走

77
00:02:34,500 --> 00:02:35,400
j就要减减

78
00:02:35,400 --> 00:02:36,580
好

79
00:02:36,580 --> 00:02:38,580
我们首先看了啊

80
00:02:38,580 --> 00:02:39,220
这个j呢

81
00:02:39,220 --> 00:02:39,860
一上来

82
00:02:39,860 --> 00:02:40,740
这个4对吧

83
00:02:40,740 --> 00:02:41,740
它就比6小

84
00:02:41,740 --> 00:02:42,200
所以呢

85
00:02:42,200 --> 00:02:44,100
j就停下来了

86
00:02:44,100 --> 00:02:46,200
就不动了

87
00:02:46,200 --> 00:02:46,800
i呢

88
00:02:46,800 --> 00:02:48,540
它指向的这个元素

89
00:02:48,540 --> 00:02:49,380
如果说

90
00:02:49,380 --> 00:02:50,740
它比这个pivot

91
00:02:50,740 --> 00:02:52,620
要小的话

92
00:02:52,620 --> 00:02:53,160
那么

93
00:02:53,160 --> 00:02:54,340
它就往前走

94
00:02:54,340 --> 00:02:56,020
如果说比pivot大

95
00:02:56,020 --> 00:02:56,680
那么呢

96
00:02:56,680 --> 00:02:58,280
它就要停下来

97
00:02:58,280 --> 00:02:58,560
好

98
00:02:58,560 --> 00:02:59,660
i

99
00:02:59,660 --> 00:03:00,920
首先一上来

100
00:03:00,920 --> 00:03:01,900
指的是7

101
00:03:01,900 --> 00:03:03,500
那么7比pivot要大

102
00:03:03,500 --> 00:03:03,960
所以呢

103
00:03:03,960 --> 00:03:05,340
i要停下来

104
00:03:05,340 --> 00:03:05,740
这样的话

105
00:03:05,740 --> 00:03:07,160
i跟j都停下来了

106
00:03:07,160 --> 00:03:08,160
这个时候

107
00:03:08,160 --> 00:03:09,240
我们交换

108
00:03:09,240 --> 00:03:09,740
i

109
00:03:09,740 --> 00:03:09,900
g

110
00:03:09,900 --> 00:03:11,480
所指向的

111
00:03:11,480 --> 00:03:12,540
这两个元素

112
00:03:12,540 --> 00:03:13,520
那么呢

113
00:03:13,520 --> 00:03:14,280
4跟7

114
00:03:14,280 --> 00:03:15,040
互换一下

115
00:03:15,040 --> 00:03:15,900
对吧

116
00:03:15,900 --> 00:03:16,720
4到前面

117
00:03:16,720 --> 00:03:17,640
7到后面

118
00:03:17,640 --> 00:03:17,980
好

119
00:03:17,980 --> 00:03:18,580
就互换了

120
00:03:18,580 --> 00:03:20,420
互换之后

121
00:03:20,420 --> 00:03:21,660
i跟j

122
00:03:21,660 --> 00:03:22,740
继续移动

123
00:03:22,740 --> 00:03:24,160
继续向着

124
00:03:24,160 --> 00:03:25,000
对方移动

125
00:03:25,000 --> 00:03:26,060
不过呢

126
00:03:26,060 --> 00:03:26,900
谁先动

127
00:03:26,900 --> 00:03:27,540
谁后动

128
00:03:27,540 --> 00:03:28,180
这里面注意

129
00:03:28,180 --> 00:03:29,560
一定要是j先动

130
00:03:29,560 --> 00:03:30,560
随后我们再来说

131
00:03:30,560 --> 00:03:31,360
为什么是j先动

132
00:03:31,360 --> 00:03:31,940
好

133
00:03:31,940 --> 00:03:32,960
j指向5

134
00:03:32,960 --> 00:03:33,620
这个时候

135
00:03:33,620 --> 00:03:34,380
5比6小

136
00:03:34,380 --> 00:03:35,480
所以j停下来

137
00:03:35,480 --> 00:03:37,200
那么i呢

138
00:03:37,200 --> 00:03:38,480
它需要找到一个

139
00:03:38,480 --> 00:03:41,740
比pivot大的元素

140
00:03:41,740 --> 00:03:43,920
才需要停下来

141
00:03:43,920 --> 00:03:44,940
i指向9

142
00:03:44,940 --> 00:03:46,560
这个时候停下来

143
00:03:46,560 --> 00:03:46,780
好

144
00:03:46,780 --> 00:03:47,460
这个时候呢

145
00:03:47,460 --> 00:03:48,100
i跟j

146
00:03:48,100 --> 00:03:49,240
所指向的元素

147
00:03:49,240 --> 00:03:50,380
9和5

148
00:03:50,380 --> 00:03:51,400
它们俩互换

149
00:03:51,400 --> 00:03:52,100
OK

150
00:03:52,100 --> 00:03:53,320
5到前面

151
00:03:53,320 --> 00:03:54,680
9到后面

152
00:03:54,680 --> 00:03:55,920
好

153
00:03:55,920 --> 00:03:56,620
接着呢

154
00:03:56,620 --> 00:03:57,580
继续往前走

155
00:03:57,580 --> 00:03:59,000
走走走

156
00:03:59,000 --> 00:03:59,900
找到一个

157
00:03:59,900 --> 00:04:00,680
1

158
00:04:00,680 --> 00:04:01,040
对吧

159
00:04:01,040 --> 00:04:01,740
1比6小

160
00:04:01,740 --> 00:04:03,300
所以j停下来

161
00:04:03,300 --> 00:04:03,920
然后呢

162
00:04:03,920 --> 00:04:05,080
i往后走

163
00:04:05,080 --> 00:04:06,160
i要找一个

164
00:04:06,160 --> 00:04:06,780
比6大的

165
00:04:06,780 --> 00:04:07,900
但是呢

166
00:04:07,900 --> 00:04:09,460
它还没有到

167
00:04:09,460 --> 00:04:10,260
结果呢

168
00:04:10,260 --> 00:04:10,820
i跟j

169
00:04:10,820 --> 00:04:12,180
重合了

170
00:04:12,180 --> 00:04:13,300
因为j已经

171
00:04:13,300 --> 00:04:14,540
在1这儿

172
00:04:14,540 --> 00:04:15,180
等着i嘛

173
00:04:15,180 --> 00:04:16,320
i往后走

174
00:04:16,320 --> 00:04:17,260
而且i跟j

175
00:04:17,260 --> 00:04:18,460
这个重合了

176
00:04:18,460 --> 00:04:19,760
重合的话

177
00:04:19,760 --> 00:04:21,019
就直接退出

178
00:04:21,019 --> 00:04:21,960
就不要再

179
00:04:21,960 --> 00:04:22,680
再动了

180
00:04:22,680 --> 00:04:24,200
最终呢

181
00:04:24,200 --> 00:04:24,780
i跟j

182
00:04:24,780 --> 00:04:26,060
都指向了1

183
00:04:26,060 --> 00:04:27,180
那么

184
00:04:27,180 --> 00:04:28,340
每一轮循环

185
00:04:28,340 --> 00:04:29,580
都是j先动

186
00:04:29,580 --> 00:04:30,380
i后动

187
00:04:30,380 --> 00:04:31,100
好

188
00:04:31,100 --> 00:04:32,660
如果说

189
00:04:32,660 --> 00:04:33,980
假如说我们这一次

190
00:04:33,980 --> 00:04:35,360
假如说现在i

191
00:04:35,360 --> 00:04:36,060
指向5

192
00:04:36,060 --> 00:04:36,760
d指向9

193
00:04:36,760 --> 00:04:38,300
假如说是i先动

194
00:04:38,300 --> 00:04:39,860
看一下会产生什么后果

195
00:04:39,860 --> 00:04:41,600
如果i先动的话

196
00:04:41,600 --> 00:04:45,020
i要找一个比pivot大的元素

197
00:04:45,020 --> 00:04:45,920
所以i

198
00:04:45,920 --> 00:04:47,220
它一直往后走

199
00:04:47,220 --> 00:04:48,680
会走到9这

200
00:04:48,680 --> 00:04:49,160
对吧

201
00:04:49,160 --> 00:04:50,240
i会走到9这

202
00:04:50,240 --> 00:04:51,320
而j呢

203
00:04:51,320 --> 00:04:52,520
在9这等了他

204
00:04:52,520 --> 00:04:53,540
所以最后

205
00:04:53,540 --> 00:04:54,280
i跟j呢

206
00:04:54,280 --> 00:04:56,280
在9这相遇了

207
00:04:56,280 --> 00:04:57,480
好

208
00:04:57,480 --> 00:04:58,840
如果i先动

209
00:04:58,840 --> 00:05:00,040
是在9这相遇

210
00:05:00,040 --> 00:05:01,120
如果即行动呢

211
00:05:01,120 --> 00:05:02,260
是在1这相遇

212
00:05:02,260 --> 00:05:03,300
那

213
00:05:03,300 --> 00:05:04,600
当他们相遇之后

214
00:05:04,600 --> 00:05:06,100
要注意到什么事情呢

215
00:05:06,100 --> 00:05:06,960
相遇之后

216
00:05:06,960 --> 00:05:08,020
我们要看看

217
00:05:08,020 --> 00:05:09,700
他们相遇的这个位置

218
00:05:09,700 --> 00:05:10,480
就是这个1

219
00:05:10,480 --> 00:05:12,400
它跟6比较一下

220
00:05:12,400 --> 00:05:13,200
如果说

221
00:05:13,200 --> 00:05:13,960
它比6

222
00:05:13,960 --> 00:05:14,880
小的话

223
00:05:14,880 --> 00:05:16,160
那么它需要跟6

224
00:05:16,160 --> 00:05:17,300
换位置

225
00:05:17,300 --> 00:05:18,100
好

226
00:05:18,100 --> 00:05:18,780
把6

227
00:05:18,780 --> 00:05:20,080
换到1这边来

228
00:05:20,080 --> 00:05:20,620
把1

229
00:05:20,620 --> 00:05:21,660
换到6这边去

230
00:05:21,660 --> 00:05:23,000
最后我们看一下

231
00:05:23,000 --> 00:05:24,560
是不是这个6

232
00:05:24,560 --> 00:05:25,420
你看看

233
00:05:25,420 --> 00:05:26,500
6左边的

234
00:05:26,500 --> 00:05:27,600
是不是都比6小

235
00:05:27,600 --> 00:05:30,160
6右边的都比6大

236
00:05:30,160 --> 00:05:33,380
而如果说我们是i先动的话

237
00:05:33,380 --> 00:05:34,220
那么呢

238
00:05:34,220 --> 00:05:36,600
6跟9不能互换位置

239
00:05:36,600 --> 00:05:38,520
6跟9如果不能互换位置的话

240
00:05:38,520 --> 00:05:38,940
你看

241
00:05:38,940 --> 00:05:39,820
它最终

242
00:05:39,820 --> 00:05:41,540
就是这样一个形态

243
00:05:41,540 --> 00:05:43,160
对吧

244
00:05:43,160 --> 00:05:44,440
就没法实现

245
00:05:44,440 --> 00:05:45,160
我们刚才那个

246
00:05:45,160 --> 00:05:46,680
以6为分界线

247
00:05:46,680 --> 00:05:48,160
分成左右两部分

248
00:05:48,160 --> 00:05:48,960
跟目标

249
00:05:48,960 --> 00:05:51,580
那有没有可能

250
00:05:51,580 --> 00:05:52,460
有没有可能

251
00:05:52,460 --> 00:05:52,960
最后

252
00:05:52,960 --> 00:05:54,599
i跟 g相对的时候

253
00:05:54,599 --> 00:05:56,099
他们这个相遇的位置

254
00:05:56,099 --> 00:05:56,599
这个元素

255
00:05:56,599 --> 00:05:58,400
比6大呢

256
00:05:58,400 --> 00:06:00,060
那极端情况下

257
00:06:00,060 --> 00:06:01,500
肯定是有这种可能

258
00:06:01,500 --> 00:06:02,280
也存在的

259
00:06:02,280 --> 00:06:02,960
比方说

260
00:06:02,960 --> 00:06:04,060
一种极端情况

261
00:06:04,060 --> 00:06:05,960
我们不是随机选择

262
00:06:05,960 --> 00:06:07,140
以第0个元素

263
00:06:07,140 --> 00:06:08,380
作为pivot吗

264
00:06:08,380 --> 00:06:09,340
那假如说

265
00:06:09,340 --> 00:06:10,700
后面的所有元素

266
00:06:10,700 --> 00:06:12,679
都比这个pivot要大

267
00:06:12,679 --> 00:06:14,919
那i跟 g相对的地方

268
00:06:14,919 --> 00:06:15,500
那个元素

269
00:06:15,500 --> 00:06:16,760
那肯定比pivot要大

270
00:06:16,760 --> 00:06:17,780
那这个时候的话

271
00:06:17,780 --> 00:06:19,400
就是最后一步

272
00:06:19,400 --> 00:06:20,640
不需要去交换

273
00:06:20,640 --> 00:06:22,760
这个pivot和某一个元素

274
00:06:22,760 --> 00:06:25,300
因为pivot的右面的

275
00:06:25,300 --> 00:06:26,740
本来已经都是

276
00:06:26,740 --> 00:06:27,960
全部比它大的

277
00:06:27,960 --> 00:06:28,700
好

278
00:06:28,700 --> 00:06:30,000
经过刚才的

279
00:06:30,000 --> 00:06:31,880
这一通操作下来之后

280
00:06:31,880 --> 00:06:33,880
我们说6放在了中间

281
00:06:33,880 --> 00:06:35,860
那么左边的都比它小

282
00:06:35,860 --> 00:06:37,480
右边的都比它大

283
00:06:37,480 --> 00:06:39,760
然后叫递归了

284
00:06:39,760 --> 00:06:40,000
好

285
00:06:40,000 --> 00:06:42,780
对左边的这一托元素来说

286
00:06:42,780 --> 00:06:44,680
我们还是一样的

287
00:06:44,680 --> 00:06:45,599
还是选择

288
00:06:45,600 --> 00:06:46,720
第一个元素

289
00:06:46,720 --> 00:06:47,980
第0个元素

290
00:06:47,980 --> 00:06:48,600
作为pivot

291
00:06:48,600 --> 00:06:49,480
一作为pivot

292
00:06:49,480 --> 00:06:51,880
执行跟刚才一样操作

293
00:06:51,880 --> 00:06:54,160
对右边这三个元素而言

294
00:06:54,160 --> 00:06:56,760
也是执行跟刚才一样操作

295
00:06:56,760 --> 00:06:58,280
对左边和右边

296
00:06:58,280 --> 00:07:00,260
分别进行一个排序的调整

297
00:07:00,260 --> 00:07:01,620
每次调完之后

298
00:07:01,620 --> 00:07:03,140
都以pivot

299
00:07:03,140 --> 00:07:04,860
重新把它分成到

300
00:07:04,860 --> 00:07:05,460
最后两部分

301
00:07:05,460 --> 00:07:06,460
所以每一部分

302
00:07:06,460 --> 00:07:08,220
重新执行地规的

303
00:07:08,220 --> 00:07:09,400
执行刚才的操作

304
00:07:09,400 --> 00:07:11,100
OK

305
00:07:11,100 --> 00:07:13,000
那什么时候地规可以终止呢

306
00:07:13,000 --> 00:07:15,240
假如说这个集合里面

307
00:07:15,240 --> 00:07:16,800
这个子集合里面

308
00:07:16,800 --> 00:07:18,200
有下一个元素了

309
00:07:18,200 --> 00:07:19,420
只剩下一个元素了

310
00:07:19,420 --> 00:07:21,820
现在就不需要再调化顺序了

311
00:07:21,820 --> 00:07:22,940
OK

312
00:07:22,940 --> 00:07:27,120
那么我们如果把这个操作

313
00:07:27,120 --> 00:07:29,060
画成一个树状结构的话

314
00:07:29,060 --> 00:07:30,260
就是右边这样图

315
00:07:30,260 --> 00:07:31,300
比方说

316
00:07:31,300 --> 00:07:32,700
我最开始的时候

317
00:07:32,700 --> 00:07:34,700
输入里面有八个元素

318
00:07:34,700 --> 00:07:39,280
那么经过一轮操作以后

319
00:07:39,280 --> 00:07:40,800
理想情况下

320
00:07:40,800 --> 00:07:42,540
我们说理想情况下

321
00:07:42,540 --> 00:07:43,060
这个pivot

322
00:07:43,060 --> 00:07:43,460
对吧

323
00:07:43,460 --> 00:07:47,580
它刚好分成了左右长度相等

324
00:07:47,580 --> 00:07:48,140
两部分

325
00:07:48,140 --> 00:07:49,240
只要说左边是4

326
00:07:49,240 --> 00:07:50,640
右边也是4

327
00:07:50,640 --> 00:07:51,740
当然了

328
00:07:51,740 --> 00:07:53,920
这里面是存在一点点差异

329
00:07:53,920 --> 00:07:55,960
就是说这个图画的不是那么严谨

330
00:07:55,960 --> 00:07:57,060
为什么呢

331
00:07:57,120 --> 00:07:59,820
因为当原始数组

332
00:07:59,820 --> 00:08:01,540
它是9个的时候

333
00:08:01,540 --> 00:08:04,560
那pivot它既不是左边

334
00:08:04,560 --> 00:08:05,500
也不是右边

335
00:08:05,500 --> 00:08:07,920
所以说左边两边都是4个

336
00:08:07,920 --> 00:08:08,720
对吧

337
00:08:08,720 --> 00:08:11,820
这里面我们就忽略这一个的差异

338
00:08:11,820 --> 00:08:15,240
我们就大略地把这个时间复杂度

339
00:08:15,240 --> 00:08:16,820
我们给大家展示一下

340
00:08:16,820 --> 00:08:18,240
所以就不要去计较那一个了

341
00:08:18,240 --> 00:08:20,180
假如说8个

342
00:08:20,180 --> 00:08:21,420
然后刚刚好

343
00:08:21,420 --> 00:08:23,040
左边两边刚好相等的话

344
00:08:23,040 --> 00:08:23,740
那左边4个

345
00:08:23,740 --> 00:08:24,340
右边4个

346
00:08:24,340 --> 00:08:24,740
对吧

347
00:08:24,740 --> 00:08:25,920
好

348
00:08:25,920 --> 00:08:27,820
对于左边这4个而言

349
00:08:27,820 --> 00:08:29,900
下一次分裂

350
00:08:29,900 --> 00:08:31,820
如果刚刚好的话

351
00:08:31,820 --> 00:08:32,720
刚好是左边两个

352
00:08:32,720 --> 00:08:33,380
右边两个

353
00:08:33,380 --> 00:08:34,159
好

354
00:08:34,159 --> 00:08:35,200
我们一起这样下去

355
00:08:35,200 --> 00:08:36,480
好

356
00:08:36,480 --> 00:08:36,919
我们想一下

357
00:08:36,919 --> 00:08:39,620
对于它这个8个变成4个

358
00:08:39,620 --> 00:08:41,000
这一步而言

359
00:08:41,000 --> 00:08:42,840
那么由于每个

360
00:08:42,840 --> 00:08:44,920
都要去跟pivot进行比较

361
00:08:44,920 --> 00:08:48,120
所以一共是比较了7次

362
00:08:48,120 --> 00:08:50,800
因为剩下7个元素

363
00:08:50,800 --> 00:08:51,840
比较7次

364
00:08:51,840 --> 00:08:55,400
那么我们这里面也近似的认为

365
00:08:55,400 --> 00:08:56,420
它比较了8次

366
00:08:56,420 --> 00:08:57,340
欧文就不去比较

367
00:08:57,340 --> 00:08:59,140
就不去计较那一次了

368
00:08:59,140 --> 00:09:02,000
因为反正都是欧文嘛

369
00:09:02,000 --> 00:09:03,180
好

370
00:09:03,180 --> 00:09:06,040
那从第二层到第三层

371
00:09:06,040 --> 00:09:09,540
那这4个分裂

372
00:09:09,540 --> 00:09:12,760
假如说我随机选取它为pivot

373
00:09:12,760 --> 00:09:12,980
的话

374
00:09:12,980 --> 00:09:13,940
剩下3个

375
00:09:13,940 --> 00:09:16,820
第二根pivot进行一个大小比较

376
00:09:16,820 --> 00:09:19,280
所以严格来讲是比较了3次

377
00:09:19,280 --> 00:09:20,600
但是我们初不认为

378
00:09:20,600 --> 00:09:21,700
它比较了4次

379
00:09:21,700 --> 00:09:23,260
进行了一次分裂

380
00:09:23,260 --> 00:09:24,560
那右边这4个呢

381
00:09:24,560 --> 00:09:25,760
也是比较了4次

382
00:09:25,760 --> 00:09:26,740
进行了一次分裂

383
00:09:26,740 --> 00:09:29,160
那左边4次

384
00:09:29,160 --> 00:09:29,820
右边4次

385
00:09:29,820 --> 00:09:32,220
整个条件是进行了8次比较

386
00:09:32,220 --> 00:09:34,740
所以说我们发现

387
00:09:34,740 --> 00:09:36,280
其实每一层

388
00:09:36,280 --> 00:09:40,620
每一层都是需要进行8次的大小比较

389
00:09:40,620 --> 00:09:42,840
最后一层呢

390
00:09:42,840 --> 00:09:44,300
它就一个

391
00:09:44,300 --> 00:09:45,580
它不需要再分裂了

392
00:09:45,580 --> 00:09:46,800
也不需要进行比较了

393
00:09:46,820 --> 00:09:48,340
所以说整体而言

394
00:09:48,340 --> 00:09:50,220
这个数字你看上去是有4层

395
00:09:50,220 --> 00:09:51,840
但实际上呢

396
00:09:51,840 --> 00:09:54,280
每一层需要进行8次的比较

397
00:09:54,280 --> 00:09:56,280
那需要比较的层数就有三层

398
00:09:56,280 --> 00:09:58,980
就是上面的三层是需要比较的

399
00:09:58,980 --> 00:10:00,780
所以总体而言

400
00:10:00,780 --> 00:10:05,160
一共进行了3乘8等于24次的大小比较

401
00:10:05,160 --> 00:10:06,920
好

402
00:10:06,920 --> 00:10:07,640
当然了

403
00:10:07,640 --> 00:10:10,820
我们这一页ppt里面讲的这些操作

404
00:10:10,820 --> 00:10:14,820
除了需要跟pivot进行大小比较之外

405
00:10:14,820 --> 00:10:18,580
还包含少量的交换操作

406
00:10:18,580 --> 00:10:20,660
比方说这个4跟7要交换

407
00:10:20,660 --> 00:10:22,220
这个5跟9的交换

408
00:10:22,220 --> 00:10:24,480
最后呢这个6跟1要交换

409
00:10:24,480 --> 00:10:24,960
对吧

410
00:10:24,960 --> 00:10:26,920
那这种交换操作

411
00:10:26,920 --> 00:10:28,560
它的是比较少的

412
00:10:28,560 --> 00:10:29,380
而且呢

413
00:10:29,380 --> 00:10:30,900
它最多情况下

414
00:10:30,900 --> 00:10:32,380
需要交换几次呢

415
00:10:32,380 --> 00:10:34,060
最多情况下需要交换

416
00:10:34,280 --> 00:10:35,420
2分之N次

417
00:10:35,420 --> 00:10:37,740
N表示这个数组的总长度

418
00:10:37,740 --> 00:10:41,740
最坏情况下每次都要交换嘛

419
00:10:41,740 --> 00:10:43,480
i跟g每动一步

420
00:10:43,480 --> 00:10:44,440
它们都要交换

421
00:10:44,440 --> 00:10:46,040
那就是交换2分之N次嘛

422
00:10:46,040 --> 00:10:49,340
所以说那最坏情况下呢

423
00:10:49,340 --> 00:10:52,280
那么每一层的这个分裂

424
00:10:52,280 --> 00:10:55,400
它就要进行N次的大小比较

425
00:10:55,400 --> 00:10:58,340
和2分之N次的固换操作

426
00:10:58,340 --> 00:11:02,440
加起来也不过是1.5倍的N嘛

427
00:11:02,440 --> 00:11:04,180
这还是On对吧

428
00:11:04,180 --> 00:11:06,400
右边这样一棵树结构

429
00:11:06,400 --> 00:11:08,440
它有几层呢

430
00:11:08,440 --> 00:11:10,360
最下面这一层

431
00:11:10,360 --> 00:11:12,100
它不用进行操作

432
00:11:12,100 --> 00:11:13,340
所以我们忽略最上面这层

433
00:11:13,340 --> 00:11:13,980
那上面呢

434
00:11:13,980 --> 00:11:14,680
刚好是三层

435
00:11:14,680 --> 00:11:15,580
三层

436
00:11:15,580 --> 00:11:17,700
三跟我们这个数组大小8

437
00:11:17,700 --> 00:11:18,580
是什么关系呢

438
00:11:18,580 --> 00:11:22,240
刚好是log以2为底的8嘛

439
00:11:22,240 --> 00:11:24,060
log8等于3

440
00:11:24,060 --> 00:11:24,360
对吧

441
00:11:24,360 --> 00:11:25,740
因为2的3次方等于8嘛

442
00:11:25,740 --> 00:11:26,940
好

443
00:11:26,940 --> 00:11:28,660
这说这个数的深度了

444
00:11:28,660 --> 00:11:30,420
但实际上数的深度的话

445
00:11:30,420 --> 00:11:32,120
这是理想情况下

446
00:11:32,120 --> 00:11:32,840
只有三层

447
00:11:32,840 --> 00:11:34,820
就是它最浅是三层

448
00:11:34,820 --> 00:11:36,040
它最多呢

449
00:11:36,040 --> 00:11:37,100
最多可能是八层

450
00:11:37,100 --> 00:11:38,020
为什么呢

451
00:11:38,020 --> 00:11:38,400
就是说

452
00:11:38,400 --> 00:11:41,000
因为我们都每次都是按理想情况

453
00:11:41,000 --> 00:11:42,180
就是左右两边

454
00:11:42,180 --> 00:11:45,100
它这个规模刚好是完全相等的

455
00:11:45,100 --> 00:11:45,900
所以呢

456
00:11:45,900 --> 00:11:46,500
是三层

457
00:11:46,500 --> 00:11:47,880
那如果说

458
00:11:47,880 --> 00:11:48,820
这个数

459
00:11:48,820 --> 00:11:51,040
它严重的左倾

460
00:11:51,040 --> 00:11:51,820
就是说

461
00:11:51,820 --> 00:11:52,880
每一个基点

462
00:11:52,880 --> 00:11:54,240
都只有左孩子

463
00:11:54,240 --> 00:11:55,460
没有右孩子的话

464
00:11:55,460 --> 00:11:56,680
大家可以想象吗

465
00:11:56,680 --> 00:11:57,200
整个数

466
00:11:57,200 --> 00:11:59,400
就退化成一个链表了

467
00:11:59,400 --> 00:12:00,960
就类似于

468
00:12:00,960 --> 00:12:01,880
这样

469
00:12:01,880 --> 00:12:02,400
下来

470
00:12:02,400 --> 00:12:03,280
然后再降下来

471
00:12:03,280 --> 00:12:04,000
再降下来

472
00:12:04,000 --> 00:12:04,760
它就是没有

473
00:12:04,760 --> 00:12:06,060
没有向右的箭头

474
00:12:06,060 --> 00:12:08,160
所以就是会有

475
00:12:08,160 --> 00:12:08,920
N层嘛

476
00:12:08,920 --> 00:12:09,760
八层

477
00:12:09,760 --> 00:12:11,040
好

478
00:12:11,040 --> 00:12:13,140
那如果说是这个

479
00:12:13,140 --> 00:12:14,280
三层的话

480
00:12:14,280 --> 00:12:14,939
其实就是

481
00:12:14,939 --> 00:12:15,980
整个

482
00:12:15,980 --> 00:12:16,939
它快速排序的

483
00:12:16,939 --> 00:12:17,660
实际复杂度

484
00:12:17,660 --> 00:12:18,219
是

485
00:12:18,219 --> 00:12:18,920
O

486
00:12:18,920 --> 00:12:19,980
在乘以

487
00:12:19,980 --> 00:12:20,540
Log

488
00:12:20,540 --> 00:12:21,439
是N倍的

489
00:12:21,439 --> 00:12:21,740
Log

490
00:12:21,740 --> 00:12:23,219
它的时间复杂度

491
00:12:23,219 --> 00:12:25,100
这是最好的情况

492
00:12:25,100 --> 00:12:25,660
那其实

493
00:12:25,660 --> 00:12:27,120
平均情况下

494
00:12:27,120 --> 00:12:28,600
也是这样一个

495
00:12:28,600 --> 00:12:29,400
数量级

496
00:12:29,400 --> 00:12:31,699
但如果最坏情况下呢

497
00:12:31,699 --> 00:12:32,600
最坏情况下

498
00:12:32,600 --> 00:12:33,719
就是我刚才说的

499
00:12:33,719 --> 00:12:34,839
整个数

500
00:12:34,839 --> 00:12:35,880
最后一个链条

501
00:12:35,880 --> 00:12:36,959
链表

502
00:12:36,959 --> 00:12:38,160
那么呢

503
00:12:38,160 --> 00:12:38,800
会有N层

504
00:12:38,800 --> 00:12:39,540
每一层

505
00:12:39,540 --> 00:12:40,860
的这个操作是O

506
00:12:40,860 --> 00:12:41,740
这样的话

507
00:12:41,740 --> 00:12:42,900
是N的平方

508
00:12:42,900 --> 00:12:44,520
我们一般说

509
00:12:44,520 --> 00:12:45,140
快速排序的

510
00:12:45,140 --> 00:12:45,720
时间复杂度

511
00:12:45,720 --> 00:12:46,580
都是按照

512
00:12:46,580 --> 00:12:47,720
N倍的Log

513
00:12:47,720 --> 00:12:48,260
来说的

514
00:12:48,260 --> 00:12:48,980
因为这个是

515
00:12:48,980 --> 00:12:50,040
平均情况

516
00:12:50,040 --> 00:12:50,760
大部分情况

517
00:12:50,760 --> 00:12:51,380
都是这样一个

518
00:12:51,380 --> 00:12:51,720
量级

519
00:12:51,720 --> 00:12:52,880
最坏情况

520
00:12:52,880 --> 00:12:53,540
是这样

521
00:12:53,540 --> 00:12:54,840
很少发生

522
00:12:54,840 --> 00:12:56,040
什么时候会出现

523
00:12:56,040 --> 00:12:56,820
最坏情况呢

524
00:12:56,820 --> 00:12:57,380
就是说

525
00:12:57,380 --> 00:12:59,300
这个原始数组

526
00:12:59,300 --> 00:13:01,000
它刚好是按照

527
00:13:01,000 --> 00:13:03,300
从大到小排序的

528
00:13:03,300 --> 00:13:03,820
好

529
00:13:03,820 --> 00:13:04,760
我们这条PT

530
00:13:04,760 --> 00:13:06,080
把这个代码

531
00:13:06,080 --> 00:13:06,960
讲一讲

532
00:13:06,960 --> 00:13:09,020
大家看

533
00:13:09,020 --> 00:13:10,240
我右边是一个

534
00:13:10,240 --> 00:13:11,120
PARTITION

535
00:13:11,120 --> 00:13:11,780
这个PARTITION

536
00:13:11,780 --> 00:13:12,860
实际上就是一个

537
00:13:12,860 --> 00:13:13,480
COS排序

538
00:13:13,480 --> 00:13:16,320
它接收的是一个数组

539
00:13:16,320 --> 00:13:17,200
这里面我们就不在

540
00:13:17,200 --> 00:13:18,260
区分数组和CPR

541
00:13:18,260 --> 00:13:19,940
同一数组来表示

542
00:13:19,940 --> 00:13:23,720
依然是使用的换型

543
00:13:23,720 --> 00:13:24,360
那就是说

544
00:13:24,360 --> 00:13:25,540
我不光是可以排

545
00:13:25,540 --> 00:13:26,360
整数数组

546
00:13:26,360 --> 00:13:26,960
也可以排

547
00:13:26,960 --> 00:13:27,660
负点数数组

548
00:13:27,660 --> 00:13:28,600
也可以排

549
00:13:28,600 --> 00:13:29,760
字符整数组

550
00:13:29,760 --> 00:13:30,260
都可以

551
00:13:30,260 --> 00:13:32,260
它没有返回值

552
00:13:32,260 --> 00:13:33,100
也就是说

553
00:13:33,100 --> 00:13:33,940
我实际上是

554
00:13:33,940 --> 00:13:35,300
原地排序

555
00:13:35,300 --> 00:13:36,720
什么叫原地排序呢

556
00:13:36,720 --> 00:13:37,200
就是说

557
00:13:37,200 --> 00:13:38,900
你传进来的这个数组

558
00:13:38,900 --> 00:13:39,340
是

559
00:13:39,340 --> 00:13:40,520
本来是无序的

560
00:13:40,520 --> 00:13:41,440
最终

561
00:13:41,440 --> 00:13:42,800
它就变成有序的

562
00:13:42,800 --> 00:13:44,260
我并没有开辟

563
00:13:44,260 --> 00:13:46,180
额外的内存空间

564
00:13:46,180 --> 00:13:47,800
说原始的内存空间

565
00:13:47,800 --> 00:13:48,740
我给你保持不动

566
00:13:48,740 --> 00:13:49,740
然后我把

567
00:13:49,740 --> 00:13:50,880
派出去之后的内容

568
00:13:50,880 --> 00:13:52,500
放到另外一块

569
00:13:52,500 --> 00:13:53,360
内存空间里面去

570
00:13:53,360 --> 00:13:54,960
这个就不叫原地排序

571
00:13:54,960 --> 00:13:56,300
原地排序就是

572
00:13:56,300 --> 00:13:57,360
直接改变

573
00:13:57,360 --> 00:13:58,500
原有的内存

574
00:13:58,500 --> 00:14:00,700
不去申请新的内存

575
00:14:00,700 --> 00:14:02,300
好

576
00:14:02,300 --> 00:14:03,440
那么以上来

577
00:14:03,440 --> 00:14:04,900
我会先判断一下

578
00:14:04,900 --> 00:14:06,480
因为这个方法

579
00:14:06,480 --> 00:14:08,260
它后面要地归

580
00:14:08,260 --> 00:14:09,520
要自己掉自己

581
00:14:09,520 --> 00:14:11,319
所有的地归函数

582
00:14:11,319 --> 00:14:12,240
你一上来

583
00:14:12,240 --> 00:14:13,000
一定要注意

584
00:14:13,000 --> 00:14:14,140
先判断什么呢

585
00:14:14,140 --> 00:14:15,459
退出条件

586
00:14:15,459 --> 00:14:16,500
break条件

587
00:14:16,500 --> 00:14:17,079
否则的话

588
00:14:17,079 --> 00:14:18,660
你每次总是

589
00:14:18,660 --> 00:14:19,660
自己掉自己

590
00:14:19,660 --> 00:14:20,280
自己掉自己

591
00:14:20,280 --> 00:14:21,520
就用一个无限循环

592
00:14:21,520 --> 00:14:22,680
你总得有一个

593
00:14:22,680 --> 00:14:23,880
跳出这个

594
00:14:23,880 --> 00:14:24,860
死循环的条件

595
00:14:24,860 --> 00:14:26,800
你上来

596
00:14:26,800 --> 00:14:27,240
你上来

597
00:14:27,240 --> 00:14:27,860
我们判断一下

598
00:14:27,860 --> 00:14:28,660
如果说

599
00:14:28,660 --> 00:14:29,660
这个数字里面

600
00:14:29,660 --> 00:14:30,800
这元素只有一个

601
00:14:30,800 --> 00:14:32,180
甚至比一个还少的话

602
00:14:32,180 --> 00:14:33,080
那显然

603
00:14:33,080 --> 00:14:35,080
就不需要进行任何操作了

604
00:14:35,080 --> 00:14:36,140
你想想

605
00:14:36,140 --> 00:14:38,220
一个数字里面

606
00:14:38,220 --> 00:14:39,060
只有一个元素

607
00:14:39,060 --> 00:14:41,300
我让你对这个数字进行排序

608
00:14:41,300 --> 00:14:42,380
那你还消排吗

609
00:14:42,380 --> 00:14:43,140
根本不消排

610
00:14:43,140 --> 00:14:43,740
是吧

611
00:14:43,740 --> 00:14:44,940
好

612
00:14:44,940 --> 00:14:45,400
退出

613
00:14:45,400 --> 00:14:47,400
然后呢

614
00:14:47,400 --> 00:14:48,820
我们还是以这个

615
00:14:48,820 --> 00:14:49,940
第零个元素

616
00:14:49,940 --> 00:14:50,580
作为pivot

617
00:14:50,580 --> 00:14:51,300
i呢

618
00:14:51,300 --> 00:14:52,160
从1开始

619
00:14:52,160 --> 00:14:52,840
c呢

620
00:14:52,840 --> 00:14:53,720
从最后面

621
00:14:53,720 --> 00:14:55,180
就是长度减1嘛

622
00:14:55,180 --> 00:14:56,500
就大的那个index开始

623
00:14:56,500 --> 00:14:57,260
好

624
00:14:57,260 --> 00:14:58,460
我们先不用管

625
00:14:58,460 --> 00:14:59,420
第11号代码

626
00:14:59,420 --> 00:15:00,100
先不管这个

627
00:15:00,100 --> 00:15:02,100
我们先来看里面的

628
00:15:02,100 --> 00:15:03,439
就是我们对到着

629
00:15:03,439 --> 00:15:04,700
这个pivot里面的

630
00:15:04,700 --> 00:15:05,460
这个case来看

631
00:15:05,460 --> 00:15:06,819
最开始

632
00:15:06,819 --> 00:15:09,819
它不是i从1

633
00:15:09,819 --> 00:15:12,300
j从8开始

634
00:15:12,300 --> 00:15:12,819
好

635
00:15:12,819 --> 00:15:14,380
我们说要先动j

636
00:15:14,380 --> 00:15:14,740
对吧

637
00:15:14,740 --> 00:15:15,420
先动j

638
00:15:15,420 --> 00:15:15,780
好

639
00:15:15,780 --> 00:15:17,600
先来看j

640
00:15:17,600 --> 00:15:18,560
每次呢

641
00:15:18,560 --> 00:15:19,319
都是j

642
00:15:19,319 --> 00:15:20,460
减减

643
00:15:20,460 --> 00:15:20,939
对吧

644
00:15:20,939 --> 00:15:21,600
j减减

645
00:15:21,600 --> 00:15:22,360
好

646
00:15:22,360 --> 00:15:24,120
那么j什么时候停下来呢

647
00:15:24,120 --> 00:15:24,880
就是说j

648
00:15:24,880 --> 00:15:26,360
如果他指的这个元素

649
00:15:26,360 --> 00:15:28,160
比pivot小的话

650
00:15:28,160 --> 00:15:28,880
那么呢

651
00:15:28,880 --> 00:15:29,680
就停下来

652
00:15:29,680 --> 00:15:30,100
好

653
00:15:30,100 --> 00:15:31,180
那我们看看啊

654
00:15:31,180 --> 00:15:32,740
如果说这个pivot

655
00:15:32,740 --> 00:15:33,120
是吧

656
00:15:33,120 --> 00:15:33,620
这个j

657
00:15:33,620 --> 00:15:35,120
j这个元素

658
00:15:35,120 --> 00:15:37,100
它比pivot这个元素

659
00:15:37,100 --> 00:15:38,280
如果小的话呢

660
00:15:38,280 --> 00:15:38,900
就break

661
00:15:38,900 --> 00:15:39,860
就停下来

662
00:15:39,860 --> 00:15:41,260
那否则

663
00:15:41,260 --> 00:15:42,780
就进入下一次

664
00:15:42,780 --> 00:15:43,680
就j减减

665
00:15:43,680 --> 00:15:44,600
暴循环嘛

666
00:15:44,600 --> 00:15:46,000
不断的j减减

667
00:15:46,000 --> 00:15:47,580
那这里面有一个

668
00:15:47,580 --> 00:15:50,140
所谓的这个限制条件

669
00:15:50,140 --> 00:15:51,160
就是呢

670
00:15:51,160 --> 00:15:52,100
这个j

671
00:15:52,100 --> 00:15:53,200
它必须大于i

672
00:15:53,200 --> 00:15:56,260
它不能跑到i左边去了

673
00:15:56,260 --> 00:15:57,880
好

674
00:15:57,880 --> 00:16:00,180
那对于我们这个k

675
00:16:00,180 --> 00:16:02,500
这个案例而言啊

676
00:16:02,500 --> 00:16:03,120
最开始的话

677
00:16:03,120 --> 00:16:03,900
实际上这个j

678
00:16:03,900 --> 00:16:05,240
它指向8之后的话呢

679
00:16:05,240 --> 00:16:06,300
它实际上根本就

680
00:16:06,300 --> 00:16:07,500
没有往前走

681
00:16:07,500 --> 00:16:08,240
没有减减

682
00:16:08,240 --> 00:16:09,460
因为它这个暴循环

683
00:16:09,460 --> 00:16:10,560
一进来

684
00:16:10,560 --> 00:16:12,040
一次就已经满足

685
00:16:12,040 --> 00:16:13,440
这个evo条件了

686
00:16:13,440 --> 00:16:13,880
所以呢

687
00:16:13,880 --> 00:16:15,300
直接就break了

688
00:16:15,300 --> 00:16:15,980
所以这个时候

689
00:16:15,980 --> 00:16:17,220
这一段代码

690
00:16:17,220 --> 00:16:18,500
执行完之后

691
00:16:18,500 --> 00:16:18,820
j

692
00:16:18,820 --> 00:16:20,100
它还是8

693
00:16:20,100 --> 00:16:21,180
好

694
00:16:21,180 --> 00:16:22,780
这个啊

695
00:16:22,780 --> 00:16:23,460
就是一度i

696
00:16:23,460 --> 00:16:24,460
那么i呢

697
00:16:24,460 --> 00:16:25,740
要加加i往后走

698
00:16:25,740 --> 00:16:28,060
如果说i所指向的元素

699
00:16:28,060 --> 00:16:29,760
它比这个p比较大的话

700
00:16:29,760 --> 00:16:31,160
直接退出break

701
00:16:31,160 --> 00:16:32,360
我们发现这个7

702
00:16:32,360 --> 00:16:33,860
一上来就大

703
00:16:33,860 --> 00:16:34,500
所以呢

704
00:16:34,500 --> 00:16:37,420
这个暴循环进来之后啊

705
00:16:37,420 --> 00:16:38,180
这个evo条件

706
00:16:38,180 --> 00:16:39,460
第一次就满足了

707
00:16:39,460 --> 00:16:40,200
所以直接break

708
00:16:40,200 --> 00:16:40,640
所以呢

709
00:16:40,640 --> 00:16:42,000
压根就没有机会

710
00:16:42,000 --> 00:16:43,040
去执行这个i再大

711
00:16:43,040 --> 00:16:43,600
所以呢

712
00:16:43,600 --> 00:16:44,820
所以i还是1

713
00:16:44,820 --> 00:16:45,620
好

714
00:16:45,620 --> 00:16:47,180
这样的话呢

715
00:16:47,180 --> 00:16:48,960
建议它还是8

716
00:16:48,960 --> 00:16:50,140
i呢

717
00:16:50,140 --> 00:16:51,040
还是1

718
00:16:51,040 --> 00:16:52,160
这个时候

719
00:16:52,160 --> 00:16:53,840
i仍然是小于j的

720
00:16:53,840 --> 00:16:54,120
对吧

721
00:16:54,120 --> 00:16:54,740
好

722
00:16:54,740 --> 00:16:56,680
我们去交换i和j

723
00:16:56,680 --> 00:16:57,800
所指向的元素

724
00:16:57,800 --> 00:16:58,600
好

725
00:16:58,600 --> 00:16:59,920
在构元里面呢

726
00:16:59,920 --> 00:17:01,400
你交换数数里面的

727
00:17:01,400 --> 00:17:02,420
两个元素的话

728
00:17:02,420 --> 00:17:04,520
直接这样写啊

729
00:17:04,520 --> 00:17:04,780
i

730
00:17:04,780 --> 00:17:05,320
j

731
00:17:05,320 --> 00:17:06,600
等于j

732
00:17:06,600 --> 00:17:07,100
i

733
00:17:07,100 --> 00:17:08,260
这样就交换了

734
00:17:08,260 --> 00:17:10,060
数数里面的两个元素

735
00:17:10,060 --> 00:17:11,820
OK

736
00:17:11,820 --> 00:17:15,500
这是第一次暴循环了

737
00:17:15,500 --> 00:17:16,320
那我们说

738
00:17:16,320 --> 00:17:18,760
外面还套了一层暴循环

739
00:17:18,760 --> 00:17:19,060
是吧

740
00:17:19,060 --> 00:17:20,260
只要i小于j

741
00:17:20,260 --> 00:17:21,819
这个条件还满足

742
00:17:21,819 --> 00:17:24,500
只要i跟j还没有相遇的话

743
00:17:24,500 --> 00:17:27,560
就重复的执行这个隔过程

744
00:17:27,560 --> 00:17:28,300
好

745
00:17:28,300 --> 00:17:29,420
我们重复执行这个过程

746
00:17:29,420 --> 00:17:30,060
你发现呢

747
00:17:30,060 --> 00:17:32,020
第二次暴循环进了之后

748
00:17:32,020 --> 00:17:33,220
我发现这个j呢

749
00:17:33,220 --> 00:17:35,220
终于往前走两步

750
00:17:35,220 --> 00:17:36,100
那么j呢

751
00:17:36,100 --> 00:17:36,920
指向5了

752
00:17:36,920 --> 00:17:37,380
也就是说

753
00:17:37,380 --> 00:17:38,360
j它等于6了

754
00:17:38,360 --> 00:17:38,940
比如说

755
00:17:38,940 --> 00:17:39,820
这个

756
00:17:39,820 --> 00:17:41,640
这个负述循环

757
00:17:41,640 --> 00:17:42,300
它实际上

758
00:17:42,300 --> 00:17:43,220
执行了两次嘛

759
00:17:43,220 --> 00:17:44,880
j终于

760
00:17:44,880 --> 00:17:46,300
等于6

761
00:17:46,300 --> 00:17:48,620
index点6嘛

762
00:17:48,620 --> 00:17:49,160
好

763
00:17:49,160 --> 00:17:50,640
然后执行这个负述循环

764
00:17:50,640 --> 00:17:51,460
这个负述循环呢

765
00:17:51,460 --> 00:17:52,280
最后我们发现

766
00:17:52,280 --> 00:17:53,160
i指向9

767
00:17:53,160 --> 00:17:54,120
就是i等于3

768
00:17:54,120 --> 00:17:55,120
好

769
00:17:55,120 --> 00:17:55,820
依然满足

770
00:17:55,820 --> 00:17:56,740
i小为j

771
00:17:56,740 --> 00:17:57,300
那还是

772
00:17:57,300 --> 00:17:58,379
它来源素

773
00:17:58,379 --> 00:17:59,100
交换

774
00:17:59,100 --> 00:18:00,720
OK

775
00:18:00,720 --> 00:18:02,800
又这个负述循环

776
00:18:02,800 --> 00:18:04,639
第三次再进来

777
00:18:04,639 --> 00:18:05,659
一直这样之前

778
00:18:05,659 --> 00:18:06,639
好

779
00:18:06,639 --> 00:18:07,120
一直这样之前

780
00:18:07,120 --> 00:18:09,179
那最后的话呢

781
00:18:09,179 --> 00:18:10,060
最后的话

782
00:18:10,060 --> 00:18:10,919
i跟j

783
00:18:10,919 --> 00:18:11,220
是吧

784
00:18:11,220 --> 00:18:12,280
它们同时指向1

785
00:18:12,280 --> 00:18:13,800
它们同时等于5

786
00:18:13,800 --> 00:18:14,179
对吧

787
00:18:14,179 --> 00:18:15,500
1对那1x是5嘛

788
00:18:15,500 --> 00:18:16,580
这个负述循环

789
00:18:16,580 --> 00:18:17,820
终于退出了

790
00:18:17,820 --> 00:18:18,899
好

791
00:18:18,899 --> 00:18:20,280
退出之后的话

792
00:18:20,280 --> 00:18:20,720
这里面

793
00:18:20,720 --> 00:18:21,520
挤到一下

794
00:18:21,520 --> 00:18:22,040
ari

795
00:18:22,040 --> 00:18:22,460
实际上

796
00:18:22,460 --> 00:18:23,460
ari此时

797
00:18:23,460 --> 00:18:24,240
就等于

798
00:18:24,240 --> 00:18:25,139
arj嘛

799
00:18:25,820 --> 00:18:26,419
我们看一下

800
00:18:26,419 --> 00:18:27,179
ari

801
00:18:27,179 --> 00:18:28,860
它跟pivot比较一下

802
00:18:28,860 --> 00:18:31,020
它比pivot要小

803
00:18:31,020 --> 00:18:32,300
1比6小嘛

804
00:18:32,300 --> 00:18:33,379
所以的话呢

805
00:18:33,379 --> 00:18:35,980
要交换pivot和i

806
00:18:35,980 --> 00:18:36,659
你看

807
00:18:36,659 --> 00:18:38,939
交换pivot和i

808
00:18:38,939 --> 00:18:39,820
这种元素

809
00:18:39,820 --> 00:18:40,740
换一下吧

810
00:18:40,740 --> 00:18:41,700
好

811
00:18:41,700 --> 00:18:42,980
换过了之后的话呢

812
00:18:42,980 --> 00:18:44,540
我令pivot等于i

813
00:18:44,540 --> 00:18:45,820
pivot不然等于0

814
00:18:45,820 --> 00:18:47,300
现在等于i

815
00:18:47,300 --> 00:18:48,020
i等于谁

816
00:18:48,020 --> 00:18:50,659
i应该是等于5

817
00:18:50,659 --> 00:18:51,020
对吧

818
00:18:51,020 --> 00:18:51,620
i等于5

819
00:18:51,620 --> 00:18:53,340
好

820
00:18:53,340 --> 00:18:54,620
这是pivot等于5了

821
00:18:54,620 --> 00:18:55,780
然后呢

822
00:18:55,820 --> 00:18:57,139
pivot等于5

823
00:18:57,139 --> 00:18:57,939
也就是说这个

824
00:18:57,939 --> 00:18:59,700
它等于是什么

825
00:18:59,700 --> 00:19:01,220
依然是等于6

826
00:19:01,220 --> 00:19:02,659
所对应的index嘛

827
00:19:02,659 --> 00:19:03,060
对吧

828
00:19:03,060 --> 00:19:03,659
这样的话

829
00:19:03,659 --> 00:19:05,340
以pivot为分解线

830
00:19:05,340 --> 00:19:06,740
分成了左右两部分

831
00:19:06,740 --> 00:19:07,700
那么我们要对

832
00:19:07,700 --> 00:19:08,500
左右两部分

833
00:19:08,500 --> 00:19:09,340
递归的

834
00:19:09,340 --> 00:19:10,419
再去调用

835
00:19:10,419 --> 00:19:11,740
这个partition函数

836
00:19:11,740 --> 00:19:13,179
那么我们看到

837
00:19:13,179 --> 00:19:14,240
partition函数

838
00:19:14,240 --> 00:19:15,120
它的参数

839
00:19:15,120 --> 00:19:16,419
就是一个切片

840
00:19:16,419 --> 00:19:17,460
所以呢

841
00:19:17,460 --> 00:19:20,240
你就把原始的这个切片

842
00:19:20,240 --> 00:19:21,780
分为左右两部分嘛

843
00:19:21,780 --> 00:19:23,360
而左边就是

844
00:19:23,360 --> 00:19:24,320
从0

845
00:19:24,320 --> 00:19:25,400
如果这边不行的话

846
00:19:25,400 --> 00:19:25,800
就是0

847
00:19:25,800 --> 00:19:27,480
从0到pivot

848
00:19:27,480 --> 00:19:31,360
那它是前臂

849
00:19:31,360 --> 00:19:32,440
后开局前

850
00:19:32,440 --> 00:19:33,520
它是包含0

851
00:19:33,520 --> 00:19:34,060
但是呢

852
00:19:34,060 --> 00:19:35,260
不包含这个pivot

853
00:19:35,260 --> 00:19:37,480
是对左边的

854
00:19:37,480 --> 00:19:39,080
地规执行partition

855
00:19:39,080 --> 00:19:40,380
对右边部分

856
00:19:40,380 --> 00:19:41,620
也执行partition

857
00:19:41,620 --> 00:19:42,880
那右边部分呢

858
00:19:42,880 --> 00:19:44,540
从pivot加1开始

859
00:19:44,540 --> 00:19:45,880
就包含这个啊

860
00:19:45,880 --> 00:19:47,040
前臂

861
00:19:47,040 --> 00:19:47,320
后开

862
00:19:47,320 --> 00:19:47,940
包含这个

863
00:19:47,940 --> 00:19:49,960
那我发现实际上

864
00:19:49,960 --> 00:19:51,960
唯独把pivot的这根素

865
00:19:51,960 --> 00:19:53,200
给抛出在外了

866
00:19:53,200 --> 00:19:53,640
好

867
00:19:53,640 --> 00:19:54,400
看一下这个

868
00:19:54,400 --> 00:19:56,460
快速排序的测试

869
00:19:56,460 --> 00:19:57,400
在吧

870
00:19:57,400 --> 00:19:59,280
最外层的包含

871
00:19:59,280 --> 00:20:00,140
还是包含

872
00:20:00,140 --> 00:20:00,860
144

873
00:20:00,860 --> 00:20:02,640
主要是要测100个case

874
00:20:02,640 --> 00:20:02,940
啊

875
00:20:02,940 --> 00:20:04,620
因为每个case的情况

876
00:20:04,620 --> 00:20:05,240
都不一样嘛

877
00:20:05,240 --> 00:20:05,860
好

878
00:20:05,860 --> 00:20:07,360
对于每个case而已呢

879
00:20:07,360 --> 00:20:08,140
我要初始化

880
00:20:08,140 --> 00:20:09,760
一个长度为20的

881
00:20:09,760 --> 00:20:10,560
这样一个数数

882
00:20:10,560 --> 00:20:12,440
还是通过随机数啊

883
00:20:12,440 --> 00:20:13,480
去进行一个

884
00:20:13,480 --> 00:20:14,300
初始化

885
00:20:14,300 --> 00:20:16,680
防止它是一个乱续的嘛

886
00:20:16,680 --> 00:20:17,260
好

887
00:20:17,260 --> 00:20:19,720
像我刚刚写好的这个partition

888
00:20:19,720 --> 00:20:21,500
实际上就是我的快速排序算法了

889
00:20:21,500 --> 00:20:24,040
就是它进行一个原地排序

890
00:20:24,040 --> 00:20:25,460
原地排序

891
00:20:25,460 --> 00:20:27,180
好

892
00:20:27,180 --> 00:20:27,980
排完之后

893
00:20:27,980 --> 00:20:29,379
我怎么能够保证

894
00:20:29,379 --> 00:20:30,560
我写的这个算法

895
00:20:30,560 --> 00:20:31,840
它准确无误呢

896
00:20:31,840 --> 00:20:33,480
那我就这样啊

897
00:20:33,480 --> 00:20:34,320
我就看看

898
00:20:34,320 --> 00:20:35,920
我就从前往后

899
00:20:35,920 --> 00:20:37,080
把这个数组呢

900
00:20:37,080 --> 00:20:37,820
过一遍

901
00:20:37,820 --> 00:20:39,280
如果说

902
00:20:39,280 --> 00:20:40,640
其中某个元素

903
00:20:40,640 --> 00:20:43,020
居然比它左边的那个元素

904
00:20:43,020 --> 00:20:44,320
要小的话

905
00:20:44,320 --> 00:20:46,480
那么它并没有严格的

906
00:20:46,480 --> 00:20:48,120
从小都得拍好序码

907
00:20:48,120 --> 00:20:49,160
那说明

908
00:20:49,160 --> 00:20:49,820
装法有问题

909
00:20:49,820 --> 00:20:50,440
提点

910
00:20:50,440 --> 00:20:50,720
fail

911
00:20:50,720 --> 00:20:51,420
fail掉

912
00:20:51,420 --> 00:20:52,420
好

913
00:20:52,420 --> 00:20:53,060
好

914
00:20:53,060 --> 00:20:53,420
好

915
00:20:53,420 --> 00:20:53,860
好

