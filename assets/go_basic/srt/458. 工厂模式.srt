1
00:00:00,520 --> 00:00:03,480
前几节课我们介绍了一些 KV 数据库

2
00:00:03,480 --> 00:00:05,040
那这些 KV 数据库呢

3
00:00:05,040 --> 00:00:06,820
它们是存储在本地的

4
00:00:06,820 --> 00:00:11,260
不像 MYSQL 、 REDIS 数据是存在另外一台服务器上面的

5
00:00:11,260 --> 00:00:12,380
你还得走网络

6
00:00:12,380 --> 00:00:13,940
那既然存在本地的话

7
00:00:13,940 --> 00:00:17,457
就是你需要指定一个本地的存储路径嘛

8
00:00:17,457 --> 00:00:19,670
好，那么我们说有什么 boot 

9
00:00:19,670 --> 00:00:21,610
badger 和 rocks dB 这几种

10
00:00:21,610 --> 00:00:25,760
那么 boot 跟 badger 呢，它是用纯购员写的

11
00:00:25,760 --> 00:00:29,160
所以我们在购员里面去调用的话会比较方便

12
00:00:29,160 --> 00:00:30,280
那 rocks dB 呢

13
00:00:30,280 --> 00:00:31,280
性能也很好

14
00:00:31,280 --> 00:00:33,240
但可惜是用 C 加加起来的

15
00:00:33,240 --> 00:00:35,660
但是也有人套了个壳子啊

16
00:00:35,660 --> 00:00:38,530
把它搞成了一个 go 的库

17
00:00:38,530 --> 00:00:40,170
但它背后的实现原理

18
00:00:40,170 --> 00:00:43,860
实际上还是把原始的那个 ROCDBC 加加码

19
00:00:43,860 --> 00:00:45,580
把它搞成动态库文件

20
00:00:45,580 --> 00:00:49,180
然后通过国购源去调那个动态库文件啊

21
00:00:49,180 --> 00:00:50,840
从而给我们的感觉是说，哎

22
00:00:50,840 --> 00:00:54,065
他可以用 go 去直接调用那个库

23
00:00:54,065 --> 00:00:56,860
好，这里面我们就只演示 boot 和 badger 啊

24
00:00:56,860 --> 00:00:58,420
那 bot 是基于 B 加数的

25
00:00:58,420 --> 00:01:01,007
badger 是基于 LSM 数的

26
00:01:01,007 --> 00:01:02,750
放了两个枚举啊

27
00:01:02,750 --> 00:01:05,290
那么我们说这两种数据库的话

28
00:01:05,290 --> 00:01:08,570
他们各自提供的 API 各不一样

29
00:01:08,570 --> 00:01:12,560
所以呢，我们想在我们的这个搜索引擎里面啊

30
00:01:12,560 --> 00:01:13,730
都把它用起来

31
00:01:13,730 --> 00:01:14,790
到时候的话

32
00:01:14,790 --> 00:01:16,590
由调用方它来决定

33
00:01:16,590 --> 00:01:19,250
他想使用哪一种类型的数据库

34
00:01:19,250 --> 00:01:20,230
由它来定

35
00:01:20,230 --> 00:01:21,370
那既然这样的话呢

36
00:01:21,370 --> 00:01:24,940
我们需要对这两种 KB 数据库做一个

37
00:01:24,940 --> 00:01:25,810
怎么说呢

38
00:01:25,810 --> 00:01:27,630
做一个接口的转换法

39
00:01:27,630 --> 00:01:32,087
使他们啊，对外暴露出一模一样的接口

40
00:01:32,087 --> 00:01:35,120
所以呀，我们就搞了一个 interface 啊

41
00:01:35,120 --> 00:01:38,702
interface 叫做 i k value dB 

42
00:01:38,702 --> 00:01:43,450
那么这个 interface 呢，你必须实现以下这几种方法啊

43
00:01:43,450 --> 00:01:46,780
第一个 open 就是打开这个数据库啊

44
00:01:46,780 --> 00:01:47,920
你就把这个数据库

45
00:01:47,920 --> 00:01:50,640
想象成是本地的一个目录

46
00:01:50,640 --> 00:01:51,840
那这个目录里面呢

47
00:01:51,840 --> 00:01:53,860
有很多文件存储的数据嘛

48
00:01:53,860 --> 00:01:54,140
对吧

49
00:01:54,140 --> 00:01:55,620
你就好比是打开一个文件一样

50
00:01:55,620 --> 00:01:56,937
open 好

51
00:01:56,937 --> 00:01:59,310
get dab pass 是获得，哎

52
00:01:59,310 --> 00:02:04,375
我这个数据是存储在我本地的哪一个目录下

53
00:02:04,375 --> 00:02:06,990
site 主要是把一个 key value 呢

54
00:02:06,990 --> 00:02:10,050
写进我们的 KV 数据库里面去

55
00:02:10,050 --> 00:02:11,850
那么这里面我们看到 KV 呢

56
00:02:11,850 --> 00:02:13,350
是将是任意长度

57
00:02:13,350 --> 00:02:15,672
字节流 BT 切片嘛

58
00:02:15,672 --> 00:02:18,280
对应的有一个批量的写入啊

59
00:02:18,280 --> 00:02:21,040
就是写入一批 K 和一批 value 

60
00:02:21,040 --> 00:02:23,102
但它们是有这个对应关系了

61
00:02:23,102 --> 00:02:27,640
get 啊，根据 key 呢来获得对应的 value 

62
00:02:27,640 --> 00:02:31,067
bad get 根据 pk 获取 p value 

63
00:02:31,067 --> 00:02:32,160
delete 啊

64
00:02:32,160 --> 00:02:33,460
删掉某一个 key 

65
00:02:33,460 --> 00:02:35,570
bat delete ，删除1 p

66
00:02:35,570 --> 00:02:40,475
has 来判断一下某个 key 是否在我的 dB 里面

67
00:02:40,475 --> 00:02:42,540
还有这个 eta d

68
00:02:42,540 --> 00:02:44,730
就是便利这个数据库嘛

69
00:02:44,730 --> 00:02:45,530
那这个呢

70
00:02:45,530 --> 00:02:46,830
这个地方没有使用

71
00:02:46,830 --> 00:02:49,590
之前我们讲的那个迭代器模式

72
00:02:49,590 --> 00:02:53,280
而是使用了我们刚刚讲的这个函数

73
00:02:53,280 --> 00:02:55,170
作为参数种方式啊

74
00:02:55,170 --> 00:02:58,010
就好比是我要去便利那个 rain 是吧

75
00:02:58,010 --> 00:03:00,290
那个环状结构里面的每一个元素

76
00:03:00,290 --> 00:03:01,180
这里面呢

77
00:03:01,180 --> 00:03:04,120
你把整个 dB 也看成是一个集合的吧

78
00:03:04,120 --> 00:03:06,920
那么你要去便利 DB 里的那么一个元素

79
00:03:06,920 --> 00:03:08,800
就是每一对 KV 嘛

80
00:03:08,800 --> 00:03:11,530
那么呢，你需要传一个函数进来

81
00:03:11,530 --> 00:03:14,070
就是说当我拿到一对 KV 之后

82
00:03:14,070 --> 00:03:15,830
你打算怎么处理它，对吧

83
00:03:15,830 --> 00:03:21,160
怎么处理它是通过你指定的这个函数来做的

84
00:03:21,160 --> 00:03:21,850
好

85
00:03:21,850 --> 00:03:23,590
还有一个是这个便利 K 

86
00:03:23,590 --> 00:03:24,402
就是说呢

87
00:03:24,402 --> 00:03:25,220
这个函数啊

88
00:03:25,220 --> 00:03:26,420
就我不关心 value 啊

89
00:03:26,420 --> 00:03:27,400
我只关心 K 

90
00:03:27,400 --> 00:03:30,210
你就说我给你拿到一个 K 之后

91
00:03:30,210 --> 00:03:32,470
你打算怎么去处理这个 K 啊

92
00:03:32,470 --> 00:03:34,330
根据函数的定义之后呢

93
00:03:34,330 --> 00:03:36,310
把这个数据库给关掉啊

94
00:03:36,310 --> 00:03:39,260
就好比是我们经常你可以打开一个文件

95
00:03:39,260 --> 00:03:40,820
那这个文件用完之后

96
00:03:40,820 --> 00:03:43,340
要把这个文件给它关闭掉嘛，对吧

97
00:03:43,340 --> 00:03:44,520
那跟数据库也是一样的啊

98
00:03:44,520 --> 00:03:45,180
先连接

99
00:03:45,180 --> 00:03:48,555
最后呢，还是要把这个数据库连接给关闭掉

100
00:03:48,555 --> 00:03:52,620
好，这是我们啊，规定了这样一个接口啊

101
00:03:52,620 --> 00:03:54,020
一组行为规范啊

102
00:03:54,020 --> 00:03:58,400
不管你内部使用的是 BER 、 rocks 、 dB 还是 boot 

103
00:03:58,400 --> 00:04:02,770
那么最终对外都要提供这些方法啊

104
00:04:02,770 --> 00:04:04,890
外部是关心内部的具体实现的

105
00:04:06,110 --> 00:04:06,830
然后呢

106
00:04:06,830 --> 00:04:10,070
下面啊，就到我们的这个所谓的工厂模式了

107
00:04:10,070 --> 00:04:14,790
工厂模式的前提就是说它得现有的接口

108
00:04:14,790 --> 00:04:17,029
好，我们看一下刚刚说的这个接口

109
00:04:17,029 --> 00:04:19,896
在工厂模式里面是如何使用的啊

110
00:04:19,896 --> 00:04:23,160
这边搞了一个所谓的 get k v DB 

111
00:04:23,160 --> 00:04:26,272
就是我想去生成一个 KVDB 

112
00:04:26,272 --> 00:04:27,530
好啊

113
00:04:27,530 --> 00:04:29,410
第一个呢，参数是说，哎

114
00:04:29,410 --> 00:04:31,830
dB 的类型数字嘛

115
00:04:31,830 --> 00:04:34,270
哎，就我们上面不是定了一个常量嘛，是吧

116
00:04:34,270 --> 00:04:36,130
bot 是零， BER 是一

117
00:04:36,130 --> 00:04:37,010
好

118
00:04:37,010 --> 00:04:38,550
那么你要创建个 dB 

119
00:04:38,550 --> 00:04:40,790
那这个 dB 我目前只是两种对吧

120
00:04:40,790 --> 00:04:42,360
你想创建哪一种是吧

121
00:04:42,360 --> 00:04:44,360
通过第一个参数你来告诉我

122
00:04:44,360 --> 00:04:46,220
你想创建哪种 dB 

123
00:04:46,220 --> 00:04:47,602
bot 还是白纸

124
00:04:47,602 --> 00:04:48,390
第二个呢

125
00:04:48,390 --> 00:04:52,810
是指定那个 dB 在本地的存储路径是什么

126
00:04:52,810 --> 00:04:54,290
好，这两个误参

127
00:04:54,290 --> 00:04:55,330
那初三呢

128
00:04:55,330 --> 00:04:56,010
返回什么

129
00:04:56,010 --> 00:04:57,890
我返回的是一个接口啊

130
00:04:57,890 --> 00:05:00,260
i key value dB 是吧

131
00:05:00,260 --> 00:05:03,840
因为我也不能说我返回 badger 或者 boot 啊

132
00:05:03,840 --> 00:05:05,360
我不能确定。为什么呢

133
00:05:05,360 --> 00:05:10,340
因为取决于你传进来的这个参数是什么

134
00:05:11,500 --> 00:05:14,880
OK ，所以从第33行啊

135
00:05:14,880 --> 00:05:17,440
从第33行这个代码上

136
00:05:17,440 --> 00:05:20,220
我们就能够看到所谓的工厂模式

137
00:05:20,220 --> 00:05:22,587
它的一个思想精髓

138
00:05:22,587 --> 00:05:24,770
他就说正常情况下

139
00:05:24,770 --> 00:05:26,800
我们不是有一个 badger 

140
00:05:26,800 --> 00:05:27,580
一个 boot 嘛

141
00:05:27,580 --> 00:05:28,600
好， badger 和 boot 

142
00:05:28,600 --> 00:05:32,160
他们可能都有自己各自的一个构造函数

143
00:05:32,160 --> 00:05:34,920
那么构造函数它只是说

144
00:05:34,920 --> 00:05:39,510
我只能生产某一种特定类型的数据，对吧

145
00:05:39,510 --> 00:05:42,230
跟那个结构体是绑定的嘛

146
00:05:42,230 --> 00:05:44,300
而我们的工厂模式

147
00:05:44,300 --> 00:05:47,550
也就是我们这边的这个 get 函数

148
00:05:47,550 --> 00:05:49,820
你把想象生成是一个工厂

149
00:05:49,820 --> 00:05:52,690
我可以生产多种产品啊

150
00:05:52,690 --> 00:05:54,690
只不过呢，所有产品啊

151
00:05:54,690 --> 00:05:57,510
他们都需要实现同一个接口

152
00:05:57,510 --> 00:06:00,320
这所有产品它们都具有相同的功能

153
00:06:00,320 --> 00:06:02,380
但它们确实是不同的产品

154
00:06:02,380 --> 00:06:04,730
就好比是我是一个汽车工厂

155
00:06:04,730 --> 00:06:09,120
那所有汽车都能够实现运载货物的功能

156
00:06:09,120 --> 00:06:10,600
但它们确实是不同的的

157
00:06:10,600 --> 00:06:12,862
汽车不同的实现方式

158
00:06:12,862 --> 00:06:14,630
所以叫工厂模式嘛

159
00:06:16,220 --> 00:06:21,370
那具体生产哪种汽车是通过参数传进来的

160
00:06:21,370 --> 00:06:24,300
好理解了工厂模式这个思想啊

161
00:06:24,300 --> 00:06:26,160
我们看看就这个例子而言

162
00:06:26,160 --> 00:06:28,580
它内部是如何实现的

163
00:06:29,720 --> 00:06:31,920
你不是把这个目录给传进来吗

164
00:06:31,920 --> 00:06:32,560
那第一步呢

165
00:06:32,560 --> 00:06:35,040
他先对这个目录按照这个斜线啊

166
00:06:35,040 --> 00:06:36,240
进行一个分割

167
00:06:36,240 --> 00:06:37,300
分割之后的话呢

168
00:06:37,300 --> 00:06:39,390
又进行了一次连接

169
00:06:39,390 --> 00:06:41,590
而且还是按照这个虚线进行连接的

170
00:06:41,590 --> 00:06:43,440
那这感觉有点多余的

171
00:06:43,440 --> 00:06:44,580
为什么先分割

172
00:06:44,580 --> 00:06:45,760
然后有连接呢

173
00:06:45,760 --> 00:06:48,560
而且这个符号都是这个斜线还变

174
00:06:48,560 --> 00:06:49,330
为什么呢

175
00:06:49,330 --> 00:06:51,090
其实关键点在于这个地方啊

176
00:06:51,090 --> 00:06:53,090
这个地方你看它是 LN 减一

177
00:06:53,090 --> 00:06:53,490
也就是说

178
00:06:53,490 --> 00:06:57,000
它实际上把最后那一集目录排除在外了

179
00:06:57,000 --> 00:06:58,830
也就是说我取的实际上是什么

180
00:06:58,830 --> 00:07:00,340
实际上是我的父目录

181
00:07:00,340 --> 00:07:03,420
也就是说你传在这个 pass 可能是 A 斜杠

182
00:07:03,420 --> 00:07:04,310
B 斜杠 C 

183
00:07:04,310 --> 00:07:07,840
那么呢，这个 parent pass 就是 A 4杠 B 啊

184
00:07:07,840 --> 00:07:10,180
他把最后那一集 C 给留出来了

185
00:07:10,180 --> 00:07:13,280
好，拿到这个 parent pass 之后啊

186
00:07:13,280 --> 00:07:17,450
他先去获得这个 parent pass 的一些相关信息

187
00:07:17,450 --> 00:07:18,950
通过这个 info 信息呢

188
00:07:18,950 --> 00:07:21,330
它来判断一下这个副路径啊

189
00:07:21,330 --> 00:07:22,350
它是否存在

190
00:07:22,350 --> 00:07:25,670
如果说副路径压根儿就不存在的话

191
00:07:25,670 --> 00:07:27,730
也就是 A 4杠 B 这个路径

192
00:07:27,730 --> 00:07:29,070
如果不存在的话呢

193
00:07:29,070 --> 00:07:30,350
它第40行啊

194
00:07:30,350 --> 00:07:32,330
他要去创建这样一个路径

195
00:07:32,330 --> 00:07:34,950
好，只要这个路径创建好之后啊

196
00:07:34,950 --> 00:07:35,810
就没事了

197
00:07:35,810 --> 00:07:37,000
最后内地 C 

198
00:07:37,000 --> 00:07:38,480
我们这个 dB 呢

199
00:07:38,480 --> 00:07:40,960
它会自己就是你不管是扔给 badger 也好

200
00:07:40,960 --> 00:07:42,000
还是扔给 bot 也好

201
00:07:42,000 --> 00:07:43,200
他会自己去创建

202
00:07:43,200 --> 00:07:44,620
但是父目录他不管

203
00:07:44,620 --> 00:07:48,057
所以呢，你需要事先把这个父目录给创建好

204
00:07:48,057 --> 00:07:50,850
那如果说这个父目录存在啊

205
00:07:50,850 --> 00:07:52,030
A 斜杠 B 存在

206
00:07:52,030 --> 00:07:54,170
但是 A 斜杠 B 啊

207
00:07:54,170 --> 00:07:56,110
就那个 B 它不是个目录啊

208
00:07:56,110 --> 00:07:57,310
它是个文件啊

209
00:07:57,310 --> 00:07:58,110
这样也不行

210
00:07:58,110 --> 00:07:58,830
所以呢

211
00:07:58,830 --> 00:08:02,050
我们需要把这个 A 斜杠 B 这个文件对吧

212
00:08:02,050 --> 00:08:03,430
这个文件给删掉

213
00:08:04,800 --> 00:08:05,200
好

214
00:08:05,200 --> 00:08:06,840
以前都是对这个目录的一些

215
00:08:06,840 --> 00:08:08,675
预先的准备工作

216
00:08:08,675 --> 00:08:12,180
后面呢，我们就搞了一个 switch case 啊

217
00:08:12,180 --> 00:08:13,980
根据你传进来的参数

218
00:08:13,980 --> 00:08:16,380
我来判断我到底应该创建哪一种

219
00:08:16,380 --> 00:08:19,320
具体的实现本来就只有两种吧

220
00:08:19,320 --> 00:08:20,740
要么是这个 badger 

221
00:08:20,740 --> 00:08:22,290
要么是 boot 

222
00:08:22,290 --> 00:08:23,580
那么我们这里面呢

223
00:08:23,580 --> 00:08:26,420
我们默认使用的是这个 bot 

224
00:08:27,610 --> 00:08:30,390
所以 bot 是在 default 里面

225
00:08:30,390 --> 00:08:31,830
如果是白纸的话

226
00:08:31,830 --> 00:08:33,760
那么我通过这个 new 对吧

227
00:08:33,760 --> 00:08:36,285
创建一个白纸的具体实例

228
00:08:36,285 --> 00:08:38,730
哎，这边 with it pass 啊

229
00:08:38,730 --> 00:08:39,309
这个是什么

230
00:08:39,309 --> 00:08:42,270
这个其实就是我们之前讲过的那个 builder 模式

231
00:08:42,270 --> 00:08:45,660
就是说呃，我并没有正常情况

232
00:08:45,660 --> 00:08:47,580
我可能是通过什么 dB 对吧

233
00:08:47,580 --> 00:08:49,180
然后它的一个

234
00:08:49,180 --> 00:08:50,167
呃

235
00:08:50,167 --> 00:08:51,470
某一个属性啊

236
00:08:51,470 --> 00:08:55,010
某一个属性 field 等于什么什么给它赋值嘛

237
00:08:55,010 --> 00:08:58,030
但这个地方呢，我是通过这个 with 啊

238
00:08:58,030 --> 00:08:58,990
给它赋值的

239
00:09:00,240 --> 00:09:02,820
那通过 V 子赋值有什么好处呢

240
00:09:02,820 --> 00:09:03,800
就是这个位子啊

241
00:09:03,800 --> 00:09:07,160
他返回的还是这个结构体本身

242
00:09:07,160 --> 00:09:09,730
这样的话你后面还可以根啊

243
00:09:09,730 --> 00:09:11,130
还可以跟位置对吧

244
00:09:12,170 --> 00:09:14,985
很多位置构成一个链条嘛

245
00:09:14,985 --> 00:09:18,480
那么比较明显的就是下面这个 boot 

246
00:09:18,480 --> 00:09:20,890
你看这个 bot 也是通过 new 

247
00:09:20,890 --> 00:09:23,380
先创建一个空的示例

248
00:09:23,380 --> 00:09:26,190
然后呢，通过什么 with pass 啊

249
00:09:26,190 --> 00:09:27,790
给某一个属性赋值

250
00:09:27,790 --> 00:09:31,442
然后呢， with bucket 给另外一个属性赋值

251
00:09:31,442 --> 00:09:35,020
就是我们之前讲过的 builder 生成器模式

252
00:09:36,210 --> 00:09:37,950
把实例创建好之后

253
00:09:37,950 --> 00:09:40,547
还需要调用这个 open 函数

254
00:09:40,547 --> 00:09:42,580
把那个数据库给打开

255
00:09:42,580 --> 00:09:44,520
最后呢，返回这个 dB 

256
00:09:44,520 --> 00:09:45,840
那这个 open 呢

257
00:09:45,840 --> 00:09:48,840
是我们在接口里面规定好的

258
00:09:48,840 --> 00:09:51,467
必须有 open 这样一个函数

259
00:09:51,467 --> 00:09:53,970
所以这里面这个 dB 呢，它是什么

260
00:09:53,970 --> 00:09:57,210
它是这样一种接口的类型

261
00:09:57,210 --> 00:10:00,270
那么我自然是可以调用这个 open 函数嘛

262
00:10:01,370 --> 00:10:02,970
我在给 DV 复制的时候

263
00:10:02,970 --> 00:10:05,370
它既可以用 badger 这种具体类型

264
00:10:05,370 --> 00:10:07,770
也可以使用 boot 这种具体类型

265
00:10:07,770 --> 00:10:10,760
最后我们还是来总结一下这个工厂模式

266
00:10:10,760 --> 00:10:14,650
你应该把它跟构造器对比来看

267
00:10:16,170 --> 00:10:18,850
你应该把它跟构造函数对比来看

268
00:10:18,850 --> 00:10:19,850
构造函数

269
00:10:19,850 --> 00:10:24,170
它只是构造某一种特定结构体的实例

270
00:10:24,170 --> 00:10:25,560
而工厂模式呢

271
00:10:25,560 --> 00:10:29,550
则是可以生成多种不同的结构体

272
00:10:29,550 --> 00:10:31,090
只不过所有的结构体

273
00:10:31,090 --> 00:10:34,170
它们都满足同一个接口

274
00:10:34,170 --> 00:10:36,350
所以呢，在使用工厂模式的时候

275
00:10:36,350 --> 00:10:39,880
你肯定你肯定得先定义一个接口啊

276
00:10:39,880 --> 00:10:44,280
然后再搞这个接口的若干种实现

277
00:10:44,280 --> 00:10:46,460
那么工厂模式这个函数

278
00:10:46,460 --> 00:10:48,400
它返回的就是这个接口

279
00:10:48,400 --> 00:10:50,320
具体生产哪一种产

280
00:10:50,320 --> 00:10:53,775
是通过入餐来决定的

281
00:10:53,775 --> 00:10:55,900
那在这个工厂模式内部呢

282
00:10:55,900 --> 00:10:59,380
肯定会存在这样的分支语句的判断
