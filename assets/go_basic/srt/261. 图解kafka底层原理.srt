1
00:00:00,520 --> 00:00:01,800
讲完 rabbit time q 

2
00:00:01,800 --> 00:00:03,200
我们来讲一讲 KAFKA 

3
00:00:03,200 --> 00:00:03,720
一边讲呢

4
00:00:03,720 --> 00:00:06,707
我们一边跟 rabbit time q 进行一个对比

5
00:00:06,707 --> 00:00:09,610
那么卡夫卡泰也是一个消息队列模型嘛

6
00:00:09,610 --> 00:00:12,530
所以这边也是一个由三方构成啊

7
00:00:12,530 --> 00:00:16,877
由生产方、消费端和我们的 broker server 端

8
00:00:16,877 --> 00:00:19,860
那么三方每一方它都是一个集群

9
00:00:19,860 --> 00:00:21,140
都使用多台服务器的

10
00:00:21,140 --> 00:00:22,400
那每一台服务器呢

11
00:00:22,400 --> 00:00:24,380
都是可增可减啊

12
00:00:24,380 --> 00:00:27,040
它是任何一方扩展服务器

13
00:00:27,040 --> 00:00:28,360
不影响其他两方

14
00:00:28,360 --> 00:00:30,910
跟 rob w m q 里面的概念类似啊

15
00:00:30,910 --> 00:00:32,689
那么 server 端这边的话

16
00:00:32,689 --> 00:00:36,260
KAFKA 里面每台服务器我们可以称之为一个 server 

17
00:00:36,260 --> 00:00:38,370
或者我们也可以称之为一个 broker 

18
00:00:38,370 --> 00:00:39,800
那其实严格来讲

19
00:00:39,800 --> 00:00:42,800
一个 server 它并不等价于一个 broker 

20
00:00:42,800 --> 00:00:43,830
看这张图

21
00:00:43,830 --> 00:00:47,450
在我们的 KAFKA 集群里面呢，有多个 broker 

22
00:00:47,450 --> 00:00:48,670
那通常情况下

23
00:00:48,670 --> 00:00:51,640
每个 broker 它是部署在一个机器上面的

24
00:00:51,640 --> 00:00:54,300
在一个机器上面起了一个卡夫卡进程

25
00:00:54,300 --> 00:00:55,780
但严格上来讲

26
00:00:55,780 --> 00:01:00,290
我完全可以在一台服务器上起多个 broker 进程

27
00:01:00,290 --> 00:01:02,850
只要他们占用不同的端口号就可以了

28
00:01:02,850 --> 00:01:04,480
只不过我们通常不这么干

29
00:01:04,480 --> 00:01:05,910
那一个 broker 内部呢

30
00:01:05,910 --> 00:01:08,932
我们发现它又分成多个 topic 

31
00:01:08,932 --> 00:01:10,680
那 topic 是什么概念呢

32
00:01:10,680 --> 00:01:13,580
topic 就相当于是某一个业务数据

33
00:01:13,580 --> 00:01:17,570
那就类比于我们 rap w m q 里面的一个队列

34
00:01:17,570 --> 00:01:19,170
因为一个队列里面存储的

35
00:01:19,170 --> 00:01:21,590
就是某一类已有数据嘛

36
00:01:21,590 --> 00:01:24,030
那即使是对同一个 topic 

37
00:01:24,030 --> 00:01:25,710
比方说 topic a 而言

38
00:01:25,710 --> 00:01:28,190
它又分成了多个 partition 

39
00:01:28,190 --> 00:01:30,760
就好比是个队列很长

40
00:01:30,760 --> 00:01:34,257
我呢，把它切分成了很多个小的队列

41
00:01:34,257 --> 00:01:37,090
所以呢，分成了 partition 0和 partition 1

42
00:01:37,090 --> 00:01:38,150
partition 2等等

43
00:01:38,150 --> 00:01:40,937
每一个 partition 是一个小的队列

44
00:01:40,937 --> 00:01:44,060
即使说对同一个 topic 的同一个 partition 

45
00:01:44,060 --> 00:01:46,160
它又有多个备份啊

46
00:01:46,160 --> 00:01:49,100
分为 leader 和 follower 备份嘛

47
00:01:49,100 --> 00:01:50,840
主要是为了容灾

48
00:01:50,840 --> 00:01:53,350
一旦某一个 broker 挂了

49
00:01:53,350 --> 00:01:57,800
那么另外的 broker 上面还存储着相同的数据

50
00:01:57,800 --> 00:01:59,650
这样的话，数据不至于丢失吗

51
00:01:59,650 --> 00:02:02,860
那把一个 topic 划分成多个 partition 

52
00:02:02,860 --> 00:02:03,940
用意何在呢

53
00:02:03,940 --> 00:02:06,760
这个主要是为了方便消费的时候

54
00:02:06,760 --> 00:02:07,760
服务的均衡

55
00:02:07,760 --> 00:02:08,960
我们继续往后看

56
00:02:08,960 --> 00:02:12,800
比方说 topic a 划分成了五五段啊

57
00:02:12,800 --> 00:02:13,882
五个 partition 

58
00:02:13,882 --> 00:02:14,610
当然了

59
00:02:14,610 --> 00:02:16,170
每个 partition 里面的数据呢

60
00:02:16,170 --> 00:02:18,110
是没有任何交集的

61
00:02:18,110 --> 00:02:21,110
这边 consumer 呢，又分成了两个组

62
00:02:21,110 --> 00:02:22,690
这个组是什么意思呢

63
00:02:22,690 --> 00:02:25,270
比方说有一份业务数据

64
00:02:25,270 --> 00:02:26,510
有一个 topic 

65
00:02:26,510 --> 00:02:32,420
它存储的是某个 app 上面用户的点击数据啊

66
00:02:32,420 --> 00:02:35,160
这个用户 A 他点击过哪几个商品

67
00:02:35,160 --> 00:02:36,302
这样的数据

68
00:02:36,302 --> 00:02:37,900
那这一份数据呢

69
00:02:37,900 --> 00:02:41,200
推荐团队和广告团队都需要

70
00:02:41,200 --> 00:02:43,920
因为他们需要根据这份数据来刻画

71
00:02:43,920 --> 00:02:46,880
用户的喜好、用户画像来进行推荐

72
00:02:46,880 --> 00:02:48,907
来进行广告的展示

73
00:02:48,907 --> 00:02:53,050
所以呢，两个团队他们分别是两个 group 

74
00:02:53,050 --> 00:02:57,860
而每个 group 内部又可以起多台 consumer 进程

75
00:02:57,860 --> 00:03:00,700
比方说组一内部写两个 consumer 

76
00:03:00,700 --> 00:03:03,300
那这两个 consumer 他们就去评分

77
00:03:03,300 --> 00:03:05,522
topic a 里面的所有数据

78
00:03:05,522 --> 00:03:08,970
而 topic 给分成了五个 partition 

79
00:03:08,970 --> 00:03:12,730
那么可能其中某两个 partition 给了 consumer 1

80
00:03:12,730 --> 00:03:15,410
另外三个 partition 给了 consumer 2

81
00:03:15,410 --> 00:03:16,920
对于组二而言

82
00:03:16,920 --> 00:03:17,865
勾数二

83
00:03:17,865 --> 00:03:21,940
他又要把整个 topic 数据完整的再消费一次

84
00:03:21,940 --> 00:03:23,280
这里面我们注意一下

85
00:03:23,280 --> 00:03:27,540
一个 partition 它只能分配给某一个 consumer 

86
00:03:27,540 --> 00:03:30,020
不可能说这一个 partition 里面

87
00:03:30,020 --> 00:03:32,020
一部分数据给了 consumer 1

88
00:03:32,020 --> 00:03:33,960
另外一部分数据给了 consumer 2

89
00:03:33,960 --> 00:03:38,100
partition 是给 consumer 分配数据的最小单元啊

90
00:03:38,100 --> 00:03:40,320
这个跟 rap and q 它很不一样

91
00:03:40,320 --> 00:03:43,980
rap mq 它是以小企为单位

92
00:03:43,980 --> 00:03:47,330
采用轮询的方式给 consumer 进行分配

93
00:03:47,330 --> 00:03:51,440
这边呢，是按照 partition 给 consumer 进行分配

94
00:03:51,440 --> 00:03:55,820
那所以说 consumer 的数目小于他证书的时候

95
00:03:55,820 --> 00:03:56,740
才是有意义的

96
00:03:56,740 --> 00:03:59,500
如果 consumer 数目比 partition 数目还要多

97
00:03:59,500 --> 00:04:03,840
那必然就是 consumer 它是取不到任何 partition 的

98
00:04:03,840 --> 00:04:04,850
接收不到数据

99
00:04:04,850 --> 00:04:07,500
那显然这个 partition 数目越多

100
00:04:07,500 --> 00:04:08,560
分得越细

101
00:04:08,560 --> 00:04:12,190
那么这个 consumer 的负载均衡就可以做的越好

102
00:04:12,190 --> 00:04:14,800
我们一般在创建这个 topic 的时候

103
00:04:14,800 --> 00:04:18,060
就需要指定你打算开辟多少个 partition 

104
00:04:18,060 --> 00:04:20,470
那尽量呢， PARTIER 数目搞的多一点

105
00:04:20,470 --> 00:04:23,510
consumer 这边他可能会动态的增加或减少

106
00:04:23,510 --> 00:04:26,010
那 consumer 数目每次发生变化时

107
00:04:26,010 --> 00:04:27,750
partition 和 consumer 之间

108
00:04:27,750 --> 00:04:31,260
这种配对关系就会自动的发生调整

109
00:04:31,260 --> 00:04:34,470
以确保每一个 consumer 都大致分得

110
00:04:34,470 --> 00:04:36,250
数目相当的 party 人

111
00:04:36,250 --> 00:04:37,190
如果有必要的话

112
00:04:37,190 --> 00:04:40,050
我们的 consumer 可以显示的指定

113
00:04:40,050 --> 00:04:42,270
我叫消费哪一个 partition 

114
00:04:42,270 --> 00:04:44,720
不使用它默认的负载均衡算

115
00:04:44,720 --> 00:04:47,740
但这样的话你就不能指定 group id 了

116
00:04:47,740 --> 00:04:49,120
也就是说所有的 consumer 

117
00:04:49,120 --> 00:04:51,320
它们实际上属于同一个 group 

118
00:04:51,320 --> 00:04:52,760
来看一下四种情况

119
00:04:52,760 --> 00:04:56,370
那最开始呢，我们的 consumer 是少于 partition 的

120
00:04:56,370 --> 00:04:57,260
这样的话

121
00:04:57,260 --> 00:05:01,180
多个 partition 会分配到同一个 consumer 上面去

122
00:05:01,180 --> 00:05:04,270
那随着 consumer 数量的增加

123
00:05:04,270 --> 00:05:07,870
那 partition 和 consumer 的对应关系会发生调整

124
00:05:07,870 --> 00:05:10,530
自动的去执行复杂均衡

125
00:05:10,530 --> 00:05:13,710
如果 consumer 数目多于 past tier 数目了

126
00:05:13,710 --> 00:05:17,010
那必然就会有的 consumer 分不到 partition 

127
00:05:17,010 --> 00:05:20,340
通常呢，是那个最先创建的老的 consumer 

128
00:05:20,340 --> 00:05:21,500
他得不到数据

129
00:05:21,500 --> 00:05:22,680
最后一张图演示

130
00:05:22,680 --> 00:05:26,700
consumer 1呢，它强制指定我叫消费他便宜

131
00:05:26,700 --> 00:05:27,947
那这样的话

132
00:05:27,947 --> 00:05:31,300
consumer 2和三就会有一台拿不到 partition 

133
00:05:31,300 --> 00:05:32,920
在一个 partition 内部

134
00:05:32,920 --> 00:05:36,200
所有的消息是按照时间顺序写进来的

135
00:05:36,200 --> 00:05:38,700
这个表格里面第一行是时间戳

136
00:05:38,700 --> 00:05:41,350
这个时间戳是依次增大的

137
00:05:41,350 --> 00:05:42,840
时间往后推移

138
00:05:42,840 --> 00:05:47,110
那对应的偏移量就是消息写进堆里面的偏量

139
00:05:47,110 --> 00:05:48,580
也是逐渐增大的

140
00:05:48,580 --> 00:05:52,100
偏量越大代表着消息越新

141
00:05:52,100 --> 00:05:53,740
它越晚到来

142
00:05:53,740 --> 00:05:56,260
但这只是在一个 partition 内部

143
00:05:56,260 --> 00:05:59,340
可以保证消息是有序存放的

144
00:05:59,340 --> 00:06:01,570
但是跨 partition 的话

145
00:06:01,570 --> 00:06:06,870
你根据 offset 没法去判断哪个消息是先来的

146
00:06:06,870 --> 00:06:07,510
哪个是后来的

147
00:06:07,510 --> 00:06:11,190
我们的 consumer 在每次消费完一条消下去之后

148
00:06:11,190 --> 00:06:13,780
它可以向这个卡夫卡呢

149
00:06:13,780 --> 00:06:15,940
进行一次 commit 上报啊

150
00:06:15,940 --> 00:06:19,100
这个上报就等价于我们在 rabbit and q 里面的

151
00:06:19,100 --> 00:06:19,760
a c k 

152
00:06:19,760 --> 00:06:23,970
确认他当前消费的消息的 offset 是多少呢

153
00:06:23,970 --> 00:06:27,110
我们的 consumer 上报给 kafka cluster 

154
00:06:27,110 --> 00:06:28,590
为什么需要上报呢

155
00:06:28,590 --> 00:06:32,480
这样的话，下一次假如说我们的 consumer 中途停止了

156
00:06:32,480 --> 00:06:35,317
那么下一次当 consumer 重启的时候

157
00:06:35,317 --> 00:06:37,130
我们的卡夫卡就知

158
00:06:37,130 --> 00:06:38,780
我应该从什么位置

159
00:06:38,780 --> 00:06:40,790
给你推送新的消息

160
00:06:40,790 --> 00:06:41,450
当然了

161
00:06:41,450 --> 00:06:44,450
如果每次消费一条都要去执行 commit it 

162
00:06:44,450 --> 00:06:47,230
那显然会加大网络延时

163
00:06:47,230 --> 00:06:50,040
那么呢，我们也可以选择批量的 commit 

164
00:06:50,040 --> 00:06:53,260
比方说每消费50条 COMMITT 一次

165
00:06:53,260 --> 00:06:56,327
或者说每隔五秒钟 commit 一次

166
00:06:56,327 --> 00:07:00,870
这个就类似于 rabbit DQ 里面的 P 2 ASK 

167
00:07:00,870 --> 00:07:03,510
就是把那个 ACK 函数的参数置为 true 

168
00:07:03,510 --> 00:07:07,690
就可以把之前没有 TK 过的全部 CK 掉

169
00:07:07,690 --> 00:07:08,920
讲完消费方式

170
00:07:08,920 --> 00:07:10,520
我们再来看一下生产的方式

171
00:07:10,520 --> 00:07:14,180
生产方他要生产某一个 topic 下面的数据

172
00:07:14,180 --> 00:07:17,770
那这个 topic 是分成了多个 partition 

173
00:07:17,770 --> 00:07:19,090
就是一个长的队列

174
00:07:19,090 --> 00:07:21,150
分成了多个短的队列

175
00:07:21,150 --> 00:07:23,260
那一条新的消息

176
00:07:23,260 --> 00:07:26,890
他怎么知道需要写到哪一个 partition 里面去呢

177
00:07:26,890 --> 00:07:28,580
啊，这边有几种方法

178
00:07:28,580 --> 00:07:29,240
第一种呢

179
00:07:29,240 --> 00:07:31,860
我们的 producer 可以显示的指定啊

180
00:07:31,860 --> 00:07:34,307
我就要写到某一个 partition 里面去

181
00:07:34,307 --> 00:07:36,250
在我们的 rap w q 里面

182
00:07:36,250 --> 00:07:38,260
不存在它提升这个概念

183
00:07:38,260 --> 00:07:39,940
所以呢，生产消息的时

184
00:07:39,940 --> 00:07:41,680
只需要指定队列就可以了

185
00:07:41,680 --> 00:07:43,840
就好比是指定里面的 topic 

186
00:07:43,840 --> 00:07:44,820
第二种方式呢

187
00:07:44,820 --> 00:07:48,280
那么每一个消息可以指定一个 K 啊

188
00:07:48,280 --> 00:07:51,240
这个 K 跟 JAVISMQ 里面 K 很类似

189
00:07:51,240 --> 00:07:53,420
都是负责实现路由

190
00:07:53,420 --> 00:07:55,770
实现负载均衡的功能

191
00:07:55,770 --> 00:07:59,030
这边呢，它会根据小写的 K 执行一次哈希

192
00:07:59,030 --> 00:08:01,330
通过哈希的结果来确定，诶

193
00:08:01,330 --> 00:08:03,490
我要写到哪一个 partition 里面去

194
00:08:03,490 --> 00:08:05,790
这就意味着相同的 key 

195
00:08:05,790 --> 00:08:09,000
它会被写到相同的 partition 里面去

196
00:08:09,000 --> 00:08:10,320
这一点是很有用的

197
00:08:10,320 --> 00:08:12,160
就是如果你需要保证

198
00:08:12,160 --> 00:08:14,450
某些消息的顺序性的话

199
00:08:14,450 --> 00:08:17,170
那么呢，你就让它们具备相同的 K 

200
00:08:17,170 --> 00:08:20,820
这样的话他们就会被写入到相同的 party 里面去

201
00:08:20,820 --> 00:08:23,920
一个爬线里面可以保证消息的有序性

202
00:08:23,920 --> 00:08:28,000
我们在 JAVWQ 里面是根据 K 来决定

203
00:08:28,000 --> 00:08:31,560
交换机要把这个消息发送给哪一个队列

204
00:08:31,560 --> 00:08:32,809
第三种方式

205
00:08:32,809 --> 00:08:35,630
如果 producer 它既没有指定 partition 

206
00:08:35,630 --> 00:08:38,530
他也没有给消息分贝 K 的话

207
00:08:38,530 --> 00:08:41,830
那么这个时候就按照时间片轮询的方式

208
00:08:41,830 --> 00:08:45,750
就是说这一秒钟我把消息呢都写入 partition 0

209
00:08:45,750 --> 00:08:48,110
下一秒钟把消息都写入 party 神一

210
00:08:48,110 --> 00:08:49,240
前面我们讲过

211
00:08:49,240 --> 00:08:52,720
即使对于同一个 topic 下面相同的 partition 

212
00:08:52,720 --> 00:08:54,367
它又有多个备份

213
00:08:54,367 --> 00:08:56,800
一个 leader 和多个 follower 

214
00:08:56,800 --> 00:08:58,810
那么我们的生产方 producer 

215
00:08:58,810 --> 00:09:02,630
他会去询问 kafka cluster 哪个是 leader 

216
00:09:02,630 --> 00:09:04,970
然后呢，他直接把这个消息呢

217
00:09:04,970 --> 00:09:06,150
写给这个 leader 

218
00:09:06,150 --> 00:09:09,715
那 leader 把这个数据先写入本地磁盘

219
00:09:09,715 --> 00:09:13,730
同时他的 follower 也会去拉取这个数据

220
00:09:13,730 --> 00:09:16,300
也写到 follower 对应的磁盘里面去

221
00:09:16,300 --> 00:09:17,130
那卡夫卡

222
00:09:17,130 --> 00:09:20,290
它默认是会把数据写到磁盘里面去的

223
00:09:20,290 --> 00:09:21,550
而 raid mq 的话

224
00:09:21,550 --> 00:09:23,550
它默认是不写磁盘的

225
00:09:23,550 --> 00:09:25,800
所以导致 rap DQ 会更快一些

226
00:09:25,800 --> 00:09:28,350
我们的 follower 把数据落盘之后

227
00:09:28,350 --> 00:09:31,170
会给 leader 发送一条 ACK 消息

228
00:09:31,170 --> 00:09:34,925
那 leader 呢，他发现所有的 follower 都已经 ACK 了

229
00:09:34,925 --> 00:09:38,410
这个时候他才会去给 producer 发数据给自己

230
00:09:38,410 --> 00:09:42,590
可以告诉 producer 这条数据真正的写入成功了

231
00:09:42,590 --> 00:09:45,450
那由于这个里面有多个 ACK 

232
00:09:45,450 --> 00:09:47,440
多个网络延时

233
00:09:47,440 --> 00:09:50,200
所以呢，显著条消息还是很慢的

234
00:09:50,200 --> 00:09:51,320
为了提升速度

235
00:09:51,320 --> 00:09:54,280
我们也可以选择不需要这里面的 ACK 

236
00:09:54,280 --> 00:09:57,450
就说 producer 不需要等待这些 SK 

237
00:09:57,450 --> 00:10:00,510
它就可以直接执行下一次的消息

238
00:10:00,510 --> 00:10:02,590
结果我们在后面讲 go 代码的时候

239
00:10:02,590 --> 00:10:04,230
可以看到这方面的设置
