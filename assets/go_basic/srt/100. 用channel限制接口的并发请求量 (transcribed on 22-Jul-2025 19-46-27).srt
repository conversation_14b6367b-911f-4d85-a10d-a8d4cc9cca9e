1
00:00:00,000 --> 00:00:04,060
在微复的框架当中我们会写很多接口

2
00:00:04,060 --> 00:00:07,860
那这些接口呢可能要限制一下它的一个并发请求度

3
00:00:07,860 --> 00:00:08,580
为什么呢

4
00:00:08,580 --> 00:00:12,160
因为一个接口内部它可能会集成大量的计算

5
00:00:12,160 --> 00:00:14,120
这样的话呢会消耗很多CPU

6
00:00:14,120 --> 00:00:16,580
那如果瞬间并发度太高的话

7
00:00:16,580 --> 00:00:18,340
你的CPU复载就会很高

8
00:00:18,340 --> 00:00:20,940
也可能呢是因为在接口内部

9
00:00:20,940 --> 00:00:25,380
它创建了很多临时的这种比较大的切片或者卖法

10
00:00:25,380 --> 00:00:27,560
导致呢比较消耗内存

11
00:00:27,560 --> 00:00:30,480
所以呢要限制最高的一个并发请求量

12
00:00:30,480 --> 00:00:32,780
也可能是因为在接口内部

13
00:00:32,780 --> 00:00:36,380
它要去调用Redis啊读写MyCycle啊

14
00:00:36,380 --> 00:00:37,400
那这样的话呢

15
00:00:37,400 --> 00:00:39,240
如果接口并发度高

16
00:00:39,240 --> 00:00:42,120
那么意味着MyCycle那边的并发压力就会很高

17
00:00:42,120 --> 00:00:42,800
好

18
00:00:42,800 --> 00:00:44,220
所以出于种种原因吧

19
00:00:44,220 --> 00:00:48,120
我们要限制一下它的一个瞬间的最高并发请求量

20
00:00:48,120 --> 00:00:52,040
比方说这边我就模拟了一个接口

21
00:00:52,040 --> 00:00:52,760
Handler

22
00:00:52,760 --> 00:00:57,120
用一个Time and Sleep来模拟它正常的EO逻辑

23
00:00:57,120 --> 00:00:57,800
好

24
00:00:57,800 --> 00:00:59,640
在命运函数里面呢

25
00:00:59,640 --> 00:01:00,800
我们是模拟

26
00:01:00,800 --> 00:01:05,020
模拟假如说有一千个用户同时来请求这个接口

27
00:01:05,020 --> 00:01:06,080
对吧

28
00:01:06,080 --> 00:01:08,340
然后呢就开一千个牺牲嘛

29
00:01:08,340 --> 00:01:08,900
来模拟

30
00:01:08,900 --> 00:01:09,360
好

31
00:01:09,360 --> 00:01:09,940
最终呢

32
00:01:09,940 --> 00:01:11,700
等待所有的仔牺牲结束

33
00:01:11,700 --> 00:01:16,880
如果我们想限制这个Handler的一个并发请求度的话

34
00:01:16,880 --> 00:01:18,680
那么你必然要什么

35
00:01:18,680 --> 00:01:20,900
必然要牺牲用户的体验

36
00:01:20,900 --> 00:01:23,100
因为用户啊

37
00:01:23,100 --> 00:01:24,840
他同时做用户来访问

38
00:01:24,840 --> 00:01:27,360
这个不是我们所能决定的对吧

39
00:01:27,360 --> 00:01:29,360
那用户就是一千个人同时来了

40
00:01:29,360 --> 00:01:29,840
那么呢

41
00:01:29,840 --> 00:01:30,640
如果你非要

42
00:01:30,640 --> 00:01:34,740
比方说你非要把这个Handler的并发度限制在100的话

43
00:01:34,740 --> 00:01:38,160
那必然后面的900个人他就要等待对吧

44
00:01:38,160 --> 00:01:40,080
等待被处理嘛

45
00:01:40,080 --> 00:01:40,679
好

46
00:01:40,679 --> 00:01:42,479
出于系统保护的原因啊

47
00:01:42,479 --> 00:01:45,179
我们还是决定限制一下这个并发度

48
00:01:45,179 --> 00:01:46,039
怎么限制呢

49
00:01:46,039 --> 00:01:49,560
可以通过Channel的阻塞机制来实现

50
00:01:49,560 --> 00:01:52,679
比方说我这边定一个Channel啊

51
00:01:52,679 --> 00:01:53,679
它等于

52
00:01:53,679 --> 00:01:54,840
Make

53
00:01:54,840 --> 00:01:56,560
Channel

54
00:01:56,560 --> 00:01:56,679
好

55
00:01:56,679 --> 00:01:58,800
Channel里面放什么元素

56
00:01:58,800 --> 00:01:59,360
无所谓啊

57
00:01:59,360 --> 00:02:00,039
不重要

58
00:02:00,039 --> 00:02:01,160
我们重点是6

59
00:02:01,160 --> 00:02:02,479
它的阻塞机制嘛

60
00:02:02,479 --> 00:02:04,840
所以的话我们就放空结构体

61
00:02:04,840 --> 00:02:07,840
比方说100啊

62
00:02:07,840 --> 00:02:10,240
就是你想把并发度限制在100

63
00:02:10,240 --> 00:02:13,240
那么每次这个Handler进来的时候啊

64
00:02:13,240 --> 00:02:14,920
你先把这个QPS里面

65
00:02:14,920 --> 00:02:16,680
你先给它放入一个元素

66
00:02:16,680 --> 00:02:19,480
空结构体

67
00:02:19,480 --> 00:02:21,600
好

68
00:02:21,600 --> 00:02:22,120
然后呢

69
00:02:22,120 --> 00:02:24,000
这个执行完之后啊

70
00:02:24,000 --> 00:02:24,480
最后的话

71
00:02:24,480 --> 00:02:25,480
你再取走一个元素

72
00:02:25,480 --> 00:02:26,800
放到Differ里面

73
00:02:26,800 --> 00:02:27,520
好

74
00:02:27,520 --> 00:02:30,600
这样的话就意味着前100个用户啊

75
00:02:30,600 --> 00:02:31,640
他们如果来了

76
00:02:31,640 --> 00:02:32,280
那他们呢

77
00:02:32,280 --> 00:02:34,820
可以顺利的往Channel里面去放入一边

78
00:02:34,820 --> 00:02:37,859
然后可以顺利的执行后面的逻辑代码

79
00:02:37,859 --> 00:02:40,620
第一执的一个用户来了之后

80
00:02:40,620 --> 00:02:42,540
他就放不进去了嘛

81
00:02:42,540 --> 00:02:44,140
那就会组织在第11行

82
00:02:44,140 --> 00:02:46,380
就会进不到后面的代码

83
00:02:46,380 --> 00:02:49,019
那么等前面的有人处理完了

84
00:02:49,019 --> 00:02:52,100
他把这个位置给释放出来了

85
00:02:52,100 --> 00:02:52,980
那后面的人呢

86
00:02:52,980 --> 00:02:53,660
他才能够进

87
00:02:53,660 --> 00:02:56,060
这样的话就可以确保在任一个瞬间

88
00:02:56,060 --> 00:03:00,859
那么正常的U代码最多只有一百的并发量

89
00:03:00,860 --> 00:03:14,120
但是我对他要先采放到

90
00:03:14,120 --> 00:03:15,560
这个位置

91
00:03:15,560 --> 00:03:16,280
你会使用冰箱最大方的一遍

92
00:03:16,280 --> 00:03:17,440
就是说

93
00:03:17,440 --> 00:03:18,000
谢谢我们两下方的一遍

