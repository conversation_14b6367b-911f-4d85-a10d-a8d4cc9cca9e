1
00:00:00,000 --> 00:00:04,000
所有的信赏项目100%都需要打日志

2
00:00:04,000 --> 00:00:08,000
日志的话主要是为了帮助我们去快速的定位问题

3
00:00:08,000 --> 00:00:16,000
当然也有些日志会被写到数据库里面去进行一些统计监控或者是右层面的数据分析

4
00:00:16,000 --> 00:00:21,000
今天我们讲一个购物里面非常流行的日志框架logras

5
00:00:21,000 --> 00:00:24,000
先来看这边已经有一个日志文件

6
00:00:24,000 --> 00:00:30,000
它是以年月日包括小时作为文件以后缀的

7
00:00:30,000 --> 00:00:36,000
2025年的2月10号的21点钟生成的这样一份日志文件

8
00:00:36,000 --> 00:00:39,000
这边还有一个不带日期后缀的文件

9
00:00:39,000 --> 00:00:43,000
一般的话不带时间后缀意味着它是一个最新的

10
00:00:43,000 --> 00:00:46,000
时间上最靠后的一个日志文件

11
00:00:46,000 --> 00:00:48,000
这边我们看到的实际上是一个软连接了

12
00:00:48,000 --> 00:00:52,000
因为在vizos上有这样一个拐弯的镜头符号

13
00:00:52,000 --> 00:00:54,000
表示是一个软连接

14
00:00:54,000 --> 00:01:00,000
它实际上链接上的就是我们这个2月10号21点这一份日志文件

15
00:01:00,000 --> 00:01:02,000
我们点开文件看看里面内容

16
00:01:02,000 --> 00:01:04,000
这边有个time时间

17
00:01:04,000 --> 00:01:06,000
这个是到年月日

18
00:01:06,000 --> 00:01:08,000
然后这个是到十分秒

19
00:01:08,000 --> 00:01:10,000
后面底后面还有具体到毫秒

20
00:01:10,000 --> 00:01:12,000
731毫秒

21
00:01:12,000 --> 00:01:14,000
Level等于info

22
00:01:14,000 --> 00:01:18,000
这个我们日志一般都会有一个所谓的级别

23
00:01:18,000 --> 00:01:20,000
最低级别为debug

24
00:01:20,000 --> 00:01:22,000
然后是info

25
00:01:22,000 --> 00:01:24,000
然后是war arrow

26
00:01:24,000 --> 00:01:26,000
等等

27
00:01:26,000 --> 00:01:28,000
不同的日志框架

28
00:01:28,000 --> 00:01:30,000
它这个级别画风可能会略微的有所出入

29
00:01:30,000 --> 00:01:32,000
但大体上都是这样一个顺序

30
00:01:32,000 --> 00:01:34,000
debug info

31
00:01:34,000 --> 00:01:36,000
这一节是具体的日志的内容本身

32
00:01:36,000 --> 00:01:38,000
后面还有一个funk函数

33
00:01:38,000 --> 00:01:40,000
那这个函数来自哪呢

34
00:01:40,000 --> 00:01:44,000
来自于dqqgoframe是我们的model名称

35
00:01:44,000 --> 00:01:48,000
而这个iltest是那个package包名

36
00:01:48,000 --> 00:01:52,000
在这个包下面有一个函数叫做testloglas

37
00:01:52,000 --> 00:01:54,000
那这个打日志这行代码呢

38
00:01:54,000 --> 00:01:56,000
就出自于这个函数

39
00:01:56,000 --> 00:01:58,000
后面还有file

40
00:01:58,000 --> 00:02:00,000
就表示来自于哪个文件

41
00:02:00,000 --> 00:02:02,000
这是文件的完整物件

42
00:02:02,000 --> 00:02:04,000
来自于这个构建的第14行

43
00:02:04,000 --> 00:02:06,000
甚至可以把行号都告诉你

44
00:02:06,000 --> 00:02:08,000
后面还有一些keyvalue

45
00:02:08,000 --> 00:02:10,000
key等于value

46
00:02:10,000 --> 00:02:12,000
然后空格key等于value

47
00:02:12,000 --> 00:02:14,000
在上行之前在代码调试阶段

48
00:02:14,000 --> 00:02:16,000
我们会打印很多的debug日志

49
00:02:16,000 --> 00:02:18,000
比如类似于这种方式

50
00:02:18,000 --> 00:02:20,000
打印很多的debug日志

51
00:02:20,000 --> 00:02:24,000
那么如果说我们在零上限时

52
00:02:24,000 --> 00:02:26,000
把日志的级别改成了info

53
00:02:26,000 --> 00:02:28,000
那么debug它低于info

54
00:02:28,000 --> 00:02:30,000
意味着所有的debug日志

55
00:02:30,000 --> 00:02:34,000
它实际上并不会真正输出

56
00:02:34,000 --> 00:02:38,000
就是说我们这种debug代码不用删不用动

57
00:02:38,000 --> 00:02:42,000
根据级别控制可以自动的把它们给屏蔽掉

58
00:02:42,000 --> 00:02:44,000
看一下loglas具体怎么使用

59
00:02:44,000 --> 00:02:48,000
首先我们需要去goget下载这个第三方库

60
00:02:48,000 --> 00:02:52,000
然后由于需要用到日志的滚动功能

61
00:02:52,000 --> 00:02:54,000
所以还得下载这个第三方库

62
00:02:54,000 --> 00:02:56,000
rotate logs

63
00:02:56,000 --> 00:02:58,000
首先调用loglas这个包下面的new函数

64
00:02:58,000 --> 00:03:00,000
然后先new一个loglas实例

65
00:03:00,000 --> 00:03:06,000
然后打算通过setlevel来设置对应的级别

66
00:03:06,000 --> 00:03:08,000
那这个级别

67
00:03:08,000 --> 00:03:10,000
那这个级别呢我们这边是使用一些枚举

68
00:03:10,000 --> 00:03:12,000
比如debuglevel

69
00:03:12,000 --> 00:03:14,000
infolevel

70
00:03:14,000 --> 00:03:16,000
具体设置为哪个级别呢取决于你传的参数是什么

71
00:03:16,000 --> 00:03:20,000
这边呢我们以字符传来传递对应的级别

72
00:03:20,000 --> 00:03:22,000
把这个参数呢同意转成小写

73
00:03:22,000 --> 00:03:24,000
然后跟每一个级别进行一个对比

74
00:03:24,000 --> 00:03:26,000
如果你设置的级别是wong级别

75
00:03:26,000 --> 00:03:28,000
那么通过debug和通过info

76
00:03:28,000 --> 00:03:30,000
大家的日志都会被频率掉

77
00:03:30,000 --> 00:03:32,000
然后可以设置日志的展现格式

78
00:03:32,000 --> 00:03:34,000
大体上有两种格式

79
00:03:34,000 --> 00:03:36,000
一种呢是普通的文本格式

80
00:03:36,000 --> 00:03:38,000
还有呢是节省格式

81
00:03:38,000 --> 00:03:40,000
刚才给大家看的这种呢是普通的文本格式

82
00:03:40,000 --> 00:03:42,000
对于文本格式

83
00:03:42,000 --> 00:03:44,000
刚才给大家看的这种呢是普通的文本格式

84
00:03:44,000 --> 00:03:46,000
对于文本格式

85
00:03:46,000 --> 00:03:48,000
刚才我们看到了我们可以精确到毫秒

86
00:03:48,000 --> 00:03:54,000
所以这个时间格式啊是通过time stamp format来进行指定的

87
00:03:54,000 --> 00:03:58,000
这边呢有三个零表示是毫秒

88
00:03:58,000 --> 00:04:02,000
这个是构园里面指定年月日十分秒的风格

89
00:04:02,000 --> 00:04:06,000
然后我们可以指定这个日志啊要不要带颜色

90
00:04:06,000 --> 00:04:08,000
false colors等于true表示强制要带颜色

91
00:04:08,000 --> 00:04:12,000
但这个强制带颜色呢也仅在某些特定的中端上面可以生效

92
00:04:12,000 --> 00:04:16,000
在windows的中端上应该是不少带颜色

93
00:04:16,000 --> 00:04:18,000
在windows的中端上应该是不少带颜色

94
00:04:18,000 --> 00:04:20,000
如果你要追求性能

95
00:04:20,000 --> 00:04:22,000
你可以强制把颜色呢把它给关闭掉

96
00:04:22,000 --> 00:04:26,000
disable colors设立会出强制的不带颜色

97
00:04:26,000 --> 00:04:29,000
那我们这次呢把这个普通文本格式给录制掉

98
00:04:29,000 --> 00:04:32,000
我们打开这个阶层格式

99
00:04:32,000 --> 00:04:36,000
阶层格式的话也是指定一下时间的展示格式

100
00:04:36,000 --> 00:04:40,000
接下来我们要指定输出到哪个文件

101
00:04:40,000 --> 00:04:42,000
以及这个文件的滚动策略

102
00:04:42,000 --> 00:04:45,000
我们要用到rotate logs这个第三方库

103
00:04:45,000 --> 00:04:47,000
那么日制文件要滚动

104
00:04:47,000 --> 00:04:51,000
你是每隔多长时间滚动一次呢

105
00:04:51,000 --> 00:04:53,000
是一天还是一小时呢

106
00:04:53,000 --> 00:04:55,000
在大公司里面你的日质量特别大

107
00:04:55,000 --> 00:04:58,000
一小时之内能产生很多日制

108
00:04:58,000 --> 00:05:00,000
你这个日制文件就已经很大了

109
00:05:00,000 --> 00:05:02,000
那么你可以一小时滚动一次

110
00:05:02,000 --> 00:05:03,000
如果你的日量不是那么大

111
00:05:03,000 --> 00:05:05,000
你可以一天滚动一次

112
00:05:05,000 --> 00:05:08,000
比如这边指定了这个rotate time

113
00:05:08,000 --> 00:05:10,000
就是一小时一小时滚动一次

114
00:05:10,000 --> 00:05:12,000
一小时滚动一次

115
00:05:12,000 --> 00:05:14,000
那么对应的这个日制文件的后缀的话

116
00:05:14,000 --> 00:05:18,000
这边就指定到了年月日以及小时

117
00:05:18,000 --> 00:05:20,000
所以这个时间风格呢

118
00:05:20,000 --> 00:05:22,000
又跟其他语言保齐致了

119
00:05:22,000 --> 00:05:24,000
ymd表示年月日

120
00:05:24,000 --> 00:05:26,000
h表示24小时的小时

121
00:05:26,000 --> 00:05:28,000
跟各个人的风格又不一样

122
00:05:28,000 --> 00:05:32,000
这边还有一个with link name

123
00:05:32,000 --> 00:05:34,000
就是它会带一个所谓的软链接吧

124
00:05:34,000 --> 00:05:36,000
就是说本来呢

125
00:05:36,000 --> 00:05:39,000
是不存在这个lograth.log文件的

126
00:05:39,000 --> 00:05:42,000
所有的日制文件都是带时间后缀的

127
00:05:42,000 --> 00:05:46,000
如果你想搞一个不带时间后缀的

128
00:05:46,000 --> 00:05:49,000
那么呢你就加上这样一行代码

129
00:05:49,000 --> 00:05:51,000
with link name

130
00:05:51,000 --> 00:05:55,000
这个软链接它指向的是最后一份最新的日制文件

131
00:05:55,000 --> 00:05:59,000
甚至呢还可以指定一些老的日制文件

132
00:05:59,000 --> 00:06:00,000
自动清除

133
00:06:00,000 --> 00:06:03,000
比如这边它指定的max agent

134
00:06:03,000 --> 00:06:05,000
最长保留时间呢是

135
00:06:05,000 --> 00:06:08,000
7乘以2小时是7天

136
00:06:08,000 --> 00:06:10,000
因为的7天之前的日

137
00:06:10,000 --> 00:06:14,000
它会自动的删掉以节约磁盘空间

138
00:06:14,000 --> 00:06:17,000
通过site output来指定我们的日制呢

139
00:06:17,000 --> 00:06:19,000
就打到这个文件里面去

140
00:06:19,000 --> 00:06:23,000
当然你也可以指定打到这个终端里面去

141
00:06:23,000 --> 00:06:27,000
就是os.svout标准输出

142
00:06:27,000 --> 00:06:29,000
然后呢还可以什么

143
00:06:29,000 --> 00:06:32,000
site report caller就是设置上报

144
00:06:32,000 --> 00:06:35,000
如果你把这个设置为true的话

145
00:06:35,000 --> 00:06:38,000
那么将来在日制里面就会出现

146
00:06:38,000 --> 00:06:42,000
刚从看到的这个funk和file这两项

147
00:06:42,000 --> 00:06:44,000
它会上报这个日制呢

148
00:06:44,000 --> 00:06:47,000
是从哪个文件的哪一行打出来的

149
00:06:47,000 --> 00:06:48,000
如果你没有开启上报

150
00:06:48,000 --> 00:06:51,000
那么这个funk和file是没有的

151
00:06:51,000 --> 00:06:55,000
是没有的最后啊还可以设置一个钩子

152
00:06:55,000 --> 00:06:57,000
钩子有什么意思呢

153
00:06:57,000 --> 00:07:00,000
就是说我们在打印这个日制的时候

154
00:07:00,000 --> 00:07:03,000
它会顺带的执行另外一项工作

155
00:07:03,000 --> 00:07:05,000
那执行什么工作呢

156
00:07:05,000 --> 00:07:07,000
这个是完全可以自定义的

157
00:07:07,000 --> 00:07:09,000
你想执行什么就执行什么

158
00:07:09,000 --> 00:07:12,000
比如说对于file

159
00:07:12,000 --> 00:07:15,000
panic这种非常严重的日制

160
00:07:15,000 --> 00:07:19,000
你希望呢顺带的进行短信报警

161
00:07:19,000 --> 00:07:21,000
或者im机器人报警

162
00:07:21,000 --> 00:07:24,000
或者邮件报警等等

163
00:07:24,000 --> 00:07:26,000
比如对于aero级别的日制

164
00:07:26,000 --> 00:07:30,000
你需要把它顺带的写到出去库里面去

165
00:07:30,000 --> 00:07:34,000
或者呢是打到某一个监控系统里面去等等

166
00:07:34,000 --> 00:07:36,000
那看一下这个钩子如何定义

167
00:07:36,000 --> 00:07:40,000
实际上啊我们需要去实现一个接口

168
00:07:40,000 --> 00:07:42,000
就是实现这个logras hook接口

169
00:07:42,000 --> 00:07:44,000
我们自己呢写一个结构体

170
00:07:44,000 --> 00:07:48,000
那这个结构体要实现这个hook接口

171
00:07:48,000 --> 00:07:50,000
你就需要实现这两个方法

172
00:07:50,000 --> 00:07:52,000
第一个呢是指定levels

173
00:07:52,000 --> 00:07:54,000
就意味着这个钩子啊

174
00:07:54,000 --> 00:07:56,000
它并不是对所有的日制都生效

175
00:07:56,000 --> 00:07:58,000
而只针对特定级别的日制才生效

176
00:07:58,000 --> 00:08:00,000
比如呢我反过来就是这样一个集合

177
00:08:00,000 --> 00:08:02,000
这个勾子啊它只对aero级别

178
00:08:02,000 --> 00:08:04,000
phatal级别和panic这三个级别的日制呢

179
00:08:04,000 --> 00:08:06,000
才生效

180
00:08:06,000 --> 00:08:08,000
如果你想对所有级别都生效

181
00:08:08,000 --> 00:08:10,000
那么你可以直接使用logras.walllevels

182
00:08:10,000 --> 00:08:12,000
好那具体勾子执行什么工作呢

183
00:08:12,000 --> 00:08:14,000
在fair函数里面来进行指定

184
00:08:14,000 --> 00:08:16,000
它参数是这个logras.entry

185
00:08:16,000 --> 00:08:18,000
那么

186
00:08:18,000 --> 00:08:20,000
那具体勾子执行什么工作呢

187
00:08:20,000 --> 00:08:22,000
在fair函数里面来进行指定

188
00:08:22,000 --> 00:08:24,000
它参数是这个logras.entry

189
00:08:24,000 --> 00:08:26,000
那么

190
00:08:26,000 --> 00:08:28,000
根据ntry

191
00:08:28,000 --> 00:08:29,000
那么根据ntry

192
00:08:29,000 --> 00:08:32,000
你就可以拿到这个日制里面的所有内容

193
00:08:32,000 --> 00:08:34,000
比如说

194
00:08:34,000 --> 00:08:36,000
ntry.message

195
00:08:36,000 --> 00:08:38,000
这个message实际上指的就是

196
00:08:38,000 --> 00:08:39,000
这个message

197
00:08:39,000 --> 00:08:41,000
你是可以把这个内容给拿出来的

198
00:08:41,000 --> 00:08:42,000
那拿到这个内容之后

199
00:08:42,000 --> 00:08:44,000
你想干嘛

200
00:08:44,000 --> 00:08:46,000
就完全由你来定

201
00:08:46,000 --> 00:08:48,000
比如说这边我只是简单演示一下

202
00:08:48,000 --> 00:08:50,000
我只是简单的把这个message

203
00:08:50,000 --> 00:08:52,000
输出到终端里面去

204
00:08:52,000 --> 00:08:54,000
你也可以把这个信息呢

205
00:08:54,000 --> 00:08:56,000
存到一个什么数据库里面去

206
00:08:56,000 --> 00:08:58,000
或者是通过报警给发出来

207
00:08:58,000 --> 00:09:00,000
而在第73行

208
00:09:00,000 --> 00:09:02,000
我往这个entry里面呢

209
00:09:02,000 --> 00:09:04,000
又添加了一下数据

210
00:09:04,000 --> 00:09:06,000
这个data

211
00:09:06,000 --> 00:09:08,000
它实际上是一种map

212
00:09:08,000 --> 00:09:09,000
那么我往这个map里面呢

213
00:09:09,000 --> 00:09:10,000
添加了一个keyvalue

214
00:09:10,000 --> 00:09:12,000
key的话就是app

215
00:09:12,000 --> 00:09:14,000
而这个value呢

216
00:09:14,000 --> 00:09:16,000
value是通过这个

217
00:09:16,000 --> 00:09:18,000
结构体的成员变量

218
00:09:18,000 --> 00:09:20,000
成员变量传过来的

219
00:09:20,000 --> 00:09:22,000
而这个成员变量是在什么时候

220
00:09:22,000 --> 00:09:24,000
进复制的

221
00:09:24,000 --> 00:09:26,000
是在这个地方还进行复制的

222
00:09:26,000 --> 00:09:28,000
那既然加了这样一项数据的话

223
00:09:28,000 --> 00:09:32,000
将来在答案出来的日志里面

224
00:09:32,000 --> 00:09:36,000
就会加上app等于dkq

225
00:09:36,000 --> 00:09:38,000
这样一项

226
00:09:38,000 --> 00:09:40,000
比如这边app等于dkq

227
00:09:40,000 --> 00:09:42,000
就是因为我在

228
00:09:42,000 --> 00:09:44,000
构子里面加了这样一个内容

229
00:09:44,000 --> 00:09:46,000
才答案出来的

230
00:09:46,000 --> 00:09:48,000
那么这个loggrass初十号之后

231
00:09:48,000 --> 00:09:50,000
我们就可以去调用

232
00:09:50,000 --> 00:09:51,000
对应的debug

233
00:09:51,000 --> 00:09:52,000
info等等函数

234
00:09:52,000 --> 00:09:54,000
来答案日志了

235
00:09:54,000 --> 00:09:56,000
比如我通过调用debug

236
00:09:56,000 --> 00:09:57,000
把这个日内容呢

237
00:09:57,000 --> 00:09:58,000
给传进来

238
00:09:58,000 --> 00:10:00,000
打印这样一个debug日志

239
00:10:00,000 --> 00:10:01,000
然后接下来呢

240
00:10:01,000 --> 00:10:03,000
我通过这个logger

241
00:10:03,000 --> 00:10:04,000
withfuse

242
00:10:04,000 --> 00:10:06,000
我创建了一个log entry

243
00:10:06,000 --> 00:10:07,000
而这个fuse呢

244
00:10:07,000 --> 00:10:09,000
传进来的实际上是一个map

245
00:10:09,000 --> 00:10:12,000
我加了一个name等于dkq

246
00:10:12,000 --> 00:10:13,000
a-等于18

247
00:10:13,000 --> 00:10:15,000
key的话肯定是字符串

248
00:10:15,000 --> 00:10:16,000
而这个value呢

249
00:10:16,000 --> 00:10:17,000
可以是认识些

250
00:10:17,000 --> 00:10:19,000
比如一会儿是字符串

251
00:10:19,000 --> 00:10:21,000
一会儿是整形

252
00:10:21,000 --> 00:10:23,000
那么通过这个loggerentree

253
00:10:23,000 --> 00:10:24,000
打的info日志

254
00:10:24,000 --> 00:10:27,000
除了会包含这样一个字符串之外

255
00:10:27,000 --> 00:10:30,000
它还会包含name等于dkq

256
00:10:30,000 --> 00:10:32,000
a-等于18

257
00:10:32,000 --> 00:10:33,000
然后呢

258
00:10:33,000 --> 00:10:34,000
我通过这个loggerentree

259
00:10:34,000 --> 00:10:35,000
当然了

260
00:10:35,000 --> 00:10:36,000
通过logger也一样啊

261
00:10:36,000 --> 00:10:38,000
还可以带这个f

262
00:10:38,000 --> 00:10:41,000
f表示格式化输出吗

263
00:10:41,000 --> 00:10:43,000
比如在这个字符串里面

264
00:10:43,000 --> 00:10:45,000
就包含了一个百分号

265
00:10:45,000 --> 00:10:46,000
del3f

266
00:10:46,000 --> 00:10:48,000
百分号f表示浮点数

267
00:10:48,000 --> 00:10:50,000
del3表示展示三位小数

268
00:10:50,000 --> 00:10:52,000
把这个3.14

269
00:10:52,000 --> 00:10:54,000
付给这个百分号f

270
00:10:54,000 --> 00:10:56,000
那甚至啊

271
00:10:56,000 --> 00:10:58,000
我还可以打印多条消息

272
00:10:58,000 --> 00:11:00,000
因为这些什么debug

273
00:11:00,000 --> 00:11:01,000
info

274
00:11:01,000 --> 00:11:02,000
warm arrow

275
00:11:02,000 --> 00:11:04,000
他们传递的实际上是一个不定常参数

276
00:11:04,000 --> 00:11:06,000
不光是可以携带一个字符串

277
00:11:06,000 --> 00:11:08,000
甚至可以携带多个字符串

278
00:11:08,000 --> 00:11:09,000
那这样的话

279
00:11:09,000 --> 00:11:10,000
这多个字符串

280
00:11:10,000 --> 00:11:13,000
他们最终会拼接在一起

281
00:11:13,000 --> 00:11:15,000
输出到日志里面

282
00:11:15,000 --> 00:11:18,000
所以不管是logger还是log entry

283
00:11:18,000 --> 00:11:21,000
他们都可以通过不同的level

284
00:11:21,000 --> 00:11:22,000
来打印日志

285
00:11:22,000 --> 00:11:23,000
只不过呢

286
00:11:23,000 --> 00:11:24,000
这个log entry

287
00:11:24,000 --> 00:11:27,000
它可以携带一些个key value

288
00:11:27,000 --> 00:11:28,000
那还有个级别呢

289
00:11:28,000 --> 00:11:30,000
是phytal级别

290
00:11:30,000 --> 00:11:31,000
phytal日志呢

291
00:11:31,000 --> 00:11:32,000
它打完之后

292
00:11:32,000 --> 00:11:35,000
会顺带的调一下这个exit

293
00:11:35,000 --> 00:11:38,000
来退出整个go进程

294
00:11:38,000 --> 00:11:40,000
那甚至呢

295
00:11:40,000 --> 00:11:41,000
还有什么

296
00:11:41,000 --> 00:11:42,000
还有panic

297
00:11:42,000 --> 00:11:43,000
panic嘛

298
00:11:43,000 --> 00:11:44,000
顾名思义

299
00:11:44,000 --> 00:11:47,000
让他把这个预致打印完之后

300
00:11:47,000 --> 00:11:48,000
他会去调用panic

301
00:11:48,000 --> 00:11:52,000
那还记得panic和这个exit是什么关系吗

302
00:11:52,000 --> 00:11:55,000
这个我们在基础片里面相继讲过

303
00:11:55,000 --> 00:11:59,000
那么通过recover可以捕获这个panic

304
00:11:59,000 --> 00:12:01,000
避免进程直接退出嘛

305
00:12:01,000 --> 00:12:03,000
我们把这个单字呢

306
00:12:03,000 --> 00:12:04,000
跑一下

307
00:12:04,000 --> 00:12:05,000
跑完之后的话

308
00:12:05,000 --> 00:12:08,000
这边立马生成了一个全新的文件

309
00:12:08,000 --> 00:12:11,000
2025年的2月11号16点

310
00:12:11,000 --> 00:12:13,000
然后这个原链接

311
00:12:13,000 --> 00:12:16,000
它指定的就是刚刚生成的这个全新的文件

312
00:12:16,000 --> 00:12:17,000
还记得吗

313
00:12:17,000 --> 00:12:21,000
刚才我把这个日志格式改成了json格式

314
00:12:21,000 --> 00:12:27,000
看这个json里面包含了一个a-18和name等于dqq

315
00:12:27,000 --> 00:12:33,000
这个是由于我们打印info日志是通过这个logntree打印的

316
00:12:33,000 --> 00:12:36,000
而这个ntree里面呢包含了name和age

317
00:12:36,000 --> 00:12:41,000
没有打印debug日志是因为我们在处置化时指定的级别呢

318
00:12:41,000 --> 00:12:42,000
是info

319
00:12:42,000 --> 00:12:44,000
所以debug被自动屏蔽了

320
00:12:44,000 --> 00:12:47,000
第二个日志级别是won级别

321
00:12:47,000 --> 00:12:50,000
那这个3.14它是用的三位小数

322
00:12:50,000 --> 00:12:54,000
因为刚才在大妈里面我们是保留三位小数嘛

323
00:12:54,000 --> 00:12:57,000
第三个是arrow级别

324
00:12:57,000 --> 00:13:01,000
arrow呢是直接通过logger来进行打印的

325
00:13:01,000 --> 00:13:02,000
它不是logntree

326
00:13:02,000 --> 00:13:05,000
所以呢它里面没有这个name和age

327
00:13:05,000 --> 00:13:12,000
那为什么我这个arrow日志里面会包含一个app等于dqq呢

328
00:13:12,000 --> 00:13:18,000
这个app啊它就是我当初在这个勾字里面加进来的

329
00:13:18,000 --> 00:13:23,000
这个勾字里面呢会加入一个app等于dqq

330
00:13:23,000 --> 00:13:30,000
同时这个勾字它仅对arrow,phytl和panic这三种基本日志生效

331
00:13:30,000 --> 00:13:33,000
那我们这个是arrow嘛自然也生效了

332
00:13:33,000 --> 00:13:38,000
同时这个勾字它还会在中端里面把message进行输出

333
00:13:38,000 --> 00:13:42,000
所以呢我们的arrow日志也输出到中端了

334
00:13:42,000 --> 00:13:46,000
arrow是由这两个字符串直接拼接而成的

335
00:13:46,000 --> 00:13:53,000
它对应到日志文件里面这个message也是由这两个字符串拼接而成的

336
00:13:53,000 --> 00:13:56,000
第四个呢是panic级别的日志

337
00:13:56,000 --> 00:13:59,000
也包含一个app等于dqq

338
00:13:59,000 --> 00:14:02,000
message是this is a panic lock

339
00:14:02,000 --> 00:14:06,000
panic lock也会输出到终端这边来

340
00:14:06,000 --> 00:14:07,000
好

341
00:14:07,000 --> 00:14:08,000
好

342
00:14:08,000 --> 00:14:09,000
好

