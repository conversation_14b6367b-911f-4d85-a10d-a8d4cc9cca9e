1
00:00:00,000 --> 00:00:02,880
这节课来学习一下GKARL昌识度

2
00:00:02,880 --> 00:00:04,720
算法本身比较简单

3
00:00:04,720 --> 00:00:07,360
但是实际用处的话也比较多

4
00:00:07,360 --> 00:00:09,100
我们来看一个案例

5
00:00:09,100 --> 00:00:12,380
比方说我们有两个用户

6
00:00:12,380 --> 00:00:15,680
每个用户他可能都有一个用户画像

7
00:00:15,680 --> 00:00:17,440
互联网喜欢搞用户画像

8
00:00:17,440 --> 00:00:20,360
就是说他对哪些东西感兴趣

9
00:00:20,360 --> 00:00:23,100
用户一可能对go分布式

10
00:00:23,100 --> 00:00:25,440
mycycle等等这些词感兴趣

11
00:00:25,440 --> 00:00:28,280
用户二他对这些词感兴趣

12
00:00:28,280 --> 00:00:32,460
那么我们在做互联网做推荐的时候

13
00:00:32,460 --> 00:00:35,260
我们要对比一下两个用户

14
00:00:35,260 --> 00:00:37,380
他们的相似度高不高

15
00:00:37,380 --> 00:00:40,060
如果两个用户的相似度比较高的话

16
00:00:40,060 --> 00:00:42,820
那我可以把其中某个用户

17
00:00:42,820 --> 00:00:45,040
他还看的视频推荐给第二个用户

18
00:00:45,040 --> 00:00:46,200
对吧等等吧

19
00:00:46,200 --> 00:00:51,080
其实比较两个集合的相似度

20
00:00:51,080 --> 00:00:53,860
这种场景还是用的蛮多的

21
00:00:53,860 --> 00:00:57,200
那么一种朴素的想法是说

22
00:00:57,200 --> 00:01:01,580
我直接挑出这两个集合他们的交集

23
00:01:01,580 --> 00:01:03,140
就像我第三张图一样

24
00:01:03,140 --> 00:01:05,100
把交集部分给找出来

25
00:01:05,100 --> 00:01:10,780
直接看一下这个交集占整个集合的占比

26
00:01:10,780 --> 00:01:13,660
就是说我这个交集处以并集

27
00:01:13,660 --> 00:01:17,840
以此作为两个集合相似度的一个度量标准

28
00:01:17,840 --> 00:01:22,060
好比方说第一个集合它里面有五个元素

29
00:01:22,060 --> 00:01:24,260
第二个集合里面有八个元素

30
00:01:24,260 --> 00:01:26,840
而交集呢假如说四个元素嘛

31
00:01:26,840 --> 00:01:29,700
那么请问这个集合的相似度对应其

32
00:01:29,700 --> 00:01:33,040
其实就是交集4除以上并集

33
00:01:33,040 --> 00:01:33,780
并集是什么

34
00:01:33,780 --> 00:01:36,100
并集是5加8减4

35
00:01:36,100 --> 00:01:37,200
对吧

36
00:01:37,200 --> 00:01:38,600
并集是5加8减4

37
00:01:38,600 --> 00:01:39,280
好

38
00:01:39,280 --> 00:01:40,240
交集除并集

39
00:01:40,240 --> 00:01:42,140
这样的话就是集合相似度

40
00:01:42,140 --> 00:01:43,360
好

41
00:01:43,360 --> 00:01:45,240
对于这样一个简单的算法

42
00:01:45,240 --> 00:01:49,620
如果说我们通过代码去实现会怎么写呢

43
00:01:49,620 --> 00:01:50,520
好

44
00:01:50,520 --> 00:01:51,720
这里面写了一个函数啊

45
00:01:51,720 --> 00:01:53,040
这卡的相似度嘛

46
00:01:53,040 --> 00:01:53,280
对吧

47
00:01:53,280 --> 00:01:56,960
那么传进来两个集合两个切片啊

48
00:01:56,960 --> 00:01:58,880
这里面使用了范形啊

49
00:01:58,880 --> 00:02:01,080
几乎我们所有的算法函数都会使用范形

50
00:02:01,080 --> 00:02:06,420
写任何跟算法相关的函数养成一个良好的习惯

51
00:02:06,420 --> 00:02:09,479
一上来我们先判断一下边界条件啊

52
00:02:09,479 --> 00:02:13,160
假如说这两个集合但凡有一个集合

53
00:02:13,160 --> 00:02:14,520
它是空的

54
00:02:14,520 --> 00:02:16,360
那这个相似度肯定是零嘛

55
00:02:16,360 --> 00:02:18,800
就不需要然后走了

56
00:02:18,800 --> 00:02:19,340
好

57
00:02:19,340 --> 00:02:21,640
那一种朴素的想法是说

58
00:02:21,640 --> 00:02:25,280
我先去4循环便利第一个集合

59
00:02:25,280 --> 00:02:27,140
拿到它的某一个元素之后

60
00:02:27,140 --> 00:02:29,400
我去便利第二个集合

61
00:02:29,400 --> 00:02:31,340
看看它在不在里面

62
00:02:31,340 --> 00:02:35,140
这样的话我就把它们的交集给找出来了

63
00:02:35,140 --> 00:02:39,320
这是通过两层的4循环签套去找交集

64
00:02:39,320 --> 00:02:40,980
一旦交集找出来了

65
00:02:40,980 --> 00:02:42,140
直接交集出并集

66
00:02:42,140 --> 00:02:44,480
直接就把这个集合生素给求出来了

67
00:02:45,660 --> 00:02:46,020
好

68
00:02:46,580 --> 00:02:48,020
但是这个算法的话

69
00:02:48,020 --> 00:02:50,320
它的时间复杂度是n的平方

70
00:02:50,320 --> 00:02:52,500
因为是两层4循环签套嘛

71
00:02:52,500 --> 00:02:53,900
O n的平方

72
00:02:55,100 --> 00:02:57,020
然后我们稍微的优化一下

73
00:02:57,020 --> 00:02:57,820
我们这样想

74
00:02:58,900 --> 00:03:01,040
我先把每一个集合

75
00:03:01,040 --> 00:03:03,900
每一个切片先放到一个map里面去

76
00:03:03,900 --> 00:03:04,760
这里面是吧

77
00:03:04,760 --> 00:03:06,760
我4循环第一个切片

78
00:03:06,760 --> 00:03:08,640
把它放到一个map里面去

79
00:03:09,440 --> 00:03:11,299
由于我只关心这个k

80
00:03:11,299 --> 00:03:14,440
我把切片元素放到map的k里面去

81
00:03:14,440 --> 00:03:16,480
value不关心不需要value

82
00:03:16,480 --> 00:03:19,600
所以的话直接用空结构体来作为value

83
00:03:20,480 --> 00:03:23,739
这样的话两个切片就表示成了两个map

84
00:03:23,739 --> 00:03:27,940
然后我通过4循环去便利第一个map

85
00:03:27,940 --> 00:03:30,260
对于每一个k而言

86
00:03:30,260 --> 00:03:33,680
我去判断一下它是否在第二个map里面

87
00:03:33,680 --> 00:03:34,680
如果在的话

88
00:03:34,680 --> 00:03:36,180
说明这个元素它是交集

89
00:03:36,180 --> 00:03:37,560
交集个数加1

90
00:03:37,560 --> 00:03:40,220
最后让交集除以上什么并级

91
00:03:40,220 --> 00:03:43,360
并级就是两个集合成组之和

92
00:03:43,360 --> 00:03:45,160
再减去交集

93
00:03:45,160 --> 00:03:47,320
这样的话就是这个极卡相似度

94
00:03:47,320 --> 00:03:50,600
好这里面我们展开一下

95
00:03:50,600 --> 00:03:53,960
把这个时间复杂度好好的说一说

96
00:03:53,960 --> 00:03:57,020
这里面有三个for循环

97
00:03:57,020 --> 00:03:59,040
每个for循环它都是on

98
00:03:59,040 --> 00:04:03,040
这样的话整体上而言整个函数是三倍的on

99
00:04:03,040 --> 00:04:05,280
实际上三倍的on也是on了

100
00:04:05,280 --> 00:04:08,440
很多同学会很晕

101
00:04:08,440 --> 00:04:12,180
这个on到底什么on o平方到底是怎么说的

102
00:04:12,180 --> 00:04:12,920
怎么算的对吧

103
00:04:12,920 --> 00:04:16,640
好我们展开讲一讲

104
00:04:16,640 --> 00:04:19,140
首先O1是什么

105
00:04:19,140 --> 00:04:24,620
O1的话就是说你这个操作跟切片

106
00:04:24,620 --> 00:04:27,760
跟这个容器的总长度没有任何关系

107
00:04:27,760 --> 00:04:30,979
它的计算量是一个确定的计算量

108
00:04:30,979 --> 00:04:34,599
不管你这个计算量是三步还是五步

109
00:04:34,599 --> 00:04:35,560
还是三十步

110
00:04:35,560 --> 00:04:39,460
总之你是一个确定的跟容器的长度n

111
00:04:39,460 --> 00:04:40,800
没有任何关系

112
00:04:40,800 --> 00:04:41,659
这是O1

113
00:04:41,660 --> 00:04:48,400
比方说我要从一个数组里面去取出第五十个元素

114
00:04:48,400 --> 00:04:50,360
那这个操作就是O1

115
00:04:50,360 --> 00:04:52,540
因为它跟数组长度没关系

116
00:04:52,540 --> 00:04:54,940
不管数组长度是一百还是一万

117
00:04:54,940 --> 00:04:56,980
那我取出第五十个元素

118
00:04:56,980 --> 00:05:00,440
这个操作的计算量是确定的

119
00:05:00,440 --> 00:05:01,220
OK

120
00:05:01,220 --> 00:05:03,040
然后再看一下这个on

121
00:05:03,040 --> 00:05:08,580
on的话我们应该用n来表示这个容器的长度

122
00:05:08,580 --> 00:05:11,320
on的话就是我要把这个容器

123
00:05:11,320 --> 00:05:12,180
便利一点

124
00:05:12,180 --> 00:05:17,300
它的时间复杂度跟容器的长度是一个线性关系

125
00:05:17,300 --> 00:05:19,020
也就是说是一个倍数关系

126
00:05:19,020 --> 00:05:20,900
那就是说这个倍数是一倍

127
00:05:20,900 --> 00:05:21,860
是两倍

128
00:05:21,860 --> 00:05:23,300
甚至是0.5倍

129
00:05:23,300 --> 00:05:24,500
无所谓

130
00:05:24,500 --> 00:05:26,320
只要是线性关系

131
00:05:26,320 --> 00:05:27,600
都是O1

132
00:05:27,600 --> 00:05:28,700
都是O1

133
00:05:28,700 --> 00:05:30,300
好

134
00:05:30,300 --> 00:05:32,200
那O1的平方就好理解了

135
00:05:32,200 --> 00:05:35,140
因为你需要两层的Four循环千套

136
00:05:35,140 --> 00:05:37,180
就是O1的平方

137
00:05:37,180 --> 00:05:38,380
每一次Four循环都是n

138
00:05:38,380 --> 00:05:40,120
那千套起来就是n平方

139
00:05:40,120 --> 00:05:43,120
那么如果是三层Four循环情况呢

140
00:05:43,120 --> 00:05:44,660
n的三四八

141
00:05:44,660 --> 00:05:46,520
OK

142
00:05:46,520 --> 00:05:49,500
那什么时候是logn呢

143
00:05:49,500 --> 00:05:52,100
当我们在讲算法的时候

144
00:05:52,100 --> 00:05:54,260
我们单单提到logn

145
00:05:54,260 --> 00:05:58,100
默认情况下都是以二为底数

146
00:05:58,100 --> 00:05:59,600
除非我特殊强调

147
00:05:59,600 --> 00:06:00,800
否则都是以二为底数

148
00:06:00,800 --> 00:06:03,580
什么时候是logn呢

149
00:06:03,580 --> 00:06:06,540
比方说我们之前讲过的二分查找

150
00:06:06,540 --> 00:06:10,440
那么在一个有序的数组里面

151
00:06:10,440 --> 00:06:12,540
查找某一个target是否存在

152
00:06:12,540 --> 00:06:13,580
如果不在的话

153
00:06:13,580 --> 00:06:16,820
它在数组里的下标是什么

154
00:06:16,820 --> 00:06:18,500
我们举个例子

155
00:06:18,500 --> 00:06:21,020
比方说这个数组长度是8

156
00:06:21,020 --> 00:06:22,919
那么二分查找

157
00:06:22,919 --> 00:06:24,080
每一次Four循环

158
00:06:24,080 --> 00:06:26,460
它都是直接砍掉一半

159
00:06:26,460 --> 00:06:26,859
对吧

160
00:06:26,859 --> 00:06:28,020
把这个数组的一半

161
00:06:28,020 --> 00:06:29,219
整个给丢弃掉

162
00:06:29,219 --> 00:06:29,960
好

163
00:06:29,960 --> 00:06:32,099
最开始数组长度为8

164
00:06:32,099 --> 00:06:34,060
那第一次Four循环

165
00:06:34,060 --> 00:06:35,099
我砍掉一半

166
00:06:35,099 --> 00:06:36,700
还剩下长度为4

167
00:06:36,700 --> 00:06:38,580
第二次Four循环

168
00:06:38,580 --> 00:06:39,479
砍掉一半

169
00:06:39,479 --> 00:06:41,500
还剩下长度为2

170
00:06:41,500 --> 00:06:43,280
第三次Four循环

171
00:06:43,280 --> 00:06:43,880
砍掉一半

172
00:06:43,880 --> 00:06:45,380
还剩下长度为1

173
00:06:45,380 --> 00:06:47,020
那只剩下一个元素了

174
00:06:47,020 --> 00:06:48,620
你直接判断一下这个元素

175
00:06:48,620 --> 00:06:51,360
是不是你要找的那个元素就可以了

176
00:06:51,360 --> 00:06:53,320
所以的话总共是什么

177
00:06:53,320 --> 00:06:54,219
看下三次

178
00:06:54,219 --> 00:06:55,360
Four循环了三次

179
00:06:55,360 --> 00:06:57,960
但这是最坏的情况

180
00:06:57,960 --> 00:06:59,460
你可能在中间某一次的时候

181
00:06:59,460 --> 00:07:00,060
已经找到了

182
00:07:00,060 --> 00:07:00,280
对吧

183
00:07:00,280 --> 00:07:01,860
我们说最坏情况下

184
00:07:01,860 --> 00:07:02,540
它是三次

185
00:07:02,540 --> 00:07:04,159
而3刚好是什么

186
00:07:04,159 --> 00:07:07,040
刚好是log以2为几的什么8

187
00:07:07,040 --> 00:07:07,380
对吧

188
00:07:07,380 --> 00:07:08,400
因为2的3次方对于8

189
00:07:08,400 --> 00:07:09,700
这就是logn

190
00:07:09,700 --> 00:07:10,500
好

191
00:07:10,500 --> 00:07:13,920
我们再看看之前写的这个二分查找的代码

192
00:07:13,920 --> 00:07:17,080
二分查找

193
00:07:17,080 --> 00:07:18,860
这里面

194
00:07:18,860 --> 00:07:19,440
也就是说

195
00:07:19,440 --> 00:07:21,540
当这个数组长度是8的时候

196
00:07:21,540 --> 00:07:23,080
那么我们这个Four循环

197
00:07:23,080 --> 00:07:26,600
最多这个Four循环会进来三次

198
00:07:26,600 --> 00:07:28,100
那每一次进来

199
00:07:28,100 --> 00:07:30,000
每一次进来

200
00:07:30,000 --> 00:07:32,120
要执行的这一坨操作

201
00:07:32,120 --> 00:07:34,280
加起来是O1

202
00:07:34,280 --> 00:07:37,280
你别看它这个代码这么多行

203
00:07:37,280 --> 00:07:39,080
它就是O1

204
00:07:39,080 --> 00:07:39,700
为什么呢

205
00:07:39,700 --> 00:07:41,100
因为这么多行代码

206
00:07:41,100 --> 00:07:45,420
它们跟这个数组的大小没有任何关系

207
00:07:45,420 --> 00:07:46,920
它是一个确定的计算量

208
00:07:46,920 --> 00:07:47,660
所以是O1

209
00:07:47,660 --> 00:07:48,620
OK

210
00:07:48,620 --> 00:07:50,720
好

211
00:07:50,720 --> 00:07:51,360
我们再说回了

212
00:07:51,360 --> 00:07:53,160
这讲这个时间辞账度

213
00:07:53,160 --> 00:07:58,300
然后我们再来说回我们最原始的这个实际问题

214
00:07:58,300 --> 00:08:00,240
在实际当中

215
00:08:00,240 --> 00:08:04,060
我们不光是要去对比这两个用户的相似度

216
00:08:04,060 --> 00:08:06,680
我们可能有成千上万个用户

217
00:08:06,680 --> 00:08:10,640
我们每一个用户都要量一量的进行一个比较

218
00:08:10,640 --> 00:08:11,620
每一个用户

219
00:08:11,620 --> 00:08:15,920
它都要跟剩下的9900个用户都进行一次对比

220
00:08:15,920 --> 00:08:18,920
那对于这种情况的话

221
00:08:18,920 --> 00:08:21,900
那实际上有一种更快的方式

222
00:08:21,900 --> 00:08:26,060
就是我们不如一上来第一步先把每一个用户

223
00:08:26,060 --> 00:08:28,860
它的这些关键词进行一个排序

224
00:08:28,860 --> 00:08:31,420
就先把每一个集合先排好序

225
00:08:31,420 --> 00:08:35,340
然后就变成了两个有序的集合

226
00:08:35,340 --> 00:08:38,220
我如何去计算它们的交集

227
00:08:38,220 --> 00:08:39,660
因为有了交集之后

228
00:08:39,660 --> 00:08:42,060
可以一下子求出这个极卡三次度

229
00:08:42,060 --> 00:08:43,660
所以我们就关注一下

230
00:08:43,660 --> 00:08:45,020
看这个具体

231
00:08:45,020 --> 00:08:46,580
好

232
00:08:46,580 --> 00:08:47,740
为了简单起见

233
00:08:47,740 --> 00:08:51,820
我把这两个集合简化成了整形数组

234
00:08:51,820 --> 00:08:55,000
这是两个排序好的整形数组

235
00:08:55,000 --> 00:08:58,220
我们如何去快速地求出它们的交集

236
00:08:58,220 --> 00:09:00,300
首先

237
00:09:00,300 --> 00:09:03,280
我弄两个变量I跟J

238
00:09:03,280 --> 00:09:06,880
I指向第一个数组的数元素

239
00:09:06,880 --> 00:09:09,300
J指向第二个数组的数元素

240
00:09:09,300 --> 00:09:11,460
然后我对比一下

241
00:09:11,460 --> 00:09:14,740
它们指向的这两个元素谁大谁小

242
00:09:14,740 --> 00:09:15,700
好

243
00:09:15,700 --> 00:09:16,900
I指向这个小对吧

244
00:09:16,900 --> 00:09:18,100
因为一小二嘛

245
00:09:18,100 --> 00:09:20,120
谁小谁往后移

246
00:09:20,120 --> 00:09:21,680
那I往后移位

247
00:09:21,680 --> 00:09:22,800
I指向三

248
00:09:22,800 --> 00:09:25,740
这个时候J小对吧

249
00:09:25,740 --> 00:09:25,940
好

250
00:09:25,940 --> 00:09:27,360
谁小谁往后移

251
00:09:27,360 --> 00:09:27,820
好

252
00:09:27,820 --> 00:09:28,520
J往后移

253
00:09:28,520 --> 00:09:32,320
这个时候I跟J指向的元素相等了

254
00:09:32,320 --> 00:09:35,480
赶紧把这个三放到我们的交集里面去

255
00:09:35,480 --> 00:09:38,040
如果相等的话

256
00:09:38,040 --> 00:09:40,500
I跟J同时往后走一步

257
00:09:40,500 --> 00:09:42,340
好 同时往后走一步

258
00:09:42,340 --> 00:09:43,780
那这个时候I小对吧

259
00:09:43,780 --> 00:09:44,780
I24嘛

260
00:09:44,780 --> 00:09:45,300
I小

261
00:09:45,300 --> 00:09:46,500
I往后移

262
00:09:46,500 --> 00:09:48,100
J小

263
00:09:48,100 --> 00:09:49,000
J往后移

264
00:09:49,000 --> 00:09:50,080
I小

265
00:09:50,080 --> 00:09:50,920
I往后走

266
00:09:50,920 --> 00:09:52,900
这个时候I跟J又相等了

267
00:09:52,900 --> 00:09:54,980
赶紧把这个九放到交集里面去

268
00:09:54,980 --> 00:09:55,780
好

269
00:09:55,780 --> 00:09:56,840
同时往后走

270
00:09:56,840 --> 00:09:58,680
I跟J同时往后走

271
00:09:58,680 --> 00:09:59,240
I小

272
00:09:59,240 --> 00:10:00,120
I往后走

273
00:10:00,120 --> 00:10:03,840
I跟J相等了23数一个交集

274
00:10:03,840 --> 00:10:04,560
好

275
00:10:04,560 --> 00:10:06,320
这个时候就可以退出了

276
00:10:06,320 --> 00:10:10,460
也就是说I跟J但凡有一个已经走到输组的末尾了

277
00:10:10,460 --> 00:10:13,380
那么整个函数就可以退出了

278
00:10:13,380 --> 00:10:15,460
因为整个交集已经到权了

279
00:10:15,460 --> 00:10:19,620
也就是说在最坏的情况之下

280
00:10:19,620 --> 00:10:23,820
I跟J他们都来到了各自输组的最后一位

281
00:10:23,820 --> 00:10:28,140
这样的话相当于把两个输组都变了一遍

282
00:10:28,140 --> 00:10:30,540
是两倍的N 对吧

283
00:10:30,540 --> 00:10:31,180
二N

284
00:10:31,180 --> 00:10:33,860
二N的话实际上也是ON

285
00:10:33,860 --> 00:10:36,460
但是ON跟ON它也是有区别的

286
00:10:36,460 --> 00:10:38,140
你两倍的N跟三倍的N

287
00:10:38,140 --> 00:10:40,660
那可能两倍的N还是要快一点

288
00:10:40,660 --> 00:10:45,220
比我们刚才那个MAP的方式三倍的N要快一些

289
00:10:45,220 --> 00:10:45,380
好

290
00:10:45,380 --> 00:10:48,420
所以这个函数就很简单了

291
00:10:48,420 --> 00:10:50,819
我们看看原代码里面

292
00:10:52,980 --> 00:10:55,060
这是我们的JCLA相似度

293
00:10:55,060 --> 00:11:00,740
这是第一个是通过MAP的方式来实现

294
00:11:00,740 --> 00:11:04,660
那第二个是通过刚才说的那种

295
00:11:04,660 --> 00:11:05,980
如果说已经排序好了的话

296
00:11:05,980 --> 00:11:07,819
那直接然后走

297
00:11:07,820 --> 00:11:09,220
这个代码我就不再细想了

298
00:11:09,220 --> 00:11:14,660
就是说大家最好自己先不看这个代码

299
00:11:14,660 --> 00:11:15,740
你先自己写一遍

300
00:11:15,740 --> 00:11:19,820
因为这样的一个算法其实还是很简单的

301
00:11:19,820 --> 00:11:23,140
但是很适合作为面试的时候

302
00:11:23,140 --> 00:11:25,100
现场coding的一个小题目

303
00:11:25,100 --> 00:11:27,860
因为一它很简单对吧

304
00:11:27,860 --> 00:11:31,100
不容易说出现那种脑筋急转弯这种情况

305
00:11:31,100 --> 00:11:35,940
好第二个如果说你平时没有经过这方面的专业训论的话

306
00:11:35,940 --> 00:11:38,380
你大概率你也写不对

307
00:11:38,380 --> 00:11:40,100
你可能会有各种概念的小问题

308
00:11:40,100 --> 00:11:40,420
对吧

309
00:11:40,420 --> 00:11:45,580
好我们看看这个对应的单元测试代码

310
00:11:45,580 --> 00:11:49,420
gcard test这是对应的单侧的代码

311
00:11:49,420 --> 00:11:54,500
对应的单元测试代码

312
00:11:54,500 --> 00:11:58,819
对应的单元测试代码

