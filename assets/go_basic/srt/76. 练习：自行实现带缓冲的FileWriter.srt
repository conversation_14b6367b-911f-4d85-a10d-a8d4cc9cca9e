1
00:00:00,580 --> 00:00:02,840
购物员本身提供了 Buff I / O 

2
00:00:02,840 --> 00:00:05,300
可以通过带缓冲的方式来读文件

3
00:00:05,300 --> 00:00:06,087
写文件

4
00:00:06,087 --> 00:00:07,750
这节课我们自己做一个练习

5
00:00:07,750 --> 00:00:12,070
就是我们自己实现一个带缓冲的 file writer 

6
00:00:12,070 --> 00:00:13,990
实现思路是这样子的

7
00:00:13,990 --> 00:00:15,510
看这个 PPT 

8
00:00:15,510 --> 00:00:17,490
那么最下面最底层啊

9
00:00:17,490 --> 00:00:21,010
黄色这一层想象成是我们的磁盘上面

10
00:00:21,010 --> 00:00:22,370
通过调 writ

11
00:00:22,370 --> 00:00:25,132
要把一些内容写到磁盘里面去嘛

12
00:00:25,132 --> 00:00:28,140
那现在中间加了一层缓冲啊

13
00:00:28,140 --> 00:00:29,280
就是加了个 buffer 

14
00:00:29,280 --> 00:00:31,820
这个 buffer 啊，其实落实到代码层面

15
00:00:31,820 --> 00:00:34,022
它就是一个 by 的切片

16
00:00:34,022 --> 00:00:37,590
也就是说我通过调用 write 函数啊

17
00:00:37,590 --> 00:00:39,055
我要把这个内容呢

18
00:00:39,055 --> 00:00:40,550
写到磁盘里面去

19
00:00:40,550 --> 00:00:44,410
那么呢，你先写到 buffer 这个 by 的切片里面去是吧

20
00:00:44,410 --> 00:00:47,095
红色这一部先放到切片里面去

21
00:00:47,095 --> 00:00:49,370
然后第二次再调用 write 

22
00:00:49,370 --> 00:00:50,970
想把绿色这部分呢

23
00:00:50,970 --> 00:00:52,510
也写到文件里面去

24
00:00:52,510 --> 00:00:53,010
那么呢

25
00:00:53,010 --> 00:00:55,610
实际上他是把绿色这部分

26
00:00:55,610 --> 00:00:57,630
追加到了红色这一份

27
00:00:57,630 --> 00:00:58,750
后面啊

28
00:00:58,750 --> 00:01:01,710
一直往这个 buffer 里面去追加新内容

29
00:01:01,710 --> 00:01:03,347
包括蓝色对吧

30
00:01:03,347 --> 00:01:06,150
只是说写进了切片里面去

31
00:01:06,150 --> 00:01:08,000
那么当我这个切片

32
00:01:08,000 --> 00:01:09,860
我这个 buffer 它满了以后

33
00:01:09,860 --> 00:01:12,840
比方说你最开始给他分配的这个容量是16

34
00:01:12,840 --> 00:01:13,180
对吧

35
00:01:13,180 --> 00:01:15,150
满了那麻了的话

36
00:01:15,150 --> 00:01:17,530
你再往里面写就写不进新内容嘛

37
00:01:17,530 --> 00:01:19,042
所以这个时候的话呢

38
00:01:19,042 --> 00:01:22,000
就需要把 buffer 里面已有的内容呢

39
00:01:22,000 --> 00:01:25,290
全部真正的写到磁盘里面去

40
00:01:25,290 --> 00:01:26,200
那所以啊

41
00:01:26,200 --> 00:01:28,160
我们需要维护一个数字啊

42
00:01:28,160 --> 00:01:31,980
一个下标就是这个 buffer and index 

43
00:01:31,980 --> 00:01:36,405
那这个数字呢，就是说目前这个 buffer 切片里面

44
00:01:36,405 --> 00:01:39,760
实际有效的元素个数是几个

45
00:01:39,760 --> 00:01:43,680
那么将来呢，只是说这个 buffer and index 是吧

46
00:01:43,680 --> 00:01:47,260
这个下标之前往左的这一步

47
00:01:47,260 --> 00:01:49,180
数据需要刷进磁盘啊

48
00:01:49,180 --> 00:01:50,980
并不是说8月份里面的所有数据

49
00:01:50,980 --> 00:01:52,307
都要刷进磁盘

50
00:01:52,307 --> 00:01:56,220
那至于说什么时候触发刷磁盘这个操作

51
00:01:56,220 --> 00:01:57,290
其实就

52
00:01:57,290 --> 00:02:01,010
当我上层调用 write 打算写的时候呢

53
00:02:01,010 --> 00:02:02,330
你先判断一下

54
00:02:02,330 --> 00:02:05,530
那么这个 buffer and index 这个下标

55
00:02:05,530 --> 00:02:10,036
再加上你将要写的这一部分数据的大小

56
00:02:10,036 --> 00:02:10,919
加完之后

57
00:02:10,919 --> 00:02:15,420
你看看有没有可能会超过这个 buffer 的总的容量

58
00:02:15,420 --> 00:02:16,600
如果超过的话

59
00:02:16,600 --> 00:02:19,900
那么你就先把老内容先刷进磁盘

60
00:02:19,900 --> 00:02:22,090
把 buffer 整个清空是吧

61
00:02:22,090 --> 00:02:25,200
清空 buffer 实际上对应到代码层面

62
00:02:25,200 --> 00:02:27,660
就是把这个 buffer and index 

63
00:02:27,660 --> 00:02:29,690
把这个数字置为零就可以了

64
00:02:29,690 --> 00:02:31,390
你并不需要真正的说啊

65
00:02:31,390 --> 00:02:32,470
我要去内存里面

66
00:02:32,470 --> 00:02:34,930
把这些东西全部给值为零啊

67
00:02:34,930 --> 00:02:35,715
不需要

68
00:02:35,715 --> 00:02:38,345
因为你每次 write 这个新内容

69
00:02:38,345 --> 00:02:43,550
它都是从 buffer and index 这个位置往后去填充吗

70
00:02:43,550 --> 00:02:45,532
好，思路很简单

71
00:02:45,532 --> 00:02:47,020
把代码写一下

72
00:02:47,020 --> 00:02:49,540
我们打算搞一个结构

73
00:02:49,540 --> 00:02:53,860
就叫 buffer file writer 

74
00:02:56,640 --> 00:02:59,570
好，那这个结构体里面需要包含什么呢

75
00:02:59,570 --> 00:03:00,680
最重要的是吧

76
00:03:00,680 --> 00:03:03,815
它需要包含一个底层的那个文件句柄

77
00:03:03,815 --> 00:03:05,940
就是最终你还是要把内容

78
00:03:05,940 --> 00:03:07,120
写到文件里面去嘛

79
00:03:07,120 --> 00:03:10,820
所以呢，他得持有一个 OS 点 file 啊

80
00:03:10,820 --> 00:03:13,290
借助 OS 点 file 去写文件

81
00:03:13,290 --> 00:03:14,440
那其次呢

82
00:03:14,440 --> 00:03:15,580
他得有一个 buffer 啊

83
00:03:15,580 --> 00:03:18,400
这个 buffer 就是一个数据的中转地嘛

84
00:03:18,400 --> 00:03:20,077
是一个 BUER 的切片

85
00:03:20,077 --> 00:03:22,910
然后我们说还需要维护一个什

86
00:03:22,910 --> 00:03:24,710
buffer and index 对吧

87
00:03:24,710 --> 00:03:28,870
一个下标 buffer and index 

88
00:03:28,870 --> 00:03:30,880
它是一个数字 int 

89
00:03:30,880 --> 00:03:32,030
好，结构体嘛

90
00:03:32,030 --> 00:03:34,780
我们先来一个结构体的构造函数啊

91
00:03:34,780 --> 00:03:35,947
new 一下

92
00:03:35,947 --> 00:03:39,400
那么构造函数就是要给这个结构体的一些

93
00:03:39,400 --> 00:03:40,040
成员变量

94
00:03:40,040 --> 00:03:41,300
去进行赋值啊

95
00:03:41,300 --> 00:03:42,457
初始化嘛

96
00:03:42,457 --> 00:03:44,330
首先这个 i felt 对吧

97
00:03:44,330 --> 00:03:46,910
需要从外部给我传进来啊

98
00:03:46,910 --> 00:03:50,380
这是一个构造函数的参数

99
00:03:50,380 --> 00:03:51,970
然后关于这个 buffer 

100
00:03:51,970 --> 00:03:54,710
那这个 buffer 它的容量上限是多少呢

101
00:03:54,710 --> 00:03:57,225
需要从外界来告诉我

102
00:03:57,225 --> 00:03:59,770
buffer size int 

103
00:03:59,770 --> 00:04:01,670
好，然后我直接 return 啊

104
00:04:01,670 --> 00:04:04,630
返回返回一个结构体指

105
00:04:04,630 --> 00:04:07,940
i fault 是直接传进来的

106
00:04:07,940 --> 00:04:10,030
那么这个 buffer 这个切片呢

107
00:04:10,030 --> 00:04:13,395
我通过 make 来初始化这个切片

108
00:04:13,395 --> 00:04:17,835
那么长度和容量都指定为 buffer size 

109
00:04:17,835 --> 00:04:20,110
还有这个 buffer and index 

110
00:04:20,110 --> 00:04:22,030
最开始的话等于零

111
00:04:22,030 --> 00:04:24,157
好，这是构造函数

112
00:04:24,157 --> 00:04:27,720
然后我们来实现一个比较简单的 flash 

113
00:04:29,580 --> 00:04:32,740
buffer file writer 实现一个 flash 

114
00:04:34,160 --> 00:04:38,320
这个 flash 就是说我要把这个 buffer 里面的内容呢

115
00:04:38,320 --> 00:04:38,680
当然了

116
00:04:38,680 --> 00:04:42,060
我指的是 buffer and index 之前的内容呢

117
00:04:42,060 --> 00:04:44,490
全部写到这个文件里面去

118
00:04:44,490 --> 00:04:49,770
所以就是 W 点 fault 直接调

119
00:04:49,770 --> 00:04:53,430
write i fault 是一个普通的文件嘛

120
00:04:53,430 --> 00:04:55,720
write 写谁呢

121
00:04:55,720 --> 00:04:56,560
写 buffer 对吧

122
00:04:56,560 --> 00:04:59,440
W 点八分好

123
00:04:59,440 --> 00:05:02,800
并不是要把 buffer 的所有内容全部写文件

124
00:05:02,800 --> 00:05:03,940
而值是什么呢

125
00:05:03,940 --> 00:05:10,160
只是从零到这个 W 点 buffer and index 对吧

126
00:05:10,160 --> 00:05:13,935
只是把这个区间上的内容写入磁盘

127
00:05:13,935 --> 00:05:15,200
刷磁盘之后的话

128
00:05:15,200 --> 00:05:15,720
我们需要什么

129
00:05:15,720 --> 00:05:20,280
我们需要及时的把这个 buffer and index 啊

130
00:05:20,280 --> 00:05:21,760
把它置为零啊

131
00:05:21,760 --> 00:05:23,092
等于零

132
00:05:23,092 --> 00:05:25,270
刚才我们实现的是 flash 

133
00:05:25,270 --> 00:05:27,410
也就是最下面这一步

134
00:05:27,410 --> 00:05:29,920
然后我们要实现 write 这个函数

135
00:05:29,920 --> 00:05:33,830
这边来一个 write 要写的内容

136
00:05:33,830 --> 00:05:36,677
content 它是一个 BT 切片

137
00:05:36,677 --> 00:05:40,040
那么最核心操作就说这个 content 呀

138
00:05:40,040 --> 00:05:45,302
它实际上就是追加到我们的这个 buffer 后面去

139
00:05:45,302 --> 00:05:47,550
那么从哪个位置往后追加呢

140
00:05:47,550 --> 00:05:50,130
是从 buffer and inde

141
00:05:50,130 --> 00:05:52,845
从这个位置往后去追加，对吧

142
00:05:52,845 --> 00:05:56,510
go 员他带了一个很好用的函数

143
00:05:56,510 --> 00:05:57,950
就是 copy 啊

144
00:05:57,950 --> 00:06:00,340
这是内置的函数

145
00:06:00,340 --> 00:06:02,770
就像我们的 line cap 一样

146
00:06:02,770 --> 00:06:06,000
调这个函数呢，你不需要在前面加任何的报名

147
00:06:06,000 --> 00:06:08,135
可以直接调 copy 

148
00:06:08,135 --> 00:06:12,120
第一个参数是你要把内容拷贝到什么地方去

149
00:06:12,120 --> 00:06:14,047
就指定那个地址

150
00:06:14,047 --> 00:06:17,550
那么我们是要拷贝到这个 W 点八分

151
00:06:18,560 --> 00:06:21,352
拷贝到这个位置往后

152
00:06:21,352 --> 00:06:23,860
buffer and index 往后啊

153
00:06:23,860 --> 00:06:25,550
子切片截取嘛

154
00:06:25,550 --> 00:06:28,930
拷贝的内容呢，就是 content 

155
00:06:28,930 --> 00:06:30,870
OK ，这样的话

156
00:06:30,870 --> 00:06:32,310
这一行代码就表示

157
00:06:32,310 --> 00:06:35,740
我要把 content 拷贝到 buffer 

158
00:06:35,740 --> 00:06:39,070
这个切片的这个位置往后

159
00:06:39,070 --> 00:06:41,220
然后我们要考虑一些特殊情况啊

160
00:06:41,220 --> 00:06:45,780
比如说你这个 content 一下子特别大

161
00:06:45,780 --> 00:06:50,182
直接就超出了我们的这个 buffer size ，对吧

162
00:06:50,182 --> 00:06:55,082
一个 content 就超过了 buffer 的所有大小怎么办

163
00:06:55,082 --> 00:06:56,100
如果那样的话

164
00:06:56,100 --> 00:06:57,120
你实际上就说

165
00:06:57,120 --> 00:06:59,880
你这个 content 都没必要往 buffer 里面放了

166
00:06:59,880 --> 00:07:02,460
你就应该直接写到磁盘里面去

167
00:07:02,460 --> 00:07:03,915
写到文件里面去

168
00:07:03,915 --> 00:07:06,090
所以我们来个 if 判断啊

169
00:07:06,090 --> 00:07:09,040
如果说这个 content 要写的内容

170
00:07:09,040 --> 00:07:11,610
本身已经大

171
00:07:11,610 --> 00:07:16,120
等于我们的这个 buffer 的总大小

172
00:07:16,120 --> 00:07:17,675
总长度的话

173
00:07:17,675 --> 00:07:19,150
那么呢

174
00:07:19,150 --> 00:07:24,390
直接 W 点 i fault 点 wr content 的对吧

175
00:07:24,390 --> 00:07:26,830
直接把它写到文件里面去

176
00:07:26,830 --> 00:07:28,170
就不用过这个 buffer 了

177
00:07:28,170 --> 00:07:29,400
不用再中转一下了

178
00:07:29,400 --> 00:07:31,110
但这样的话会出问题啊

179
00:07:31,110 --> 00:07:33,110
就是说本来呢

180
00:07:33,110 --> 00:07:36,030
这个 buffer 里面已经有一些内容了

181
00:07:36,030 --> 00:07:39,020
而这个 content 它是后来的是吧

182
00:07:39,020 --> 00:07:40,200
后来的内容呢

183
00:07:40,200 --> 00:07:43,140
你到先写到这个文件里面去了

184
00:07:43,140 --> 00:07:44,210
那样不行

185
00:07:44,210 --> 00:07:46,070
所以的话要求说

186
00:07:46,070 --> 00:07:48,870
如果 buffer 目前它里面有内容的话

187
00:07:48,870 --> 00:07:51,750
那么需要先把 buffer 里面有的内容呢

188
00:07:51,750 --> 00:07:53,590
先写到磁盘里面去

189
00:07:53,590 --> 00:07:57,460
所以啊，我们可以直接调用这个 flash ，哎

190
00:07:57,460 --> 00:08:00,620
先 flash 一下就执行这两行嘛

191
00:08:00,620 --> 00:08:04,355
然后再去执行这个 i fault write 

192
00:08:04,355 --> 00:08:05,400
好

193
00:08:05,400 --> 00:08:07,400
那再考虑另外的情况啊

194
00:08:07,400 --> 00:08:12,440
else 我先把这一行粘贴到 else 里面来

195
00:08:12,440 --> 00:08:14,900
好，那虽然说这个 content 呀

196
00:08:14,900 --> 00:08:17,520
它没有超出 buffer 的总长度

197
00:08:17,520 --> 00:08:21,785
但是呢，由于 buffer 里面本来可能是有内容的对吧

198
00:08:21,785 --> 00:08:24,880
那如果说这个 content 的长度

199
00:08:24,880 --> 00:08:27,910
再加上这个 Buff and index 

200
00:08:27,910 --> 00:08:31,060
他超过了这个八分的总长度的话

201
00:08:31,060 --> 00:08:32,018
也不行

202
00:08:32,018 --> 00:08:33,200
也需要什么

203
00:08:33,200 --> 00:08:35,582
也需要先 flash 对吧

204
00:08:35,582 --> 00:08:37,270
所以这边再来个判断啊

205
00:08:37,270 --> 00:08:40,950
如果说 W 点八分 and index 

206
00:08:40,950 --> 00:08:45,590
它再加上啊，你将要写的这个内容的长度

207
00:08:45,590 --> 00:08:47,430
如果超出了

208
00:08:49,030 --> 00:08:52,090
buffer 的这个总长度、总容量的话

209
00:08:52,090 --> 00:08:55,890
那么呢，也需要先 flash 一下，对吧

210
00:08:55,890 --> 00:08:58,710
那么你一旦调过 flash 的话

211
00:08:58,710 --> 00:09:02,140
这个按 index 就值为零了吗

212
00:09:02,140 --> 00:09:03,610
那变成零之后的话

213
00:09:03,610 --> 00:09:05,810
你再执行这个 copy 拷贝

214
00:09:05,810 --> 00:09:08,040
相当于是说这边是零嘛啊

215
00:09:08,040 --> 00:09:09,640
相当是你要把这个 content 

216
00:09:09,640 --> 00:09:11,960
就拷贝到这个 buffer 的起始位置

217
00:09:11,960 --> 00:09:14,172
这当然是能够容纳下了

218
00:09:14,172 --> 00:09:15,050
好注意

219
00:09:15,050 --> 00:09:19,050
那一旦你把 content 追加到 buffer 后面之后

220
00:09:19,050 --> 00:09:23,370
那么对应的这个 and inde

221
00:09:23,370 --> 00:09:25,170
它是不是得往后走啊

222
00:09:25,170 --> 00:09:26,490
是不是得增加呀，对吧

223
00:09:26,490 --> 00:09:28,750
加上等于 L 

224
00:09:30,970 --> 00:09:33,650
那么对于最上面这个 if 是吧

225
00:09:33,650 --> 00:09:34,450
对于这种情况

226
00:09:34,450 --> 00:09:37,610
我们的 buffer and index 实际上不需要动

227
00:09:37,610 --> 00:09:38,230
为什么呢

228
00:09:38,230 --> 00:09:42,477
因为我们这个 flash 已经把它置为零了

229
00:09:42,477 --> 00:09:45,880
然后呢，你这个 content 你并没有往 buffer 里面追加

230
00:09:45,880 --> 00:09:47,535
你是直接写字盘了

231
00:09:47,535 --> 00:09:51,620
所以说这个按 index 就还应该保持等于零

232
00:09:51,620 --> 00:09:52,730
就可以了

233
00:09:52,730 --> 00:09:55,320
好，我们的核心功能就写完了啊

234
00:09:55,320 --> 00:09:56,927
最后我们再来封装一个

235
00:09:56,927 --> 00:09:59,330
因为这个是写 bat 切片嘛

236
00:09:59,330 --> 00:10:00,370
我们再来封装一个

237
00:10:00,370 --> 00:10:04,290
简单的就是直接支持写 string 啊

238
00:10:04,290 --> 00:10:05,440
写字符串

239
00:10:05,440 --> 00:10:07,860
它是一个字符串

240
00:10:07,860 --> 00:10:08,970
这个很简单啊

241
00:10:08,970 --> 00:10:14,750
我们只需要那个调一下这个 write 

242
00:10:14,750 --> 00:10:20,910
然后呢，把这个 content 转变成 bite 切片就可以了

243
00:10:20,910 --> 00:10:22,320
好，搞定

244
00:10:22,320 --> 00:10:23,820
那么实现完之后

245
00:10:23,820 --> 00:10:26,590
我们再来写一些测试代码啊

246
00:10:26,590 --> 00:10:29,510
来验证一下我们写的这个 buffer 的 file writer 

247
00:10:29,510 --> 00:10:31,170
功能上是没有问题的

248
00:10:31,170 --> 00:10:34,840
然后看看它的这个性能到底能好多少

249
00:10:34,840 --> 00:10:36,570
这边我就直接写好了啊

250
00:10:36,570 --> 00:10:37,510
第一种方式呢

251
00:10:37,510 --> 00:10:40,390
是不用我们自己写的这个 buffer 的 writer 啊

252
00:10:40,390 --> 00:10:43,490
就直接使用原始的那个 file 去写文件

253
00:10:43,490 --> 00:10:47,010
比如说你指定这个写入的文件名称啊

254
00:10:47,010 --> 00:10:48,410
包含路径啊

255
00:10:48,410 --> 00:10:50,485
open file 打开文件

256
00:10:50,485 --> 00:10:52,670
然后呢，这边是要 for 循环

257
00:10:52,670 --> 00:10:54,290
这个是10万次啊

258
00:10:54,290 --> 00:10:55,852
for 循环10万次

259
00:10:55,852 --> 00:10:57,840
我上面定义好了一个

260
00:10:57,840 --> 00:10:59,620
这个全局的一个常量啊

261
00:10:59,620 --> 00:11:01,480
就这个 log text 

262
00:11:01,480 --> 00:11:02,780
它是一个常量

263
00:11:02,780 --> 00:11:04,065
就这样一

264
00:11:04,065 --> 00:11:06,410
随便搞了一个字符串吧

265
00:11:06,410 --> 00:11:08,430
我要把这个字符串呢

266
00:11:08,430 --> 00:11:10,120
写到这个文件里面去

267
00:11:10,120 --> 00:11:11,700
我要写10万次

268
00:11:11,700 --> 00:11:15,140
这是原始的写文件方法

269
00:11:15,140 --> 00:11:16,420
下面还有啊

270
00:11:16,420 --> 00:11:18,202
这是打开一个普通文件

271
00:11:18,202 --> 00:11:20,090
然后呢，我要把这个 fault 啊

272
00:11:20,090 --> 00:11:24,510
传给我们自己写的这个 buffer file writer 啊

273
00:11:24,510 --> 00:11:28,660
这边指定缓冲大小是40964 K 

274
00:11:28,660 --> 00:11:29,410
然后呢

275
00:11:29,410 --> 00:11:34,810
用我们自己刚刚实现的这个 buffer 的 writer 去调 red stream 

276
00:11:34,810 --> 00:11:37,902
因为我们上面不是实现了这个 red stream 吗

277
00:11:37,902 --> 00:11:39,860
好，也是一模一样的内容

278
00:11:39,860 --> 00:11:42,750
也是 for 循环10万次写文件

279
00:11:42,750 --> 00:11:45,500
然后我们分别把这两个函数调一下

280
00:11:45,500 --> 00:11:47,300
看看它们的性能如何

281
00:11:47,300 --> 00:11:49,280
这边是测试函数

282
00:11:49,280 --> 00:11:51,200
先是无缓冲的方式

283
00:11:51,200 --> 00:11:53,340
然后呢是这个带缓冲的方式

284
00:11:53,340 --> 00:11:55,840
是写到两个不同的文件里面去

285
00:11:55,840 --> 00:11:58,722
分别统计他们这个耗时

286
00:11:58,722 --> 00:12:00,947
把单侧跑一下

287
00:12:00,947 --> 00:12:03,240
直接搞的话是900多毫秒

288
00:12:03,240 --> 00:12:06,330
如果带缓冲的话是60多毫秒对吧

289
00:12:06,330 --> 00:12:08,885
基本上是快了十倍左右吧

290
00:12:08,885 --> 00:12:10,560
让我们看看这个文件啊

291
00:12:10,560 --> 00:12:14,300
在这个 data 目录下一个是 NO buffer 

292
00:12:14,300 --> 00:12:15,820
看看它一共多少行

293
00:12:15,820 --> 00:12:17,760
翻到最下面啊

294
00:12:17,760 --> 00:12:19,270
一共是10万行

295
00:12:19,270 --> 00:12:22,050
还有一个是 with buffer ，带 buffer 的啊

296
00:12:22,050 --> 00:12:25,075
带 buffer 的话来到最下面

297
00:12:25,075 --> 00:12:27,110
好，这个也是10万行

298
00:12:27,110 --> 00:12:27,940
没有问题
