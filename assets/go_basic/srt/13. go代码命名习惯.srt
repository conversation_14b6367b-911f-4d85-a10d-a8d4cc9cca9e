1
00:00:00,340 --> 00:00:02,060
我们后面还要写很多代码

2
00:00:02,060 --> 00:00:04,007
所以我们把它放到一个

3
00:00:04,007 --> 00:00:05,870
单独的目录里面去点

4
00:00:05,870 --> 00:00:07,930
这边我们新建一个文件夹

5
00:00:07,930 --> 00:00:09,470
叫做 basic 吧

6
00:00:09,470 --> 00:00:10,787
随便起个目录

7
00:00:10,787 --> 00:00:12,840
然后把这个 hello world 

8
00:00:12,840 --> 00:00:17,480
点 go 呢，把它拖到这个目录里面去

9
00:00:17,480 --> 00:00:20,450
然后我们来讲一讲关于 go 原里面这些

10
00:00:20,450 --> 00:00:23,330
不管是代码还是文件的命名方式吧

11
00:00:23,330 --> 00:00:26,970
但这个没有明确规定必须怎么命名

12
00:00:26,970 --> 00:00:30,190
只是一个大家都习惯这种方式了啊

13
00:00:30,190 --> 00:00:31,730
不是说必须遵守

14
00:00:31,730 --> 00:00:34,370
比如说像这种函数名称啊

15
00:00:34,370 --> 00:00:35,590
函数名称的话

16
00:00:35,590 --> 00:00:40,000
假如你有多个单词 hello world 啊

17
00:00:40,000 --> 00:00:44,130
那么呢，每个单词首字母你可以用大写来写

18
00:00:44,130 --> 00:00:45,870
这样的话你很容易就看出来

19
00:00:45,870 --> 00:00:47,640
单词的分界点在哪吗

20
00:00:47,640 --> 00:00:49,240
其实你像 Python 的话

21
00:00:49,240 --> 00:00:50,760
它有另外一种应用方式

22
00:00:50,760 --> 00:00:52,160
称之为蛇形

23
00:00:52,160 --> 00:00:56,072
比方说 hello world 

24
00:00:56,072 --> 00:01:00,090
OK ，它是用这个下划线来区分不同的单词

25
00:01:00,090 --> 00:01:04,300
那这种方式呢，我们称之为蛇形形式

26
00:01:04,300 --> 00:01:06,760
而这种命名方式呢

27
00:01:06,760 --> 00:01:09,720
我们称之为驼峰形式

28
00:01:09,720 --> 00:01:12,720
蛇形嘛，就好比一条小蛇，对吧

29
00:01:12,720 --> 00:01:14,360
有的地方是隆起的

30
00:01:14,360 --> 00:01:16,280
有的地方呢，是贴着地面的

31
00:01:16,280 --> 00:01:18,420
而驼峰呢，像这种大些的地

32
00:01:18,420 --> 00:01:20,582
就好比是一个驼峰一样

33
00:01:20,582 --> 00:01:22,430
那关于第一个单词

34
00:01:22,430 --> 00:01:25,085
这个首字母到底大写还是小写

35
00:01:25,085 --> 00:01:27,270
在构圆里面也是有讲究的

36
00:01:27,270 --> 00:01:29,010
大写表示包外可见

37
00:01:29,010 --> 00:01:30,830
小写表示仅包内可见

38
00:01:30,830 --> 00:01:33,342
这个都我们到后面再详细讲解

39
00:01:33,342 --> 00:01:34,700
这边大家只需要知道

40
00:01:34,700 --> 00:01:37,280
go a 里面一般习惯使用驼峰形式

41
00:01:37,280 --> 00:01:39,160
而不使用蛇形形式啊

42
00:01:39,160 --> 00:01:40,520
不管是函数名称也好

43
00:01:40,520 --> 00:01:42,060
还是说变量名称也好

44
00:01:42,060 --> 00:01:44,240
然后我们再来关注这个文件名是吧

45
00:01:44,240 --> 00:01:46,670
上一节课我们这个 hello world 点 g

46
00:01:46,670 --> 00:01:49,195
这边呢，又是使用的蛇形

47
00:01:49,195 --> 00:01:50,770
那这个有讲究吗

48
00:01:50,770 --> 00:01:53,520
其实你使用驼峰形式也完全 OK 啊

49
00:01:53,520 --> 00:01:55,000
代码能够正常运行

50
00:01:55,000 --> 00:01:55,820
包括这边

51
00:01:55,820 --> 00:01:59,820
你如果函数名称非要使用射线也没问题

52
00:01:59,820 --> 00:02:01,582
代码也能正常运行

53
00:02:01,582 --> 00:02:02,970
只不过我们看看

54
00:02:02,970 --> 00:02:04,950
购物员标准库的命名方式啊

55
00:02:04,950 --> 00:02:07,737
我们在 vs code 里面按住 CTRL 键

56
00:02:07,737 --> 00:02:11,975
同时呢去点击 FMT 这个包

57
00:02:11,975 --> 00:02:13,800
然后我们点击这个

58
00:02:13,800 --> 00:02:15,720
点到源码里面去

59
00:02:15,720 --> 00:02:17,207
好，这是源码

60
00:02:17,207 --> 00:02:20,900
在源码里面我们找找它的这个命名方式啊

61
00:02:20,900 --> 00:02:23,290
你看这个是首字母大写的

62
00:02:23,290 --> 00:02:25,660
我们找一个两个单词的函数

63
00:02:25,660 --> 00:02:26,500
这个是吧

64
00:02:26,500 --> 00:02:27,620
它是两个单词

65
00:02:27,620 --> 00:02:29,780
由 web 和 error 两个单词构成

66
00:02:29,780 --> 00:02:31,140
它使用的是驼峰形式

67
00:02:31,140 --> 00:02:32,525
没有使用蛇形

68
00:02:32,525 --> 00:02:34,320
那关于文件名呢

69
00:02:34,320 --> 00:02:36,200
我们点开这个包啊

70
00:02:36,200 --> 00:02:38,685
你看这个包向我们展开一下

71
00:02:38,685 --> 00:02:39,440
找一找

72
00:02:39,440 --> 00:02:44,020
你看它这里面文件名的命名方式全部是小写

73
00:02:44,020 --> 00:02:45,780
然后用下划线进行分割

74
00:02:45,780 --> 00:02:48,480
像这个 ghost stringer 下划

75
00:02:48,480 --> 00:02:50,140
example 下划线 test 对吧

76
00:02:50,140 --> 00:02:51,437
完全是蛇形

77
00:02:51,437 --> 00:02:55,190
然后还有个地方就是像我们这个 go 点 mod 啊

78
00:02:55,190 --> 00:02:57,585
这个 module 的名称

79
00:02:57,585 --> 00:03:00,477
这边我是使用的全部小写

80
00:03:00,477 --> 00:03:03,860
我们可以找一个比较大的第三方库

81
00:03:03,860 --> 00:03:05,790
来看看它的命名习惯

82
00:03:05,790 --> 00:03:08,962
比如这个是字节的 sonic 库

83
00:03:08,962 --> 00:03:11,010
它的构建 mod 文件里面

84
00:03:11,010 --> 00:03:13,240
module 名称是这样命名的对吧

85
00:03:13,240 --> 00:03:15,590
这边有斜杠分隔

86
00:03:15,590 --> 00:03:17,390
然后呢，也全部都是小写

87
00:03:17,390 --> 00:03:18,910
我们再来看下面这些

88
00:03:18,910 --> 00:03:20,270
也全部是小写

89
00:03:20,270 --> 00:03:22,250
然后他这边呢，不是用下划线

90
00:03:22,250 --> 00:03:25,140
是用的横杠来分隔两个单词

91
00:03:25,140 --> 00:03:28,200
你看这个也是横杠分隔两个单词

92
00:03:28,200 --> 00:03:30,495
这个呢也是横杠分隔

93
00:03:30,495 --> 00:03:31,860
那看来这种形式

94
00:03:31,860 --> 00:03:35,372
就是大家普遍比较习惯的命名方式

95
00:03:35,372 --> 00:03:37,190
最后谈一谈我个人的观点

96
00:03:37,190 --> 00:03:39,990
我觉得这种不成文的命名习惯

97
00:03:39,990 --> 00:03:41,840
它并不是一个强制要求

98
00:03:41,840 --> 00:03:43,470
如果你真的不想遵守

99
00:03:43,470 --> 00:03:44,430
也可以不遵守

100
00:03:44,430 --> 00:03:46,742
我们应该尊重彼此的选择

101
00:03:46,742 --> 00:03:48,700
就好比是男生留长发

102
00:03:48,700 --> 00:03:50,275
女生剃光头

103
00:03:50,275 --> 00:03:52,490
可能不符合大众审美

104
00:03:52,490 --> 00:03:54,520
但是既然当事人选择这样做

105
00:03:54,520 --> 00:03:55,880
我们就应该尊重他
