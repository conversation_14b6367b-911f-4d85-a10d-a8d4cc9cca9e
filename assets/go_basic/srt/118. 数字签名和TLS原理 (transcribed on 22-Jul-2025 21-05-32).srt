1
00:00:00,520 --> 00:00:03,080
这几个来看两个密码学的高级应用

2
00:00:03,080 --> 00:00:05,120
一个是数字签名数字证书

3
00:00:05,120 --> 00:00:06,400
一个是tls

4
00:00:07,160 --> 00:00:08,700
先看一下数字签名

5
00:00:08,960 --> 00:00:09,720
签名嘛

6
00:00:09,980 --> 00:00:11,000
就是亲上你的名字

7
00:00:11,000 --> 00:00:12,800
就代表你承认了这份文件

8
00:00:13,060 --> 00:00:14,080
承认了这个数据

9
00:00:14,600 --> 00:00:16,120
那什么叫数字签名呢

10
00:00:16,379 --> 00:00:20,220
就是通过密码的技术来确保一个人他是承认

11
00:00:20,480 --> 00:00:22,020
某个文件某个数据的

12
00:00:22,280 --> 00:00:23,040
来看这个图

13
00:00:23,560 --> 00:00:24,840
最开始的是名文

14
00:00:25,080 --> 00:00:26,620
比如名文里面写着

15
00:00:26,880 --> 00:00:27,900
我欠你10块钱

16
00:00:28,420 --> 00:00:28,920
这是名文

17
00:00:28,920 --> 00:00:30,960
对这样一个信息

18
00:00:31,220 --> 00:00:32,000
这样一个字符章

19
00:00:32,240 --> 00:00:34,040
我先实行一次哈希腊酸

20
00:00:34,560 --> 00:00:35,320
得到一个摘要

21
00:00:35,580 --> 00:00:36,340
那么这个摘要

22
00:00:36,600 --> 00:00:37,880
就是一个固定长度

23
00:00:38,400 --> 00:00:40,700
他不管名文有多长有多短

24
00:00:40,960 --> 00:00:42,480
摘要长度总是固定的

25
00:00:43,000 --> 00:00:43,520
然后呢

26
00:00:43,760 --> 00:00:44,800
我使用私钥

27
00:00:45,300 --> 00:00:45,820
进加密

28
00:00:46,320 --> 00:00:47,360
前面我们讲过

29
00:00:48,120 --> 00:00:49,920
可以使用公钥加密私钥解密

30
00:00:50,160 --> 00:00:52,720
反过来也可以使用私钥加密公钥解密

31
00:00:53,500 --> 00:00:54,000
都可以

32
00:00:54,260 --> 00:00:55,800
这边是使用私钥进加密

33
00:00:56,560 --> 00:00:57,080
注意

34
00:00:57,080 --> 00:01:00,400
在数字签文里面这个地方必须使用私钥进加密

35
00:01:00,920 --> 00:01:01,680
得到密文

36
00:01:02,460 --> 00:01:02,960
然后呢

37
00:01:03,220 --> 00:01:05,519
我要把两个东西公开出去

38
00:01:06,300 --> 00:01:09,880
就把我的名文和密文全部公开出去

39
00:01:10,140 --> 00:01:11,160
让别人都知道

40
00:01:11,920 --> 00:01:12,440
然后呢

41
00:01:12,440 --> 00:01:13,200
别人

42
00:01:13,720 --> 00:01:14,480
就是大众

43
00:01:14,740 --> 00:01:16,020
会做两件事情

44
00:01:16,539 --> 00:01:17,560
这件事情呢

45
00:01:17,820 --> 00:01:19,100
就他把这个密文呢

46
00:01:19,360 --> 00:01:21,140
也按照同样的哈希算法

47
00:01:21,400 --> 00:01:22,160
也哈希斯

48
00:01:22,679 --> 00:01:23,440
得到一个摘要

49
00:01:23,700 --> 00:01:24,480
然后呢

50
00:01:24,720 --> 00:01:25,500
他对

51
00:01:25,759 --> 00:01:27,280
我公开的这个密文呢

52
00:01:27,539 --> 00:01:28,820
用我的公钥

53
00:01:29,080 --> 00:01:30,100
来执行解密

54
00:01:30,620 --> 00:01:32,660
因为说私钥是我自己保管嘛

55
00:01:32,920 --> 00:01:33,420
而公钥

56
00:01:33,680 --> 00:01:35,220
是公开给全社会的

57
00:01:35,480 --> 00:01:36,760
所以他每个人都知道我的公钥

58
00:01:37,020 --> 00:01:38,300
他拿着我的公钥呢

59
00:01:38,540 --> 00:01:39,060
对这个密文

60
00:01:39,320 --> 00:01:40,080
执行一次解密

61
00:01:40,860 --> 00:01:41,100
那

62
00:01:41,360 --> 00:01:42,140
按理来说

63
00:01:42,640 --> 00:01:43,420
这个

64
00:01:43,660 --> 00:01:44,180
GEL2

65
00:01:44,440 --> 00:01:45,720
应该跟GEL1

66
00:01:45,980 --> 00:01:46,740
是一样的

67
00:01:47,500 --> 00:01:47,760
同时

68
00:01:48,020 --> 00:01:48,520
我们看右边

69
00:01:49,040 --> 00:01:50,060
右边的话

70
00:01:50,320 --> 00:01:51,860
这边是私钥加密

71
00:01:51,860 --> 00:01:53,140
这边是公钥解密

72
00:01:53,640 --> 00:01:55,700
所以解密之后的这个GEL3

73
00:01:55,700 --> 00:01:57,740
应该跟这个GEL1也是一样

74
00:01:58,520 --> 00:02:01,840
从而我们推出这个GEL2跟GEL3

75
00:02:01,840 --> 00:02:03,380
他俩应该是一样

76
00:02:04,140 --> 00:02:07,980
那如果大众验证了GEL2和3是一样的

77
00:02:08,240 --> 00:02:08,759
那么呢

78
00:02:09,000 --> 00:02:13,620
大众就承认这个密文确实是由我签署的

79
00:02:13,880 --> 00:02:14,640
为什么呢

80
00:02:15,160 --> 00:02:16,680
因为不可能是由别人签署的

81
00:02:16,680 --> 00:02:18,480
如果是别人签署的话

82
00:02:18,720 --> 00:02:19,240
别人

83
00:02:19,500 --> 00:02:21,540
他是不知道我的私钥的

84
00:02:22,320 --> 00:02:24,620
而大众解密使用的是我的公钥

85
00:02:25,120 --> 00:02:26,160
必须是我的公钥

86
00:02:26,400 --> 00:02:27,440
配上我的私钥

87
00:02:27,680 --> 00:02:29,480
这样的话解密解回来

88
00:02:29,740 --> 00:02:31,780
这个3和1才是相当的

89
00:02:32,560 --> 00:02:35,360
所以实际上是通过非对顺加密

90
00:02:35,620 --> 00:02:36,140
然后呢

91
00:02:36,400 --> 00:02:39,720
又知道私钥是只有本人保管的

92
00:02:40,240 --> 00:02:40,740
从而呢

93
00:02:41,000 --> 00:02:41,760
来断电

94
00:02:42,280 --> 00:02:42,800
一段信息

95
00:02:43,040 --> 00:02:44,840
就是由某个人他承认的

96
00:02:45,100 --> 00:02:45,600
他签署的

97
00:02:45,600 --> 00:02:48,160
那这个地方为什么要搞这个哈希呢

98
00:02:48,420 --> 00:02:48,920
我们想一下

99
00:02:49,180 --> 00:02:50,720
如果把哈希去掉

100
00:02:50,980 --> 00:02:52,260
直接从明文

101
00:02:52,519 --> 00:02:53,280
私钥加密

102
00:02:53,540 --> 00:02:54,299
得到密文

103
00:02:54,820 --> 00:02:55,579
然后我把

104
00:02:55,840 --> 00:02:57,880
明文和密文都看出去

105
00:02:58,140 --> 00:02:58,920
那么大众

106
00:02:59,160 --> 00:02:59,940
用我的公钥

107
00:03:00,440 --> 00:03:01,480
对密文解密

108
00:03:01,480 --> 00:03:02,760
一样可以接受密文

109
00:03:03,280 --> 00:03:04,799
直接两个明文对比一下

110
00:03:05,579 --> 00:03:06,600
一样可以证明

111
00:03:07,119 --> 00:03:07,620
这个信息

112
00:03:07,880 --> 00:03:08,640
是我签署的

113
00:03:09,420 --> 00:03:10,179
那为什么中间

114
00:03:10,440 --> 00:03:11,980
还要多搞一次哈希呢

115
00:03:12,239 --> 00:03:13,000
其实啊

116
00:03:13,260 --> 00:03:13,519
搞哈希

117
00:03:13,760 --> 00:03:14,540
主要是否出于

118
00:03:14,799 --> 00:03:15,820
计算效率的考虑

119
00:03:16,579 --> 00:03:17,359
因为我们说

120
00:03:17,600 --> 00:03:18,640
非对顺加密

121
00:03:18,880 --> 00:03:19,399
是很慢的

122
00:03:19,660 --> 00:03:20,160
他很吃

123
00:03:20,420 --> 00:03:20,940
计算资源

124
00:03:21,460 --> 00:03:22,480
那这个明文呢

125
00:03:22,980 --> 00:03:23,500
通常情况

126
00:03:23,760 --> 00:03:24,780
是很长的

127
00:03:25,300 --> 00:03:26,579
那通过哈希之后

128
00:03:27,079 --> 00:03:28,880
把它转成一个很短的

129
00:03:29,380 --> 00:03:29,900
信息

130
00:03:30,160 --> 00:03:31,440
再去过我们的

131
00:03:31,700 --> 00:03:32,200
非对顺加密

132
00:03:32,720 --> 00:03:33,740
计算会比较快

133
00:03:34,000 --> 00:03:35,780
同时哈希效率也是比较高的

134
00:03:36,540 --> 00:03:37,820
那有了数字签名之后

135
00:03:38,080 --> 00:03:39,620
那其实就有了数字证书

136
00:03:40,140 --> 00:03:41,660
什么是数字证书呢

137
00:03:42,180 --> 00:03:43,460
其实你把这个证书啊

138
00:03:43,720 --> 00:03:46,020
就理解为我们这边这个明文就可以了

139
00:03:46,540 --> 00:03:48,060
只不过呢这个证书上

140
00:03:48,840 --> 00:03:50,380
他存储的核心信息

141
00:03:50,620 --> 00:03:51,900
是某一个人的公钥

142
00:03:52,420 --> 00:03:55,500
就说我们在上一个环节数字签名这

143
00:03:55,740 --> 00:03:59,080
不是说每个人要把他的公钥公开出去吗

144
00:03:59,840 --> 00:04:02,660
关键是这个环节就公布公钥这个环节

145
00:04:02,920 --> 00:04:04,960
怎么保证不会被别人伪造呢

146
00:04:05,720 --> 00:04:08,040
比方说你的公钥本来是ABC

147
00:04:08,280 --> 00:04:10,080
然后呢我开发了一个网站

148
00:04:10,340 --> 00:04:12,380
我在网站上写着你的公钥

149
00:04:12,640 --> 00:04:13,400
是123

150
00:04:13,920 --> 00:04:14,940
我归捣乱

151
00:04:15,200 --> 00:04:16,220
我欺骗大众

152
00:04:16,740 --> 00:04:17,240
这样可以吗

153
00:04:18,019 --> 00:04:18,780
那现在是不可以的

154
00:04:19,560 --> 00:04:20,320
所以啊

155
00:04:20,579 --> 00:04:21,600
数字证书

156
00:04:21,860 --> 00:04:24,680
就是要通过一种安全权威的渠道

157
00:04:24,920 --> 00:04:26,720
来证明这就是你的公钥

158
00:04:27,480 --> 00:04:30,040
在实际当中并不是说每个人都需要公钥

159
00:04:30,300 --> 00:04:32,600
通常是一些机构或者公司

160
00:04:32,860 --> 00:04:33,640
他才需要公钥

161
00:04:33,640 --> 00:04:35,440
他要开发一个网站

162
00:04:35,680 --> 00:04:36,719
这个网站呢要支持

163
00:04:36,960 --> 00:04:37,479
HTBS

164
00:04:38,500 --> 00:04:40,560
那么这个公司就需要一个公钥

165
00:04:41,060 --> 00:04:44,400
那为了证明这个公钥确实是这公司的CA机构

166
00:04:44,640 --> 00:04:46,180
需要给这个公司呢

167
00:04:46,440 --> 00:04:47,200
做一个登记

168
00:04:47,719 --> 00:04:50,800
那么我们统一去查询CA机构就知道

169
00:04:51,039 --> 00:04:52,580
某家公司的公钥是什么

170
00:04:52,580 --> 00:04:54,120
但问题是

171
00:04:54,380 --> 00:04:55,400
现在一个CA机构

172
00:04:55,659 --> 00:04:58,219
他说A公司的公钥是某某

173
00:04:58,719 --> 00:05:01,020
那我为什么要相信这个CA机构呢

174
00:05:02,060 --> 00:05:04,860
那其实啊这个CA机构也是分等级的

175
00:05:05,120 --> 00:05:05,640
这是一级

176
00:05:05,900 --> 00:05:06,659
二级三级

177
00:05:06,919 --> 00:05:07,680
就类似于

178
00:05:07,940 --> 00:05:09,219
国家省市

179
00:05:09,479 --> 00:05:10,500
这种数状结构

180
00:05:11,260 --> 00:05:11,780
我们呢

181
00:05:12,039 --> 00:05:14,080
必须相信一个最顶级的CA

182
00:05:14,340 --> 00:05:15,099
就是跟CA

183
00:05:16,140 --> 00:05:16,640
这个你必须相信

184
00:05:16,900 --> 00:05:17,419
否则的话

185
00:05:17,659 --> 00:05:19,200
这个新体系就崩塌了

186
00:05:19,960 --> 00:05:22,280
那么通常我们的操作系统

187
00:05:22,780 --> 00:05:24,060
或者我们的浏览器

188
00:05:24,320 --> 00:05:25,599
都会自带一些

189
00:05:25,860 --> 00:05:27,400
比较顶级的CA

190
00:05:28,159 --> 00:05:30,200
好现在假设这个一级CA

191
00:05:30,460 --> 00:05:31,480
我们是相信的

192
00:05:32,260 --> 00:05:32,760
然后呢

193
00:05:33,020 --> 00:05:35,080
一级CA他要收取二级CA

194
00:05:35,840 --> 00:05:36,360
比方说

195
00:05:36,599 --> 00:05:37,880
中央政府他说

196
00:05:38,140 --> 00:05:38,659
河北

197
00:05:38,919 --> 00:05:40,960
这是一个合法的二级CA

198
00:05:41,719 --> 00:05:44,539
那中央政府说河北是一个合法的二级CA

199
00:05:44,800 --> 00:05:45,320
这句话

200
00:05:45,820 --> 00:05:47,360
我为什么要相信他呢

201
00:05:48,120 --> 00:05:51,960
只要中央政府给这句话加一个数字签名就可以了

202
00:05:53,000 --> 00:05:54,280
因为你相信中央政府

203
00:05:54,780 --> 00:05:55,800
他都签名了

204
00:05:56,060 --> 00:05:57,600
你自然应该相信这句话本身

205
00:05:58,360 --> 00:05:58,880
所以啊

206
00:05:59,140 --> 00:06:00,160
从一级CA

207
00:06:00,680 --> 00:06:01,700
授权二级CA

208
00:06:02,200 --> 00:06:05,540
这个过程本身是借助于数字签名来完成的

209
00:06:06,040 --> 00:06:07,320
那你相信二级CA

210
00:06:07,580 --> 00:06:11,160
二级CA再用他的数字签名再来授权三级CA

211
00:06:11,680 --> 00:06:12,440
也是一样的过程

212
00:06:13,220 --> 00:06:13,720
到时候呢

213
00:06:13,980 --> 00:06:14,500
三级CA

214
00:06:14,500 --> 00:06:18,080
他可能会为某一家公司的公钥来做背书

215
00:06:18,340 --> 00:06:19,880
一个公司有了公钥

216
00:06:20,140 --> 00:06:20,900
就可以上线

217
00:06:21,160 --> 00:06:22,940
基于HTPS网站

218
00:06:23,200 --> 00:06:25,260
刚才我们提到HTPS网站

219
00:06:26,020 --> 00:06:28,320
那这个HTPS他为什么需要使用公钥呢

220
00:06:29,100 --> 00:06:29,860
严格来说

221
00:06:30,120 --> 00:06:32,160
这里面其实用到了TLS协议

222
00:06:32,920 --> 00:06:34,720
或者是SSL协议

223
00:06:35,480 --> 00:06:37,800
就之前我们讲那个网络的私针协议啊

224
00:06:38,040 --> 00:06:40,100
说这个第2层是IP层

225
00:06:40,600 --> 00:06:42,660
第3层是TCP或者UDP层

226
00:06:43,420 --> 00:06:44,440
再往上是应用层

227
00:06:44,700 --> 00:06:47,520
那么HTPS就属于一种应用层协议

228
00:06:48,280 --> 00:06:51,100
我们在HTPS和TCP

229
00:06:51,620 --> 00:06:52,380
协议中间

230
00:06:52,640 --> 00:06:53,660
再插入一层

231
00:06:53,660 --> 00:06:56,220
就是这个TLS或者是SSL

232
00:06:56,480 --> 00:06:57,760
这两个协议基本差不多

233
00:06:58,260 --> 00:07:00,060
那插入这个协议是干嘛的

234
00:07:00,820 --> 00:07:03,380
他实际上就是对HTPS

235
00:07:03,640 --> 00:07:04,420
对应用层

236
00:07:04,660 --> 00:07:05,700
传下来的数据

237
00:07:05,940 --> 00:07:08,760
再执行一次加密加密之后再扔给TCP

238
00:07:09,280 --> 00:07:11,840
那这样的话我们在网络上传出数据

239
00:07:12,100 --> 00:07:13,880
就都是经过加密之后的

240
00:07:14,400 --> 00:07:16,180
你在百度上搜了个词

241
00:07:16,440 --> 00:07:17,460
这个词到底是什么

242
00:07:17,720 --> 00:07:20,280
是加密之后才发给百度的

243
00:07:21,060 --> 00:07:22,340
百度是那个搜索结果

244
00:07:22,340 --> 00:07:25,160
这个搜索结果也是加密之后才发给你的

245
00:07:25,920 --> 00:07:26,940
黑客从中间

246
00:07:27,200 --> 00:07:27,980
截取数据

247
00:07:28,220 --> 00:07:29,260
他完全无法破解

248
00:07:29,760 --> 00:07:31,820
那具体来说这个过程是这样子的

249
00:07:32,580 --> 00:07:33,860
左边是客户端

250
00:07:34,620 --> 00:07:35,400
右边是服务端

251
00:07:35,660 --> 00:07:37,700
那服务端就像是百度

252
00:07:38,720 --> 00:07:41,800
看一下客户端和服务端之间是如何传递出去的

253
00:07:42,560 --> 00:07:46,140
我们假设小民想把一个文件传给服务端

254
00:07:46,660 --> 00:07:47,680
就好比是你要把你的

255
00:07:47,940 --> 00:07:49,980
一个搜索词传给百度

256
00:07:49,980 --> 00:07:53,060
那么第一步呢我们称之为握手

257
00:07:53,300 --> 00:07:55,100
TLS握手阶段

258
00:07:55,620 --> 00:07:56,640
在这个阶段

259
00:07:56,900 --> 00:07:57,400
客户端

260
00:07:57,660 --> 00:07:58,680
他使用一个

261
00:07:58,940 --> 00:08:00,220
RSA加密算法

262
00:08:00,740 --> 00:08:02,780
这是一种非对称加密算法

263
00:08:03,540 --> 00:08:05,080
那么加密对象

264
00:08:05,340 --> 00:08:07,380
就是客户端自己的

265
00:08:07,640 --> 00:08:08,420
AESK

266
00:08:08,660 --> 00:08:10,720
AES是一种对称加密算法

267
00:08:11,480 --> 00:08:12,760
这是加密对象

268
00:08:13,280 --> 00:08:14,560
加密的话需要使用公钥

269
00:08:14,820 --> 00:08:16,100
这边使用的是

270
00:08:16,860 --> 00:08:17,880
服务端的公钥

271
00:08:17,880 --> 00:08:20,180
因为服务的公钥是公开的嘛

272
00:08:20,440 --> 00:08:22,240
我客户端可以拿到这个公钥

273
00:08:23,000 --> 00:08:23,260
好

274
00:08:23,520 --> 00:08:24,800
生成RSA密文

275
00:08:25,300 --> 00:08:26,320
把这个密文呢

276
00:08:26,580 --> 00:08:27,360
传给服务端

277
00:08:27,860 --> 00:08:28,640
这不是安全的

278
00:08:28,640 --> 00:08:29,920
因为传递的是密文

279
00:08:30,940 --> 00:08:32,720
而且是使用的服务端公钥传递的

280
00:08:33,500 --> 00:08:35,039
所以服务端收到这个公钥之后

281
00:08:35,539 --> 00:08:37,080
他使用自己的

282
00:08:37,600 --> 00:08:38,100
私钥

283
00:08:38,100 --> 00:08:40,140
就可以去解密这个密文

284
00:08:40,659 --> 00:08:41,680
解密之后

285
00:08:41,940 --> 00:08:43,220
就能够拿到

286
00:08:43,480 --> 00:08:43,980
客户端的

287
00:08:44,240 --> 00:08:45,020
AESK

288
00:08:45,780 --> 00:08:47,580
那么通过握手阶段

289
00:08:48,080 --> 00:08:48,600
服务端

290
00:08:48,860 --> 00:08:49,360
就拿到了

291
00:08:49,620 --> 00:08:50,380
客户端的

292
00:08:50,640 --> 00:08:51,160
AESK

293
00:08:51,920 --> 00:08:52,440
接下来

294
00:08:52,700 --> 00:08:53,720
是正式的数据

295
00:08:53,980 --> 00:08:54,480
传出这段

296
00:08:55,000 --> 00:08:55,260
那么

297
00:08:55,500 --> 00:08:57,040
要传出这个数据呢

298
00:08:57,820 --> 00:08:59,100
经过客户端的

299
00:08:59,340 --> 00:08:59,860
AESK

300
00:09:00,120 --> 00:09:00,880
加密吧

301
00:09:01,140 --> 00:09:02,160
得到这个密文

302
00:09:02,680 --> 00:09:03,180
把密文

303
00:09:03,440 --> 00:09:03,960
传给服务端

304
00:09:04,720 --> 00:09:05,760
那服务端

305
00:09:06,000 --> 00:09:07,280
由于已经有了

306
00:09:07,540 --> 00:09:08,820
客户端的AESK

307
00:09:09,340 --> 00:09:09,840
这是一种

308
00:09:10,100 --> 00:09:10,620
对称讲运

309
00:09:11,380 --> 00:09:11,900
所以呢

310
00:09:12,160 --> 00:09:13,180
可以解密出

311
00:09:13,440 --> 00:09:14,200
原始的数据

312
00:09:14,960 --> 00:09:19,580
所以TELS就通过这种方式来保证在网路上传出的数据呢

313
00:09:19,840 --> 00:09:20,600
是加密之后的

314
00:09:21,360 --> 00:09:23,160
而且对端能够顺利的解密

315
00:09:23,680 --> 00:09:24,960
而且这个地方看到

316
00:09:25,200 --> 00:09:26,240
这种RIC

317
00:09:26,480 --> 00:09:27,260
非对称加密

318
00:09:27,520 --> 00:09:28,280
它加密的对线呢

319
00:09:28,540 --> 00:09:29,820
仅仅是一个K

320
00:09:30,080 --> 00:09:30,840
K通常很小

321
00:09:31,360 --> 00:09:33,920
而我们真正要传出的数据可能很大

322
00:09:34,480 --> 00:09:36,000
对大数据使用这种

323
00:09:36,260 --> 00:09:37,800
对称加密也是比较合适的

324
00:09:37,800 --> 00:09:41,340
对大数据

325
00:09:41,640 --> 00:09:42,500
没问题

326
00:09:43,040 --> 00:09:43,960
对大数据

327
00:09:44,200 --> 00:09:45,860
得到大数据

328
00:09:46,260 --> 00:09:48,060
对大数据

329
00:09:48,800 --> 00:09:49,740
对大数据

330
00:09:50,400 --> 00:09:51,260
在这样子里面

331
00:09:51,920 --> 00:09:52,600
对大数据

332
00:09:52,900 --> 00:09:54,040
是

