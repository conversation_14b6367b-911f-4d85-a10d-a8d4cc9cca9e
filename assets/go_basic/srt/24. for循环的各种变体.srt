1
00:00:00,000 --> 00:00:01,300
看一下 for 循环

2
00:00:01,300 --> 00:00:04,810
for 循环最简单的常规写法是这样子的

3
00:00:04,810 --> 00:00:05,922
for 

4
00:00:05,922 --> 00:00:07,250
然后是两个分号

5
00:00:07,250 --> 00:00:08,510
分割成三部分

6
00:00:08,510 --> 00:00:09,470
第一个分号

7
00:00:09,470 --> 00:00:13,530
前面我先来初始化一些局部变量

8
00:00:13,530 --> 00:00:16,033
跟我们前面讲的 if 是一样子的

9
00:00:16,033 --> 00:00:17,770
来一个局部变量

10
00:00:17,770 --> 00:00:21,050
A 冒号等于零啊

11
00:00:21,050 --> 00:00:22,310
这个等于几都可以啊

12
00:00:22,310 --> 00:00:24,250
等于六也可以

13
00:00:24,250 --> 00:00:26,185
然后第二个分号

14
00:00:26,185 --> 00:00:28,830
那第二部分呢，是一个逻辑判断

15
00:00:28,830 --> 00:00:29,770
跟衣服一样

16
00:00:29,770 --> 00:00:31,880
是一个逻辑表达式

17
00:00:31,880 --> 00:00:32,850
比如说

18
00:00:34,650 --> 00:00:36,150
大于等于零

19
00:00:36,150 --> 00:00:39,970
好，第三部分它会执行一个操作

20
00:00:39,970 --> 00:00:41,190
这个操作是任意的

21
00:00:41,190 --> 00:00:46,115
比方说我就是 A 等于 A 减

22
00:00:46,115 --> 00:00:47,510
大括号

23
00:00:47,510 --> 00:00:52,130
大括号里面我来一个 sum 等于 sum 加上 A 

24
00:00:52,130 --> 00:00:53,557
什么意思呢

25
00:00:53,557 --> 00:00:56,840
我第一步要先执行这一部分

26
00:00:56,840 --> 00:00:58,920
初始化一个局部变量 A 

27
00:00:58,920 --> 00:01:03,180
这个变量 A 它是在 for 循环里面局部可见

28
00:01:03,180 --> 00:01:07,150
在这个地方你是拿不到变量 A 的

29
00:01:07,150 --> 00:01:08,720
还是把它删掉

30
00:01:08,720 --> 00:01:12,410
中间这一部分会判断一个表达式是否成立

31
00:01:12,410 --> 00:01:14,052
如果不成立

32
00:01:14,052 --> 00:01:16,340
根本就不会走到里面来啊

33
00:01:16,340 --> 00:01:18,800
直接就跳过整个 for 表达式

34
00:01:18,800 --> 00:01:20,755
直接往后走了

35
00:01:20,755 --> 00:01:22,040
如果成立呢

36
00:01:22,040 --> 00:01:24,640
如果成立会走到大框里面来

37
00:01:24,640 --> 00:01:27,140
大框里面可能是有好多行代

38
00:01:27,140 --> 00:01:28,805
全部执行完

39
00:01:28,805 --> 00:01:33,457
那执行完之后会来执行第三部分

40
00:01:33,457 --> 00:01:35,390
把第三部分执行完之后

41
00:01:35,390 --> 00:01:39,420
它再来判断第二部分是否依然成立

42
00:01:39,420 --> 00:01:41,820
比方说 A 不然等于六

43
00:01:41,820 --> 00:01:44,540
把六呢加到 sum 里面去

44
00:01:44,540 --> 00:01:47,210
然后呢，执行 A 等于 A 减一

45
00:01:47,210 --> 00:01:48,110
A 变成五了

46
00:01:48,110 --> 00:01:49,010
A 变成五了

47
00:01:49,010 --> 00:01:51,190
判断一下这个，哦，还成立

48
00:01:51,190 --> 00:01:52,150
还成立的话

49
00:01:52,150 --> 00:01:54,472
把五再加上去

50
00:01:54,472 --> 00:01:57,070
然后再执行这个减一变成四

51
00:01:57,070 --> 00:01:59,440
把四呢再加到 SA 里面去

52
00:01:59,440 --> 00:02:02,362
所以一直这样循环， for 循环嘛

53
00:02:02,362 --> 00:02:06,040
那直到说 A 减为零了

54
00:02:06,040 --> 00:02:08,350
把零加到 sum 里面去

55
00:02:08,350 --> 00:02:10,250
然后执行减一变为一

56
00:02:10,250 --> 00:02:11,350
变为一之后

57
00:02:11,350 --> 00:02:14,000
判断一下第二部分不成立

58
00:02:14,000 --> 00:02:18,440
那这样的话就会直接跳到第十行

59
00:02:18,440 --> 00:02:20,180
然后我们的第八行呢

60
00:02:20,180 --> 00:02:22,240
可以简单这样写是吧

61
00:02:22,240 --> 00:02:25,680
加等于把这部分可以删掉

62
00:02:25,680 --> 00:02:27,600
我们的第三部分的话

63
00:02:27,600 --> 00:02:31,240
可以直接简写为 A 减减

64
00:02:31,240 --> 00:02:32,902
就是 A 减一嘛

65
00:02:32,902 --> 00:02:35,130
然后我们的第一部分的话

66
00:02:35,130 --> 00:02:37,690
可以初始化多个局部变量

67
00:02:37,690 --> 00:02:39,490
比如说我来两个吧

68
00:02:39,490 --> 00:02:44,125
A 逗号 B ， B 呢等于30

69
00:02:44,125 --> 00:02:47,090
然后每次搞完之后的话

70
00:02:47,090 --> 00:02:49,240
A 等于 A 减一

71
00:02:49,240 --> 00:02:52,907
B 呢等于 B 除以二

72
00:02:52,907 --> 00:02:55,830
好，这样执行这样一个操作

73
00:02:55,830 --> 00:02:57,380
然后中间第二部分

74
00:02:57,380 --> 00:03:00,740
这个表达式我可以搞得更复杂一点

75
00:03:00,740 --> 00:03:03,520
来一个企业运算吧

76
00:03:03,520 --> 00:03:05,860
比如说我要求这个 B 呢

77
00:03:05,860 --> 00:03:07,657
它需要大于 A 

78
00:03:07,657 --> 00:03:10,710
好，这两个条件同时满足的话

79
00:03:10,710 --> 00:03:13,172
我的 for 循环才会继续

80
00:03:13,172 --> 00:03:14,620
那刚才讲了

81
00:03:14,620 --> 00:03:16,360
由于我的第三部分

82
00:03:16,360 --> 00:03:20,460
是在每一次把大括号里面的执行完之后

83
00:03:20,460 --> 00:03:22,040
才执行的第三部分

84
00:03:22,040 --> 00:03:24,360
那所以说我可以把第三部分呢

85
00:03:24,360 --> 00:03:26,987
剪切 CTRLX 剪切下来

86
00:03:26,987 --> 00:03:29,980
放到大括号的最后一行

87
00:03:29,980 --> 00:03:33,910
诶，跟刚才这种写法效果是完全等价的

88
00:03:33,910 --> 00:03:35,500
然后我的第一部分是

89
00:03:35,500 --> 00:03:37,340
初始化一些局部变量吗

90
00:03:37,340 --> 00:03:41,770
那假如说我希望 A 跟 B 在 for 循环退出之后

91
00:03:41,770 --> 00:03:44,470
依然可见可以直接访问的话

92
00:03:44,470 --> 00:03:47,027
那我可以把这个 A 跟 B 呢

93
00:03:47,027 --> 00:03:50,080
放到这个 for 循环上面去

94
00:03:50,080 --> 00:03:51,600
好，放这边来

95
00:03:51,600 --> 00:03:54,500
这样的话语法依然是完全 OK 的

96
00:03:54,500 --> 00:03:58,200
那么这样的话，我在 for 循环退出之后啊

97
00:03:58,200 --> 00:04:00,580
依然可以拿到 A 和 B 

98
00:04:00,580 --> 00:04:02,262
没有任何报错

99
00:04:02,262 --> 00:04:03,530
那这样的话

100
00:04:03,530 --> 00:04:07,250
我的这两个分号就可要可不要啊

101
00:04:07,250 --> 00:04:08,150
可以把它删掉

102
00:04:08,150 --> 00:04:09,602
不删也行

103
00:04:09,602 --> 00:04:12,915
依然没有任何红线报错

104
00:04:12,915 --> 00:04:16,540
然后 for 循环里面可以出现 break 和 continue 

105
00:04:16,540 --> 00:04:18,060
这两个关键字

106
00:04:18,060 --> 00:04:19,160
什么意思呢

107
00:04:19,160 --> 00:04:22,570
比如说我加一个判断啊

108
00:04:22,570 --> 00:04:27,530
如果说 A 它等于四的话

109
00:04:27,530 --> 00:04:31,310
那么我就直接 break 

110
00:04:31,310 --> 00:04:32,630
好

111
00:04:32,630 --> 00:04:36,677
A 它不是从六开始嘛

112
00:04:36,677 --> 00:04:38,590
逐渐的 A 减一嘛

113
00:04:38,590 --> 00:04:40,370
从654对吧

114
00:04:40,370 --> 00:04:41,450
变成四的时候

115
00:04:41,450 --> 00:04:42,370
我直接 break 

116
00:04:42,370 --> 00:04:47,070
这个 break 就意味着我直接退出整个 for 循环啊

117
00:04:47,070 --> 00:04:48,930
直接执行第15行代码

118
00:04:48,930 --> 00:04:50,230
这个叫 break 

119
00:04:50,230 --> 00:04:51,797
break 的时候

120
00:04:51,797 --> 00:04:54,520
这行代码就不会再执行了

121
00:04:54,520 --> 00:04:57,770
而如果改成 continue 呢

122
00:04:57,770 --> 00:05:01,820
continue 意思是说对这一次 for 循环而言

123
00:05:01,820 --> 00:05:05,360
那么后面的不再执行了

124
00:05:05,360 --> 00:05:08,480
但是呢，会进入到下一轮的 for 循环

125
00:05:08,480 --> 00:05:09,920
只要下了 for 循

126
00:05:09,920 --> 00:05:12,080
这个条件依然满足的话

127
00:05:12,080 --> 00:05:14,617
就还是会照常执行

128
00:05:14,617 --> 00:05:17,990
所以 break 是结束整个 for 循环

129
00:05:17,990 --> 00:05:22,910
而 continue 是说跳过这一次 for 循环肯定掉

130
00:05:22,910 --> 00:05:24,250
前面的依然执行

131
00:05:24,250 --> 00:05:26,350
但后面的就不执行了

132
00:05:26,350 --> 00:05:28,072
进入下一轮 for 循环

133
00:05:28,072 --> 00:05:31,320
然后我们的 for 循环还可以支持嵌套啊

134
00:05:31,320 --> 00:05:34,422
就如同我们的 if 语句可以嵌套一样

135
00:05:34,422 --> 00:05:38,210
for 循环里面你还可以再嵌套其他 for 循环

136
00:05:38,210 --> 00:05:39,730
I 从零开始

137
00:05:39,730 --> 00:05:42,970
I 小于十， I 加加

138
00:05:42,970 --> 00:05:45,870
而且这个嵌套可以有很多层

139
00:05:45,870 --> 00:05:48,862
我来个三层的 for 循环嵌套

140
00:05:48,862 --> 00:05:51,827
sum 加上等于 I 乘以 J 

141
00:05:51,827 --> 00:05:53,350
这边两层 for 循环

142
00:05:53,350 --> 00:05:55,230
它最后面还有一层 for 循环

143
00:05:55,230 --> 00:05:57,275
三层的 for 循环嵌套

144
00:05:57,275 --> 00:05:59,930
刚才我们说这个 for 关键词

145
00:05:59,930 --> 00:06:01,830
后面只剩下了第二部分

146
00:06:01,830 --> 00:06:04,410
那甚至第二部分都可以没有

147
00:06:04,410 --> 00:06:06,005
就只剩下一个 for 

148
00:06:06,005 --> 00:06:07,972
比如这样 for 

149
00:06:07,972 --> 00:06:10,930
这相当于是一个无限循环

150
00:06:10,930 --> 00:06:14,300
就意味着说我第一次打印出这样一个字符串

151
00:06:14,300 --> 00:06:17,720
然后紧接着又会进入到第二次 for 循环

152
00:06:17,720 --> 00:06:20,100
又是输出相同内容啊

153
00:06:20,100 --> 00:06:22,310
输出无穷多

154
00:06:22,310 --> 00:06:24,240
程序永远不会结束

155
00:06:24,240 --> 00:06:25,620
在不停的输出

156
00:06:25,620 --> 00:06:28,560
一般在代码里面不会出现这样的情况

157
00:06:28,560 --> 00:06:30,210
那会出现什么情况呢

158
00:06:30,210 --> 00:06:34,240
比如说我们会执行某个操作

159
00:06:34,240 --> 00:06:35,780
然后呢

160
00:06:35,780 --> 00:06:38,930
当发生某个情况的时候

161
00:06:38,930 --> 00:06:40,850
我们会执行一个 break 

162
00:06:40,850 --> 00:06:44,920
这样的话来结束这个无限的循环

163
00:06:44,920 --> 00:06:47,520
因为 break 可以结束这个 for 循环吗
