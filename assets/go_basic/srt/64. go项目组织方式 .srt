1
00:00:00,619 --> 00:00:02,020
这节课非常重要

2
00:00:02,020 --> 00:00:05,060
我们来讲一讲一个正规的购物员项目

3
00:00:05,060 --> 00:00:06,300
该如何起步

4
00:00:06,300 --> 00:00:07,547
如何初始化

5
00:00:07,547 --> 00:00:09,850
一个项目里面有多个 go 文件

6
00:00:09,850 --> 00:00:10,990
甚至多个目录

7
00:00:10,990 --> 00:00:13,150
它们之间该如何互相引用

8
00:00:13,150 --> 00:00:14,440
互相依赖

9
00:00:14,440 --> 00:00:16,850
正如我给大家的这段代码一样

10
00:00:16,850 --> 00:00:19,850
我一般在所有的项目根目录下

11
00:00:19,850 --> 00:00:23,020
都会有一个 go 点 mod 这样一个文件

12
00:00:23,020 --> 00:00:26,270
那 go 点 mod 这个文件往上看一集

13
00:00:26,270 --> 00:00:27,650
就这个 go basic 

14
00:00:27,650 --> 00:00:30,525
那么 go basic 就是一个 module 

15
00:00:30,525 --> 00:00:34,560
因为这个目录下面存在一个构建 mod 文件

16
00:00:34,560 --> 00:00:36,440
那我看到过很多学员

17
00:00:36,440 --> 00:00:38,770
他用 vs code 打开一个目录

18
00:00:38,770 --> 00:00:41,010
这个目录下面有好多子目录

19
00:00:41,010 --> 00:00:44,592
每一个子目录下面都有一个构建 mod 

20
00:00:44,592 --> 00:00:47,520
甚至说有的子下面有构建 mod 

21
00:00:47,520 --> 00:00:49,420
有的子目下面没有构建 mod 

22
00:00:49,420 --> 00:00:50,780
就很混乱

23
00:00:50,780 --> 00:00:52,860
那我呢，还是建议大

24
00:00:52,860 --> 00:00:54,600
一个 vs 课窗

25
00:00:54,600 --> 00:00:56,607
只打开一个 MODU 

26
00:00:56,607 --> 00:01:00,210
就说你打开的这一级目录下面一眼

27
00:01:00,210 --> 00:01:01,710
在一级子目录下

28
00:01:01,710 --> 00:01:04,460
一眼就能看到 gooding mode 这个文件

29
00:01:04,460 --> 00:01:06,290
如果你想同时打开多个

30
00:01:06,290 --> 00:01:08,830
比方说你想把我代码打开一下

31
00:01:08,830 --> 00:01:11,180
然后呢，自己再打开一个新目录是吧

32
00:01:11,180 --> 00:01:12,520
照着我这个代码呢

33
00:01:12,520 --> 00:01:14,445
敲一遍怎么办呢

34
00:01:14,445 --> 00:01:16,360
你可以点开文件

35
00:01:16,360 --> 00:01:18,262
点这个新建窗口

36
00:01:18,262 --> 00:01:21,110
然后呢，在新的 vs code 窗口里面

37
00:01:21,110 --> 00:01:24,170
再打开你自己的那个代码目录

38
00:01:24,170 --> 00:01:26,202
这样的话就不会互相干扰

39
00:01:26,202 --> 00:01:30,020
好，那先说这个 go 点 mod 文件是如何生成的

40
00:01:30,020 --> 00:01:33,660
我先把 go 点 mod 和 go 点 sum 这两个文件呢

41
00:01:33,660 --> 00:01:35,127
先删除删掉

42
00:01:35,127 --> 00:01:36,580
重新演示一遍

43
00:01:36,580 --> 00:01:40,920
那我其实啊，就是说我先选好我的这个代码

44
00:01:40,920 --> 00:01:42,980
存放的目录是 go basic 

45
00:01:42,980 --> 00:01:45,025
那么我来到这个目录下

46
00:01:45,025 --> 00:01:46,060
就是 CD 嘛

47
00:01:46,060 --> 00:01:47,160
进到这个目录下

48
00:01:47,160 --> 00:01:48,220
进来之后的话

49
00:01:48,220 --> 00:01:49,520
我执行一个命令

50
00:01:49,520 --> 00:01:53,000
go mode edit 初始化

51
00:01:53,000 --> 00:01:56,530
后面跟上我的那个 module 名称

52
00:01:56,530 --> 00:02:01,540
比如就叫 d q q go basic 

53
00:02:01,540 --> 00:02:05,710
那么这个 module 名称它不是说必须得带斜线啊

54
00:02:05,710 --> 00:02:06,880
K 没有斜线

55
00:02:06,880 --> 00:02:09,409
这是个很普通的字符串

56
00:02:09,409 --> 00:02:10,349
回车

57
00:02:10,349 --> 00:02:12,230
好，你看它这边呢

58
00:02:12,230 --> 00:02:14,630
会自动生成一个 go 点 mod 文件

59
00:02:14,630 --> 00:02:16,830
我们点开这个文件里面呢

60
00:02:16,830 --> 00:02:18,910
就是有这两个信息是吧

61
00:02:18,910 --> 00:02:19,910
module 后面

62
00:02:19,910 --> 00:02:23,690
那么这一传就是刚才从命令里面传过来的

63
00:02:23,690 --> 00:02:24,552
这一串

64
00:02:24,552 --> 00:02:26,800
这是我本地的 go 的版本

65
00:02:26,800 --> 00:02:28,490
1.23.3

66
00:02:28,490 --> 00:02:30,360
我们这个 model 名称啊

67
00:02:30,360 --> 00:02:33,680
将来在写 go 代码的时候是需要用到的

68
00:02:33,680 --> 00:02:35,445
待会儿给大家演示一下

69
00:02:35,445 --> 00:02:36,800
在构元里面

70
00:02:36,800 --> 00:02:40,997
代码的最小组织单位是包啊，是 package 

71
00:02:40,997 --> 00:02:44,490
而且呢，一般情况下就是一个目录

72
00:02:44,490 --> 00:02:46,270
就是你操作系统的一个目录

73
00:02:46,270 --> 00:02:48,730
它对应的就是 go 圆里面的一个包

74
00:02:48,730 --> 00:02:49,852
也就意味着

75
00:02:49,852 --> 00:02:52,970
一个目录下面不是有好多个文件吗

76
00:02:52,970 --> 00:02:54,730
那么每一个 go 文件

77
00:02:54,730 --> 00:02:59,350
它对应的这个 package 名称应该是保持一致的，对吧

78
00:02:59,350 --> 00:03:01,970
它叫 project prepare 

79
00:03:01,970 --> 00:03:03,812
你点开另外一个构件

80
00:03:03,812 --> 00:03:07,160
它肯定也叫 project prepare 啊

81
00:03:07,160 --> 00:03:08,140
一般情况下

82
00:03:08,140 --> 00:03:14,630
一个目录下不能存在两个或多个 package 名称

83
00:03:14,630 --> 00:03:16,000
第二点我们发现

84
00:03:16,000 --> 00:03:17,440
那这个 package 名

85
00:03:17,440 --> 00:03:19,930
其实跟你这个目录名称

86
00:03:19,930 --> 00:03:21,130
没有任何关系是吧

87
00:03:21,130 --> 00:03:22,990
目录名称可能有一个下划线

88
00:03:22,990 --> 00:03:25,110
我们的 package 名称可以没有下划线

89
00:03:25,110 --> 00:03:27,370
甚至 package 名称可以跟这个目录名称

90
00:03:27,370 --> 00:03:28,690
完完全全不一样

91
00:03:28,690 --> 00:03:29,930
这个没事

92
00:03:29,930 --> 00:03:30,590
好

93
00:03:30,590 --> 00:03:32,770
你这个目录下面有子目录对吧

94
00:03:32,770 --> 00:03:34,010
有 test 这个目录

95
00:03:34,010 --> 00:03:35,940
test 的话就又是一个包

96
00:03:35,940 --> 00:03:38,080
那 test 下面所有这个文件

97
00:03:38,080 --> 00:03:40,620
它们的这个 package 名称得保提示

98
00:03:40,620 --> 00:03:42,450
你看我这个时候的话

99
00:03:42,450 --> 00:03:45,910
我的 package 名称跟这个目录名称保持一致了

100
00:03:45,910 --> 00:03:47,330
这只是巧合而已啊

101
00:03:47,330 --> 00:03:48,837
你完全可以不一致

102
00:03:48,837 --> 00:03:50,280
我刚才说了

103
00:03:50,280 --> 00:03:51,540
在构元里面

104
00:03:51,540 --> 00:03:53,960
代码的最小组织单位是包

105
00:03:53,960 --> 00:03:55,840
而不是 go 文件

106
00:03:55,840 --> 00:03:58,000
其实 go 文件没有任何用处

107
00:03:58,000 --> 00:03:59,677
它只是给人看的

108
00:03:59,677 --> 00:04:01,130
对于编译器而言

109
00:04:01,130 --> 00:04:03,330
它才不管你有几个 go 文件

110
00:04:03,330 --> 00:04:05,460
你每一个构文件名称是什么

111
00:04:05,460 --> 00:04:09,660
它不管 go 文件名称只是给人看的

112
00:04:09,660 --> 00:04:10,730
我们一般的话

113
00:04:10,730 --> 00:04:12,510
把不同的函数功能

114
00:04:12,510 --> 00:04:16,430
放到一个可读性比较好的一个购物文件里面

115
00:04:16,430 --> 00:04:19,360
这样的话方便人去管理代码吗

116
00:04:19,360 --> 00:04:20,921
那也就意味

117
00:04:20,921 --> 00:04:23,920
同一个目录下面就是 package 

118
00:04:23,920 --> 00:04:25,180
相同的情况之下

119
00:04:25,180 --> 00:04:27,580
你把所有的这一些代码

120
00:04:27,580 --> 00:04:30,730
全部合并到一个购物文件里面去

121
00:04:30,730 --> 00:04:33,830
跟你分摊到不同的购物文件里面

122
00:04:33,830 --> 00:04:35,190
完全是等价的

123
00:04:35,190 --> 00:04:37,170
比如说这两个构文件

124
00:04:37,170 --> 00:04:41,777
那么我如果把这个 go 文件直接剪切过去

125
00:04:41,777 --> 00:04:44,480
剪切到这个购物文件里面来啊

126
00:04:44,480 --> 00:04:45,745
追加到后面

127
00:04:45,745 --> 00:04:47,480
然后这个 package 重复了吗

128
00:04:47,480 --> 00:04:50,280
把 package 这一行把它给删掉啊

129
00:04:50,280 --> 00:04:54,035
只保留最上面的这一个 package 

130
00:04:54,035 --> 00:04:55,740
然后这个 import 的话

131
00:04:55,740 --> 00:04:58,607
我们一般都放在最上面

132
00:04:58,607 --> 00:04:59,710
那然后呢

133
00:04:59,710 --> 00:05:02,280
这个 A 点 go 就可以删除了

134
00:05:02,280 --> 00:05:05,450
那么你看代码完全没有任何问题

135
00:05:05,450 --> 00:05:06,970
那既然是这样的话

136
00:05:06,970 --> 00:05:10,880
那么我在 copy slice 点 go 这一个文件里面

137
00:05:10,880 --> 00:05:12,320
我写了两个函数对吧

138
00:05:12,320 --> 00:05:13,520
写了一个 F 函数

139
00:05:13,520 --> 00:05:14,860
而在 F 函数里面呢

140
00:05:14,860 --> 00:05:18,490
去调用了上面写的这个 copy size 函数

141
00:05:18,490 --> 00:05:23,420
那么我如果把这个 F 函数剪切走啊

142
00:05:23,420 --> 00:05:27,125
把它移到另外一个 go 文件里面去

143
00:05:27,125 --> 00:05:28,960
加上 package 名称

144
00:05:28,960 --> 00:05:33,660
再把必要的 import 给加进来保存一下

145
00:05:33,660 --> 00:05:36,570
那么这样代码是完全没有任何问题的

146
00:05:36,570 --> 00:05:38,040
我在 A 点 go 里面

147
00:05:38,040 --> 00:05:41,870
去掉另外一个购物文件里面的函数也好

148
00:05:41,870 --> 00:05:44,840
或者引用它的某一个变量也好

149
00:05:44,840 --> 00:05:46,597
可以直接使用

150
00:05:46,597 --> 00:05:50,290
但如果说这个 package 不一样了啊

151
00:05:50,290 --> 00:05:51,110
刀都变了

152
00:05:51,110 --> 00:05:56,002
比如说在这个 test 文件里面

153
00:05:56,002 --> 00:05:58,240
他的包叫 test 对吧

154
00:05:58,240 --> 00:06:00,920
跟刚才的这个包名已经不一样了

155
00:06:00,920 --> 00:06:03,970
那这个时候你想在 test 这个文件里

156
00:06:03,970 --> 00:06:06,580
去引用 copy 这个函数

157
00:06:06,580 --> 00:06:09,720
或者说想去引用 A 这个变量

158
00:06:09,720 --> 00:06:11,820
那么呢，就稍微麻烦一点啊

159
00:06:11,820 --> 00:06:15,700
你需要在这个不管是函数还是变量前面

160
00:06:15,700 --> 00:06:18,260
加上对应的包名，好吧

161
00:06:18,260 --> 00:06:19,200
这个函数呢

162
00:06:19,200 --> 00:06:21,460
它是来自于这个包的中间

163
00:06:21,460 --> 00:06:23,280
加一个点进行分隔

164
00:06:23,280 --> 00:06:27,580
同时啊，同时还需要在你的 import 里面

165
00:06:27,580 --> 00:06:31,100
把这个包给引进来啊

166
00:06:31,100 --> 00:06:34,340
注意一下这个 import 语句该怎么写

167
00:06:34,340 --> 00:06:37,720
之前我都是直接应用标准库里面的嘛

168
00:06:37,720 --> 00:06:39,090
这个很简单

169
00:06:39,090 --> 00:06:41,700
所有的这些标准库里面的包

170
00:06:41,700 --> 00:06:45,317
都在我们的 go root source 这个目录下

171
00:06:45,317 --> 00:06:48,410
比如这是我的 go root 目录

172
00:06:48,410 --> 00:06:52,510
我们点到 SRC 这个目录下

173
00:06:52,510 --> 00:06:54,420
我们之前见过

174
00:06:54,420 --> 00:06:58,315
像这个 SMT 是一个薄

175
00:06:58,315 --> 00:07:00,900
像这个 reflect 反射这个包

176
00:07:00,900 --> 00:07:02,490
像这个 strength 

177
00:07:02,490 --> 00:07:03,970
还有 CHROKEN 

178
00:07:03,970 --> 00:07:05,937
还有 slices 

179
00:07:05,937 --> 00:07:08,180
还有 testing 啊

180
00:07:08,180 --> 00:07:10,640
都是我们之前用过的一些包

181
00:07:10,640 --> 00:07:12,200
所以标准库的话

182
00:07:12,200 --> 00:07:15,170
直接把包名放进来就可以了

183
00:07:15,170 --> 00:07:18,180
那如果是我自己写的某一个包呢

184
00:07:18,180 --> 00:07:19,675
看一下啊

185
00:07:19,675 --> 00:07:22,560
这一部分 d qq go basi

186
00:07:22,560 --> 00:07:23,740
这一部分是什么

187
00:07:23,740 --> 00:07:26,860
这一部分不就是我们的 module 名称吗

188
00:07:26,860 --> 00:07:27,300
对吧

189
00:07:27,300 --> 00:07:28,632
module 名称

190
00:07:28,632 --> 00:07:29,750
再往后走

191
00:07:29,750 --> 00:07:32,400
project prepare 是什么

192
00:07:32,400 --> 00:07:36,205
project prepare 是我们的这一集目录名称

193
00:07:36,205 --> 00:07:38,050
注意啊，不是包的名称

194
00:07:38,050 --> 00:07:40,830
因为刚才我们知道我们的包的名称里面

195
00:07:40,830 --> 00:07:42,560
是没有下划线的

196
00:07:42,560 --> 00:07:45,040
所以刚才这个地方有下划线

197
00:07:45,040 --> 00:07:49,115
表示的是我们的操作系统的文件目录名称

198
00:07:49,115 --> 00:07:50,040
注意一下

199
00:07:50,040 --> 00:07:52,250
这个地方有没有用到

200
00:07:52,250 --> 00:07:55,010
我们这个项目的根目录名称

201
00:07:55,010 --> 00:07:58,762
我们的项目根目录叫做 go 下划线 basic 

202
00:07:58,762 --> 00:08:02,010
它根本就没有出现在这个地方

203
00:08:02,010 --> 00:08:03,680
所以啊，总结一下啊

204
00:08:03,680 --> 00:08:05,890
我们这个 import 一上来

205
00:08:05,890 --> 00:08:07,690
先把 module 名称放这

206
00:08:07,690 --> 00:08:10,170
然后呢，就是这个目录

207
00:08:10,170 --> 00:08:11,390
这个目录是哪个目录了

208
00:08:11,390 --> 00:08:15,230
这个目录就是说从我们的项目根目录下开始

209
00:08:15,230 --> 00:08:16,250
往下找

210
00:08:16,250 --> 00:08:18,930
就找到了 project prepare 

211
00:08:18,930 --> 00:08:19,550
当然了

212
00:08:19,550 --> 00:08:23,370
你的 project prepare 这个目录下可能还有子目录

213
00:08:23,370 --> 00:08:25,360
子目录下还有子目录啊

214
00:08:25,360 --> 00:08:26,580
就是这一集的话

215
00:08:26,580 --> 00:08:28,575
目录可以包含多集

216
00:08:28,575 --> 00:08:30,420
至于说前面啊

217
00:08:30,420 --> 00:08:33,140
这东西其实可以不要直接删掉

218
00:08:33,140 --> 00:08:33,919
也没问题

219
00:08:33,919 --> 00:08:35,820
代码也不会报错啊

220
00:08:35,820 --> 00:08:36,629
注意啊

221
00:08:36,629 --> 00:08:38,360
这个地方是目录名称

222
00:08:38,360 --> 00:08:41,830
但是呢，我们在调用相关函数

223
00:08:41,830 --> 00:08:43,730
或者引用相关编程的时候

224
00:08:43,730 --> 00:08:46,410
这个前面放的是 package 名称

225
00:08:46,410 --> 00:08:48,475
它可不是目录名称

226
00:08:48,475 --> 00:08:49,090
好

227
00:08:49,090 --> 00:08:51,850
那么刚才我们看到他这个地方呢

228
00:08:51,850 --> 00:08:54,150
是自动的给我们加了这样一个玩意儿

229
00:08:54,150 --> 00:08:57,490
你可以把这个玩意呢，理解为是别名

230
00:08:57,490 --> 00:08:59,730
那别名有什么用呢

231
00:08:59,730 --> 00:09:03,830
就假如说你感觉这个名称太长了啊

232
00:09:03,830 --> 00:09:05,440
写起来很麻烦

233
00:09:05,440 --> 00:09:07,250
你可以给它起一个短的名称

234
00:09:07,250 --> 00:09:09,447
比如叫做 A 、 B 、 C 

235
00:09:09,447 --> 00:09:12,220
好，别名改为 A 、 B 、 C 的话

236
00:09:12,220 --> 00:09:15,887
那么下面你就都可以改成 A 、 B 、 C 

237
00:09:15,887 --> 00:09:17,950
这就是别名的作用
