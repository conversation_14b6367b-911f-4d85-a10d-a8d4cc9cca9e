1
00:00:00,639 --> 00:00:02,460
看下 E 复表达式

2
00:00:02,460 --> 00:00:03,620
构圆里面的话

3
00:00:03,620 --> 00:00:05,260
这个 if 语句呢

4
00:00:05,260 --> 00:00:08,320
来判断一个逻辑表达式是否成立

5
00:00:08,320 --> 00:00:10,560
什么叫逻辑表达式呢

6
00:00:10,560 --> 00:00:13,270
比如你判断一下五是否大 I 9

7
00:00:13,270 --> 00:00:14,870
要么是，要么不是嘛

8
00:00:14,870 --> 00:00:17,930
本质上它返回的是一个布尔变量

9
00:00:17,930 --> 00:00:19,260
如果是的话

10
00:00:19,260 --> 00:00:21,360
那么会走到大括号里面

11
00:00:21,360 --> 00:00:24,650
来执行大括号里面的所有代码

12
00:00:24,650 --> 00:00:26,560
而且这个逻辑表达式

13
00:00:26,560 --> 00:00:28,560
它不需要放在小框里面

14
00:00:28,560 --> 00:00:30,580
其他语言的话，一般都需要放在小框里面

15
00:00:30,580 --> 00:00:32,760
go 圆不需要再来一个

16
00:00:32,760 --> 00:00:34,580
先找一个 A 变量

17
00:00:34,580 --> 00:00:39,250
if a 如果小于五的话

18
00:00:39,250 --> 00:00:42,120
这个大框里面可有很多行代码啊

19
00:00:42,120 --> 00:00:43,492
不仅只有一行

20
00:00:43,492 --> 00:00:45,550
那即使只有一行代码的话

21
00:00:45,550 --> 00:00:47,430
这个大括号也是需要写的

22
00:00:47,430 --> 00:00:48,860
那跟其他也不太一样

23
00:00:48,860 --> 00:00:49,690
那其他语言的话

24
00:00:49,690 --> 00:00:52,370
就是说如果 if 里面只有一行代码

25
00:00:52,370 --> 00:00:53,730
那么大括号可以不写

26
00:00:53,730 --> 00:00:56,490
甚至说可以把 G 和代码呢

27
00:00:56,490 --> 00:00:58,390
直接放在衣服后面

28
00:00:58,390 --> 00:00:59,810
在构表里面的话

29
00:00:59,810 --> 00:01:01,150
没有那么多花样啊

30
00:01:01,150 --> 00:01:02,797
就是只有一种写法

31
00:01:02,797 --> 00:01:06,240
那甚至于说我们在衣服表达式里面

32
00:01:06,240 --> 00:01:09,820
本身还可以再创建一个局部变量

33
00:01:09,820 --> 00:01:10,835
什么意思呢

34
00:01:10,835 --> 00:01:13,570
比如说来个 if 

35
00:01:13,570 --> 00:01:16,507
B 冒号等于八

36
00:01:16,507 --> 00:01:20,467
分号主表示分号 B 大于 A 

37
00:01:20,467 --> 00:01:21,580
B 大于 A 

38
00:01:21,580 --> 00:01:22,400
注意看啊

39
00:01:22,400 --> 00:01:24,660
那么我通过分号呢

40
00:01:24,660 --> 00:01:26,120
就把它分成两部分

41
00:01:26,120 --> 00:01:27,700
那分号前面的

42
00:01:27,700 --> 00:01:31,375
实际上是在初始化一个局部变量

43
00:01:31,375 --> 00:01:34,700
分号后面才是真正的逻辑判断

44
00:01:34,700 --> 00:01:36,490
那既然是局部变量吧

45
00:01:36,490 --> 00:01:39,840
我们说大括号来限定一个作用域

46
00:01:39,840 --> 00:01:42,160
所以说这个 B 变量呢

47
00:01:42,160 --> 00:01:45,710
它仅在这个大括号里面是可见的

48
00:01:45,710 --> 00:01:48,780
那么比如说我在大括号外面

49
00:01:48,780 --> 00:01:52,380
我想去使用 B 这个变量是不行的

50
00:01:52,380 --> 00:01:54,050
超出了 B 的作用域嘛

51
00:01:54,050 --> 00:01:55,940
所以这边有一个红线报错

52
00:01:55,940 --> 00:01:57,440
再来一个更复杂的

53
00:01:57,440 --> 00:02:01,582
我要在分号前面初始化好多个局部变量

54
00:02:01,582 --> 00:02:07,030
C 、 D 、 E 冒号等于一、四、七分号

55
00:02:07,030 --> 00:02:08,160
通过这种方式啊

56
00:02:08,160 --> 00:02:10,160
一下子给三个变量同时复制

57
00:02:10,160 --> 00:02:12,080
然后在后面这个表达式里面

58
00:02:12,080 --> 00:02:14,340
我就可以去使用 C 、 D 、 E 

59
00:02:14,340 --> 00:02:16,450
包括 A 这个点

60
00:02:16,450 --> 00:02:18,177
但是用不着 B 

61
00:02:18,177 --> 00:02:20,010
如果 C 小于 D 

62
00:02:20,010 --> 00:02:24,670
然后呢， and 要同时满足小括号

63
00:02:24,670 --> 00:02:29,470
C 大于 E 竖线或 C 大于三

64
00:02:29,470 --> 00:02:30,740
好，解释一下

65
00:02:30,740 --> 00:02:34,250
这边的话就出现一个比较复杂的逻辑判断

66
00:02:34,250 --> 00:02:36,600
这个符号表

67
00:02:36,600 --> 00:02:37,955
且就意味着说

68
00:02:37,955 --> 00:02:41,940
左边的跟右边的必须同时满足

69
00:02:41,940 --> 00:02:43,820
那么这个 if 和表达式才满足

70
00:02:43,820 --> 00:02:46,385
才会走到大括号里面来

71
00:02:46,385 --> 00:02:48,620
而右边的话，用小括号括起来

72
00:02:48,620 --> 00:02:51,340
就表示说计算优先级更高嘛

73
00:02:51,340 --> 00:02:53,807
啊，你得先计算它俩

74
00:02:53,807 --> 00:02:58,520
然后呢，再跟这一部分进行一个浅的判断

75
00:02:58,520 --> 00:03:00,510
而这两条竖线表示或

76
00:03:00,510 --> 00:03:03,250
或的话就表示要么左边满足

77
00:03:03,250 --> 00:03:04,310
要么右边满足

78
00:03:04,310 --> 00:03:06,610
只要有一个成立就 OK 

79
00:03:06,610 --> 00:03:09,260
那么整体就算是成立的

80
00:03:09,260 --> 00:03:12,240
而这个且呢，表示左侧需要成立

81
00:03:12,240 --> 00:03:13,740
右侧也需要成立

82
00:03:13,740 --> 00:03:14,760
除了衣服之外

83
00:03:14,760 --> 00:03:17,047
还可以后面跟一个 else 

84
00:03:17,047 --> 00:03:19,810
就是说如果这个表达式成立的话

85
00:03:19,810 --> 00:03:21,830
会走到这个大括号里面来

86
00:03:21,830 --> 00:03:22,850
那如果不成立呢

87
00:03:22,850 --> 00:03:23,670
不成立的话

88
00:03:23,670 --> 00:03:26,745
会走到 else 的这个大括号里面来

89
00:03:26,745 --> 00:03:29,620
我们写上 B 小于等于 A 

90
00:03:29,620 --> 00:03:30,400
要注意啊

91
00:03:30,400 --> 00:03:33,660
前面我说这个 B 是一个局部变量

92
00:03:33,660 --> 00:03:35,010
而这个局部变量

93
00:03:35,010 --> 00:03:37,980
它不光是在这个大括号里面可见

94
00:03:37,980 --> 00:03:42,440
同时它在 else 的这个括号里面也是可见的

95
00:03:42,440 --> 00:03:45,875
你看我是可以直接去使用这个 B 变量的

96
00:03:45,875 --> 00:03:49,130
除了 if ， else 这种组合还得更复杂

97
00:03:49,130 --> 00:03:51,720
我再来一个 if ， else 

98
00:03:51,720 --> 00:03:54,330
if 再来个判断

99
00:03:54,330 --> 00:03:55,970
三小于 B 的话

100
00:03:55,970 --> 00:03:57,900
这边向上三小于 B 

101
00:03:57,900 --> 00:04:00,250
就是我先判断这个成立不成立

102
00:04:00,250 --> 00:04:01,477
如果成立的话

103
00:04:01,477 --> 00:04:04,100
那么后面的干脆就不走了

104
00:04:04,100 --> 00:04:05,340
就不再判断了

105
00:04:05,340 --> 00:04:08,580
而如果说这个不成立的话

106
00:04:08,580 --> 00:04:11,260
我再来判断这个是否成立啊

107
00:04:11,260 --> 00:04:12,080
如果成立的话

108
00:04:12,080 --> 00:04:13,880
会走到这个里面来

109
00:04:13,880 --> 00:04:17,780
那后面的根本就不会再管了

110
00:04:17,780 --> 00:04:19,889
那如果说这个不成立的话

111
00:04:19,889 --> 00:04:22,170
它再来判断后面的啊

112
00:04:22,170 --> 00:04:24,450
你可以有多个 else if 

113
00:04:24,450 --> 00:04:26,830
好多个 else if 等等

114
00:04:26,830 --> 00:04:29,805
最后啊，你可以再跟一个 else 

115
00:04:29,805 --> 00:04:32,330
就以上的这些个衣服啊

116
00:04:32,330 --> 00:04:34,495
else if 啊，全部不不满足

117
00:04:34,495 --> 00:04:36,590
我会走到 L 4里面来

118
00:04:36,590 --> 00:04:39,417
但最后这个 else 你可要也可以不要

119
00:04:39,417 --> 00:04:42,400
而且我们说不管是在原始这个 if 里面

120
00:04:42,400 --> 00:04:43,680
还是 else if 里面

121
00:04:43,680 --> 00:04:45,070
还是最后的 else 里面

122
00:04:45,070 --> 00:04:47,880
这个变量 B 都是可见的

123
00:04:47,880 --> 00:04:51,490
那甚至于说我们还可以出现这种 if 的嵌套啊

124
00:04:51,490 --> 00:04:53,865
我在这边再来嵌套一个 if 

125
00:04:53,865 --> 00:04:56,040
三大于九怎么怎么样

126
00:04:56,040 --> 00:04:59,350
然后还可以再嵌套 else if a 

127
00:04:59,350 --> 00:05:00,687
A 大于六怎么样

128
00:05:00,687 --> 00:05:03,900
那甚至说里面还可以再嵌套

129
00:05:03,900 --> 00:05:07,540
直接来一个布尔变量 false 吧等等

130
00:05:07,540 --> 00:05:09,080
可以嵌套很多层
