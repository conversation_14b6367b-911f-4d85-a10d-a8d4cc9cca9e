1
00:00:00,440 --> 00:00:02,360
来看一下购物园里的零值

2
00:00:02,360 --> 00:00:04,059
或者做 zero value 

3
00:00:04,059 --> 00:00:05,480
或者做默认值

4
00:00:05,480 --> 00:00:08,640
就是说这变量你声明了没有给它赋值

5
00:00:08,640 --> 00:00:09,660
那么这种情况下

6
00:00:09,660 --> 00:00:12,225
它默认值到底是什么

7
00:00:12,225 --> 00:00:14,420
好，堆中整形啊

8
00:00:14,420 --> 00:00:15,620
默认值是零

9
00:00:15,620 --> 00:00:17,650
只声明未赋值是零

10
00:00:17,650 --> 00:00:18,250
当然了

11
00:00:18,250 --> 00:00:21,210
这种整形包含了有符号的和无符号的

12
00:00:21,210 --> 00:00:24,950
包含了 INT 8、 int 16、 int 32和 int 64

13
00:00:24,950 --> 00:00:26,110
都是零

14
00:00:26,110 --> 00:00:27,740
对于任意的数据小

15
00:00:27,740 --> 00:00:29,300
我们都可以通过百分号 V 

16
00:00:29,300 --> 00:00:31,742
来进行一个格式化的输出

17
00:00:31,742 --> 00:00:32,810
对于浮点值

18
00:00:32,810 --> 00:00:34,810
不论-5.32还是-5.64

19
00:00:34,810 --> 00:00:36,070
只声明未赋值

20
00:00:36,070 --> 00:00:37,450
它都是零啊

21
00:00:37,450 --> 00:00:38,290
0.0

22
00:00:39,310 --> 00:00:42,030
同理，对于 BT 它也是零啊

23
00:00:42,030 --> 00:00:44,370
这三种都是数字类型嘛

24
00:00:44,370 --> 00:00:45,415
都是零

25
00:00:45,415 --> 00:00:46,970
其中这个 RIN 啊

26
00:00:46,970 --> 00:00:48,590
RIN 本质上是 in 的三二

27
00:00:48,590 --> 00:00:50,520
所以 RIN 呢也是零

28
00:00:50,520 --> 00:00:52,250
再来看对于布尔变量

29
00:00:52,250 --> 00:00:54,250
布尔变量只声明未赋值

30
00:00:54,250 --> 00:00:55,970
它默认值是 false 

31
00:00:56,990 --> 00:00:58,230
对于字符串来说

32
00:00:58,230 --> 00:01:00,350
它默认值是空字符串啊

33
00:01:00,350 --> 00:01:02,590
刚才我们把这个代码跑了一下

34
00:01:02,590 --> 00:01:04,010
确实是零

35
00:01:04,010 --> 00:01:06,432
然后 false ，然后空字符串

36
00:01:06,432 --> 00:01:07,300
再来看一下

37
00:01:07,300 --> 00:01:10,030
对于指针 P 是一个指针类型

38
00:01:10,030 --> 00:01:12,040
不管是整形指针也好

39
00:01:12,040 --> 00:01:13,180
字符串指针也好

40
00:01:13,180 --> 00:01:15,700
还是说那种结构体指针也好

41
00:01:15,700 --> 00:01:18,130
任何指针它默认值呢

42
00:01:18,130 --> 00:01:21,430
都是 new 空指针嘛

43
00:01:21,430 --> 00:01:22,990
再来看结构体

44
00:01:22,990 --> 00:01:25,410
这边呢，随便定义了一个结构体啊

45
00:01:25,410 --> 00:01:28,177
包含一个 BB 和一个 int 

46
00:01:28,177 --> 00:01:32,257
那么我们声明一个结构体类型的变量的话

47
00:01:32,257 --> 00:01:33,670
它的零值是什么呢

48
00:01:33,670 --> 00:01:37,170
它的零值实际上就是每一个成员变量

49
00:01:37,170 --> 00:01:38,290
分别取零值

50
00:01:38,290 --> 00:01:40,510
比如说这个 gender 就是 false 

51
00:01:40,510 --> 00:01:41,930
edge 就是零是吧

52
00:01:41,930 --> 00:01:44,200
这个 user 啊，就是 false 和零

53
00:01:44,200 --> 00:01:46,707
这是结构体的零值

54
00:01:46,707 --> 00:01:48,000
再来看 error 

55
00:01:48,000 --> 00:01:48,820
这个 error 啊

56
00:01:48,820 --> 00:01:50,060
它实际上是接口嘛

57
00:01:50,060 --> 00:01:51,720
我们这个地方是要演示一个

58
00:01:51,720 --> 00:01:54,270
对于常规的接口来说

59
00:01:54,270 --> 00:01:55,850
那么只声明不赋值

60
00:01:55,850 --> 00:01:58,952
它默认值呢，是等于 new 的

61
00:01:58,952 --> 00:02:02,047
接口类型的零值是 new 

62
00:02:02,047 --> 00:02:04,830
那既然说他的零值是 new 的话

63
00:02:04,830 --> 00:02:06,470
我们当然可以把 new 呢

64
00:02:06,470 --> 00:02:09,088
赋给一个接口类型的变量

65
00:02:09,088 --> 00:02:10,960
再来看数组啊

66
00:02:10,960 --> 00:02:12,140
中括号里面有数字啊

67
00:02:12,140 --> 00:02:13,320
就是有长度嘛

68
00:02:13,320 --> 00:02:14,880
有数字表示有数组

69
00:02:14,880 --> 00:02:15,660
中括号为空

70
00:02:15,660 --> 00:02:17,052
表示为切片

71
00:02:17,052 --> 00:02:18,950
长度为三的这样一个数组

72
00:02:18,950 --> 00:02:20,010
当我生成之后

73
00:02:20,010 --> 00:02:22,950
那此时此刻它里面已经有三个整数了

74
00:02:22,950 --> 00:02:24,730
这三个整数呢，全部是零

75
00:02:24,730 --> 00:02:28,440
也说每一个成员依次的取零值就可以了

76
00:02:28,440 --> 00:02:30,460
假如说你是一个字符串构成的

77
00:02:30,460 --> 00:02:32,020
长度为三的数组的话

78
00:02:32,020 --> 00:02:35,090
那么数字里面已经有三个空字符串

79
00:02:35,090 --> 00:02:38,475
所以呢，这个时候是三个零

80
00:02:38,475 --> 00:02:42,240
关于三大引用类型切片、 Mac 和 channel 

81
00:02:42,240 --> 00:02:43,600
只声明未初始化

82
00:02:43,600 --> 00:02:44,990
它们都是 new 

83
00:02:44,990 --> 00:02:46,520
看第32行

84
00:02:46,520 --> 00:02:48,895
这边声明一个切片

85
00:02:48,895 --> 00:02:50,890
那么它实际上呢，是 new 

86
00:02:50,890 --> 00:02:51,800
但 new 的话

87
00:02:51,800 --> 00:02:54,380
如果你直接进行打印

88
00:02:54,380 --> 00:02:57,587
还能看到它是一个空的中括号

89
00:02:57,587 --> 00:02:59,130
里面没有任何元素

90
00:02:59,130 --> 00:03:00,870
我们调用 line 和 KB 的话

91
00:03:00,870 --> 00:03:04,075
会发现这个切片的长度和容量都是零

92
00:03:04,075 --> 00:03:06,330
第34行只生了一个 map 

93
00:03:06,330 --> 00:03:08,772
那此时这个 map 呢， T 是 new 

94
00:03:08,772 --> 00:03:10,120
如果直接打印的话

95
00:03:10,120 --> 00:03:12,320
会发现它是一个空的 map 

96
00:03:12,320 --> 00:03:14,400
调用长度会发现 map 呢

97
00:03:14,400 --> 00:03:15,652
长度为零

98
00:03:15,652 --> 00:03:17,770
对于 channel 只声明之后的话

99
00:03:17,770 --> 00:03:21,037
直接输入打印单出的就是 new 

100
00:03:21,037 --> 00:03:24,220
那既然三大有类型全部是 new 嘛

101
00:03:24,220 --> 00:03:25,460
那就不能直接使用

102
00:03:25,460 --> 00:03:27,597
那必须先初始化

103
00:03:27,597 --> 00:03:30,612
他们都可以通过 mic 来进行初始化

104
00:03:30,612 --> 00:03:31,860
对于切片来说

105
00:03:31,860 --> 00:03:33,750
你不能直接去读写这个切片

106
00:03:33,750 --> 00:03:37,665
因为读写你必然需要指定下标

107
00:03:37,665 --> 00:03:39,600
但目前来看长度为零嘛

108
00:03:39,600 --> 00:03:41,060
不管直径下多少几

109
00:03:41,060 --> 00:03:43,212
都会发生下标越界

110
00:03:43,212 --> 00:03:45,570
对于 map 来说不能读和写

111
00:03:45,570 --> 00:03:47,470
比如像第36行

112
00:03:47,470 --> 00:03:49,952
你打算写入一个 key value 

113
00:03:49,952 --> 00:03:52,350
这样的话可以报一个 panic 

114
00:03:52,350 --> 00:03:56,992
他说不允许向一个 new map 里面去写入元素

115
00:03:56,992 --> 00:04:00,250
对于 channel 也需要先通过 make 来进行初始化

116
00:04:00,250 --> 00:04:03,520
否则呢，是不能够去读写这个 channel 的

117
00:04:03,520 --> 00:04:07,070
构建里面还有一种数据类型叫做函数类型

118
00:04:07,070 --> 00:04:08,487
你像这个 FK 

119
00:04:08,487 --> 00:04:11,200
FK 它本身是一种函数类型啊

120
00:04:11,200 --> 00:04:13,720
不管这个函数的参数是什么样子

121
00:04:13,720 --> 00:04:14,720
返回值是什么样子

122
00:04:14,720 --> 00:04:17,110
它总之吧是一个函数类型

123
00:04:17,110 --> 00:04:20,430
对于函数类型的变量来说啊

124
00:04:20,430 --> 00:04:23,770
你没有去详细的定义这个函数的具体实现

125
00:04:23,770 --> 00:04:27,080
那么这个 FK 它就是一个 new 

126
00:04:27,080 --> 00:04:31,345
以上是所有类型的零值到底是什么

127
00:04:31,345 --> 00:04:33,810
然后来看一个跟零值相关的

128
00:04:33,810 --> 00:04:36,090
算是构建的一个设计哲学吧

129
00:04:36,090 --> 00:04:37,270
第一个

130
00:04:37,270 --> 00:04:38,850
我们从一个 map 里

131
00:04:38,850 --> 00:04:41,542
根据 K 查询相应的 value 

132
00:04:41,542 --> 00:04:42,800
那刚才我说了

133
00:04:42,800 --> 00:04:44,720
这个 map 是一个空的

134
00:04:44,720 --> 00:04:46,480
map 里吧里没有任何 K 

135
00:04:46,480 --> 00:04:47,880
如果一个 K 不存在

136
00:04:47,880 --> 00:04:50,880
那么从 map 里面取的 value 就是零值

137
00:04:50,880 --> 00:04:51,780
那目前来看

138
00:04:51,780 --> 00:04:54,380
这个 map 它对应的 value 是布尔类型

139
00:04:54,380 --> 00:04:56,835
所以呢，这个零值就是 false 

140
00:04:56,835 --> 00:04:58,680
但同时如果说这个七

141
00:04:58,680 --> 00:05:00,320
这个 K 本来是存在的

142
00:05:00,320 --> 00:05:03,345
而它对应的 value 刚好也是 false 

143
00:05:03,345 --> 00:05:05,960
所以两种情况下缺的都是 false 

144
00:05:05,960 --> 00:05:08,755
那我怎么知道是哪种情况呢

145
00:05:08,755 --> 00:05:12,680
所以啊，这个根据 K 去 map 里面取对应 value 

146
00:05:12,680 --> 00:05:15,032
它返回的实际上是两个值

147
00:05:15,032 --> 00:05:16,430
根据第二个值

148
00:05:16,430 --> 00:05:19,345
它可以告诉我们这个 K 是否存在

149
00:05:19,345 --> 00:05:22,190
如果这个布尔变量是 false 的话

150
00:05:22,190 --> 00:05:23,650
表示 K 不存在

151
00:05:23,650 --> 00:05:24,690
这种情况下

152
00:05:24,690 --> 00:05:27,090
这个 value 我们是不能使用的

153
00:05:27,090 --> 00:05:28,290
再来看一个例子

154
00:05:28,290 --> 00:05:30,130
那刚才我们声明了一个 channel 

155
00:05:30,130 --> 00:05:33,580
现在通过 make 来初始化这个 channel 

156
00:05:33,580 --> 00:05:35,850
此处呢，初始化容量为十

157
00:05:35,850 --> 00:05:36,430
当然了

158
00:05:36,430 --> 00:05:38,280
就算容量为零也是一样的

159
00:05:38,280 --> 00:05:40,670
跟这个容量没有什么关系

160
00:05:40,670 --> 00:05:44,460
那么我们把这个 channel 关闭之后

161
00:05:44,460 --> 00:05:45,890
再去从 china 里面呢

162
00:05:45,890 --> 00:05:47,930
去试图读出一个元素

163
00:05:47,930 --> 00:05:51,390
由于目前圈子里面还没有放入任何元素

164
00:05:51,390 --> 00:05:53,510
所以它实际上是一个空气动

165
00:05:53,510 --> 00:05:57,010
那这个时候你读能读出来吗

166
00:05:57,010 --> 00:05:58,270
实际上是可以的

167
00:05:58,270 --> 00:06:00,450
就是一旦说把 channel 关闭了

168
00:06:00,450 --> 00:06:03,785
那么这个读操作他可以立刻返回一个结果

169
00:06:03,785 --> 00:06:04,810
返回什么呢

170
00:06:04,810 --> 00:06:06,110
就是返回零值

171
00:06:06,110 --> 00:06:08,910
因为圈子里面存放的是整形嘛

172
00:06:08,910 --> 00:06:11,182
所以返回的就是数字零

173
00:06:11,182 --> 00:06:13,320
所以当我们取到输入字典之后

174
00:06:13,320 --> 00:06:14,800
我们不知道是不是

175
00:06:14,800 --> 00:06:17,490
由于 china 里面本来就有一个元素

176
00:06:17,490 --> 00:06:18,510
刚好是零值

177
00:06:18,510 --> 00:06:21,090
还是说由于 china 被关闭

178
00:06:21,090 --> 00:06:23,680
同时为空导致足算是个零值

179
00:06:23,680 --> 00:06:25,770
为了区分这两种情况

180
00:06:25,770 --> 00:06:29,740
所以呢，这个读操作本身也会返回两个值

181
00:06:29,740 --> 00:06:31,450
如果第二个值 OK 

182
00:06:31,450 --> 00:06:33,440
这个布尔变量它是 true 的话

183
00:06:33,440 --> 00:06:36,980
表示这个 channel 里面确实是有元素的

184
00:06:36,980 --> 00:06:40,775
那么这个 value 是可以正常使用的

185
00:06:40,775 --> 00:06:43,020
而如果 OK 为 false 

186
00:06:43,020 --> 00:06:45,590
代表 channel 在此之前就已经为空了

187
00:06:45,590 --> 00:06:47,877
那么这个 value 是不能直接使用的

188
00:06:47,877 --> 00:06:50,660
所以这都是零值带来的麻

189
00:06:50,660 --> 00:06:53,120
导致呢，必须返回两个值

190
00:06:53,120 --> 00:06:55,832
把通过额外的一个布尔变量

191
00:06:55,832 --> 00:06:58,705
来对情况做出一个准确判断

192
00:06:58,705 --> 00:06:59,790
关于磷脂

193
00:06:59,790 --> 00:07:02,650
还有个应用是在空接口的类型断言里面

194
00:07:02,650 --> 00:07:04,210
我们来看第13行

195
00:07:04,210 --> 00:07:07,357
搞了一个空接口 any 嘛， FC 

196
00:07:07,357 --> 00:07:08,580
第44行啊

197
00:07:08,580 --> 00:07:10,620
通过在这个 any 后面加个点

198
00:07:10,620 --> 00:07:11,400
加小括号

199
00:07:11,400 --> 00:07:13,740
我试图把这个空接口呢

200
00:07:13,740 --> 00:07:16,202
断言为浮点六四

201
00:07:16,202 --> 00:07:19,310
那此时我还没有给 FC 赋值吗

202
00:07:19,310 --> 00:07:22,110
那他做一个 any 做一个接口

203
00:07:22,110 --> 00:07:23,030
空接口吗

204
00:07:23,030 --> 00:07:24,572
它实际上是 new 

205
00:07:24,572 --> 00:07:25,780
那既然 new 的话

206
00:07:25,780 --> 00:07:29,185
我能向浮点六四进行断言吗

207
00:07:29,185 --> 00:07:32,460
本身这个操作是可以执行的啊

208
00:07:32,460 --> 00:07:34,540
不会发生所谓的空指针异常

209
00:07:34,540 --> 00:07:35,935
只不过呢

210
00:07:35,935 --> 00:07:37,640
这 OK 是 false 

211
00:07:37,640 --> 00:07:39,740
就意味着断言失败

212
00:07:39,740 --> 00:07:42,977
new 嘛，你肯定不能断言为服务点六四

213
00:07:42,977 --> 00:07:45,390
而当这个 OK 为 false 的时候

214
00:07:45,390 --> 00:07:47,790
那对应的这个 V 1是多少呢

215
00:07:47,790 --> 00:07:48,890
这个 V 1样

216
00:07:48,890 --> 00:07:51,510
它其实就是浮点六四类型

217
00:07:51,510 --> 00:07:53,407
我们把鼠标放在 V 上面

218
00:07:53,407 --> 00:07:54,800
这边有个自动提示啊

219
00:07:54,800 --> 00:07:56,360
V 就是浮点六四

220
00:07:56,360 --> 00:07:57,020
只不过呢

221
00:07:57,020 --> 00:07:59,060
它是浮点六四的零值

222
00:07:59,060 --> 00:08:00,890
就是0.0

223
00:08:00,890 --> 00:08:02,620
好，第16行

224
00:08:02,620 --> 00:08:04,340
我给 FC 赋值了哦

225
00:08:04,340 --> 00:08:05,640
它终于有一个具体值了

226
00:08:05,640 --> 00:08:06,847
是3.14

227
00:08:06,847 --> 00:08:09,590
那这个3.14它是一个字面量吗

228
00:08:09,590 --> 00:08:12,350
那这个3.14它到底是-5.32呢

229
00:08:12,350 --> 00:08:14,045
还是-5.64呢

230
00:08:14,045 --> 00:08:15,370
在构元里面啊

231
00:08:15,370 --> 00:08:18,657
字面量的小数它实际上是浮点六四

232
00:08:18,657 --> 00:08:19,660
所以这个时候啊

233
00:08:19,660 --> 00:08:22,330
IFC 的具体类型是服务电流四

234
00:08:22,330 --> 00:08:25,020
如果你非要把它往服务点三二

235
00:08:25,020 --> 00:08:26,740
来进行断连的话

236
00:08:26,740 --> 00:08:28,755
对不起会断言失败

237
00:08:28,755 --> 00:08:31,930
所以这个时候的话就 OK ，依然是 false 

238
00:08:31,930 --> 00:08:32,919
OK 是 false 

239
00:08:32,919 --> 00:08:35,620
那对应的这个 V 2也是零值

240
00:08:35,620 --> 00:08:37,350
谁的临值啊

241
00:08:37,350 --> 00:08:40,732
它是浮点三二的定值，也是0.0

242
00:08:40,732 --> 00:08:41,700
最后一次啊

243
00:08:41,700 --> 00:08:43,940
我打算往服5.64进行断言

244
00:08:43,940 --> 00:08:45,930
这一次终于可以断言成功了

245
00:08:45,930 --> 00:08:47,530
所以呢， OK 是出

246
00:08:47,530 --> 00:08:48,540
OK 是出的话

247
00:08:48,540 --> 00:08:51,340
V 3就是服务164

248
00:08:51,340 --> 00:08:53,330
那这个六四值是几呢

249
00:08:53,330 --> 00:08:55,390
就是当初你负的这个值吗

250
00:08:55,390 --> 00:08:56,310
3.14嘛

251
00:08:56,310 --> 00:08:57,290
所以第三次啊

252
00:08:57,290 --> 00:08:57,970
这个 V 3啊

253
00:08:57,970 --> 00:09:00,355
他终于等于3.14了

254
00:09:00,355 --> 00:09:03,510
后面我们学数据库编程学 GOM 

255
00:09:03,510 --> 00:09:04,510
GOM 的话

256
00:09:04,510 --> 00:09:05,910
它是一个第三方库吧

257
00:09:05,910 --> 00:09:07,700
它并不是构编的标准库

258
00:09:07,700 --> 00:09:08,730
但是这个库呢

259
00:09:08,730 --> 00:09:12,412
它也遵循了标准库的一些设计思想

260
00:09:12,412 --> 00:09:16,320
比如说将来我们的这个 user 表

261
00:09:16,320 --> 00:09:20,250
它对应到的就是某一个数据库里面的表

262
00:09:20,250 --> 00:09:21,850
那表里面呢有两列

263
00:09:21,850 --> 00:09:24,290
一个是 gender ，一个是 edge 

264
00:09:24,290 --> 00:09:27,450
现在啊，我要去根据这样一个 where 条件

265
00:09:27,450 --> 00:09:29,670
where r edge 等于零进行查询

266
00:09:29,670 --> 00:09:30,710
查询结果呢

267
00:09:30,710 --> 00:09:34,115
会放到 U 这个结构体里面去

268
00:09:34,115 --> 00:09:36,610
本来 U 这个结构体它是一个空九体嘛

269
00:09:36,610 --> 00:09:38,350
那么我打算把这个查询结果呢

270
00:09:38,350 --> 00:09:39,030
付给 U 

271
00:09:39,030 --> 00:09:41,905
所以的话，这边加了一个取值符号

272
00:09:41,905 --> 00:09:46,200
那将来如果我从这个 U 里面读出来

273
00:09:46,200 --> 00:09:47,690
gender 等于 false 

274
00:09:47,690 --> 00:09:49,042
edge 等于零

275
00:09:49,042 --> 00:09:50,450
能说明什么呢

276
00:09:50,450 --> 00:09:51,980
能说明数据库里面

277
00:09:51,980 --> 00:09:54,255
刚好就有这样一条记录吗

278
00:09:54,255 --> 00:09:55,182
不一定

279
00:09:55,182 --> 00:09:58,570
也可能是由于你根据这个 edge 等于零

280
00:09:58,570 --> 00:10:00,990
这个 where 条件查不到任何结果

281
00:10:00,990 --> 00:10:02,570
那查不到任何结果呢

282
00:10:02,570 --> 00:10:06,540
也会导致查询结果全部为零值

283
00:10:06,540 --> 00:10:10,690
就是说也会导致这个 U 它的 edge 等于零

284
00:10:10,690 --> 00:10:12,310
gender 等于 false 

285
00:10:12,310 --> 00:10:14,350
所以到底根据这个 word 条件

286
00:10:14,350 --> 00:10:17,415
能不能查到真实的结果呢

287
00:10:17,415 --> 00:10:21,480
我们得根据它返回的这个 error 来进行判断

288
00:10:21,480 --> 00:10:23,190
如果这个 error 不等于 new 

289
00:10:23,190 --> 00:10:26,727
并且这个 error 等于 record but found 

290
00:10:26,727 --> 00:10:30,332
代表这个 where 条件没有命中任何结果

291
00:10:30,332 --> 00:10:32,490
所以这是很多同学困惑的点

292
00:10:32,490 --> 00:10:34,110
就是他始终想不明白

293
00:10:34,110 --> 00:10:36,950
为什么查不到对应的结果

294
00:10:36,950 --> 00:10:39,050
还得返回一个 error 呢

295
00:10:39,050 --> 00:10:40,880
这是零时导致的

296
00:10:40,880 --> 00:10:43,410
我们在这个地方还真的需要一个 LR 
