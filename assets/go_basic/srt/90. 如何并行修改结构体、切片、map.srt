1
00:00:00,499 --> 00:00:01,560
前面我们讲过

2
00:00:01,560 --> 00:00:03,760
并发电信就是多个继承

3
00:00:03,760 --> 00:00:05,420
他们共同的去修改

4
00:00:05,420 --> 00:00:08,290
同一块内存空间可能会出问题

5
00:00:08,290 --> 00:00:11,480
那同一块内存空间指的就是全局变量

6
00:00:11,480 --> 00:00:13,180
那局部变量就不存

7
00:00:13,180 --> 00:00:14,420
并方案解决问题

8
00:00:14,420 --> 00:00:15,800
因为局部变量是

9
00:00:15,800 --> 00:00:18,287
每个阶层都拥有自己的一份

10
00:00:18,287 --> 00:00:20,510
各改各的，互不影响

11
00:00:20,510 --> 00:00:21,890
那这个全局变量

12
00:00:21,890 --> 00:00:24,290
除了可以是简单的数据线之外

13
00:00:24,290 --> 00:00:26,605
也可能是一种结构体

14
00:00:26,605 --> 00:00:27,630
多个集

15
00:00:27,630 --> 00:00:30,230
同时去修改某一个全局的结构体

16
00:00:30,230 --> 00:00:32,580
变染也存在并发症性

17
00:00:32,580 --> 00:00:35,650
因为结构体本质上就是对简单的数据类型

18
00:00:35,650 --> 00:00:37,595
进行了一个包装嘛

19
00:00:37,595 --> 00:00:39,490
比如说两个继承人

20
00:00:39,490 --> 00:00:43,570
他们都想对这个结构体的年龄执行加减操作

21
00:00:43,570 --> 00:00:46,405
那么最终结果可能会不符合预期

22
00:00:46,405 --> 00:00:49,807
但是如果两个集成事先约定好了

23
00:00:49,807 --> 00:00:52,100
一个继承只负责修改 name 

24
00:00:52,100 --> 00:00:54,780
另外一个继承只负责修改 edge 

25
00:00:54,780 --> 00:00:57,280
这样的话是不会互相干扰的

26
00:00:57,280 --> 00:00:59,097
结果不会出问题

27
00:00:59,097 --> 00:01:01,870
就如同这边开了两个集成

28
00:01:01,870 --> 00:01:04,290
一个呢只负责修改 name 

29
00:01:04,290 --> 00:01:06,285
一个只负责修改 edge 

30
00:01:06,285 --> 00:01:09,070
这样最终 name 就是 fried 

31
00:01:09,070 --> 00:01:11,120
edge 就是20

32
00:01:11,120 --> 00:01:12,875
不会出问题

33
00:01:12,875 --> 00:01:13,790
那同理

34
00:01:13,790 --> 00:01:18,335
对于数组或者切片跟结构体也是类似的

35
00:01:18,335 --> 00:01:21,850
结构体是多个变量的一个封装

36
00:01:21,850 --> 00:01:22,840
一个集合

37
00:01:22,840 --> 00:01:26,660
那数组或切片也是多个元素的集合嘛

38
00:01:26,660 --> 00:01:28,830
这里面呢，就以数组为例

39
00:01:28,830 --> 00:01:30,395
切片同理

40
00:01:30,395 --> 00:01:34,230
如果说多个集成都想去修改这个数组的

41
00:01:34,230 --> 00:01:35,272
首元素

42
00:01:35,272 --> 00:01:37,110
同时执行加加

43
00:01:37,110 --> 00:01:41,675
那自然也会出现并发哪些结果不符合预期

44
00:01:41,675 --> 00:01:45,300
但如果说两个集成怎样

45
00:01:45,300 --> 00:01:46,572
事先约定好

46
00:01:46,572 --> 00:01:51,320
其中一只负责修改里面下标为基础元素

47
00:01:51,320 --> 00:01:52,510
而7×2呢

48
00:01:52,510 --> 00:01:56,397
只负责修改数组里面下标为偶数的元素

49
00:01:56,397 --> 00:01:59,640
大家不要去同时修改同一个变量

50
00:01:59,640 --> 00:02:01,597
那这样的话也不会出问题

51
00:02:01,597 --> 00:02:03,410
比如这个数组呢

52
00:02:03,410 --> 00:02:04,390
长度为十

53
00:02:04,390 --> 00:02:06,510
里面是有十个零

54
00:02:06,510 --> 00:02:09,570
这边开了两个集乘

55
00:02:09,570 --> 00:02:12,670
第一个阶乘它是 I ，从零开始

56
00:02:12,670 --> 00:02:14,560
每次呢是 I 加二

57
00:02:14,560 --> 00:02:18,912
也就是说他只关心下标为偶数的元素

58
00:02:18,912 --> 00:02:20,180
下标为偶数

59
00:02:20,180 --> 00:02:21,972
把它们复为零

60
00:02:21,972 --> 00:02:23,290
第二个是什么呢

61
00:02:23,290 --> 00:02:24,750
I 是从一开始

62
00:02:24,750 --> 00:02:26,610
每次呢也是 I 加二

63
00:02:26,610 --> 00:02:29,660
他只关心下标为基础元

64
00:02:29,660 --> 00:02:30,930
下对的奇数

65
00:02:30,930 --> 00:02:32,470
把它置为一

66
00:02:32,470 --> 00:02:35,800
那这样的话，结果完全是可预期的

67
00:02:35,800 --> 00:02:38,560
当这两个集成全部执行完之后

68
00:02:38,560 --> 00:02:42,225
那么最终这个数组里面就应该长成这个样子

69
00:02:42,225 --> 00:02:46,567
0101交替出现也没问题

70
00:02:46,567 --> 00:02:50,210
所以如果将来你有一个特别大的数

71
00:02:50,210 --> 00:02:50,890
或者切片

72
00:02:50,890 --> 00:02:51,770
需要复制的话

73
00:02:51,770 --> 00:02:54,100
那么你可以开并

74
00:02:54,100 --> 00:02:56,700
两个、三个或者多个集成

75
00:02:56,700 --> 00:02:59,380
并行的给这个数组或切片赋值

76
00:02:59,380 --> 00:03:02,830
只要大家事先约定好各自复制的范围

77
00:03:02,830 --> 00:03:04,190
不要产生交集

78
00:03:04,190 --> 00:03:05,980
结果就没问题

79
00:03:05,980 --> 00:03:09,162
那很自然我们就会想到 map 

80
00:03:09,162 --> 00:03:13,350
因为 map 它也是多个元素构成的集合吗

81
00:03:13,350 --> 00:03:16,000
它是不是也可以并行修改呢

82
00:03:16,000 --> 00:03:17,220
其实不是

83
00:03:17,220 --> 00:03:18,440
在构院里面

84
00:03:18,440 --> 00:03:20,340
如果开多个集成

85
00:03:20,340 --> 00:03:23,000
同时去修改同一个 app 的话

86
00:03:23,000 --> 00:03:25,742
会发生 final error 

87
00:03:25,742 --> 00:03:28,210
就跟之前我们碰见思索一样

88
00:03:28,210 --> 00:03:30,230
思索也是一种 final error 

89
00:03:30,230 --> 00:03:31,957
它还不是 panic 

90
00:03:31,957 --> 00:03:34,840
那么我们之前讲过的 recover 

91
00:03:34,840 --> 00:03:37,385
它实际上只能捕获 panic 

92
00:03:37,385 --> 00:03:39,640
避免进程直接退出

93
00:03:39,640 --> 00:03:42,390
但是呢，如果发生 final error 

94
00:03:42,390 --> 00:03:44,360
recover 是无济于事的

95
00:03:44,360 --> 00:03:46,157
程序还是会崩掉

96
00:03:46,157 --> 00:03:49,472
比如这边我开了两个携程

97
00:03:49,472 --> 00:03:53,210
第一个 for 循环 I ， I 呢是偶数

98
00:03:53,210 --> 00:03:54,240
第二个呢

99
00:03:54,240 --> 00:03:56,017
I 是奇数

100
00:03:56,017 --> 00:03:58,670
他们关心的这个 map 里面的 K 

101
00:03:58,670 --> 00:03:59,750
一个是偶数

102
00:03:59,750 --> 00:04:00,670
一个奇数

103
00:04:00,670 --> 00:04:02,370
所以从 K 上面来看

104
00:04:02,370 --> 00:04:05,000
他们并没有产生交集

105
00:04:05,000 --> 00:04:08,170
但是呢，他们再去修改同一个 app 

106
00:04:08,170 --> 00:04:10,910
那这样的话也是会发生 final error 

107
00:04:10,910 --> 00:04:11,390
当然了

108
00:04:11,390 --> 00:04:12,490
这个只是概率

109
00:04:12,490 --> 00:04:14,110
就是你多次运行

110
00:04:14,110 --> 00:04:16,190
发现有时候也没有发生开头函数

111
00:04:16,190 --> 00:04:17,070
有时候发生了

112
00:04:17,070 --> 00:04:19,097
那是一个概率问题

113
00:04:19,097 --> 00:04:22,340
但毕竟 TORERO 是一个非常致命的问题

114
00:04:22,340 --> 00:04:25,140
所以线上的我们沿出现这种情况

115
00:04:25,140 --> 00:04:29,820
严格不允许多线程并行的去写同一个 map 

116
00:04:29,820 --> 00:04:31,660
那如果我们有这样的需

117
00:04:31,660 --> 00:04:32,920
该怎么办呢

118
00:04:32,920 --> 00:04:35,740
购员给我们提供了另外一种数据类型

119
00:04:35,740 --> 00:04:37,837
就是 sink 点 map 

120
00:04:37,837 --> 00:04:40,580
它允许并行的去读或写

121
00:04:40,580 --> 00:04:44,342
但是呢会比原始的 Mac 用起来稍微麻烦点

122
00:04:44,342 --> 00:04:48,080
比如打算并行的往这个 sync 点 app 里面去

123
00:04:48,080 --> 00:04:48,980
写元素的话

124
00:04:48,980 --> 00:04:52,072
那么写元素需要调用 store 函数

125
00:04:52,072 --> 00:04:54,720
把 K 和 V 传进来

126
00:04:54,720 --> 00:04:58,850
读的话需要调用 load 函数把 key 传进来

127
00:04:58,850 --> 00:05:01,010
返回 value 和是否存在

128
00:05:01,010 --> 00:05:03,252
表示这个 key 是否存在嘛

129
00:05:03,252 --> 00:05:05,060
仅当 K 存在的前提下

130
00:05:05,060 --> 00:05:07,820
你才可以去使用这个 V 

131
00:05:07,820 --> 00:05:11,840
注意看，我在函数的最开头加了一个 deer funk 

132
00:05:11,840 --> 00:05:13,680
里面加了一个 recover 

133
00:05:13,680 --> 00:05:15,580
试图去捕获 panic 

134
00:05:15,580 --> 00:05:18,680
但实际上待会儿并不会发生 panic 

135
00:05:18,680 --> 00:05:20,490
而是发生 final error 

136
00:05:20,490 --> 00:05:22,240
程序该崩还是要崩

137
00:05:22,240 --> 00:05:25,987
我们跑起来看一看

138
00:05:25,987 --> 00:05:28,590
好这边发生了 fighter error 对吧

139
00:05:28,590 --> 00:05:30,970
他说你并发的去写这个 map 

140
00:05:30,970 --> 00:05:33,467
所以呢， FIERROR 崩掉了

141
00:05:33,467 --> 00:05:34,900
那崩掉之后的话

142
00:05:34,900 --> 00:05:36,992
后面代码就没有执行

143
00:05:36,992 --> 00:05:38,250
正常情况下

144
00:05:38,250 --> 00:05:41,190
我根据这个 K 1应该是能够读出 value 

145
00:05:41,190 --> 00:05:42,270
应该有一个输出的

146
00:05:42,270 --> 00:05:44,500
但是呢，后面并没有输出

147
00:05:44,500 --> 00:05:47,330
直接就进程退出了

148
00:05:47,330 --> 00:05:51,930
我们把并发写 Mac 这一段代码先注释掉

149
00:05:51,930 --> 00:05:54,460
然后我们再来运行一次

150
00:05:54,460 --> 00:05:58,440
好，这次呢，能够顺利的执行到最后一行

151
00:05:58,440 --> 00:06:00,240
91行它会有一个输出
