1
00:00:00,379 --> 00:00:03,210
讲一下结构体 type 

2
00:00:03,210 --> 00:00:04,730
这是一个关键字

3
00:00:04,730 --> 00:00:06,250
然后是结构体名称

4
00:00:06,250 --> 00:00:07,870
比如叫做 user 

5
00:00:08,970 --> 00:00:10,410
然后是 STRUCT 

6
00:00:12,210 --> 00:00:14,360
STRUCT 即表示结构体吗

7
00:00:14,360 --> 00:00:18,420
所以说 tab 和 STRUCT 这两个是关键词

8
00:00:18,420 --> 00:00:19,240
固定写法

9
00:00:19,240 --> 00:00:22,882
结构体名称中间这一部分你可以随便写

10
00:00:22,882 --> 00:00:25,030
可以包含多个单词啊

11
00:00:25,030 --> 00:00:27,430
我们习惯上用驼峰形式嘛

12
00:00:27,430 --> 00:00:28,950
那结构体里面的话

13
00:00:28,950 --> 00:00:32,085
可以包含多个成员变量

14
00:00:32,085 --> 00:00:35,670
成员变量可以是各种各样的类型

15
00:00:35,670 --> 00:00:38,930
比如说 id 是整数

16
00:00:40,080 --> 00:00:44,290
比如说 score ，它是一个浮点六四

17
00:00:44,290 --> 00:00:49,260
再比如说 name 和地址、 address 

18
00:00:49,260 --> 00:00:52,080
他们呢都是 string 啊

19
00:00:52,080 --> 00:00:55,840
当我的 name 和 address 类型相同的时候

20
00:00:55,840 --> 00:00:57,630
我可以这样简写

21
00:00:57,630 --> 00:00:58,890
就是把它们两个写在一起

22
00:00:58,890 --> 00:01:01,657
然后呢类型只写一遍就可以了

23
00:01:01,657 --> 00:01:04,500
那所以结构体大家可以把它简单理解为

24
00:01:04,500 --> 00:01:07,040
就是把多个变量进行了打包

25
00:01:07,040 --> 00:01:09,122
构成了一个整体

26
00:01:09,122 --> 00:01:12,290
然后你也可以把这个结构体理解为

27
00:01:12,290 --> 00:01:14,990
是一种更加复杂的数据类型

28
00:01:14,990 --> 00:01:18,130
就跟我们的整型浮点型一样

29
00:01:18,130 --> 00:01:19,310
是一种数据类型

30
00:01:19,310 --> 00:01:23,780
就意味着将来我可以去声明一个变量 U 

31
00:01:23,780 --> 00:01:27,777
它是 user 这样的一种类型

32
00:01:27,777 --> 00:01:29,690
那我怎么给这个 U 赋值呢

33
00:01:29,690 --> 00:01:33,870
可以这样赋 U 等于 user 大括号

34
00:01:35,410 --> 00:01:37,340
id 冒号

35
00:01:37,340 --> 00:01:39,290
随便取个整数

36
00:01:39,290 --> 00:01:42,430
score ，浮点数， name 

37
00:01:42,430 --> 00:01:44,310
这是一个字符串

38
00:01:44,310 --> 00:01:46,620
用双引号引起来

39
00:01:46,620 --> 00:01:47,950
大大小小

40
00:01:47,950 --> 00:01:49,722
address 

41
00:01:49,722 --> 00:01:52,850
中国这样的

42
00:01:52,850 --> 00:01:56,160
那目前呢，我们这个 U 还是有个红线报错

43
00:01:56,160 --> 00:01:58,880
因为你声明了这个变量

44
00:01:58,880 --> 00:01:59,940
但是没有使用它

45
00:01:59,940 --> 00:02:00,820
那怎么使用呢

46
00:02:00,820 --> 00:02:03,220
我们简单的进行一个输出打印

47
00:02:03,220 --> 00:02:09,590
比如说 print u 点 name 好

48
00:02:09,590 --> 00:02:12,630
我可以通过结构体变量加个点

49
00:02:12,630 --> 00:02:15,570
后面就可以跟他的任何一个成变量

50
00:02:15,570 --> 00:02:16,830
name 也好

51
00:02:16,830 --> 00:02:18,120
地址也好

52
00:02:18,120 --> 00:02:20,115
id 跟 score 都可以

53
00:02:20,115 --> 00:02:21,970
那么这边这几个成变量

54
00:02:21,970 --> 00:02:23,930
赋值顺序可以随便写

55
00:02:23,930 --> 00:02:29,210
比如说我把这个 address 颠倒到前面去

56
00:02:29,210 --> 00:02:31,327
逗号分隔一下

57
00:02:31,327 --> 00:02:34,610
那甚至说某个成员我可以不赋值

58
00:02:34,610 --> 00:02:35,580
比如 score 

59
00:02:35,580 --> 00:02:37,060
我把它删

60
00:02:37,060 --> 00:02:37,890
保存一下

61
00:02:37,890 --> 00:02:38,870
这样也是可以的

62
00:02:38,870 --> 00:02:41,260
那如果 score 你不给它赋值的话

63
00:02:41,260 --> 00:02:42,760
它默认是零值

64
00:02:42,760 --> 00:02:45,960
就是说凡是整数或者浮点数不赋值

65
00:02:45,960 --> 00:02:47,335
默认是零值

66
00:02:47,335 --> 00:02:48,720
ball 类型不赋值

67
00:02:48,720 --> 00:02:50,890
默认是 false 字符串

68
00:02:50,890 --> 00:02:53,340
不赋值默认是空字符串

69
00:02:53,340 --> 00:02:55,937
我们后面会专门讲字符串

70
00:02:55,937 --> 00:02:58,220
那甚至可以这样弄

71
00:02:58,220 --> 00:03:02,940
比如说 U 等于 user 

72
00:03:02,940 --> 00:03:04,610
id 等于32

73
00:03:04,610 --> 00:03:07,580
score 等于34.9

74
00:03:07,580 --> 00:03:11,270
name 是大乔乔

75
00:03:11,270 --> 00:03:14,300
address 是中国

76
00:03:14,300 --> 00:03:17,040
这样搞保存也是可以的

77
00:03:17,040 --> 00:03:20,100
就说我把这个成员变量名称啊

78
00:03:20,100 --> 00:03:21,080
省略不写了

79
00:03:21,080 --> 00:03:23,590
但是这种写法有一个前提条件

80
00:03:23,590 --> 00:03:27,200
就是呢，你必须给所有的成员变量全部复制

81
00:03:27,200 --> 00:03:28,800
而且这个赋值的顺序

82
00:03:28,800 --> 00:03:32,192
必须跟当初定义这个结构体里面

83
00:03:32,192 --> 00:03:34,810
所有的产顺序严格保持一致

84
00:03:34,810 --> 00:03:37,390
结构体除了运用自己的成员变量之外

85
00:03:37,390 --> 00:03:39,880
还可以运用自己的成员方

86
00:03:39,880 --> 00:03:40,890
演示一下

87
00:03:40,890 --> 00:03:44,330
funk u user 

88
00:03:44,330 --> 00:03:45,935
hello 

89
00:03:45,935 --> 00:03:49,575
关于函数我们后面会专门细讲

90
00:03:49,575 --> 00:03:50,670
先来看一下

91
00:03:50,670 --> 00:03:52,530
这本来是一个普通的函数

92
00:03:52,530 --> 00:03:53,170
只不过呢

93
00:03:53,170 --> 00:03:55,310
它在这个函数名称的前

94
00:03:55,310 --> 00:03:56,330
加了个小括号

95
00:03:56,330 --> 00:03:58,030
小括号里面加了个 user 啊

96
00:03:58,030 --> 00:04:01,040
这个 U 是一个变量

97
00:04:01,040 --> 00:04:03,310
它的名称可以随便写啊

98
00:04:03,310 --> 00:04:06,110
你写一个 M 也可以

99
00:04:06,110 --> 00:04:08,680
me 都行

100
00:04:08,680 --> 00:04:10,630
我在里面随便实现一下

101
00:04:10,630 --> 00:04:12,130
输出一个东西吧

102
00:04:12,130 --> 00:04:18,505
my name is me 点 name 

103
00:04:18,505 --> 00:04:23,560
就好比说你把这个密传到 hello 函数里面来了

104
00:04:23,560 --> 00:04:25,340
你可以直接去使用这个 me 

105
00:04:25,340 --> 00:04:28,402
就如同你在这边使用这个 U 1样

106
00:04:28,402 --> 00:04:32,130
通过 me 可以拿到它的某一个成员变量

107
00:04:32,130 --> 00:04:35,050
好，那这个我们称之为叫做

108
00:04:36,310 --> 00:04:38,287
成员方

109
00:04:38,287 --> 00:04:40,680
我们也可以去调用这个成员方法

110
00:04:40,680 --> 00:04:44,350
比方说我在这个地方就调一下 U 

111
00:04:44,350 --> 00:04:46,040
它的成员方法

112
00:04:46,040 --> 00:04:47,295
hello 

113
00:04:47,295 --> 00:04:48,280
注意啊

114
00:04:48,280 --> 00:04:49,660
你在这个地方是吧

115
00:04:49,660 --> 00:04:52,380
你这个名称 me 是随便写的

116
00:04:52,380 --> 00:04:55,080
那么在函数体里面就必须使用 me 

117
00:04:55,080 --> 00:04:57,450
而在这边我去调用这个方法的时候

118
00:04:57,450 --> 00:04:59,960
我是通过 U 来调用的

119
00:04:59,960 --> 00:05:02,570
那么调用参数函数应该会输

120
00:05:02,570 --> 00:05:03,640
my name is 

121
00:05:03,640 --> 00:05:05,010
my name is 什么呢

122
00:05:05,010 --> 00:05:06,400
My name i

123
00:05:06,400 --> 00:05:08,307
大乔乔

124
00:05:08,307 --> 00:05:11,182
我们这边用的是 print ln 

125
00:05:11,182 --> 00:05:12,720
那 LN 的话

126
00:05:12,720 --> 00:05:16,120
小括号里面可以放任意多个参数

127
00:05:16,120 --> 00:05:17,470
这边是两个

128
00:05:17,470 --> 00:05:21,820
将来还可以有第三个、第四个

129
00:05:21,820 --> 00:05:23,710
这样写都行

130
00:05:23,710 --> 00:05:24,640
那这样的话

131
00:05:24,640 --> 00:05:29,000
它实际上是说把这四部分依次的进行打印

132
00:05:29,000 --> 00:05:32,910
每一部分中间会加一个空格来进行分隔

133
00:05:32,910 --> 00:05:34,460
待会儿我们看一下

134
00:05:34,460 --> 00:05:35,840
这边只有两部分

135
00:05:35,840 --> 00:05:39,150
那么 is 后面是不是有一个空

136
00:05:39,150 --> 00:05:40,440
运行一下

137
00:05:41,480 --> 00:05:43,820
我们说 main 函数是程序入口吗

138
00:05:43,820 --> 00:05:45,520
所以他一上来啊

139
00:05:45,520 --> 00:05:47,300
你先不要管上面这些

140
00:05:47,300 --> 00:05:49,720
他一上来先去找 main 函数

141
00:05:49,720 --> 00:05:50,960
好，找到 main 函数

142
00:05:50,960 --> 00:05:52,980
然后20行有一个输出

143
00:05:52,980 --> 00:05:55,190
输出 name 是大大小小

144
00:05:55,190 --> 00:05:58,550
然后呢，第22行调这个 hello 

145
00:05:58,550 --> 00:05:59,470
于是乎呢

146
00:05:59,470 --> 00:06:01,510
他走到 hello 里面来发现哦

147
00:06:01,510 --> 00:06:03,400
原来是应该输出这样的东西

148
00:06:03,400 --> 00:06:05,820
那 is 后面确实加了个空格

149
00:06:05,820 --> 00:06:08,190
然后才是大小小

150
00:06:08,190 --> 00:06:12,640
再来演示一个匿名结构体 type 

151
00:06:12,640 --> 00:06:15,530
student struct 

152
00:06:17,820 --> 00:06:23,492
name 字符串、 age 年龄整数

153
00:06:23,492 --> 00:06:24,670
那么这样的话

154
00:06:24,670 --> 00:06:27,130
相当于是我定义了一个结构体

155
00:06:27,130 --> 00:06:30,460
这个结构体名称叫做学生啊

156
00:06:30,460 --> 00:06:33,280
跟上面这种定义 user 方法是一样的

157
00:06:33,280 --> 00:06:37,270
无非是说这个 user 它是定义在外面的对吧

158
00:06:37,270 --> 00:06:41,460
而这个学生他是定义在这个 main 函数里面的

159
00:06:41,460 --> 00:06:44,460
后面我们会讲变成了纵欲

160
00:06:44,460 --> 00:06:46,080
他们中域一个是全局的

161
00:06:46,080 --> 00:06:47,397
一个是局部的

162
00:06:47,397 --> 00:06:49,130
现在你先不用关心

163
00:06:49,130 --> 00:06:51,550
那问题是说，假如说啊

164
00:06:51,550 --> 00:06:53,800
假如说我把这个 type 改一下

165
00:06:53,800 --> 00:06:56,940
改成挖的话

166
00:06:56,940 --> 00:07:02,110
那么这个学生就不再是结构体名称了

167
00:07:02,110 --> 00:07:04,195
他是一个变量名称

168
00:07:04,195 --> 00:07:06,910
因为前面是用 while 来进行修饰嘛

169
00:07:06,910 --> 00:07:11,180
所以这个学生跟这个 U 性质是一样的

170
00:07:11,180 --> 00:07:15,832
它都是一个结构体类型的变量

171
00:07:15,832 --> 00:07:17,230
那么呢

172
00:07:17,230 --> 00:07:20,590
我把这个 student 跟这个 U 类比一下

173
00:07:20,590 --> 00:07:22,020
比如说啊

174
00:07:22,020 --> 00:07:25,917
我可以给他的这个 name 赋值对吧

175
00:07:25,917 --> 00:07:27,320
随便赋个值

176
00:07:28,430 --> 00:07:30,750
然后呢，可以给它的这个 A 点赋值

177
00:07:30,750 --> 00:07:31,710
那如果不赋值的话

178
00:07:31,710 --> 00:07:33,795
默认是等于零嘛

179
00:07:33,795 --> 00:07:34,850
同理啊

180
00:07:34,850 --> 00:07:37,590
那么前面的这个 U 

181
00:07:37,590 --> 00:07:40,530
也是可以给他复制的 U 点

182
00:07:41,740 --> 00:07:44,700
score 等于10

183
00:07:44,700 --> 00:07:48,020
也可以赋值如来个优

184
00:07:48,020 --> 00:07:49,500
name 等于

185
00:07:50,870 --> 00:07:52,190
学生点 name 是吧

186
00:07:52,190 --> 00:07:55,242
你把学生 name 呢，付给了 you 的 name 

187
00:07:55,242 --> 00:07:57,180
这就是匿名结构体

188
00:07:57,180 --> 00:08:00,070
那立面结构体用在什么场景下呢

189
00:08:00,070 --> 00:08:03,370
就是如果你在一个函数里面

190
00:08:03,370 --> 00:08:04,610
需要一个结构体

191
00:08:04,610 --> 00:08:05,910
而这个结构体呢

192
00:08:05,910 --> 00:08:08,767
仅在这个函数里面使用一次

193
00:08:08,767 --> 00:08:09,910
那么的话

194
00:08:09,910 --> 00:08:13,700
我们并不关心这个结构体名称叫什么啊

195
00:08:13,700 --> 00:08:15,920
我们只关心有这样一个变量

196
00:08:15,920 --> 00:08:17,855
它是一个结构体就够了

197
00:08:17,855 --> 00:08:19,847
就不需要重用了

198
00:08:19,847 --> 00:08:21,480
具名的结构体呢

199
00:08:21,480 --> 00:08:24,610
你可以在很多地方反复的去声明

200
00:08:24,610 --> 00:08:26,477
各种各样不同的 user 

201
00:08:26,477 --> 00:08:28,330
这边声明一个 U 

202
00:08:28,330 --> 00:08:32,636
你下面还可以再来一个 U 2

203
00:08:32,636 --> 00:08:36,630
它也等于 user 这一种结构体

204
00:08:36,630 --> 00:08:42,394
U 2点 address 等于上海

205
00:08:42,394 --> 00:08:45,650
所以你就可以反复的使用这个 user 结构体嘛

206
00:08:45,650 --> 00:08:47,610
而这个玩意呢，它是匿名的

207
00:08:47,610 --> 00:08:48,957
没有名称

208
00:08:48,957 --> 00:08:51,640
它只能创建一个结构体

209
00:08:51,640 --> 00:08:53,720
实例第33行

210
00:08:53,720 --> 00:08:57,610
这种写法我是搞了一个空的结果

211
00:08:57,610 --> 00:08:59,690
因为大括号里面没有给任何人赋值嘛

212
00:08:59,690 --> 00:09:03,230
你像我这边好歹还给三个成员赋值了

213
00:09:03,230 --> 00:09:05,640
而这个大括号里面空的都没有赋值

214
00:09:05,640 --> 00:09:06,880
这样也是可以的

215
00:09:06,880 --> 00:09:09,307
都没复制意味着都是零值嘛

216
00:09:09,307 --> 00:09:12,900
然后你可以显示的逐个逐个的给它赋值

217
00:09:12,900 --> 00:09:14,880
由于 U 2是第一次出现

218
00:09:14,880 --> 00:09:16,500
所以呢，需要我先声明

219
00:09:16,500 --> 00:09:19,080
声明的话，你要么就是用 one 来声明

220
00:09:19,080 --> 00:09:22,922
要么呢就是在等号前面加一个冒号来声明

221
00:09:22,922 --> 00:09:26,047
最后我们演示一下结构体指针

222
00:09:26,047 --> 00:09:29,890
哎，指针也是编程语言里面很重要的一个概念

223
00:09:29,890 --> 00:09:32,340
比如我来一个 U 3

224
00:09:32,340 --> 00:09:35,820
它等于 U 

225
00:09:35,820 --> 00:09:40,750
输出 U 3的 name 

226
00:09:40,750 --> 00:09:44,110
好，那这样的话， U 3和 U 2相等嘛，对吧

227
00:09:44,110 --> 00:09:46,030
他们俩是内容完全一样的

228
00:09:46,030 --> 00:09:51,250
那如果说我在前面加一个取值符号呢

229
00:09:51,250 --> 00:09:54,680
啊，这个是取值符号

230
00:09:54,680 --> 00:09:55,790
这意味着

231
00:09:55,790 --> 00:09:58,930
我要把 U 2这个结构体的地址取出来

232
00:09:58,930 --> 00:10:01,040
付给 U 3

233
00:10:01,040 --> 00:10:02,670
我们任何变量

234
00:10:02,670 --> 00:10:05,490
不管是简单的整数还是复杂结构体

235
00:10:05,490 --> 00:10:06,710
它们在内存里面

236
00:10:06,710 --> 00:10:09,290
都是要占用一定的内存空间的

237
00:10:09,290 --> 00:10:12,007
而内存空间它都是有地址的

238
00:10:12,007 --> 00:10:14,390
就好比是一个一个宿舍

239
00:10:14,390 --> 00:10:15,690
他是一个一个空间

240
00:10:15,690 --> 00:10:18,810
每个空间都有一个宿舍编号

241
00:10:18,810 --> 00:10:19,550
好

242
00:10:19,550 --> 00:10:22,030
所以 U 3就相当于是那个502

243
00:10:22,030 --> 00:10:23,790
503那个宿舍编号

244
00:10:23,790 --> 00:10:26,660
我拿着这个编号

245
00:10:26,660 --> 00:10:27,580
拿着地址

246
00:10:27,580 --> 00:10:30,490
我就可以找到对应的内存空间

247
00:10:30,490 --> 00:10:31,750
找到对应的宿舍

248
00:10:31,750 --> 00:10:32,690
从宿舍里面

249
00:10:32,690 --> 00:10:36,415
我就可以找到我想要的某一个成员

250
00:10:36,415 --> 00:10:38,910
所以呢，这个 U 

251
00:10:39,980 --> 00:10:44,700
它是这种 user 的指针类型啊

252
00:10:44,700 --> 00:10:47,740
我们一般是用星号来表示一种指针类型

253
00:10:48,850 --> 00:10:50,110
或者可以怎么做呢

254
00:10:50,110 --> 00:10:52,822
这样做我来个 U 

255
00:10:52,822 --> 00:10:55,450
等于 new 啊， new 1把

256
00:10:55,450 --> 00:10:57,440
OK ，那这样的话， U 4啊

257
00:10:57,440 --> 00:11:01,435
他也是这种 user 的指针类型

258
00:11:01,435 --> 00:11:02,460
也就是说

259
00:11:02,460 --> 00:11:05,140
new 这个关键字是购物原则的关键词

260
00:11:05,140 --> 00:11:07,280
这个关键词它本质上

261
00:11:07,280 --> 00:11:10,440
它会向操作系统去申请一块内存空间

262
00:11:10,440 --> 00:11:13,477
用来容纳 user 这样一种结构体

263
00:11:13,477 --> 00:11:17,442
然后呢，返回这个结构体所对应的指针

264
00:11:17,442 --> 00:11:18,180
当然了

265
00:11:18,180 --> 00:11:21,580
这个时候这个 user 里面所有成员都还是空的

266
00:11:21,580 --> 00:11:23,115
就是零值

267
00:11:23,115 --> 00:11:26,860
你可以显示的给他们逐个赋值嘛

268
00:11:26,860 --> 00:11:32,950
new 是先创建空的结构体

269
00:11:32,950 --> 00:11:36,900
再返回其指针
