1
00:00:00,140 --> 00:00:01,510
好，我们看一下这个

2
00:00:01,510 --> 00:00:04,240
这边有一个对应的测试代码啊

3
00:00:04,240 --> 00:00:06,342
就专门测这个代理的

4
00:00:06,342 --> 00:00:07,090
好

5
00:00:07,090 --> 00:00:11,612
这边是打算把这个 QPS 限制为每秒钟十个

6
00:00:11,612 --> 00:00:15,640
然后呢，能通过单例去搞了一个代理啊

7
00:00:15,640 --> 00:00:18,810
这个是传 EDCD 的集群的 IP 地址

8
00:00:18,810 --> 00:00:19,870
这个三是

9
00:00:19,870 --> 00:00:22,430
这个三应该是那个心跳的频率啊

10
00:00:22,430 --> 00:00:24,430
每一个三秒钟心上报心跳

11
00:00:24,430 --> 00:00:26,790
这个 QPS 是限流啊

12
00:00:26,790 --> 00:00:27,855
每秒十个

13
00:00:27,855 --> 00:00:30,390
然后随便伪造了一个 end point 

14
00:00:30,390 --> 00:00:32,849
然后把这个 end point 通过

15
00:00:32,849 --> 00:00:33,970
通过代理啊

16
00:00:33,970 --> 00:00:35,850
通过代理进行这个注册

17
00:00:35,850 --> 00:00:36,785
注册

18
00:00:36,785 --> 00:00:40,010
实际上你通过代理进行注册背后

19
00:00:40,010 --> 00:00:43,650
它还是会真正的去往 ETCD 上面写啊

20
00:00:43,650 --> 00:00:46,137
这样的话 ETECD 上面就知道哦

21
00:00:46,137 --> 00:00:47,900
这个服务下面啊

22
00:00:47,900 --> 00:00:50,760
就多了这样一个 end point 是吧

23
00:00:50,760 --> 00:00:53,142
本地的这个5000端口号啊

24
00:00:53,142 --> 00:00:55,050
啊，然后我再 defer 一下

25
00:00:55,050 --> 00:00:56,410
把它给注销掉

26
00:00:56,410 --> 00:00:58,730
啊，这个 defer 是在最后才执行的吧

27
00:00:58,730 --> 00:01:01,365
啊，起码星在还没有执行啊

28
00:01:01,365 --> 00:01:04,310
啊，你刚刚不是注册了一个 and point 吗

29
00:01:04,310 --> 00:01:07,070
然后我去 get service and points 是吧

30
00:01:07,070 --> 00:01:09,390
我获得所有的这个终端

31
00:01:09,390 --> 00:01:10,750
它返回的是一个切片

32
00:01:10,750 --> 00:01:12,210
我把切片呢打出来

33
00:01:12,210 --> 00:01:13,910
所以这个时候这个切片里面

34
00:01:13,910 --> 00:01:16,050
应该是只有这一个元素

35
00:01:16,050 --> 00:01:19,430
OK ，然后我呢，我又搞了第二个啊

36
00:01:19,430 --> 00:01:21,510
第二个，你看这 IP 变了吗

37
00:01:21,510 --> 00:01:23,450
本来是从2~3了嘛，对吧

38
00:01:23,450 --> 00:01:24,270
IP 变了啊

39
00:01:24,270 --> 00:01:27,600
再增加一个终端注册上去

40
00:01:27,600 --> 00:01:29,140
然后呢，再赌一次

41
00:01:29,140 --> 00:01:30,080
OK ，应该上面还有

42
00:01:30,080 --> 00:01:31,265
上面还有一对吧

43
00:01:31,265 --> 00:01:33,660
总共啊，我陆陆续续的啊

44
00:01:33,660 --> 00:01:35,220
往上面添加了三个终端

45
00:01:35,220 --> 00:01:36,200
每添加一个呢

46
00:01:36,200 --> 00:01:38,590
我都会去调一下这个 get 啊

47
00:01:38,590 --> 00:01:41,987
看看目前一共有哪些终端

48
00:01:41,987 --> 00:01:46,240
好，然后我想测一测那个限流啊

49
00:01:46,240 --> 00:01:48,590
想限流这边啊

50
00:01:48,590 --> 00:01:50,510
先休息了一秒钟

51
00:01:50,510 --> 00:01:52,330
为什么故意去休息一秒钟呢

52
00:01:52,330 --> 00:01:55,380
因为当我这个 proxy 啊

53
00:01:55,380 --> 00:01:58,420
我通过单例创建好这个 proxy 之后

54
00:01:59,570 --> 00:02:02,210
你看我在通过单例创建 pk 的时候

55
00:02:02,210 --> 00:02:04,610
我不是已经创建了一个限流器吗

56
00:02:04,610 --> 00:02:07,610
我这个令牌桶已经开始工作了啊

57
00:02:07,610 --> 00:02:08,570
这样的话呢

58
00:02:08,570 --> 00:02:10,370
我之所以休息一秒钟

59
00:02:10,370 --> 00:02:14,400
主要是想让那个令牌桶满攒满

60
00:02:14,400 --> 00:02:16,340
因为当初我不是看看啊

61
00:02:16,340 --> 00:02:19,440
当初我这个令牌桶里面容量是 QPS 嘛

62
00:02:19,440 --> 00:02:22,600
也就是我桶里面最多可以容纳十个令牌，对吧

63
00:02:22,600 --> 00:02:25,450
那我同时根据我那个生产速度

64
00:02:25,450 --> 00:02:29,170
我可以保证一秒钟就可以把桶呢给塞满

65
00:02:29,170 --> 00:02:31,630
一秒钟它里面就有十个令牌了

66
00:02:31,630 --> 00:02:33,690
好，有十个令牌之后啊

67
00:02:33,690 --> 00:02:35,870
我马不停蹄的啊

68
00:02:35,870 --> 00:02:37,670
我快速的 for 循环什么

69
00:02:37,670 --> 00:02:39,590
for 循环15次啊

70
00:02:39,590 --> 00:02:40,890
for 循环15次

71
00:02:40,890 --> 00:02:44,850
然后呢，我从里面去取得啊

72
00:02:44,850 --> 00:02:45,850
我想去访问啊

73
00:02:45,850 --> 00:02:49,450
我去调用这个 get service and points 这个接口

74
00:02:49,450 --> 00:02:53,832
好，由于我对这个接口不是进行了限流保护吗

75
00:02:53,832 --> 00:02:56,000
啊，所以说你前十次啊

76
00:02:56,000 --> 00:02:58,160
因为我同里面已经有十个令牌了

77
00:02:58,160 --> 00:03:00,080
所以呢，这个 for 循环15次

78
00:03:00,080 --> 00:03:04,980
那么前十次的话可以正常的访问这个函数

79
00:03:04,980 --> 00:03:06,860
可以正常的拿到结果

80
00:03:07,900 --> 00:03:11,660
这个结果这个 and points 里面应该是有三个终端

81
00:03:11,660 --> 00:03:12,060
对吧

82
00:03:12,060 --> 00:03:14,700
就是 IP 分别是三、二、一嘛

83
00:03:14,700 --> 00:03:15,430
第三个

84
00:03:15,430 --> 00:03:15,880
好

85
00:03:15,880 --> 00:03:17,600
但是从第11次开始

86
00:03:17,600 --> 00:03:20,870
第11次开始应该会命中那个现有策略

87
00:03:20,870 --> 00:03:21,690
就拿不到了

88
00:03:21,690 --> 00:03:22,540
我们看一下

89
00:03:22,540 --> 00:03:25,390
如果命中限流策略拿不了的话

90
00:03:25,390 --> 00:03:26,650
它返回的是 new 

91
00:03:26,650 --> 00:03:28,270
也就是说我这边啊

92
00:03:28,270 --> 00:03:30,590
应该是说后面五次啊

93
00:03:30,590 --> 00:03:32,730
应该是输出是六啊

94
00:03:32,730 --> 00:03:34,470
空的空的切片

95
00:03:34,470 --> 00:03:35,380
好

96
00:03:35,380 --> 00:03:37,640
然后这边呢，一一

97
00:03:37,640 --> 00:03:41,140
你可以认为这数字 for 循环很快就完成了

98
00:03:41,140 --> 00:03:42,180
几乎一瞬间就完成了

99
00:03:42,180 --> 00:03:42,720
为什么呢

100
00:03:42,720 --> 00:03:45,015
因为你这个

101
00:03:45,015 --> 00:03:47,967
这个函数本身会命中本地缓存吗

102
00:03:47,967 --> 00:03:51,070
所以他也不会去走什么网络访问 E 、 T 、 C 、 D 啊

103
00:03:51,070 --> 00:03:52,450
全都是全部是本地的嘛

104
00:03:52,450 --> 00:03:54,270
所以很快就 for 循环了

105
00:03:54,270 --> 00:03:57,420
然后呢，我在休眠一秒钟啊

106
00:03:57,420 --> 00:04:00,440
又是目的是想等那个桶满对吧

107
00:04:00,440 --> 00:04:02,940
这样桶里面又有十个令牌

108
00:04:02,940 --> 00:04:06,180
然后我再重复跟之前做一模一样的工作

109
00:04:06,180 --> 00:04:08,760
那这个结果应该跟这一次的结果

110
00:04:08,760 --> 00:04:09,960
是一模一样的啊

111
00:04:09,960 --> 00:04:12,040
这个单侧跑起来看一看

112
00:04:15,180 --> 00:04:18,094
好，我们来分析一下这个结果啊

113
00:04:18,094 --> 00:04:21,800
呃，第一条日志是输出说开始监听服务

114
00:04:21,800 --> 00:04:24,910
这个 test service 的变化

115
00:04:24,910 --> 00:04:28,770
这个是从代理的第71行输出出来的

116
00:04:28,770 --> 00:04:29,510
为什么呢

117
00:04:29,510 --> 00:04:34,490
是因为我们第18行代码啊

118
00:04:34,490 --> 00:04:37,427
你去获得所有的终端的时候呢

119
00:04:37,427 --> 00:04:42,400
这个函数里面他会去触发这个监听操作，对吧

120
00:04:42,400 --> 00:04:44,915
那在监听操作里面呢

121
00:04:44,915 --> 00:04:48,000
会有第71行，对吧

122
00:04:48,000 --> 00:04:50,040
监听服务什么的变化

123
00:04:50,040 --> 00:04:50,690
诶

124
00:04:50,690 --> 00:04:54,060
就是刚才日里面大家看的这个第71行

125
00:04:54,060 --> 00:04:56,660
监听哪一个服务的激烈变化

126
00:04:56,660 --> 00:04:58,820
OK ，开始监听了对吧

127
00:04:58,820 --> 00:04:59,580
开始监听

128
00:04:59,580 --> 00:05:02,380
然后他说刷新对应的这个服务

129
00:05:02,380 --> 00:05:03,620
下面对应的 server ，诶

130
00:05:03,620 --> 00:05:06,710
他拿到了这个幺啊一这个 IP 

131
00:05:06,710 --> 00:05:08,410
为什么会有正常认知呢

132
00:05:08,410 --> 00:05:11,910
是因为刚才我们不是去监听这个变化吗

133
00:05:11,910 --> 00:05:13,550
那么监听的话

134
00:05:13,550 --> 00:05:15,860
我们在 test 代码里面

135
00:05:15,860 --> 00:05:17,580
不是会往上面去注册一个吗

136
00:05:17,580 --> 00:05:20,120
啊，会往 E 、 D 、 C 、 D 上面去写入一条数据

137
00:05:20,120 --> 00:05:21,620
那一旦写入的话

138
00:05:21,620 --> 00:05:24,660
那我这边监听是不是就可以拿到一些事件

139
00:05:24,660 --> 00:05:26,400
好，拿到这个时间之后

140
00:05:26,400 --> 00:05:27,400
哎，我就知道哦

141
00:05:27,400 --> 00:05:29,920
是哪一个服务对应的节点变化了

142
00:05:29,920 --> 00:05:33,910
然后呢，我就会去跟 ETCD 做一次数据同步

143
00:05:33,910 --> 00:05:34,950
去调这个函数

144
00:05:34,950 --> 00:05:37,130
好，这个函数调进来呢

145
00:05:37,130 --> 00:05:38,970
在下面会输出啊

146
00:05:38,970 --> 00:05:42,530
就从我说我从 etc 上面拿到了这个服务

147
00:05:42,530 --> 00:05:45,030
下面对应的有哪些 server 啊

148
00:05:45,030 --> 00:05:48,470
目前来看呢，是六 K 1个 IP 

149
00:05:48,470 --> 00:05:52,810
好，然后在我们的这个 test 代码里面

150
00:05:54,640 --> 00:05:57,280
就这啊，这会输出说，哎

151
00:05:57,280 --> 00:05:59,975
他有这样一个 IP 

152
00:05:59,975 --> 00:06:01,340
那同样道理啊

153
00:06:01,340 --> 00:06:04,140
随着你这个 end point 不断的加入

154
00:06:04,140 --> 00:06:04,840
那么呢

155
00:06:04,840 --> 00:06:09,760
这个他从 ETC 上面拿到的会越来越多

156
00:06:09,760 --> 00:06:10,840
因为每次加入

157
00:06:10,840 --> 00:06:14,710
他都会触发那个监听到新的事件嘛

158
00:06:14,710 --> 00:06:16,900
模拟那个数字的 for 循环

159
00:06:16,900 --> 00:06:18,120
那么我们看到

160
00:06:18,120 --> 00:06:20,040
对于前十次而言啊

161
00:06:20,040 --> 00:06:21,140
对于前十次而言

162
00:06:21,140 --> 00:06:24,120
他都可以取到对应的 and points 

163
00:06:24,120 --> 00:06:25,180
就是这三台

164
00:06:25,180 --> 00:06:26,600
那么从后面五次呢

165
00:06:26,600 --> 00:06:28,180
后面五次返回 new 嘛

166
00:06:28,180 --> 00:06:29,672
就取不到了，为空

167
00:06:29,672 --> 00:06:32,570
那如果把这个工作重复做一次的话

168
00:06:32,570 --> 00:06:35,165
跟刚才那个结果是一样的

169
00:06:35,165 --> 00:06:38,540
最后啊，我这个 test 函数要退出了

170
00:06:38,540 --> 00:06:41,870
退出的话会触发所有的那个 deer ，对吧

171
00:06:41,870 --> 00:06:43,070
这 deer 会触发啊

172
00:06:43,070 --> 00:06:46,120
那么呢，我们说这里面有三个 D 份

173
00:06:46,120 --> 00:06:49,520
那最先执行的是最后面这个地方对吧

174
00:06:49,520 --> 00:06:51,740
就他最先是把这个三

175
00:06:51,740 --> 00:06:53,980
这个 IP 呢，注销掉

176
00:06:53,980 --> 00:06:55,840
然后是注销二这个 IP 

177
00:06:55,840 --> 00:06:57,620
我们看一下对应的预知

178
00:06:58,750 --> 00:07:03,660
你看他注销这个服务下面对应的节点三对吧

179
00:07:03,660 --> 00:07:05,280
那么你一旦注销的话

180
00:07:05,280 --> 00:07:08,322
我们那个监听那还在监听着呢对吧

181
00:07:08,322 --> 00:07:10,810
那么监听的话就会触发这行认知

182
00:07:10,810 --> 00:07:13,090
它发现诶，这个节点变化了

183
00:07:13,090 --> 00:07:18,240
那么最新的节点就是一和这个二

184
00:07:18,240 --> 00:07:22,170
然后呢，我们再一次注销二这个 IP 

185
00:07:22,170 --> 00:07:24,390
然后监听那发现又变化了

186
00:07:24,390 --> 00:07:27,682
那么它最新的 server 就只剩下这个一了

187
00:07:27,682 --> 00:07:30,040
最后呢，去注销这个一

188
00:07:30,040 --> 00:07:31,700
一一旦注销完的话

189
00:07:31,700 --> 00:07:33,300
那么整个程序就退出了

190
00:07:33,300 --> 00:07:38,160
所以呢，他就没有来得及去打印降的预支
