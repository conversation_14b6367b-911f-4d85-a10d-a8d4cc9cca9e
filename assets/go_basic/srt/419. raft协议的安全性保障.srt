1
00:00:00,099 --> 00:00:04,019
安全性 rapper 算法可以保证以下这五点

2
00:00:04,019 --> 00:00:06,120
在任意时刻始终为真

3
00:00:06,120 --> 00:00:09,630
第一点，在指定 term 的情况之下

4
00:00:09,630 --> 00:00:11,817
只可能有一个 leader 

5
00:00:11,817 --> 00:00:13,360
如果有多个 leader 

6
00:00:13,360 --> 00:00:15,630
那么它们对应的 term 肯定不一样

7
00:00:15,630 --> 00:00:18,050
第二点，对于 leader 而言

8
00:00:18,050 --> 00:00:19,340
它上面那一只呢

9
00:00:19,340 --> 00:00:20,840
只能是往后追加

10
00:00:20,840 --> 00:00:23,215
不可能被重写或删除

11
00:00:23,215 --> 00:00:25,830
只有作为 follow 他的认知

12
00:00:25,830 --> 00:00:30,307
但是未交的日志才可能被删除或重写

13
00:00:30,307 --> 00:00:31,330
第三点

14
00:00:31,330 --> 00:00:35,010
如果两个基点上面存在相同的认知

15
00:00:35,010 --> 00:00:37,810
也就是说 term 和 index 都相同

16
00:00:37,810 --> 00:00:41,530
那么呢，在这个日志之前的所有日志

17
00:00:41,530 --> 00:00:44,670
这两个节点上面的应该是一一对应的

18
00:00:44,670 --> 00:00:46,002
完全相等的

19
00:00:46,002 --> 00:00:49,000
就只要有一个位置能够匹配上

20
00:00:49,000 --> 00:00:52,080
那么这个位置往前的都是一样的

21
00:00:52,080 --> 00:00:53,870
第四点，就是一个认知

22
00:00:53,870 --> 00:00:57,927
如果已经在某个 term 之内已经被提交了

23
00:00:57,927 --> 00:01:00,560
那么在之后的所有的 term 之内

24
00:01:00,560 --> 00:01:04,030
它肯定存在于 leader 那个节点上

25
00:01:04,030 --> 00:01:04,998
第五点

26
00:01:04,998 --> 00:01:06,520
如果在一个节点上

27
00:01:06,520 --> 00:01:09,160
比如说 index 等于十这个日值

28
00:01:09,160 --> 00:01:11,742
它已经被应用到状态机了

29
00:01:11,742 --> 00:01:13,270
那么在其他节点上

30
00:01:13,270 --> 00:01:15,690
index 等于十这个位置

31
00:01:15,690 --> 00:01:18,170
应该对应的都是同一条鱼值

32
00:01:18,170 --> 00:01:20,530
也就是说 index 等于十这个位置

33
00:01:20,530 --> 00:01:22,290
既然被应用到步态机了

34
00:01:22,290 --> 00:01:24,530
说明他肯定是被提交了

35
00:01:24,530 --> 00:01:27,270
那么其他位置上 index 等于

36
00:01:27,270 --> 00:01:29,330
这个位置自然也被提交了

37
00:01:29,330 --> 00:01:31,117
那么 index 等于十这个位置

38
00:01:31,117 --> 00:01:34,830
大家对应的肯定都是同一条日志

39
00:01:34,830 --> 00:01:36,420
因为前面我们

40
00:01:36,420 --> 00:01:38,780
你仅仅根据 index 相同

41
00:01:38,780 --> 00:01:41,080
是不能确定是同一条日制的

42
00:01:41,080 --> 00:01:45,090
必须是 com 和 index 同时相同才能确定调日志

43
00:01:45,090 --> 00:01:49,105
但现在的有一个前提是说他已经被提交了

44
00:01:49,105 --> 00:01:51,640
那这个时候就不用去关心 term 

45
00:01:51,640 --> 00:01:52,540
只要被提交

46
00:01:52,540 --> 00:01:54,160
那其实只要 index 相同

47
00:01:54,160 --> 00:01:56,380
就可以肯定是同一条日式
