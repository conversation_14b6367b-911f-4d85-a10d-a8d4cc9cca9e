1
00:00:00,200 --> 00:00:02,900
这几个看一下 GOOM 的日志

2
00:00:02,900 --> 00:00:05,340
和连接池相关的东西

3
00:00:05,340 --> 00:00:08,500
那它们发生在我们创建连接的这一步啊

4
00:00:08,500 --> 00:00:09,680
那创建连接的话

5
00:00:09,680 --> 00:00:12,840
这个字符串我们在第一节课快速入门

6
00:00:12,840 --> 00:00:13,600
已经讲过了

7
00:00:13,600 --> 00:00:15,482
这里就不再赘述了

8
00:00:15,482 --> 00:00:20,110
然后的话，我们在这边可以指定一个日志啊

9
00:00:20,110 --> 00:00:22,730
通过 open 去创建链接嘛

10
00:00:22,730 --> 00:00:24,040
那创建连接的话

11
00:00:24,040 --> 00:00:28,630
之前我们是把这个 configure 这个选项 option 就为空了

12
00:00:28,630 --> 00:00:31,867
那现在呢，我们给大家搞一个 CONFIG 

13
00:00:31,867 --> 00:00:34,080
CONFIG 里面可以加很多东西的

14
00:00:34,080 --> 00:00:37,120
比如说这边来了一个 naming strategy 是吧

15
00:00:37,120 --> 00:00:38,680
这种命名策略对吧

16
00:00:38,680 --> 00:00:39,782
命名策略

17
00:00:39,782 --> 00:00:41,910
那么在模型映射里面的话

18
00:00:41,910 --> 00:00:44,110
它有一个默认的对应关系吗

19
00:00:44,110 --> 00:00:47,550
比方说把结构体的名称变为车型和复数

20
00:00:47,550 --> 00:00:50,650
就是表明把这个结构体的成员呢

21
00:00:50,650 --> 00:00:53,292
从驼峰转为蛇形就是列名

22
00:00:53,292 --> 00:00:55,380
那实际上啊，比方说这边啊

23
00:00:55,380 --> 00:00:58,260
你如果开了这个 table prefix 

24
00:00:58,260 --> 00:00:59,900
表明加一个前缀

25
00:00:59,900 --> 00:01:03,430
比如说就以这个 T 下划线作为前缀的话

26
00:01:03,430 --> 00:01:06,510
那么呢，它会默认的对应到表里面

27
00:01:06,510 --> 00:01:10,370
都会带一个 T 下划线来作为前缀

28
00:01:10,370 --> 00:01:14,280
比如说你的结构体名称叫做 user 啊

29
00:01:14,280 --> 00:01:15,285
大写 U 

30
00:01:15,285 --> 00:01:17,490
那么对应成表面的话

31
00:01:17,490 --> 00:01:21,390
除了把驼峰转为实性负数加一个 S 之外

32
00:01:21,390 --> 00:01:24,810
它还会带上这个 T 下划线来作为前缀

33
00:01:24,810 --> 00:01:27,750
再一个，你比方说这个 single lar table 对吧

34
00:01:27,750 --> 00:01:29,720
就是说单数的表格

35
00:01:29,720 --> 00:01:32,340
那这样的话，如果你把这一项就为 true 呢

36
00:01:32,340 --> 00:01:37,100
那将来这个 user 它堆的表名就叫 user 

37
00:01:37,100 --> 00:01:39,040
就仅仅是驼峰转车型

38
00:01:39,040 --> 00:01:41,982
它就不会去给你加那个复数 S 了

39
00:01:41,982 --> 00:01:46,417
所以如果说我们在这边把它已经置为 true 了

40
00:01:46,417 --> 00:01:49,140
再返回去我们在这边的话

41
00:01:49,140 --> 00:01:51,260
这个 table name 啊就可以注释掉

42
00:01:51,260 --> 00:01:52,370
就可以不要了

43
00:01:52,370 --> 00:01:54,660
比方说还有这个 NO lower case 

44
00:01:54,660 --> 00:01:56,760
就是不需要你给我转小写

45
00:01:56,760 --> 00:01:59,590
意思就是说我在结构体里面

46
00:01:59,590 --> 00:02:00,870
我这些列对吧

47
00:02:00,870 --> 00:02:02,450
我叫什么名字

48
00:02:02,450 --> 00:02:04,870
那么呢，我在表里面也叫什么名字

49
00:02:04,870 --> 00:02:07,430
不需要你给我驼峰转世型

50
00:02:07,430 --> 00:02:08,650
不需要做这一步了

51
00:02:08,650 --> 00:02:10,430
再比方说 name reacer 

52
00:02:10,430 --> 00:02:13,500
就是我遇见这样的列名呢

53
00:02:13,500 --> 00:02:16,860
我要把它替换成这样的形式啊

54
00:02:16,860 --> 00:02:18,345
这就很个性化了

55
00:02:18,345 --> 00:02:20,000
然后在这个 CONFIG 里面

56
00:02:20,000 --> 00:02:21,620
除了这个命名策略之外

57
00:02:21,620 --> 00:02:23,860
还可以指定一个 logo 

58
00:02:23,860 --> 00:02:28,000
而这个 log 就是我在上面指定的这样一个

59
00:02:29,220 --> 00:02:30,840
就是说 GURM 啊

60
00:02:30,840 --> 00:02:33,340
它内部会默认的打一些日志

61
00:02:33,340 --> 00:02:35,580
那么这些日志你应该怎么控制呢

62
00:02:35,580 --> 00:02:38,230
哎，通过这个 logo 点 new ，注意啊

63
00:02:38,230 --> 00:02:43,685
这个 logo 它是从 GOM 下面出来的 log new 

64
00:02:43,685 --> 00:02:48,870
然后这边呢，是搞一个购员标准库的 log 

65
00:02:48,870 --> 00:02:51,257
它来自于标准库的 log 嘛

66
00:02:51,257 --> 00:02:52,390
log 点 new 啊

67
00:02:52,390 --> 00:02:54,890
这边是传递一个输出吧

68
00:02:54,890 --> 00:02:56,610
writer ，你打算把日志呢

69
00:02:56,610 --> 00:02:57,750
输出到什么地方去

70
00:02:57,750 --> 00:03:01,440
这边的话，我是通过 open file 打开一个普通文件啊

71
00:03:01,440 --> 00:03:02,440
就把这个日志呢

72
00:03:02,440 --> 00:03:06,322
输出到我的这个文件里面去

73
00:03:06,322 --> 00:03:07,030
当然了

74
00:03:07,030 --> 00:03:09,290
你也可以输出到你的标准输出点去

75
00:03:09,290 --> 00:03:11,425
输出到你的控制台里面去

76
00:03:11,425 --> 00:03:14,810
你把这个 local file 替换成 OS 点

77
00:03:14,810 --> 00:03:17,722
s t t out 就可以了

78
00:03:17,722 --> 00:03:20,520
这边是指定每一条日志的分隔符啊

79
00:03:20,520 --> 00:03:23,032
以斜杠 R 、斜杠 N 来作为分隔

80
00:03:23,032 --> 00:03:26,110
然后他指定这个时间相关的格式啊

81
00:03:26,110 --> 00:03:28,970
这里面是包含了 data 和 time 

82
00:03:28,970 --> 00:03:31,265
然后年月、日、时、分秒都有

83
00:03:31,265 --> 00:03:34,170
甚至里面还有一些更精细的配置啊

84
00:03:34,170 --> 00:03:35,290
跟日相关的

85
00:03:35,290 --> 00:03:38,010
这边是指定那个 slow thrash hold 

86
00:03:38,010 --> 00:03:39,780
就是慢查询域值嘛

87
00:03:39,780 --> 00:03:41,390
这边指定是500 ms 

88
00:03:41,390 --> 00:03:43,940
就是说将来所有的 SQL 语句

89
00:03:43,940 --> 00:03:45,600
不管是增删改查啊

90
00:03:45,600 --> 00:03:46,800
只要任何一条语句

91
00:03:46,800 --> 00:03:49,380
它的执行时间超过了500 ms 

92
00:03:49,380 --> 00:03:51,590
那么呢，就是一条慢查询

93
00:03:51,590 --> 00:03:55,270
那慢查询它会打一条专门的慢查询的

94
00:03:55,270 --> 00:03:56,390
告警日志啊

95
00:03:56,390 --> 00:03:58,650
这边是指定日志的这个级别

96
00:03:58,650 --> 00:04:01,730
就是有什么 debug 、 info 、 WERROR 对吧

97
00:04:01,730 --> 00:04:04,795
这边是指定最低级别的为 info 

98
00:04:04,795 --> 00:04:05,510
注意啊

99
00:04:05,510 --> 00:04:08,510
我们所有的这些机制相关配置

100
00:04:08,510 --> 00:04:12,625
都是仅仅针对 GOM 他自己打的日志

101
00:04:12,625 --> 00:04:15,415
还有一个参数化的 query 

102
00:04:15,415 --> 00:04:16,610
这个是什么意思呢

103
00:04:16,610 --> 00:04:17,570
待会儿我们演示一下

104
00:04:17,570 --> 00:04:20,450
就是你在代码里面那个具体的参数

105
00:04:20,450 --> 00:04:22,627
它会有问号来进行占位

106
00:04:22,627 --> 00:04:25,660
而不展示真实的那个参数值

107
00:04:25,660 --> 00:04:27,940
color f ，我就日日是否带颜色吗

108
00:04:27,940 --> 00:04:29,300
我们把颜色给他禁掉啊

109
00:04:29,300 --> 00:04:31,322
这样的话效率会会更高一些

110
00:04:31,322 --> 00:04:34,570
好，所以这边定义好这样一个 logo 之后的话

111
00:04:34,570 --> 00:04:36,350
我们在 open 的时候呢

112
00:04:36,350 --> 00:04:39,330
这个 CONFIG 里面就可以来指定啊

113
00:04:39,330 --> 00:04:42,007
使用我刚刚自定义的这个 log 

114
00:04:42,007 --> 00:04:44,340
我们先来执行一个操作啊

115
00:04:44,340 --> 00:04:46,220
先看看日据长成什么样子

116
00:04:46,220 --> 00:04:48,632
还是跑这个 create 插入

117
00:04:48,632 --> 00:04:49,870
好，你看啊

118
00:04:49,870 --> 00:04:52,650
我们刚才是执行了一个插入语句嘛

119
00:04:52,650 --> 00:04:54,770
那么他会把这个语句呢

120
00:04:54,770 --> 00:04:57,505
完整的打到日志里面去

121
00:04:57,505 --> 00:05:02,230
我们在代码里面只是一个简单的 dB 点 create 

122
00:05:02,230 --> 00:05:04,430
那背后使用的 SQL 语句是什么呢

123
00:05:04,430 --> 00:05:07,727
诶，它可以给你打到日志里面去

124
00:05:07,727 --> 00:05:09,300
还说 rose 等于一

125
00:05:09,300 --> 00:05:11,180
表示是这一行语句呢

126
00:05:11,180 --> 00:05:12,440
它影响了一行

127
00:05:12,440 --> 00:05:14,190
因为只插入了一行记录嘛

128
00:05:14,190 --> 00:05:17,422
然后整体耗时呢，是21 ms 

129
00:05:17,422 --> 00:05:19,540
那比方说我们稍微改一改啊

130
00:05:19,540 --> 00:05:21,800
我们把这个参数化

131
00:05:21,800 --> 00:05:24,287
这个改成 true 来试一试

132
00:05:24,287 --> 00:05:27,410
然后把这个超时呢，改成10 ms 

133
00:05:27,410 --> 00:05:29,187
就算超时

134
00:05:29,187 --> 00:05:30,490
保存一下

135
00:05:30,490 --> 00:05:33,740
我们再来运行一下这个单侧

136
00:05:33,740 --> 00:05:35,320
看一下日志

137
00:05:35,320 --> 00:05:35,820
好

138
00:05:35,820 --> 00:05:36,500
这边的话

139
00:05:36,500 --> 00:05:37,980
他就把那个具体值

140
00:05:37,980 --> 00:05:40,340
全部使用问号来进行代替了

141
00:05:40,340 --> 00:05:40,700
对吧

142
00:05:40,700 --> 00:05:44,675
这个就是我们的这个 parameters 值这一项在起作用

143
00:05:44,675 --> 00:05:48,565
然后呢，它这个耗时是20 ms 

144
00:05:48,565 --> 00:05:50,380
那20 ms 已经超过了

145
00:05:50,380 --> 00:05:51,720
我们设置的这个阈值

146
00:05:51,720 --> 00:05:52,260
10 ms 

147
00:05:52,260 --> 00:05:53,920
它是一个慢查询

148
00:05:53,920 --> 00:05:56,020
但我们这里所说的慢查询

149
00:05:56,020 --> 00:05:57,860
它不一定真的是查询

150
00:05:57,860 --> 00:06:01,640
它也可能是插入、删除或者修改啊

151
00:06:01,640 --> 00:06:04,937
总之吧，慢查询关键在慢这个字

152
00:06:04,937 --> 00:06:06,190
那么已经很慢了

153
00:06:06,190 --> 00:06:07,150
很慢的话呢

154
00:06:07,150 --> 00:06:09,650
它在日志里面会专门告诉你，诶

155
00:06:09,650 --> 00:06:12,050
他是一个什么 slow circle 啊

156
00:06:12,050 --> 00:06:14,017
已经超过了10 ms 了

157
00:06:14,017 --> 00:06:17,887
所以这项功能对于 dB 来说是非常有帮助的

158
00:06:17,887 --> 00:06:21,230
第一遍只需要去日志里面搜一下这个 slow circle 

159
00:06:21,230 --> 00:06:23,810
就可以把所有的曼查询全部给逮出来

160
00:06:23,810 --> 00:06:25,990
然后呢，去找每一个算账

161
00:06:25,990 --> 00:06:28,030
而且使用问号作为账二符

162
00:06:28,030 --> 00:06:30,060
也是有一定实际用处的

163
00:06:30,060 --> 00:06:32,080
可能这个真实的参数值啊

164
00:06:32,080 --> 00:06:33,440
包含一些敏感信息

165
00:06:33,440 --> 00:06:36,640
我不希望把这个数据全部暴露在日里面，对吧

166
00:06:36,640 --> 00:06:38,030
你可以问号代替

167
00:06:38,030 --> 00:06:40,890
而且也可能这个真实值呢，会很长

168
00:06:40,890 --> 00:06:43,360
导致你的整个日文件很大

169
00:06:43,360 --> 00:06:44,710
对于 DB 来说

170
00:06:44,710 --> 00:06:47,110
他可能并不关心你具体的值是什么

171
00:06:47,110 --> 00:06:48,490
它可能只关心，诶

172
00:06:48,490 --> 00:06:50,290
你这个 SQL 语句

173
00:06:50,290 --> 00:06:53,890
整体的这个架构写法是什么样子的

174
00:06:53,890 --> 00:06:57,150
你这个写法有没有用到索引啊

175
00:06:57,150 --> 00:06:59,440
它会不会造成马拉询

176
00:06:59,440 --> 00:07:00,960
这个是 DAB 比较关心的

177
00:07:00,960 --> 00:07:03,327
他可能也不关心这个具体值

178
00:07:03,327 --> 00:07:06,870
再来看这个 configure 除了自定义预计之外

179
00:07:06,870 --> 00:07:08,990
还有一个什么转 run 

180
00:07:08,990 --> 00:07:11,170
就是干跑吧就不跑啊

181
00:07:11,170 --> 00:07:14,950
实际上就是说我只把对应的 SQL 语句打出来

182
00:07:14,950 --> 00:07:17,990
但是呢，我并不会真正的去执行这个语句

183
00:07:17,990 --> 00:07:20,440
比如说我把这一项改成 true 

184
00:07:20,440 --> 00:07:22,580
然后呢，再来运行一次

185
00:07:22,580 --> 00:07:25,430
好，你看上一次是真正执行嘛

186
00:07:25,430 --> 00:07:27,310
他说这个 id 是51

187
00:07:27,310 --> 00:07:28,090
而这次呢

188
00:07:28,090 --> 00:07:29,680
我是干跑啊

189
00:07:29,680 --> 00:07:30,380
拽一拽

190
00:07:30,380 --> 00:07:31,900
所以呢，你看 id 是零

191
00:07:31,900 --> 00:07:34,857
就说他并没有真正的去执行插入

192
00:07:34,857 --> 00:07:36,330
那么在日志里面

193
00:07:36,330 --> 00:07:39,510
依然是能够把这个 SQL 语句给打出来

194
00:07:39,510 --> 00:07:41,497
但是我们看一下库里面

195
00:07:41,497 --> 00:07:43,400
它最大的这个 id 呢

196
00:07:43,400 --> 00:07:44,520
还是51

197
00:07:44,520 --> 00:07:46,470
用最后一次定格插入

198
00:07:46,470 --> 00:07:49,620
再来说这个自动的 ping 

199
00:07:49,620 --> 00:07:50,690
那默认情况下

200
00:07:50,690 --> 00:07:53,110
当我们去执行完这个 open 之后

201
00:07:53,110 --> 00:07:54,530
连接串联好之后的话

202
00:07:54,530 --> 00:07:56,330
它会自动的去 ping 一下啊

203
00:07:56,330 --> 00:07:59,180
跟那个数据库进行一次简短的通信

204
00:07:59,180 --> 00:08:00,720
来确保这个连接呢

205
00:08:00,720 --> 00:08:02,480
已经是建立好的

206
00:08:02,480 --> 00:08:03,360
没有任何问题

207
00:08:03,360 --> 00:08:05,090
后面的话可以直接用

208
00:08:05,090 --> 00:08:09,020
你像我们之前在学那个 REDIS 时啊

209
00:08:09,020 --> 00:08:10,460
REDIS 你命好之后

210
00:08:10,460 --> 00:08:13,350
你还得显示的去调一下那个 pin 

211
00:08:13,350 --> 00:08:15,650
而这里面呢，我们由于已经默认的

212
00:08:15,650 --> 00:08:16,870
已经自动拼过了

213
00:08:16,870 --> 00:08:18,330
所以的话你 open 之后啊

214
00:08:18,330 --> 00:08:20,360
就不需要再去调那个 ping 了

215
00:08:20,360 --> 00:08:25,372
以上都是在这个 open 里面跟 configure 相关的配置

216
00:08:25,372 --> 00:08:26,580
然后再来看一下

217
00:08:26,580 --> 00:08:28,120
如果这个 dB 啊

218
00:08:28,120 --> 00:08:29,900
已经连接建立好了

219
00:08:29,900 --> 00:08:31,020
建立好之后的话

220
00:08:31,020 --> 00:08:35,177
还可以控制一些跟连接池相关的参数

221
00:08:35,177 --> 00:08:37,909
那本质上咱们这个连接它不是一个连接

222
00:08:37,909 --> 00:08:40,310
它实际上是一个连接池啊

223
00:08:40,310 --> 00:08:42,970
通过 DB 调这个 DB 方法

224
00:08:42,970 --> 00:08:45,280
然后呢，基于这个 SQL dB 啊

225
00:08:45,280 --> 00:08:48,680
我们可以设置一些跟连接池相关的参数

226
00:08:48,680 --> 00:08:50,290
什么是连接池呢

227
00:08:50,290 --> 00:08:52,460
就是在并发情况下啊

228
00:08:52,460 --> 00:08:54,850
可能需要多个链接

229
00:08:54,850 --> 00:08:58,030
因为每个请求都需要单独的占一个链接嘛

230
00:08:58,030 --> 00:09:00,190
假如说同时开了好多请求

231
00:09:00,190 --> 00:09:03,030
那么你就需要同时开好多个连接

232
00:09:03,030 --> 00:09:03,670
所以呢

233
00:09:03,670 --> 00:09:07,970
不如说我一开始我就先创建好很多链接啊

234
00:09:07,970 --> 00:09:09,790
将来你需要用了

235
00:09:09,790 --> 00:09:12,260
我就把连接呢分配给你

236
00:09:12,260 --> 00:09:14,040
所以是一个连接池吗

237
00:09:14,040 --> 00:09:15,350
那么既然是池子

238
00:09:15,350 --> 00:09:17,170
就涉及到很多控制参数啊

239
00:09:17,170 --> 00:09:20,210
比如说 max open collections 对吧

240
00:09:20,210 --> 00:09:23,090
你这个池子里面你最多打算开几个链接

241
00:09:23,090 --> 00:09:24,200
也不能太多

242
00:09:24,200 --> 00:09:27,630
这里面指定啊，最多只开辟100个连接

243
00:09:27,630 --> 00:09:30,270
但它并不是一开始就开辟这么连接啊

244
00:09:30,270 --> 00:09:32,560
一开始的话，开辟连接数是有限的

245
00:09:32,560 --> 00:09:35,190
这边指定 max idol connections 啊

246
00:09:35,190 --> 00:09:36,850
最多的空闲连接数

247
00:09:36,850 --> 00:09:39,650
实际上也是一开始初始时创建的

248
00:09:39,650 --> 00:09:41,457
连接数就是十个

249
00:09:41,457 --> 00:09:42,960
因为一开始的时

250
00:09:42,960 --> 00:09:44,600
这个还没有需求嘛

251
00:09:44,600 --> 00:09:46,540
在没有任何需求的情况之下

252
00:09:46,540 --> 00:09:49,120
我也会去开辟十个链接

253
00:09:49,120 --> 00:09:52,000
那假如说瞬间来了20个请求

254
00:09:52,000 --> 00:09:53,290
那我需要20个链接

255
00:09:53,290 --> 00:09:56,210
它就会临时的再去多开辟十个链接

256
00:09:56,210 --> 00:09:58,490
那假如说瞬间有100个请求呢

257
00:09:58,490 --> 00:10:00,572
那自然就需要100个链接了

258
00:10:00,572 --> 00:10:03,420
那如果瞬间有110个请求呢

259
00:10:03,420 --> 00:10:05,260
他也是只开辟100个连接

260
00:10:05,260 --> 00:10:08,675
因为这边设置了上限就是100个连接嘛

261
00:10:08,675 --> 00:10:11,650
那假如说这样的一个请求高峰期过去了

262
00:10:11,650 --> 00:10:13,090
请求做完了

263
00:10:13,090 --> 00:10:14,340
他就要把连接呢

264
00:10:14,340 --> 00:10:16,165
归还到池子里面去

265
00:10:16,165 --> 00:10:17,900
这个时候发现池子里面

266
00:10:17,900 --> 00:10:20,140
大部分连接都是空闲的

267
00:10:20,140 --> 00:10:21,180
没人使用

268
00:10:21,180 --> 00:10:25,207
那么呢，他就要把那些空闲连接把它给关闭掉

269
00:10:25,207 --> 00:10:28,470
但也并不是说把所有空闲连接全部给关闭掉

270
00:10:28,470 --> 00:10:30,780
它还是会保留什么

271
00:10:30,780 --> 00:10:33,540
最多会保留十个空闲连接

272
00:10:33,540 --> 00:10:36,507
以备将来的不时之需嘛

273
00:10:36,507 --> 00:10:39,622
这边还有一个 max live time 

274
00:10:39,622 --> 00:10:42,760
这点是说我们的 MYSQL 本身呢

275
00:10:42,760 --> 00:10:44,680
他会去检查，诶

276
00:10:44,680 --> 00:10:46,740
客户端给我开了一个链接

277
00:10:46,740 --> 00:10:49,105
那这个连接有没有在用

278
00:10:49,105 --> 00:10:52,000
MYSQL 本身有一个设置啊

279
00:10:52,000 --> 00:10:54,690
假如说是六小时吧

280
00:10:54,690 --> 00:10:57,560
MYSQL 发现这个连接打开之后

281
00:10:57,560 --> 00:11:00,010
六个小时之内一直没有使用

282
00:11:00,010 --> 00:11:01,870
那么 MYSQL 作为服务端

283
00:11:01,870 --> 00:11:05,200
它会主动的把这个连接呢，给关闭掉

284
00:11:05,200 --> 00:11:07,390
所以呢，我们的 go 代码

285
00:11:07,390 --> 00:11:08,770
我们相当于是客户端嘛

286
00:11:08,770 --> 00:11:09,850
啊，相对于数据库来说

287
00:11:09,850 --> 00:11:10,680
我们的客户端

288
00:11:10,680 --> 00:11:11,740
我们作为客户端

289
00:11:11,740 --> 00:11:13,080
为了避免这种尴尬

290
00:11:13,080 --> 00:11:15,620
就为了避免服务端主动把链接关闭了

291
00:11:15,620 --> 00:11:16,630
我们怎么办

292
00:11:16,630 --> 00:11:20,250
我们呢，就主动的去关闭连接啊

293
00:11:20,250 --> 00:11:22,060
你是六个小时

294
00:11:22,060 --> 00:11:23,560
那我这边比你更短一点

295
00:11:23,560 --> 00:11:24,835
我设置四个小时

296
00:11:24,835 --> 00:11:26,140
我四个小时之后

297
00:11:26,140 --> 00:11:27,460
我主动去检查一下

298
00:11:27,460 --> 00:11:30,920
发现这个链接已经连续四个小时没人用了

299
00:11:30,920 --> 00:11:32,527
那我去把它关闭掉

300
00:11:32,527 --> 00:11:34,050
那我关闭了之后的话

301
00:11:34,050 --> 00:11:36,767
我会立刻呢再创建一个

302
00:11:36,767 --> 00:11:39,460
或者是等到下一次有实际需求时

303
00:11:39,460 --> 00:11:41,350
我再去创建一个全新链接

304
00:11:41,350 --> 00:11:42,580
这是一种方案了

305
00:11:42,580 --> 00:11:44,220
那其实也有另一种方案

306
00:11:44,220 --> 00:11:47,520
就是我也不需要去关闭

307
00:11:47,520 --> 00:11:49,790
然后重新打开这种方式来搞

308
00:11:49,790 --> 00:11:52,620
我就定期的去发送一个 ping 

309
00:11:52,620 --> 00:11:53,800
因为发送 ping 的话

310
00:11:53,800 --> 00:11:55,965
也相当于是我在用这个链接吗

311
00:11:55,965 --> 00:11:56,820
那这样的话

312
00:11:56,820 --> 00:12:00,137
服务端也不会强制把这个连接呢给我关闭掉

313
00:12:00,137 --> 00:12:01,190
但这种方式的话

314
00:12:01,190 --> 00:12:04,830
就需要我们在代码里面起一个后台携程啊

315
00:12:04,830 --> 00:12:06,950
定期的去执行拼操作
