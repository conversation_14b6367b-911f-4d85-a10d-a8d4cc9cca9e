1
00:00:00,180 --> 00:00:01,880
为什么要学习爬虫呢

2
00:00:01,880 --> 00:00:03,340
首先从技术上讲

3
00:00:03,340 --> 00:00:06,600
爬虫它主要应用的是 web 开发相关的技能

4
00:00:06,600 --> 00:00:08,980
所以 pg 爬虫主要是对 web 开发

5
00:00:08,980 --> 00:00:10,842
进行一个技能的巩固

6
00:00:10,842 --> 00:00:11,770
第二呢

7
00:00:11,770 --> 00:00:13,090
我们大部分程序员

8
00:00:13,090 --> 00:00:15,190
都不是专业的爬虫工程师

9
00:00:15,190 --> 00:00:16,870
但是掌握了爬虫技能

10
00:00:16,870 --> 00:00:19,400
可以帮助我们提升一些工作效率

11
00:00:19,400 --> 00:00:22,110
第三，我们学习了爬虫的工作原理

12
00:00:22,110 --> 00:00:24,930
才能够知道如何去反爬虫

13
00:00:24,930 --> 00:00:27,930
我们这门课主要是将两类爬虫框架

14
00:00:27,930 --> 00:00:28,970
一类是 KY 

15
00:00:28,970 --> 00:00:30,270
一类是无头浏览器

16
00:00:30,270 --> 00:00:32,369
那么最终呢，我们会用 KY 框架

17
00:00:32,369 --> 00:00:35,945
带大家去爬取一些视频的基本信息

18
00:00:35,945 --> 00:00:38,450
有消防站为了防止机器爬虫

19
00:00:38,450 --> 00:00:40,550
他们要求必须由人工进行一些

20
00:00:40,550 --> 00:00:42,480
鼠标的点击或滑动操作

21
00:00:42,480 --> 00:00:44,060
那么我们使用无头浏览器

22
00:00:44,060 --> 00:00:47,440
教大家如何去进行一些表单的填写

23
00:00:47,440 --> 00:00:50,570
这样的话可以完成登录、注册等功能

24
00:00:50,570 --> 00:00:53,310
然后通过模拟鼠标的滚动操作

25
00:00:53,310 --> 00:00:56,302
可以爬取百度上面的图片

26
00:00:56,302 --> 00:00:57,800
我还可以利用爬虫技术

27
00:00:57,800 --> 00:00:59,720
帮我去完成一些后台操作

28
00:00:59,720 --> 00:01:01,400
比方说对所有的评论

29
00:01:01,400 --> 00:01:03,400
进行一些敏感词的过滤

30
00:01:03,400 --> 00:01:05,940
然后呢自动的进行一个举报

31
00:01:05,940 --> 00:01:09,220
总之我觉得每个程序员都有必要学习一下

32
00:01:09,220 --> 00:01:10,040
爬虫技巧
