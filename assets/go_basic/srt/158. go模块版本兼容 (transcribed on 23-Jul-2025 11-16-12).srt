1
00:00:00,000 --> 00:00:03,000
这里是GoModule第六次课

2
00:00:03,000 --> 00:00:05,000
我们讲一讲一个项目里面

3
00:00:05,000 --> 00:00:09,000
如果依赖同一个Module的Bot版本号该如何处理

4
00:00:09,000 --> 00:00:11,000
今天这次课跟上一次课

5
00:00:11,000 --> 00:00:12,000
鱼翼化版本规范

6
00:00:12,000 --> 00:00:15,000
命运期限关键先听一下

7
00:00:15,000 --> 00:00:18,000
我们说GoModule它的版本号

8
00:00:18,000 --> 00:00:20,000
是一个小写V开头

9
00:00:20,000 --> 00:00:23,000
后面跟一个鱼翼化的版本号

10
00:00:23,000 --> 00:00:25,000
通常情况下

11
00:00:25,000 --> 00:00:28,000
如果我们是使用的Gate仓库的话

12
00:00:28,000 --> 00:00:31,000
那么我们在这个GoModule里面

13
00:00:31,000 --> 00:00:32,000
在这个require里面

14
00:00:32,000 --> 00:00:34,000
你不是会依赖各种第三号库吗

15
00:00:34,000 --> 00:00:37,000
这个库后面会跟一个版本号

16
00:00:37,000 --> 00:00:38,000
你看V1.3对吧

17
00:00:38,000 --> 00:00:42,000
刚好符合我们刚才讲的这个Module的版本规范

18
00:00:42,000 --> 00:00:46,000
这个V1.3它其实对应的就是GitHub上面

19
00:00:46,000 --> 00:00:48,000
比方这个GIN对吧

20
00:00:48,000 --> 00:00:50,000
那GIN它在GitHub上面

21
00:00:50,000 --> 00:00:51,000
它有一个Tags对吧

22
00:00:51,000 --> 00:00:54,000
有很多个历史过往的Tags

23
00:00:54,000 --> 00:00:57,000
那Tags里面你看名称V1.2.1

24
00:00:57,000 --> 00:00:59,000
就是这个V1.2.1

25
00:00:59,000 --> 00:01:03,000
也就是说所谓的Module的版本号

26
00:01:03,000 --> 00:01:05,000
就是GitTags的名称

27
00:01:05,000 --> 00:01:07,000
OK

28
00:01:07,000 --> 00:01:09,000
那大家看第二种情况比较特殊

29
00:01:09,000 --> 00:01:10,000
这个是吧

30
00:01:10,000 --> 00:01:12,000
这个Module2.2怎么这么长

31
00:01:12,000 --> 00:01:14,000
其实是英文

32
00:01:14,000 --> 00:01:16,000
你看它是这个ModernGo的

33
00:01:16,000 --> 00:01:17,000
好ModernGo

34
00:01:17,000 --> 00:01:18,000
ModernGo

35
00:01:18,000 --> 00:01:20,000
它在GitHub上面

36
00:01:20,000 --> 00:01:22,000
它的Tags你看Tags1.0.3

37
00:01:22,000 --> 00:01:24,000
虽然说1.0.3

38
00:01:24,000 --> 00:01:27,000
是一个很规范的语义化版本

39
00:01:27,000 --> 00:01:30,000
但是它不是一个规范的GoModule版本

40
00:01:30,000 --> 00:01:34,000
因为GoModule版本需要有一个V作为前缀

41
00:01:34,000 --> 00:01:35,000
它不是

42
00:01:35,000 --> 00:01:38,000
好如果不是规范的GoModule版本的话呢

43
00:01:38,000 --> 00:01:43,000
那么在这个地方实际上生存的是一个V版本号

44
00:01:43,000 --> 00:01:45,000
V版本号

45
00:01:45,000 --> 00:01:46,000
那这个V版本号呢

46
00:01:46,000 --> 00:01:47,000
它最后这一份

47
00:01:47,000 --> 00:01:49,000
最后这个字符传是什么

48
00:01:49,000 --> 00:01:54,000
实际上就是对应的GitCommit的那个字符传前缀

49
00:01:54,000 --> 00:01:55,000
你看

50
00:01:55,000 --> 00:01:58,000
它这边也有这个GitCommit对应的那个字符传前缀

51
00:01:58,000 --> 00:02:00,000
是BACD9C

52
00:02:00,000 --> 00:02:02,000
这边也是BACD9C

53
00:02:02,000 --> 00:02:03,000
对吧

54
00:02:03,000 --> 00:02:06,000
大家对这个GitCommit如果不是很清楚的话

55
00:02:06,000 --> 00:02:07,000
给大家

56
00:02:07,000 --> 00:02:08,000
演示一下

57
00:02:08,000 --> 00:02:10,000
反正说我自己开发一个项目

58
00:02:10,000 --> 00:02:11,000
通过这个GitLog

59
00:02:11,000 --> 00:02:13,000
可以查看以往所有的提交

60
00:02:13,000 --> 00:02:14,000
比方说

61
00:02:14,000 --> 00:02:15,000
我有提交过一次代码

62
00:02:15,000 --> 00:02:16,000
那一次提交

63
00:02:16,000 --> 00:02:20,000
它对应的这个Commit的这个字符传是这么多

64
00:02:20,000 --> 00:02:21,000
我们一般的话呢

65
00:02:21,000 --> 00:02:22,000
会拿前缀

66
00:02:22,000 --> 00:02:23,000
是吧

67
00:02:23,000 --> 00:02:24,000
来唯一标识这一次提交

68
00:02:24,000 --> 00:02:25,000
所以说

69
00:02:25,000 --> 00:02:27,000
它就是以某一次代码提交

70
00:02:27,000 --> 00:02:29,000
作为这样一个二号

71
00:02:29,000 --> 00:02:33,000
中间这个字符传式继承就是代码提交的时间

72
00:02:33,000 --> 00:02:34,000
OK

73
00:02:34,000 --> 00:02:37,000
那还有情况也会声称为二号

74
00:02:37,000 --> 00:02:40,000
就是你不能够保证所有的

75
00:02:40,000 --> 00:02:42,000
给仓库

76
00:02:42,000 --> 00:02:43,000
它都有TAX

77
00:02:43,000 --> 00:02:44,000
对吧

78
00:02:44,000 --> 00:02:45,000
有些项目

79
00:02:45,000 --> 00:02:46,000
人家就没有TAX

80
00:02:46,000 --> 00:02:48,000
就是直接在一个分支上

81
00:02:48,000 --> 00:02:49,000
MASTER分支上

82
00:02:49,000 --> 00:02:50,000
不断地提交

83
00:02:50,000 --> 00:02:52,000
如果没有TAX的话

84
00:02:52,000 --> 00:02:54,000
那么也会生成这样一个

85
00:02:54,000 --> 00:02:55,000
为版本

86
00:02:55,000 --> 00:02:56,000
就是大家看到的

87
00:02:56,000 --> 00:02:57,000
类似这样的一个字符传

88
00:02:57,000 --> 00:02:58,000
OK

89
00:02:59,000 --> 00:03:00,000
然后我们再来说

90
00:03:00,000 --> 00:03:01,000
一个项目里面

91
00:03:01,000 --> 00:03:04,000
如果依赖同一个Module的Bot二号

92
00:03:04,000 --> 00:03:05,000
该如何处理

93
00:03:05,000 --> 00:03:06,000
比方说

94
00:03:06,000 --> 00:03:09,000
这个Module是我们自己开发的项目

95
00:03:09,000 --> 00:03:10,000
项目里面

96
00:03:10,000 --> 00:03:12,000
你依赖了Module A和B

97
00:03:12,000 --> 00:03:13,000
这两个Module

98
00:03:13,000 --> 00:03:14,000
A呢

99
00:03:14,000 --> 00:03:15,000
依赖XX哈希

100
00:03:15,000 --> 00:03:16,000
V1

101
00:03:16,000 --> 00:03:17,000
V1

102
00:03:17,000 --> 00:03:18,000
是主版本号

103
00:03:18,000 --> 00:03:19,000
我上一节课讲过

104
00:03:19,000 --> 00:03:21,000
那这个Module B呢

105
00:03:21,000 --> 00:03:22,000
它也依赖XX哈希

106
00:03:22,000 --> 00:03:24,000
只不过它依赖的是2

107
00:03:24,000 --> 00:03:25,000
这个主版本号

108
00:03:26,000 --> 00:03:27,000
那这样的话呢

109
00:03:27,000 --> 00:03:29,000
你如何去做兼容

110
00:03:29,000 --> 00:03:30,000
对吧

111
00:03:30,000 --> 00:03:31,000
在这个构点Module里面

112
00:03:31,000 --> 00:03:32,000
在构点Module里面

113
00:03:32,000 --> 00:03:33,000
在构点Module里面

114
00:03:33,000 --> 00:03:35,000
需要去同时依赖XX哈希

115
00:03:35,000 --> 00:03:37,000
V1和V2

116
00:03:37,000 --> 00:03:39,000
它们是这个Indirect

117
00:03:39,000 --> 00:03:40,000
Indirect

118
00:03:40,000 --> 00:03:41,000
表示是间接依赖

119
00:03:41,000 --> 00:03:43,000
就是我们的这个Module

120
00:03:43,000 --> 00:03:45,000
它直接依赖A跟B

121
00:03:45,000 --> 00:03:48,000
但是它间接依赖了XX哈希

122
00:03:48,000 --> 00:03:49,000
对吧

123
00:03:49,000 --> 00:03:50,000
好

124
00:03:50,000 --> 00:03:51,000
我们还是要把这两个版本

125
00:03:51,000 --> 00:03:52,000
V跟V2

126
00:03:52,000 --> 00:03:54,000
都写在这个require里面

127
00:03:54,000 --> 00:03:57,000
然后我们的这个构代码这边

128
00:03:57,000 --> 00:03:58,000
比方构代码这边

129
00:03:58,000 --> 00:04:00,000
你可能需要同时

130
00:04:00,000 --> 00:04:02,000
有的地方可能需要依赖V

131
00:04:02,000 --> 00:04:03,000
有的地方依赖V2

132
00:04:03,000 --> 00:04:04,000
没关系

133
00:04:04,000 --> 00:04:05,000
可以兼容

134
00:04:05,000 --> 00:04:08,000
你把这个V对应的这个Module

135
00:04:08,000 --> 00:04:09,000
名称Import进来

136
00:04:09,000 --> 00:04:11,000
然后把这个V2对应的Module

137
00:04:11,000 --> 00:04:13,000
名称也Import进来

138
00:04:13,000 --> 00:04:14,000
大家注意看

139
00:04:14,000 --> 00:04:15,000
这个V2这个Module名称

140
00:04:15,000 --> 00:04:18,000
它实际上是有一个什么斜杠V2

141
00:04:18,000 --> 00:04:19,000
有这样的后坠

142
00:04:19,000 --> 00:04:20,000
依赖进来

143
00:04:20,000 --> 00:04:21,000
你在代码里面

144
00:04:21,000 --> 00:04:22,000
如果依赖V的话

145
00:04:22,000 --> 00:04:23,000
那直接是XX哈希

146
00:04:23,000 --> 00:04:24,000
XX哈希

147
00:04:24,000 --> 00:04:25,000
XX哈希

148
00:04:25,000 --> 00:04:26,000
对吧

149
00:04:26,000 --> 00:04:27,000
三六四君

150
00:04:27,000 --> 00:04:28,000
依赖V2的话

151
00:04:28,000 --> 00:04:30,000
那你对这个V2起一个边名

152
00:04:30,000 --> 00:04:31,000
随便起一个边名

153
00:04:31,000 --> 00:04:32,000
比方就哈希V2

154
00:04:32,000 --> 00:04:33,000
那你哈希V2

155
00:04:33,000 --> 00:04:34,000
后面是对那个函数

156
00:04:34,000 --> 00:04:35,000
是吧

157
00:04:35,000 --> 00:04:36,000
你是在构代码里面

158
00:04:36,000 --> 00:04:39,000
是可以兼容不同的版本号的

159
00:04:39,000 --> 00:04:40,000
那注意

160
00:04:40,000 --> 00:04:42,000
这个地方又出现个什么

161
00:04:42,000 --> 00:04:44,000
又出现个后坠V2

162
00:04:44,000 --> 00:04:45,000
那这什么意思

163
00:04:45,000 --> 00:04:47,000
你首先要清楚

164
00:04:47,000 --> 00:04:49,000
就是我们Import

165
00:04:49,000 --> 00:04:50,000
这里面写的这个字符上

166
00:04:50,000 --> 00:04:52,000
它就是Module的名称

167
00:04:52,000 --> 00:04:55,000
这个带V2后坠也是Module名称

168
00:04:55,000 --> 00:04:57,000
那同一个Module

169
00:04:57,000 --> 00:04:59,000
怎么会有两个Module名称呢

170
00:04:59,000 --> 00:05:01,000
这是可有的

171
00:05:01,000 --> 00:05:02,000
给大家看看

172
00:05:02,000 --> 00:05:03,000
好

173
00:05:03,000 --> 00:05:04,000
比方说是刚才那个XX哈希

174
00:05:04,000 --> 00:05:05,000
对吧

175
00:05:05,000 --> 00:05:06,000
XX哈希

176
00:05:06,000 --> 00:05:08,000
目前我们看它的最新版本

177
00:05:08,000 --> 00:05:10,000
我们点开这个GoldenMod

178
00:05:10,000 --> 00:05:12,000
看看它的模块名称叫什么

179
00:05:12,000 --> 00:05:14,000
它的模块名称就叫什么

180
00:05:14,000 --> 00:05:15,000
V2

181
00:05:15,000 --> 00:05:16,000
V2

182
00:05:16,000 --> 00:05:17,000
对吧

183
00:05:17,000 --> 00:05:18,000
就叫V2

184
00:05:18,000 --> 00:05:20,000
但是我们再打开同样的一个项目

185
00:05:20,000 --> 00:05:21,000
塔塔希

186
00:05:21,000 --> 00:05:23,000
我们切换到一个之前的一个版本

187
00:05:23,000 --> 00:05:24,000
Tags

188
00:05:24,000 --> 00:05:26,000
切换到这个V1版本

189
00:05:26,000 --> 00:05:27,000
V1

190
00:05:27,000 --> 00:05:29,000
好现在切换到V1了

191
00:05:29,000 --> 00:05:30,000
我们再看看V

192
00:05:30,000 --> 00:05:33,000
之前曾经历史上Module的时候

193
00:05:33,000 --> 00:05:35,000
它对你的GoldenModule里面

194
00:05:35,000 --> 00:05:37,000
它的那个Module名称叫什么

195
00:05:37,000 --> 00:05:39,000
这个时候你看它这个Module名称

196
00:05:39,000 --> 00:05:42,000
就没有那个V1或者V2厚坠

197
00:05:42,000 --> 00:05:43,000
没那个厚坠

198
00:05:43,000 --> 00:05:46,000
也就是说同样的一份

199
00:05:46,000 --> 00:05:47,000
Gtop上面代码

200
00:05:47,000 --> 00:05:49,000
由于历史版本原因

201
00:05:49,000 --> 00:05:51,000
它曾经有时候

202
00:05:51,000 --> 00:05:52,000
是不带V厚坠的

203
00:05:52,000 --> 00:05:54,000
有时候是带V厚坠的

204
00:05:54,000 --> 00:05:55,000
OK

205
00:05:55,000 --> 00:05:57,000
所以刚才我们在代码里面

206
00:05:57,000 --> 00:05:59,000
才看到说

207
00:05:59,000 --> 00:06:00,000
是吧

208
00:06:00,000 --> 00:06:01,000
有的地方是这样

209
00:06:01,000 --> 00:06:02,000
有的地方是这样

210
00:06:02,000 --> 00:06:03,000
OK

211
00:06:03,000 --> 00:06:05,000
那这个版本号呢

212
00:06:05,000 --> 00:06:06,000
再可以强调一下

213
00:06:06,000 --> 00:06:08,000
就是说只有是V2

214
00:06:08,000 --> 00:06:09,000
如果是更高的版本的话

215
00:06:09,000 --> 00:06:10,000
才需要带这个号坠

216
00:06:10,000 --> 00:06:11,000
如果是V1的话

217
00:06:11,000 --> 00:06:13,000
默认是就默认是V1嘛

218
00:06:13,000 --> 00:06:15,000
你不需要带这个号坠

219
00:06:15,000 --> 00:06:16,000
好

220
00:06:16,000 --> 00:06:18,000
然后既然说不同的这个版本

221
00:06:18,000 --> 00:06:19,000
它是有版本号的

222
00:06:19,000 --> 00:06:21,000
那么我们对应的GoGet

223
00:06:21,000 --> 00:06:22,000
和GoInstall

224
00:06:22,000 --> 00:06:25,000
都可以指定特定的版本号

225
00:06:25,000 --> 00:06:26,000
比方说

226
00:06:26,000 --> 00:06:27,000
我们这个GoGet

227
00:06:27,000 --> 00:06:28,000
对吧

228
00:06:28,000 --> 00:06:29,000
后面是Module名称

229
00:06:29,000 --> 00:06:30,000
后面这个at

230
00:06:30,000 --> 00:06:31,000
atV

231
00:06:31,000 --> 00:06:32,000
是吧

232
00:06:32,000 --> 00:06:33,000
指定这个号

233
00:06:33,000 --> 00:06:34,000
那通道理

234
00:06:34,000 --> 00:06:35,000
你可以把这个get

235
00:06:35,000 --> 00:06:36,000
安装install也是一样子

236
00:06:36,000 --> 00:06:37,000
对吧

237
00:06:37,000 --> 00:06:39,000
那么有个特定的版本

238
00:06:39,000 --> 00:06:40,000
我们使用GoGet-U

239
00:06:40,000 --> 00:06:41,000
U

240
00:06:41,000 --> 00:06:42,000
表示update

241
00:06:42,000 --> 00:06:43,000
对吧

242
00:06:43,000 --> 00:06:44,000
要更新吧

243
00:06:44,000 --> 00:06:45,000
但这个更新的话

244
00:06:45,000 --> 00:06:46,000
它并不会更新主版本号

245
00:06:46,000 --> 00:06:47,000
比方说

246
00:06:47,000 --> 00:06:48,000
你之前

247
00:06:48,000 --> 00:06:49,000
你下载的是V2

248
00:06:49,000 --> 00:06:51,000
你这个GoGet-U

249
00:06:51,000 --> 00:06:52,000
-U的话

250
00:06:52,000 --> 00:06:55,000
你顶多是更新到V2的那个最高版本

251
00:06:55,000 --> 00:06:57,000
因为它还有次版本号

252
00:06:57,000 --> 00:06:59,000
更新到V2的最高的那个次版本号

253
00:06:59,000 --> 00:07:01,000
但是它并不会自动更新到V3

254
00:07:01,000 --> 00:07:03,000
然后我们再来看一个特例

255
00:07:03,000 --> 00:07:05,000
比方说这个Redis这个库

256
00:07:05,000 --> 00:07:06,000
这个库

257
00:07:06,000 --> 00:07:09,000
我们看看它目前的这个GoGet-Module

258
00:07:09,000 --> 00:07:11,000
里面的Module名称叫什么

259
00:07:11,000 --> 00:07:13,000
打开GoGet-Module

260
00:07:15,000 --> 00:07:18,000
它目前这个Module名称叫什么什么V9

261
00:07:18,000 --> 00:07:20,000
它已经更新到V9这个版本了

262
00:07:20,000 --> 00:07:23,000
但是我们看一下它的历史版本

263
00:07:23,000 --> 00:07:25,000
我们回退过去

264
00:07:25,000 --> 00:07:27,000
我们切换了一个更早的版本

265
00:07:27,000 --> 00:07:29,000
找一个V6的版本

266
00:07:29,000 --> 00:07:30,000
好

267
00:07:30,000 --> 00:07:32,000
在这个V6版本下

268
00:07:32,000 --> 00:07:34,000
我们看看它的Module名称叫什么

269
00:07:34,000 --> 00:07:36,000
GoGet-Module

270
00:07:36,000 --> 00:07:38,000
按理来说

271
00:07:38,000 --> 00:07:39,000
你既然是V6

272
00:07:39,000 --> 00:07:40,000
对你的Module名称

273
00:07:40,000 --> 00:07:42,000
应该是有一个后缀V6

274
00:07:42,000 --> 00:07:43,000
但是它没有

275
00:07:43,000 --> 00:07:44,000
对吧

276
00:07:44,000 --> 00:07:46,000
它没有按照那个规范来

277
00:07:46,000 --> 00:07:47,000
为什么呢

278
00:07:47,000 --> 00:07:49,000
因为这个GoModule这个规范

279
00:07:49,000 --> 00:07:50,000
它是后来才出现的

280
00:07:50,000 --> 00:07:52,000
它并不是伴随着Go战胜DT

281
00:07:52,000 --> 00:07:54,000
或者Go战胜DT就有的

282
00:07:54,000 --> 00:07:56,000
所以在这个规范出现之前

283
00:07:56,000 --> 00:07:58,000
有很多第三方的库

284
00:07:58,000 --> 00:08:00,000
它并不知道有这个规范

285
00:08:00,000 --> 00:08:01,000
所以它们这个Module名称

286
00:08:01,000 --> 00:08:03,000
就没有后缀

287
00:08:03,000 --> 00:08:05,000
对于这种情况怎么办

288
00:08:05,000 --> 00:08:06,000
对于这种情况

289
00:08:06,000 --> 00:08:09,000
我们在依赖V6版本的时候

290
00:08:09,000 --> 00:08:10,000
我们看

291
00:08:10,000 --> 00:08:11,000
依赖GoRedis

292
00:08:11,000 --> 00:08:12,000
V6

293
00:08:12,000 --> 00:08:13,000
好

294
00:08:13,000 --> 00:08:14,000
这个V6版本

295
00:08:14,000 --> 00:08:17,000
它没有斜杠V6后缀

296
00:08:17,000 --> 00:08:18,000
对应的后缀

297
00:08:18,000 --> 00:08:19,000
就会有一个什么

298
00:08:19,000 --> 00:08:20,000
加

299
00:08:20,000 --> 00:08:21,000
Incompatible

300
00:08:21,000 --> 00:08:22,000
不兼容

301
00:08:22,000 --> 00:08:24,000
加这样一个标识就可以了

302
00:08:24,000 --> 00:08:26,000
我们看下面这两个又是一个离板

303
00:08:26,000 --> 00:08:27,000
你看

304
00:08:27,000 --> 00:08:28,000
它有个点V1

305
00:08:28,000 --> 00:08:29,000
点V3

306
00:08:29,000 --> 00:08:30,000
对吧

307
00:08:30,000 --> 00:08:31,000
我们刚才不是说

308
00:08:31,000 --> 00:08:32,000
这个V1版本

309
00:08:32,000 --> 00:08:34,000
它不需要加这个后缀吗

310
00:08:34,000 --> 00:08:36,000
因为V1是默认的版本

311
00:08:36,000 --> 00:08:38,000
只有V2或者更多高版本的话

312
00:08:38,000 --> 00:08:39,000
才需要加这个版本号

313
00:08:39,000 --> 00:08:41,000
那为什么这个V1它也加了呢

314
00:08:41,000 --> 00:08:43,000
而且这个前面也不是应该是斜线吗

315
00:08:43,000 --> 00:08:45,000
怎么它这个地方怎么是点呢

316
00:08:45,000 --> 00:08:46,000
对吧

317
00:08:46,000 --> 00:08:47,000
这里面就是一个特例

318
00:08:47,000 --> 00:08:48,000
就是一个特例

319
00:08:48,000 --> 00:08:49,000
就是说这种

320
00:08:49,000 --> 00:08:50,000
GoPKG点印

321
00:08:50,000 --> 00:08:52,000
GoPKG点印

322
00:08:52,000 --> 00:08:54,000
以这个开头的这个module

323
00:08:54,000 --> 00:08:55,000
能称的

324
00:08:55,000 --> 00:08:57,000
不管你是V1还是V2

325
00:08:57,000 --> 00:09:00,000
这个版本号统一都要带上

326
00:09:00,000 --> 00:09:01,000
而且前面是底儿

327
00:09:01,000 --> 00:09:02,000
不是血线

328
00:09:02,000 --> 00:09:03,000
OK

329
00:09:03,000 --> 00:09:05,000
那最后我们再说一下这个V0

330
00:09:05,000 --> 00:09:08,000
由于我们知道主版本号是0的话

331
00:09:08,000 --> 00:09:10,000
它是不受这个兼容性的约束的

332
00:09:10,000 --> 00:09:16,000
所以说我们在这个模块版本兼容整个解放里面

333
00:09:16,000 --> 00:09:18,000
压根就没有考虑V0这种情况

334
00:09:18,000 --> 00:09:21,000
因为你这种情况确实是无法兼顾

