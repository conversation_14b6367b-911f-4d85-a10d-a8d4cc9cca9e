1
00:00:00,520 --> 00:00:02,800
来看一些跟切片相关的

2
00:00:02,800 --> 00:00:04,560
这个标准库自带的函数

3
00:00:04,560 --> 00:00:06,000
因为是标准库

4
00:00:06,000 --> 00:00:08,060
所以的话性能会比较高一些

5
00:00:08,060 --> 00:00:10,530
全部来自于 slices 这个包

6
00:00:10,530 --> 00:00:12,340
先来看跟排序相关的

7
00:00:12,340 --> 00:00:14,400
这边随便搞了一个切片

8
00:00:14,400 --> 00:00:15,940
整形切片

9
00:00:15,940 --> 00:00:18,000
直接掉 slices 这个包

10
00:00:18,000 --> 00:00:19,500
下面的 sort 函

11
00:00:19,500 --> 00:00:22,440
就可以对这个切片完成排序

12
00:00:22,440 --> 00:00:25,810
那这个排序函数是没有返回值的

13
00:00:25,810 --> 00:00:28,050
就意味着它实际上是原地排序

14
00:00:28,050 --> 00:00:32,159
就说它直接会修改原始的这个切片

15
00:00:32,159 --> 00:00:33,980
把它从无序改为有序

16
00:00:33,980 --> 00:00:38,147
而这个有序指的是从小到大排序

17
00:00:38,147 --> 00:00:40,970
所以我们还是直接打印这个 AR 

18
00:00:40,970 --> 00:00:43,242
这是有序了

19
00:00:43,242 --> 00:00:46,320
那如果你想按照从大到小排序呢

20
00:00:46,320 --> 00:00:47,897
想反过来呢

21
00:00:47,897 --> 00:00:48,690
所以呢

22
00:00:48,690 --> 00:00:51,590
我们还需要提供一种自定义的排序方式

23
00:00:51,590 --> 00:00:54,170
就是说除了 salt 之外

24
00:00:54,170 --> 00:00:56,430
它还有一个 salt funk 啊

25
00:00:56,430 --> 00:01:00,450
允许呢，你传第二个参数就传一个函数进来

26
00:01:00,450 --> 00:01:01,890
而在这个函数里面呢

27
00:01:01,890 --> 00:01:04,726
你可以去自定义你的排序方式

28
00:01:04,726 --> 00:01:07,780
比如说这边是返回 A 减 B 的话

29
00:01:07,780 --> 00:01:09,840
那还是从小到大

30
00:01:09,840 --> 00:01:12,290
如果你改成 B 减 A 的话

31
00:01:12,290 --> 00:01:14,952
就是从大到小

32
00:01:14,952 --> 00:01:17,040
所以不用管 A 和 B 代表谁

33
00:01:17,040 --> 00:01:18,310
他的意思是说

34
00:01:18,310 --> 00:01:20,940
当我需要去比较两个元素的时候

35
00:01:20,940 --> 00:01:21,980
那这两个元素呢

36
00:01:21,980 --> 00:01:23,915
用 A 和 B 来表示

37
00:01:23,915 --> 00:01:25,780
谁在前谁在后是吧

38
00:01:25,780 --> 00:01:27,987
由于这个函数来决定

39
00:01:27,987 --> 00:01:31,130
前者减后者就是从小到大

40
00:01:31,130 --> 00:01:34,877
后者减前者就是从大到小

41
00:01:34,877 --> 00:01:37,787
这还只是一个简单的整形切片

42
00:01:37,787 --> 00:01:39,350
那如果更复杂一点

43
00:01:39,350 --> 00:01:41,480
我先定义一个结构

44
00:01:41,480 --> 00:01:45,070
user ，包含年龄和身高

45
00:01:45,070 --> 00:01:46,700
然后这边有个切片

46
00:01:46,700 --> 00:01:48,750
那切片里面每一个元素呢

47
00:01:48,750 --> 00:01:50,780
是这个结构体的指针

48
00:01:50,780 --> 00:01:52,830
当指针不指针无所谓

49
00:01:52,830 --> 00:01:54,912
关键是结构体

50
00:01:54,912 --> 00:01:59,240
那么如何对这样一个由结构体构成的切片

51
00:01:59,240 --> 00:02:01,475
来进行排序呢

52
00:02:01,475 --> 00:02:05,580
这个时候啊，就必须使用这个 salt funk 啊

53
00:02:05,580 --> 00:02:07,330
自定义排序方式

54
00:02:07,330 --> 00:02:08,988
因为你要排序嘛

55
00:02:08,988 --> 00:02:12,120
别人又不知道你是想按照年龄排序呢

56
00:02:12,120 --> 00:02:14,210
还是想按照身高排序呢

57
00:02:14,210 --> 00:02:16,060
是升序呢还是降序呢

58
00:02:16,060 --> 00:02:16,440
对吧

59
00:02:16,440 --> 00:02:20,885
那完全由我们自己的这个函数来决定

60
00:02:20,885 --> 00:02:24,440
假如说我们想按照身高来进行排序啊

61
00:02:24,440 --> 00:02:26,290
完全不管年龄

62
00:02:26,290 --> 00:02:29,460
那是不是可以直接按照上面这种方式

63
00:02:29,460 --> 00:02:31,140
直接相减呢

64
00:02:31,140 --> 00:02:32,160
按身高吧

65
00:02:32,160 --> 00:02:35,400
直接是 B 点 height 减去 A 点 hat 

66
00:02:35,400 --> 00:02:38,880
这样的话是按照身高进行降序排列是吧

67
00:02:38,880 --> 00:02:40,710
因为是 B 在前

68
00:02:40,710 --> 00:02:41,590
A 在后嘛

69
00:02:41,590 --> 00:02:42,740
降序排列

70
00:02:42,740 --> 00:02:46,342
那第23行这种写法实际上是不对的

71
00:02:46,342 --> 00:02:47,340
为什么呢

72
00:02:47,340 --> 00:02:49,300
因为以这个例子为例

73
00:02:49,300 --> 00:02:50,910
一个人身高1.8米

74
00:02:50,910 --> 00:02:52,470
一个人身高1.7米

75
00:02:52,470 --> 00:02:56,070
那1.8-1.7是0.1嘛

76
00:02:56,070 --> 00:02:58,400
0.1的话再转成整数

77
00:02:58,400 --> 00:03:00,550
因为这边要求返回整数嘛

78
00:03:00,550 --> 00:03:02,630
那转成整数的话变成零了

79
00:03:02,630 --> 00:03:06,000
零的话就代表着两个值是相等的

80
00:03:06,000 --> 00:03:08,540
但实际上1.8米和1.7米

81
00:03:08,540 --> 00:03:10,462
这个差别还是蛮大的

82
00:03:10,462 --> 00:03:12,860
他把小数据给直接忽略了嘛

83
00:03:12,860 --> 00:03:14,570
所以这个实际上不符合我们预期

84
00:03:14,570 --> 00:03:16,390
因那我严格来讲呢

85
00:03:16,390 --> 00:03:18,960
应该按照下面这种方式来进行

86
00:03:18,960 --> 00:03:22,247
还是按照按身高降序排列来做

87
00:03:22,247 --> 00:03:24,460
如果 B 的身高更高一些

88
00:03:24,460 --> 00:03:26,650
我们希望它返回一个正数

89
00:03:26,650 --> 00:03:28,630
如果 B 的身高更低一些

90
00:03:28,630 --> 00:03:29,970
希望返回一个负数

91
00:03:29,970 --> 00:03:32,957
所以呢，直接进行大小比较

92
00:03:32,957 --> 00:03:34,420
A 的身高更大

93
00:03:34,420 --> 00:03:36,110
返回一个正数

94
00:03:36,110 --> 00:03:37,800
A 的身高更小

95
00:03:37,800 --> 00:03:38,970
返回个负数

96
00:03:38,970 --> 00:03:40,300
如果身高相等

97
00:03:40,300 --> 00:03:41,830
返回零

98
00:03:41,830 --> 00:03:43,580
看下一

99
00:03:43,580 --> 00:03:47,400
切片还直接提供一个最大和最小啊

100
00:03:47,400 --> 00:03:49,280
直接把这个切片传进来

101
00:03:49,280 --> 00:03:52,875
能够返回切片里面的最大元素和最小元素

102
00:03:52,875 --> 00:03:54,360
还是直接判断一下

103
00:03:54,360 --> 00:03:57,360
切片里面是否包含某一个特定的元素

104
00:03:57,360 --> 00:03:59,780
contains 这个方法五

105
00:03:59,780 --> 00:04:02,940
这个元素目前来看应该是不包含五啊

106
00:04:02,940 --> 00:04:03,710
所以的话

107
00:04:03,710 --> 00:04:06,717
这个包含这边应该返回的是 false 

108
00:04:06,717 --> 00:04:07,950
第37哈

109
00:04:07,950 --> 00:04:11,550
这边是通过 make 初始化了一个切片 CR 

110
00:04:11,550 --> 00:04:13,170
它的长度和容量呢

111
00:04:13,170 --> 00:04:16,020
跟 R 的长度是相等的

112
00:04:16,020 --> 00:04:17,750
然后直接通过 copy 

113
00:04:17,750 --> 00:04:20,730
这个 copy 也是购物员标准库自带的一个函数啊

114
00:04:20,730 --> 00:04:21,709
可以直接使用

115
00:04:21,709 --> 00:04:26,520
就如同我们的 lcp append new 函数一样

116
00:04:26,520 --> 00:04:28,080
前面不需要加任何包名

117
00:04:28,080 --> 00:04:30,220
可以直接使用 copy 嘛

118
00:04:30,220 --> 00:04:32,520
就是说我要把第二个元素

119
00:04:32,520 --> 00:04:34,570
拷贝到第一个元素里面去

120
00:04:34,570 --> 00:04:36,760
那我要把 AR 这个切片

121
00:04:36,760 --> 00:04:39,582
拷贝到 CR 这个切片里面去

122
00:04:39,582 --> 00:04:40,660
你注意啊

123
00:04:40,660 --> 00:04:43,920
那这个时候你的这个 CR 的长度啊

124
00:04:43,920 --> 00:04:46,140
得大于等于 AR 的长度

125
00:04:46,140 --> 00:04:46,790
当然了

126
00:04:46,790 --> 00:04:48,750
如果你非要说我这个比它小

127
00:04:48,750 --> 00:04:51,380
比方说这边来个 LN 减一吧，是吧

128
00:04:51,380 --> 00:04:54,930
C 2的长度如果就比 A 2长度小的话

129
00:04:54,930 --> 00:04:56,327
那么就意味着

130
00:04:56,327 --> 00:04:58,540
AR 里面只有一部分元素

131
00:04:58,540 --> 00:05:00,860
能够拷贝到 CR 里面去

132
00:05:00,860 --> 00:05:02,920
超出了 CR 的长度

133
00:05:02,920 --> 00:05:05,320
多余的元素是拷贝不进来的

134
00:05:05,320 --> 00:05:08,500
也就是说在这个 copy 的过程当中

135
00:05:08,500 --> 00:05:11,100
CR 的长度是不会变的

136
00:05:11,100 --> 00:05:12,010
长度不会变

137
00:05:12,010 --> 00:05:13,210
那更不用说扩容了

138
00:05:13,210 --> 00:05:14,585
肯定也不会扩容

139
00:05:14,585 --> 00:05:16,870
所以如果这边是减一的话

140
00:05:16,870 --> 00:05:18,910
那么 AR 里面的最后一个元素

141
00:05:18,910 --> 00:05:20,457
是拷贝不过来的

142
00:05:20,457 --> 00:05:22,620
我们把这简易的先删掉

143
00:05:22,620 --> 00:05:23,880
好，这样的话

144
00:05:23,880 --> 00:05:25,240
CR 里面存放的元素

145
00:05:25,240 --> 00:05:27,220
跟 AR 应该是完全一样的

146
00:05:27,220 --> 00:05:28,712
但是啊，注意一下

147
00:05:28,712 --> 00:05:30,590
只是说里面的内容一样

148
00:05:30,590 --> 00:05:33,070
但实际上 CR 跟 AR 是完全两块

149
00:05:33,070 --> 00:05:35,257
不同的内存空间吧

150
00:05:35,257 --> 00:05:38,140
包括它底层所指向的那个数组

151
00:05:38,140 --> 00:05:40,917
也是完全不同的两块数组

152
00:05:40,917 --> 00:05:42,170
那第41行

153
00:05:42,170 --> 00:05:45,525
我要判断 A 2跟 CR 是否相等

154
00:05:45,525 --> 00:05:47,630
如果我们把这个 A 2跟 C 2

155
00:05:47,630 --> 00:05:49,590
单纯当成是一个切片

156
00:05:49,590 --> 00:05:51,340
一个结构体来看的话

157
00:05:51,340 --> 00:05:55,000
由于它们指向的底层数组是两块

158
00:05:55,000 --> 00:05:58,140
不同的内存空间地址是不一样的

159
00:05:58,140 --> 00:06:00,950
所以 AR 跟 CR 这两个结构体

160
00:06:00,950 --> 00:06:03,390
它们的第一个成变量肯定是不一样的

161
00:06:03,390 --> 00:06:04,770
所以应该是不相等的

162
00:06:04,770 --> 00:06:07,367
但是呢，这个 slices equal 

163
00:06:07,367 --> 00:06:11,410
它比较的是里面存放的内容的值

164
00:06:11,410 --> 00:06:13,287
是否足以相等

165
00:06:13,287 --> 00:06:15,390
那他们存放的值确实是相等的

166
00:06:15,390 --> 00:06:16,050
所以的话

167
00:06:16,050 --> 00:06:18,767
我们的第41行应该是返回一个 true 

168
00:06:18,767 --> 00:06:22,460
那如果说我把 AR 的底层数组

169
00:06:22,460 --> 00:06:24,360
里面的元素给改了

170
00:06:24,360 --> 00:06:26,187
执行了加加

171
00:06:26,187 --> 00:06:27,330
那这样的话

172
00:06:27,330 --> 00:06:29,850
AR 跟 CR 所存储的元素的值

173
00:06:29,850 --> 00:06:31,130
确实就不一样了

174
00:06:31,130 --> 00:06:33,547
这个 equal 返回的是 false 

175
00:06:33,547 --> 00:06:35,480
我们再来看下面这个

176
00:06:35,480 --> 00:06:37,720
如果我是直接把这个 AR 

177
00:06:37,720 --> 00:06:39,972
通过等号赋给 DR 

178
00:06:39,972 --> 00:06:42,860
因为前面是通过 copy 拷贝嘛

179
00:06:42,860 --> 00:06:45,410
四竖框之两个切片

180
00:06:45,410 --> 00:06:48,910
两个结构体直接复制拷贝而已

181
00:06:48,910 --> 00:06:50,410
AR 和 D

182
00:06:50,410 --> 00:06:54,290
它们还是共享底层的数组空间的

183
00:06:54,290 --> 00:06:56,880
那他们存放的元素自然是相等的

184
00:06:56,880 --> 00:06:59,315
所以这个 equal 肯定返回 true 嘛

185
00:06:59,315 --> 00:07:05,000
如果通过 AR 把底层的数组元素值修改了

186
00:07:05,000 --> 00:07:07,697
那么当然也会影响到 DR 

187
00:07:07,697 --> 00:07:11,570
所以呢， DR 跟 AR 它们依然是相等的

188
00:07:11,570 --> 00:07:14,260
底层数组的元素依然是一样的

189
00:07:14,260 --> 00:07:17,440
因为它本来就指向同一块底层数组吧
