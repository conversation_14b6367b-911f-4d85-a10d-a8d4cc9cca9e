1
00:00:00,420 --> 00:00:02,677
来看一下基准测试

2
00:00:02,677 --> 00:00:06,630
那我们说单元测试是为了检查你写的代码

3
00:00:06,630 --> 00:00:08,802
逻辑上是否完全正确

4
00:00:08,802 --> 00:00:10,320
而基础测试呢

5
00:00:10,320 --> 00:00:12,960
他不在乎代码的正确性

6
00:00:12,960 --> 00:00:15,200
只在乎代码的运行效率

7
00:00:15,200 --> 00:00:17,760
就是说它运行的快还是慢

8
00:00:17,760 --> 00:00:19,080
需要消耗多少 CPU 

9
00:00:19,080 --> 00:00:20,720
需要消耗多少内存

10
00:00:20,720 --> 00:00:22,600
跟单侧函数类似

11
00:00:22,600 --> 00:00:27,000
我们的基准测试从形式上也要满足三个 test 

12
00:00:27,000 --> 00:00:30,600
第一个，它的文件名称必须以下划线

13
00:00:30,600 --> 00:00:32,700
test 点 go 作为后缀

14
00:00:32,700 --> 00:00:33,840
第二个

15
00:00:33,840 --> 00:00:38,740
基准测试函数名称必须是以 bt Mark 作为前缀

16
00:00:38,740 --> 00:00:42,902
当初我们单测是以 test 作为前缀

17
00:00:42,902 --> 00:00:44,120
第三个

18
00:00:44,120 --> 00:00:45,720
基准测试函数的参

19
00:00:45,720 --> 00:00:48,500
必须是 testing 点 B 的指针

20
00:00:48,500 --> 00:00:52,317
当初我们的单侧是 testing 点 T 的指针

21
00:00:52,317 --> 00:00:54,790
好，满足这三个 test 就可以了

22
00:00:54,790 --> 00:00:57,982
这边呢，我写了一个基准测试函数

23
00:00:57,982 --> 00:00:59,920
以 benchmark 开头是吧

24
00:00:59,920 --> 00:01:02,170
以这个 testing 点 B 组参

25
00:01:02,170 --> 00:01:05,440
看一下正测试的函数

26
00:01:05,440 --> 00:01:07,120
这个编写套路是什么

27
00:01:07,120 --> 00:01:09,400
因为要拷贝两个切片吧

28
00:01:09,400 --> 00:01:13,140
啊，打算从 source 拷贝到 death 里面去

29
00:01:13,140 --> 00:01:15,940
那我先创建好两个切片

30
00:01:15,940 --> 00:01:17,720
而且很长，长度一半

31
00:01:17,720 --> 00:01:19,200
因为我要测试性能了

32
00:01:19,200 --> 00:01:20,300
太短的话

33
00:01:20,300 --> 00:01:22,202
这个效果对比不出来

34
00:01:22,202 --> 00:01:25,790
好，我的参数准备好之后的话

35
00:01:25,790 --> 00:01:30,620
就开始大量的去调用我写好的这个 copy 函数

36
00:01:30,620 --> 00:01:32,160
那么调用多少次呢

37
00:01:32,160 --> 00:01:34,930
这边是 for 循环了 B 点 N 次

38
00:01:34,930 --> 00:01:37,020
这个 B 啊，就是参数传过

39
00:01:37,020 --> 00:01:40,215
这个 B ，那个 BDN 到底是多少次呢

40
00:01:40,215 --> 00:01:42,250
它不是一个确定数字

41
00:01:42,250 --> 00:01:45,660
它实际上是指定一个时间期限

42
00:01:45,660 --> 00:01:47,660
比如默认是两秒钟

43
00:01:47,660 --> 00:01:49,942
那么两秒钟之内

44
00:01:49,942 --> 00:01:52,550
这个 for 循环它能跑多少

45
00:01:52,550 --> 00:01:53,840
就跑多少轮

46
00:01:53,840 --> 00:01:56,880
在这个 for 循环上面来了个 B 点

47
00:01:56,880 --> 00:02:00,242
reset timer ，要重置计时器嘛

48
00:02:00,242 --> 00:02:03,810
据说将来我要统计这个 for 循环

49
00:02:03,810 --> 00:02:05,675
到底花了多长时间

50
00:02:05,675 --> 00:02:08,690
我不想把上面这两行代码的耗时呢

51
00:02:08,690 --> 00:02:09,830
也统计在内

52
00:02:09,830 --> 00:02:10,430
所以呢

53
00:02:10,430 --> 00:02:13,700
我是把上面这两行代码排除在外了是吧

54
00:02:13,700 --> 00:02:16,230
从第55行才开始计时

55
00:02:16,230 --> 00:02:19,390
然后呢，我又写了另外一个基准测试函数

56
00:02:19,390 --> 00:02:24,290
这个函数不是想测试我自己写的某一个函数

57
00:02:24,290 --> 00:02:25,050
性能怎么样

58
00:02:25,050 --> 00:02:28,300
而是想测试一下我们的购物员标准库

59
00:02:28,300 --> 00:02:30,280
自带的这个 copy 函数啊

60
00:02:30,280 --> 00:02:33,690
之前我们在讲切片相关函数那节讲过

61
00:02:33,690 --> 00:02:35,047
copy 函数

62
00:02:35,047 --> 00:02:37,700
它也是可以把一个切片啊

63
00:02:37,700 --> 00:02:40,600
从 source 拷贝到 best 里面去是吧

64
00:02:40,600 --> 00:02:43,320
写法跟上面这个写法几乎一模一样

65
00:02:43,320 --> 00:02:45,160
无非调的函数

66
00:02:45,160 --> 00:02:47,350
一个是标准库代表

67
00:02:47,350 --> 00:02:48,950
一个是我自己实现的

68
00:02:48,950 --> 00:02:53,190
然后呢，我想把这两个基本特征函数都跑一遍

69
00:02:53,190 --> 00:02:57,007
看看它们分别的耗时是多少

70
00:02:57,007 --> 00:03:00,400
我能不能一下子把这两个函数全部跑一遍呢

71
00:03:00,400 --> 00:03:01,360
答案是可以的

72
00:03:01,360 --> 00:03:04,680
看一下我们的基准测试对应的命令是什么

73
00:03:04,680 --> 00:03:06,247
也是 go test 

74
00:03:06,247 --> 00:03:08,300
然后他不需要加这个钢杯

75
00:03:08,300 --> 00:03:11,270
因为杠杯主要是为了观察一些中间输出嘛

76
00:03:11,270 --> 00:03:13,430
观察结果是否正确

77
00:03:13,430 --> 00:03:17,020
而基准测试不在乎结果的正确性

78
00:03:17,020 --> 00:03:18,160
只在乎性能

79
00:03:18,160 --> 00:03:20,620
所以的话你肯定是不需要加这个钢杯的

80
00:03:20,620 --> 00:03:22,090
后面加一个目录

81
00:03:22,090 --> 00:03:26,930
只是说你的基准测试函数在哪个目录下

82
00:03:26,930 --> 00:03:28,760
它会去扫描整个目录

83
00:03:28,760 --> 00:03:31,432
找到所有的 test go 文件

84
00:03:31,432 --> 00:03:35,700
然后执行所有的单侧函数和基准测试函数

85
00:03:35,700 --> 00:03:40,200
如果说像类似于这样的单次函数

86
00:03:40,200 --> 00:03:40,900
你不想跑

87
00:03:40,900 --> 00:03:43,940
你想把所有的单次函数直接屏蔽掉的话

88
00:03:43,940 --> 00:03:44,777
怎么办

89
00:03:44,777 --> 00:03:48,650
可以通过杠 run 等于这样一个符号来屏蔽

90
00:03:48,650 --> 00:03:51,370
因为后面我们讲正则表达式的时候

91
00:03:51,370 --> 00:03:52,090
你就知

92
00:03:52,090 --> 00:03:55,330
其实这个符号表示的是开始位置

93
00:03:55,330 --> 00:03:56,890
而这个美元符号呢

94
00:03:56,890 --> 00:03:58,590
表示的是结束位置

95
00:03:58,590 --> 00:04:01,810
那哪个函数它是刚刚开始

96
00:04:01,810 --> 00:04:03,010
立马就结束了呢

97
00:04:03,010 --> 00:04:04,950
没有不存在这个函数

98
00:04:04,950 --> 00:04:06,830
所以这样的话是把所有的单词函数

99
00:04:06,830 --> 00:04:08,070
全部给它屏蔽掉

100
00:04:08,070 --> 00:04:09,322
不运行

101
00:04:09,322 --> 00:04:12,060
那么基准测试要运行谁呢

102
00:04:12,060 --> 00:04:15,960
基准测试需要满足这样的正则表达式

103
00:04:15,960 --> 00:04:17,980
dollar 表示结尾

104
00:04:17,980 --> 00:04:18,360
好

105
00:04:18,360 --> 00:04:22,580
只要是以 coffee slice 作为后缀的 benchmark 函

106
00:04:22,580 --> 00:04:23,560
我都运行

107
00:04:23,560 --> 00:04:26,560
那么我们的这个是以 coffee slice 结尾

108
00:04:26,560 --> 00:04:29,890
我们的这个也是以 copy slice 结尾啊

109
00:04:29,890 --> 00:04:31,170
这两个都会运行

110
00:04:31,170 --> 00:04:33,050
然后为了避免它缓存

111
00:04:33,050 --> 00:04:35,710
上次结果呢，加一个杠 count 等于一

112
00:04:35,710 --> 00:04:37,610
正如我们在单测里面

113
00:04:37,610 --> 00:04:40,550
可以通过 time out 来指定这个时间一样

114
00:04:40,550 --> 00:04:42,370
我们在基准测试里面

115
00:04:42,370 --> 00:04:47,720
也可以通过杠 bat time 来指定运行多长时间

116
00:04:47,720 --> 00:04:49,990
因为默认是两秒钟嘛

117
00:04:49,990 --> 00:04:54,120
那假如说我们的这个每次 for 循环进来

118
00:04:54,120 --> 00:04:57,220
这个函数运行时间非常长

119
00:04:57,220 --> 00:04:59,995
比如说需要运行个500 ms 

120
00:04:59,995 --> 00:05:03,420
那两秒钟总共就只能 for 循环四轮啊

121
00:05:03,420 --> 00:05:04,920
这个次数太少了

122
00:05:04,920 --> 00:05:06,260
统计容太少

123
00:05:06,260 --> 00:05:09,182
这个对比结果可能不那么可信

124
00:05:09,182 --> 00:05:11,870
所以呢，你希望它 for 循环次数更多

125
00:05:11,870 --> 00:05:14,330
那么你就把那个时间拉的更长

126
00:05:14,330 --> 00:05:17,030
比方说从两秒钟改成十秒钟

127
00:05:17,030 --> 00:05:18,137
甚至是一分钟

128
00:05:18,137 --> 00:05:21,700
可以通过这个参数来进行指定啊

129
00:05:21,700 --> 00:05:22,880
它默认是联网中

130
00:05:22,880 --> 00:05:26,210
好，我们就直接运行一下这个命令来看一看

131
00:05:26,210 --> 00:05:27,940
好，看一下这个结果啊

132
00:05:27,940 --> 00:05:32,140
这边指出 go 的 OS 操作系统是 windows 

133
00:05:32,140 --> 00:05:35,480
然后我们的 CPU 架构呢，是 AMD 64跑的

134
00:05:35,480 --> 00:05:37,730
这个包呢是这个包

135
00:05:37,730 --> 00:05:39,722
这是 CPU 型号

136
00:05:39,722 --> 00:05:41,870
然后 benchmark coffee slice 

137
00:05:41,870 --> 00:05:45,997
这个是我们自己写的这个 coffee slice 这个函数嘛

138
00:05:45,997 --> 00:05:50,220
这个数字表示是说它在指定的时间之内

139
00:05:50,220 --> 00:05:51,560
因为默认是两秒钟吗

140
00:05:51,560 --> 00:05:53,040
在两秒钟时间之内

141
00:05:53,040 --> 00:05:54,580
那么这个 for 循环

142
00:05:54,580 --> 00:05:58,040
也就是这个 B 点 N 到底等于几呢

143
00:05:58,040 --> 00:05:59,500
B 点 N 就等于这么多

144
00:05:59,500 --> 00:06:01,480
26万多次

145
00:06:01,480 --> 00:06:03,985
那么对于每一次报循环来说

146
00:06:03,985 --> 00:06:05,880
它耗时是多少呢

147
00:06:05,880 --> 00:06:08,700
耗时是3800纳秒

148
00:06:08,700 --> 00:06:10,540
这里面每一个 OP 

149
00:06:10,540 --> 00:06:13,830
指的就是每一次 for 循环的考试

150
00:06:13,830 --> 00:06:17,692
好，如果是换成这个 STD 标准库的话

151
00:06:17,692 --> 00:06:21,180
在两秒钟之内它运行了这么多次

152
00:06:21,180 --> 00:06:22,140
次数更多吗

153
00:06:22,140 --> 00:06:23,920
那意味着每一次的耗时会更短

154
00:06:23,920 --> 00:06:27,770
那它每次耗时只需要124纳秒

155
00:06:27,770 --> 00:06:29,480
所以从这个地方我们也看到

156
00:06:29,480 --> 00:06:32,140
确实标准库的这个 copy 函数啊

157
00:06:32,140 --> 00:06:35,717
比我们自己写的这个要快得多得多

158
00:06:35,717 --> 00:06:38,650
所以如果标准库里面有相应的实现

159
00:06:38,650 --> 00:06:40,390
我们一般就直接使用标准库

160
00:06:40,390 --> 00:06:41,800
就不要自己实现了

161
00:06:41,800 --> 00:06:44,530
那么刚才在运行这个命令的时候

162
00:06:44,530 --> 00:06:47,272
实际上后面还可以再跟两个参数

163
00:06:47,272 --> 00:06:48,880
一个是 CPU profile 

164
00:06:48,880 --> 00:06:50,550
一个是 memory profile 

165
00:06:50,550 --> 00:06:53,950
它主要是说把一些 CPU 的快照信息

166
00:06:53,950 --> 00:06:55,810
和内存的快照信息呢

167
00:06:55,810 --> 00:06:58,210
导出到一个文件里面去

168
00:06:58,210 --> 00:07:01,940
而这个文件将来可以结合我们的 p proof 

169
00:07:01,940 --> 00:07:03,640
来进行性能分析

170
00:07:03,640 --> 00:07:05,580
比如说你说这个函数慢

171
00:07:05,580 --> 00:07:06,320
为什么慢

172
00:07:06,320 --> 00:07:09,412
到底是哪一行在消耗 CPU 

173
00:07:09,412 --> 00:07:11,890
或者说这个函数特别吃内存

174
00:07:11,890 --> 00:07:14,110
那到底是哪一行代码特别吃内存

175
00:07:14,110 --> 00:07:17,140
都可以通过 p proof 工具来进行分析

176
00:07:17,140 --> 00:07:17,620
当然了

177
00:07:17,620 --> 00:07:18,460
p proof 工具

178
00:07:18,460 --> 00:07:22,640
前提是你得先生成对应的这个 profile 文件

179
00:07:22,640 --> 00:07:24,640
然后呢，还可以加一个选项

180
00:07:24,640 --> 00:07:26,590
就是 bench ma 

181
00:07:26,590 --> 00:07:30,130
我们把这个半截 mam 再接到后面再来一次

182
00:07:30,130 --> 00:07:30,840
好

183
00:07:30,840 --> 00:07:34,010
由于加了这样一个 bench mam 

184
00:07:34,010 --> 00:07:37,970
它会把跟内存开销相关信息也打印出来

185
00:07:37,970 --> 00:07:40,620
就是多了后面这两列

186
00:07:40,620 --> 00:07:42,825
他就说没勾 P 

187
00:07:42,825 --> 00:07:46,970
需要额外的申请多大的内存空间

188
00:07:46,970 --> 00:07:49,955
那么他一共申请了几次

189
00:07:49,955 --> 00:07:52,470
我们这个拷贝函数很特殊了

190
00:07:52,470 --> 00:07:56,180
因为我们在之前已经把需要的内存

191
00:07:56,180 --> 00:07:57,600
全部升级好了吗

192
00:07:57,600 --> 00:07:59,500
在整个拷贝的过程当中啊

193
00:07:59,500 --> 00:08:01,580
不需要申请额外的内存空间

194
00:08:01,580 --> 00:08:03,592
所以呢，这边都是零

195
00:08:03,592 --> 00:08:05,870
所以基准测试是考察性能吗

196
00:08:05,870 --> 00:08:07,410
性能主要是看两方面

197
00:08:07,410 --> 00:08:10,060
第一方面，看谁运气的快

198
00:08:10,060 --> 00:08:10,940
运气的越快

199
00:08:10,940 --> 00:08:12,670
代表它消耗的 CPU 越少

200
00:08:12,670 --> 00:08:13,440
第二个呢

201
00:08:13,440 --> 00:08:16,900
看谁额外申请的内存空间更多
