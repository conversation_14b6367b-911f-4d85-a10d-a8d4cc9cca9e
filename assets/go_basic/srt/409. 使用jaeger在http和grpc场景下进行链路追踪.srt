1
00:00:00,700 --> 00:00:03,300
大家看，在这个接口的 UI 里面呢

2
00:00:03,300 --> 00:00:05,360
这里面是有两个 trace ，对吧

3
00:00:05,360 --> 00:00:06,420
两个 trace 啊

4
00:00:06,420 --> 00:00:08,880
我们点开其中一个

5
00:00:08,880 --> 00:00:10,240
那这个 trace 里面呢

6
00:00:10,240 --> 00:00:12,210
会包含了四个 span 

7
00:00:12,210 --> 00:00:12,730
好

8
00:00:12,730 --> 00:00:13,750
那问题是说

9
00:00:13,750 --> 00:00:15,630
杰哥他是怎么知道

10
00:00:15,630 --> 00:00:18,492
就这个 trace 的划分的依据是什么

11
00:00:18,492 --> 00:00:21,240
还记得我们在上一节课大纲里面

12
00:00:21,240 --> 00:00:24,230
我们是通过设置了一个全局的 tracer 

13
00:00:24,230 --> 00:00:25,850
通过这个全 trier 

14
00:00:25,850 --> 00:00:28,330
我们去创建了多个 span ，对吧

15
00:00:28,330 --> 00:00:29,745
创建了四个 s span 

16
00:00:29,745 --> 00:00:31,390
那这多个 span 里面呢

17
00:00:31,390 --> 00:00:33,310
只有一个是根斯 span 

18
00:00:33,310 --> 00:00:35,310
因为它不存在父亲啊

19
00:00:35,310 --> 00:00:38,420
其他的都是有这个父亲的对吧

20
00:00:38,420 --> 00:00:40,430
所以呢，一方面啊

21
00:00:40,430 --> 00:00:42,830
它是在同一个进程里面使用的

22
00:00:42,830 --> 00:00:46,720
同一个 tracer 进行上报的同时呢

23
00:00:46,720 --> 00:00:48,800
根据这个父子引用关系

24
00:00:48,800 --> 00:00:53,250
那么自然而然的就可以把它们分成两个 trace 

25
00:00:53,250 --> 00:00:53,670
对吧

26
00:00:53,670 --> 00:00:55,250
按照这个根斯 ban 嘛

27
00:00:55,250 --> 00:00:58,710
一个根斯 span 可以带出一坨 trace 出来

28
00:00:58,710 --> 00:00:59,420
好

29
00:00:59,420 --> 00:01:02,470
那其实我这边还有一个 RPC 啊

30
00:01:02,470 --> 00:01:04,770
RPC 场景景下的一个 trace 

31
00:01:04,770 --> 00:01:06,455
比方说这个 trace 啊

32
00:01:06,455 --> 00:01:09,440
这个 quest 里面呢，有两个 span 

33
00:01:09,440 --> 00:01:11,190
客户端是一个 span 

34
00:01:11,190 --> 00:01:13,065
然后服务端是一个 s span 

35
00:01:13,065 --> 00:01:14,330
那大家要注意一下啊

36
00:01:14,330 --> 00:01:16,470
比方我点开客户端和服务端啊

37
00:01:16,470 --> 00:01:20,010
这个是客户端 span 里面的相关信息啊

38
00:01:20,010 --> 00:01:21,570
不管是 tag 也好

39
00:01:21,570 --> 00:01:22,370
还是 log 也好

40
00:01:22,370 --> 00:01:24,090
还是所谓的 baggage item 也好

41
00:01:24,090 --> 00:01:27,060
都是我们在 go 代码里面给这个 SPP 

42
00:01:27,060 --> 00:01:28,840
通过赋值给它赋进来的

43
00:01:28,840 --> 00:01:31,435
然后呢，通过 span 点 finish 

44
00:01:31,435 --> 00:01:35,410
把 stand 的相关信息上报到 ZG 这边来

45
00:01:35,410 --> 00:01:38,750
好，那客户端它是一台在服务器上对吧

46
00:01:38,750 --> 00:01:39,350
一台服务器

47
00:01:39,350 --> 00:01:41,867
而服务端呢，是另外一台服务器

48
00:01:41,867 --> 00:01:45,060
他们两台服务器开的两个 go 进程

49
00:01:45,060 --> 00:01:47,500
显然是使用的两个 tracer 对吧

50
00:01:47,500 --> 00:01:51,280
那两个 tracer 上报了两个 span jack 

51
00:01:51,280 --> 00:01:52,760
怎么知道这两个死 spa

52
00:01:52,760 --> 00:01:54,540
它们属于同一个 trace 呢

53
00:01:54,540 --> 00:01:58,330
在生成 client 端这个根办的时候呢

54
00:01:58,330 --> 00:02:03,250
他会去创建一个所谓叫做 uber trace id 啊

55
00:02:03,250 --> 00:02:06,370
uber trace id ，这是一个 key value 啊

56
00:02:06,370 --> 00:02:07,780
这个 UBER 退下 D 呢

57
00:02:07,780 --> 00:02:11,017
会附到这个 client 端的 span 上面去

58
00:02:11,017 --> 00:02:14,190
然后 client 在进行 RPC 调用的时候

59
00:02:14,190 --> 00:02:18,705
会把这个 UBERTCD 传给服务端

60
00:02:18,705 --> 00:02:20,130
那服务端呢

61
00:02:20,130 --> 00:02:22,350
他在创建服务端 span 的时候

62
00:02:22,350 --> 00:02:24,490
他要指定我的父亲是谁

63
00:02:24,490 --> 00:02:28,022
而我的父亲实际上就是这个 client 端的 span 

64
00:02:28,022 --> 00:02:33,300
那他要去如何获得这个 client span 呢

65
00:02:33,300 --> 00:02:35,040
它实际上是查询 JAGGER 

66
00:02:35,040 --> 00:02:38,850
他拿着这个 UBER 数据下 D 去接个上面查询一把

67
00:02:38,850 --> 00:02:41,630
把这个开了三把它给查询出来

68
00:02:41,630 --> 00:02:43,090
然后做自己的父亲

69
00:02:43,090 --> 00:02:45,650
然后呢，把自己这个 span 上报给杰哥

70
00:02:45,650 --> 00:02:49,070
这样的话，杰哥就把他们两个 span 给关了起来了

71
00:02:49,070 --> 00:02:52,000
而认为他们是处于同一个 trace 里面的

72
00:02:52,000 --> 00:02:55,740
好，我们看一下具体到代码里面怎么写

73
00:02:55,740 --> 00:02:57,610
所以这里面呃

74
00:02:57,610 --> 00:02:59,350
出现一个很关键的信息

75
00:02:59,350 --> 00:03:01,530
就是这个 UBER 退赛 D 啊

76
00:03:01,530 --> 00:03:04,370
实际上他要把这个 uber trace i

77
00:03:04,370 --> 00:03:05,840
这个 key value 呢

78
00:03:05,840 --> 00:03:09,310
给注入到我们的这个 span 里面去

79
00:03:09,310 --> 00:03:11,050
入到 span context 里面去

80
00:03:11,050 --> 00:03:14,690
然后进行一个 RPT 的序列化传输嘛

81
00:03:14,690 --> 00:03:16,180
那么到另外一端

82
00:03:16,180 --> 00:03:17,820
它需要通过 extract 

83
00:03:17,820 --> 00:03:20,920
再把这个 UKAD 把它给反序列化

84
00:03:20,920 --> 00:03:22,895
把它给取出来

85
00:03:22,895 --> 00:03:25,100
先来看一个 web 接口啊

86
00:03:25,100 --> 00:03:27,310
这边是 client 端

87
00:03:27,310 --> 00:03:28,400
client 端啊

88
00:03:28,400 --> 00:03:29,260
看一下这

89
00:03:29,260 --> 00:03:32,730
他还是去通过调这个 grad 函数

90
00:03:32,730 --> 00:03:34,700
在调 grad 函数之前啊

91
00:03:34,700 --> 00:03:38,010
他会去先往这个 context 里面

92
00:03:38,010 --> 00:03:39,610
通过 with value 形式啊

93
00:03:39,610 --> 00:03:41,570
传了一个 USD 和 TRUCD 

94
00:03:41,570 --> 00:03:44,550
然后把这个 context 传给了 grad 函数

95
00:03:44,550 --> 00:03:46,150
在 grade 函数内部呢

96
00:03:46,150 --> 00:03:49,710
核心是发起一个 HTTP 的这样一个

97
00:03:49,710 --> 00:03:51,610
get 请求啊

98
00:03:51,610 --> 00:03:54,450
还是通过 header 这种方式对吧

99
00:03:54,450 --> 00:03:57,190
把吹3 D 和诱导的根传给复方

100
00:03:57,190 --> 00:04:00,860
也就是说我们在上上一节课讲

101
00:04:00,860 --> 00:04:04,640
如何在 RPC 里面传递追踪信息，对吧

102
00:04:04,640 --> 00:04:08,240
通过 header 或者通过 outgoing context 

103
00:04:08,240 --> 00:04:09,020
那这边呢

104
00:04:09,020 --> 00:04:09,820
也是一样的

105
00:04:09,820 --> 00:04:11,880
代码该怎么写还怎么写

106
00:04:11,880 --> 00:04:14,000
跟上上节课不一样的地方在于

107
00:04:14,000 --> 00:04:17,399
上上节课我们是通过 log 杂志的方式

108
00:04:17,399 --> 00:04:22,230
把追踪信息达到了本地的文件里面去啊

109
00:04:22,230 --> 00:04:25,190
今天呢，我们是通过创建 span 的方式

110
00:04:25,190 --> 00:04:28,040
把追踪信息写给杰克数据库

111
00:04:28,040 --> 00:04:30,420
这边是往 header 里面放了一些信息

112
00:04:30,420 --> 00:04:34,460
然后下面就通过普通的这个 can and do 对吧

113
00:04:34,460 --> 00:04:35,620
去发送一个请求

114
00:04:35,620 --> 00:04:37,820
那中间我特意用空行，对吧

115
00:04:37,820 --> 00:04:39,600
用空行把中间这一段代码

116
00:04:39,600 --> 00:04:41,200
把它们给区分出来

117
00:04:41,200 --> 00:04:44,330
那中间这一坨代码你写和不写

118
00:04:44,330 --> 00:04:47,290
不影响正常的 RPC 调用啊

119
00:04:47,290 --> 00:04:52,145
你写上仅仅是为了给杰哥上报追踪数据而已

120
00:04:52,145 --> 00:04:53,030
好，所以呢

121
00:04:53,030 --> 00:04:53,910
这是什么

122
00:04:53,910 --> 00:04:55,390
创建一个 span 啊

123
00:04:55,390 --> 00:04:56,450
这是一个根死 span 

124
00:04:56,450 --> 00:04:58,802
只付了一个 operation name 

125
00:04:58,802 --> 00:05:01,640
然后呢，通过 finish 对吧

126
00:05:01,640 --> 00:05:03,470
像 JER 去商某数据

127
00:05:03,470 --> 00:05:05,600
接下来他会往这个 span 里面呢

128
00:05:05,600 --> 00:05:07,040
去放入一些信息

129
00:05:07,040 --> 00:05:08,360
添加加 tag 呀

130
00:05:08,360 --> 00:05:09,220
log 呀，对吧

131
00:05:09,220 --> 00:05:12,890
因为我们刚才在这个 UI 界面里面看到的是吧

132
00:05:12,890 --> 00:05:15,070
这些信息都是我们在 go 代码里面

133
00:05:15,070 --> 00:05:16,610
给填进来的嘛

134
00:05:16,610 --> 00:05:20,910
好，这个地方呢，它通过去便利 request header 

135
00:05:20,910 --> 00:05:22,570
他把 header 里面的所有信息呢

136
00:05:22,570 --> 00:05:26,955
直接以 tag 的形式付给了这个 span 

137
00:05:26,955 --> 00:05:28,520
然后我们说啊

138
00:05:28,520 --> 00:05:32,630
我们说这个你把这个 span 在上报给这个之前

139
00:05:32,630 --> 00:05:36,360
还需要上报一个所谓的叫做 UBER 啊

140
00:05:36,360 --> 00:05:39,060
UBERTSD 上报这样一条数据

141
00:05:39,060 --> 00:05:42,330
它是如何去生成这个 UBERTRAD 呢

142
00:05:42,330 --> 00:05:43,730
这是个随机字符串啊

143
00:05:43,730 --> 00:05:44,530
如何生成的

144
00:05:44,530 --> 00:05:47,030
实际上通过这个 inject 

145
00:05:47,030 --> 00:05:47,890
你们看啊

146
00:05:47,890 --> 00:05:49,500
这个 inject 注入嘛

147
00:05:49,500 --> 00:05:54,420
那就需要把这个 spend context 注入到这个 carrier 里面去

148
00:05:54,420 --> 00:05:57,800
这个 carrier 它使用的是 open tron 里面一个现成的

149
00:05:57,800 --> 00:06:01,330
自带的一个 HTTP headers carry 啊

150
00:06:01,330 --> 00:06:04,350
对应的这个 format 自然是 HTTP header 了

151
00:06:04,350 --> 00:06:05,380
这个地方注意一下

152
00:06:05,380 --> 00:06:07,780
我在调用 inject 函数之后

153
00:06:07,780 --> 00:06:12,200
我会再次的把这个 request header 进行一个打印

154
00:06:12,200 --> 00:06:16,980
本来这个 request header 里面就是有这两条数据嘛，对吧

155
00:06:16,980 --> 00:06:19,410
但是这个地方答案出来

156
00:06:19,410 --> 00:06:22,210
你会发现这个 header 里面实际上多了一个信息

157
00:06:22,210 --> 00:06:25,192
就是多了一条 UBERTRLD 

158
00:06:25,192 --> 00:06:28,550
这样这个 header 会一并发送给服务端

159
00:06:28,550 --> 00:06:31,260
好，我们看一下服务端代码

160
00:06:31,260 --> 00:06:33,282
http server 

161
00:06:33,282 --> 00:06:36,350
那服务端我是直接使用了一个中间件啊

162
00:06:36,350 --> 00:06:38,630
所有的逻辑都在中间件里

163
00:06:38,630 --> 00:06:39,850
中间件里面啊

164
00:06:39,850 --> 00:06:44,020
它实际上核心是通过这个 next 去执行

165
00:06:44,020 --> 00:06:45,680
核心的那个 u handler 嘛

166
00:06:45,680 --> 00:06:46,650
那么在此之前呢

167
00:06:46,650 --> 00:06:49,060
他需要向杰克去上报数据

168
00:06:49,060 --> 00:06:51,240
所以说需要创建一个 span 

169
00:06:51,240 --> 00:06:53,245
看这啊，创建一个 span 

170
00:06:53,245 --> 00:06:54,480
创建 span 的话

171
00:06:54,480 --> 00:06:57,880
他第一步呢，是把这个 operation name 给传进来

172
00:06:57,880 --> 00:07:02,100
那这个 opera name 呢，直接是使用的 request urr i 

173
00:07:02,100 --> 00:07:04,320
就是你想请求哪个路径吗

174
00:07:04,320 --> 00:07:07,670
把它作为这个 operate name 去标记这个 span 

175
00:07:07,670 --> 00:07:08,180
好

176
00:07:08,180 --> 00:07:08,900
这边啊

177
00:07:08,900 --> 00:07:13,300
你看这个 start span 后面是一些什么不定长参数啊

178
00:07:13,300 --> 00:07:15,330
是一些 option 

179
00:07:15,330 --> 00:07:19,112
这里面呢，就来了一个所谓的 rpc server option 

180
00:07:19,112 --> 00:07:21,000
那从这个名字让我们看到啊

181
00:07:21,000 --> 00:07:23,060
它实际上在标记我这个 span 是吧

182
00:07:23,060 --> 00:07:27,192
它是一个 rpc server 端的这样一个 span 

183
00:07:27,192 --> 00:07:27,750
好

184
00:07:27,750 --> 00:07:29,210
同时呢，他把什么

185
00:07:29,210 --> 00:07:32,140
他把 client 的这个 span context 

186
00:07:32,140 --> 00:07:34,030
作为我的父亲给传进来了

187
00:07:34,030 --> 00:07:35,070
还指明了什么

188
00:07:35,070 --> 00:07:36,430
父子关系嘛，对吧

189
00:07:36,430 --> 00:07:37,910
这个 client span 是父

190
00:07:37,910 --> 00:07:41,192
这个 service ban 呢，是子指定父子关系

191
00:07:41,192 --> 00:07:41,820
好

192
00:07:41,820 --> 00:07:46,860
那关键问题是这个 client span 是如何获取的

193
00:07:46,860 --> 00:07:49,367
好，看一下上面代码

194
00:07:49,367 --> 00:07:53,750
它实际上是通过调用这个 tracer 的 extract 啊

195
00:07:53,750 --> 00:07:57,510
通过 extract 去构造了这样一个 span context 

196
00:07:57,510 --> 00:07:59,130
因为我们看这个 extract 吧

197
00:07:59,130 --> 00:08:01,470
它返回的就是一个 span context 

198
00:08:01,470 --> 00:08:05,630
它是从哪里去构造出这样一个 span context 呢

199
00:08:05,630 --> 00:08:07,500
它实际上你看啊

200
00:08:07,500 --> 00:08:09,862
我说这个啊

201
00:08:09,862 --> 00:08:11,730
GIN 的 context 

202
00:08:11,730 --> 00:08:16,050
request header 里面不是有一条那个 uber trace id 吗

203
00:08:16,050 --> 00:08:20,530
它实际上是拿着那个 UBERTRLD 去查询 JAGGER ，诶

204
00:08:20,530 --> 00:08:22,670
找到对应的 SPP 信息

205
00:08:22,670 --> 00:08:25,490
也就是说呢，通过这个 UBERTRE 下 D 

206
00:08:25,490 --> 00:08:27,292
它能够把

207
00:08:27,292 --> 00:08:27,960
好

208
00:08:27,960 --> 00:08:31,090
他能够把这个 span 给查询到

209
00:08:31,090 --> 00:08:32,576
好

210
00:08:32,576 --> 00:08:34,530
那光查询到还不行

211
00:08:34,530 --> 00:08:35,730
你还得什

212
00:08:35,730 --> 00:08:37,390
从数据库里面读出小数据

213
00:08:37,390 --> 00:08:39,292
你还得反序列化嘛，对吧

214
00:08:39,292 --> 00:08:41,700
所以说呢，这里面指定了反序列化方

215
00:08:41,700 --> 00:08:43,179
是这个 htp header 啊

216
00:08:43,179 --> 00:08:46,040
通过这种 carrier 进行一个反序列化

217
00:08:46,040 --> 00:08:49,732
最终呢，就构造出了这样一个 span context 

218
00:08:49,732 --> 00:08:53,180
刚好这边可以用上

219
00:08:53,180 --> 00:08:55,580
好，通过调 finish 啊

220
00:08:55,580 --> 00:08:58,100
把这个 service span 上报给杰哥

221
00:08:58,100 --> 00:09:03,850
然后下面是会给这个 spect from context 放一些 tag 

222
00:09:03,850 --> 00:09:08,230
那 tag 呢，信息就是从 request header 里面取出来的

223
00:09:08,230 --> 00:09:10,940
那这一条信息对吧

224
00:09:10,940 --> 00:09:12,130
可以不放

225
00:09:12,130 --> 00:09:13,127
可以忽略掉

226
00:09:13,127 --> 00:09:17,760
好，我们把这个 cat 和服务端都跑起来看一看啊

227
00:09:17,760 --> 00:09:20,690
大家看这个 cat kk 这边的话

228
00:09:20,690 --> 00:09:25,690
我本来往这个 header 里面只放了 user id 跟 trace id 

229
00:09:25,690 --> 00:09:28,690
但实际上我执行了 inject 之后

230
00:09:28,690 --> 00:09:31,010
你发现这个 header 里面多了一个什么

231
00:09:31,010 --> 00:09:33,855
多了一个 uber cc id 对吧

232
00:09:33,855 --> 00:09:35,610
对应到这个上面

233
00:09:35,610 --> 00:09:36,750
从这个 UI 界面上

234
00:09:36,750 --> 00:09:37,750
我们也能看到

235
00:09:37,750 --> 00:09:41,710
刚才的这个看的和 server 端的 SPC 信息

236
00:09:41,710 --> 00:09:44,170
这里面有垂 c id 和 uzi id 

237
00:09:44,170 --> 00:09:47,290
这边呢，也能取到这个吹彩 id 和 uzi id 

238
00:09:47,290 --> 00:09:51,310
再看一下在 GRPC 里面如何跟 GIGER 进行集成

239
00:09:51,310 --> 00:09:52,867
先看客户端

240
00:09:52,867 --> 00:09:56,140
客户端这边的话是通过调这个 hello 函数啊

241
00:09:56,140 --> 00:09:59,915
hello 函数里面的核心是去执行加 PC 调用嘛

242
00:09:59,915 --> 00:10:01,520
传递的这个 context 啊

243
00:10:01,520 --> 00:10:02,400
这个 context 的话

244
00:10:02,400 --> 00:10:06,310
你看一方面我这个 context 里面通过 with value 是吧

245
00:10:06,310 --> 00:10:08,190
添加了一个 key value 

246
00:10:08,190 --> 00:10:11,350
同时呢，还通过这个 matt data 

247
00:10:11,350 --> 00:10:13,550
new outgoing context 是吧

248
00:10:13,550 --> 00:10:16,165
我新建了一个 outgoing context 

249
00:10:16,165 --> 00:10:18,310
这个 out gm 控控 ext 里面呢

250
00:10:18,310 --> 00:10:20,120
传递了一些 matter data 

251
00:10:20,120 --> 00:10:23,290
这个 matt data 本质上啊，它就是一个 map 

252
00:10:23,290 --> 00:10:25,862
你看它实际上就是一个 map 啊

253
00:10:25,862 --> 00:10:26,970
app 里面呢

254
00:10:26,970 --> 00:10:28,890
目前是有一对 k value 

255
00:10:28,890 --> 00:10:31,870
K 是这个 organization ， value 是大桥小

256
00:10:31,870 --> 00:10:33,255
那注意啊

257
00:10:33,255 --> 00:10:36,250
那你通过 with value 这种方式是吧

258
00:10:36,250 --> 00:10:37,690
那这个 k value 

259
00:10:37,690 --> 00:10:41,700
它是不会伴随 RPC 一起传递给副本的

260
00:10:41,700 --> 00:10:46,120
但是这个 out going context 里面的这个 key value 

261
00:10:46,120 --> 00:10:48,680
是会传递给 server 端的

262
00:10:48,680 --> 00:10:49,310
好

263
00:10:49,310 --> 00:10:51,890
那在执行这个 RPC 调用之前呢

264
00:10:51,890 --> 00:10:53,370
我实际上过了一个什么

265
00:10:53,370 --> 00:10:54,690
过了一个拦截器啊

266
00:10:54,690 --> 00:10:57,110
我们说在 JRPC 里面是叫做拦截器

267
00:10:57,110 --> 00:10:59,620
在 web 开发里面叫做中间件啊

268
00:10:59,620 --> 00:11:01,020
只是加法不同而已

269
00:11:01,020 --> 00:11:02,980
实际上本质都是一样的

270
00:11:02,980 --> 00:11:05,855
你可以都把他们称之为中间件

271
00:11:05,855 --> 00:11:08,320
好，在这个 client 这边呢

272
00:11:08,320 --> 00:11:12,910
我可以搞一个 client 端的 GRPC 的拦截器

273
00:11:12,910 --> 00:11:13,900
看一下啊

274
00:11:13,900 --> 00:11:15,910
我一上来呢，先去，哎

275
00:11:15,910 --> 00:11:20,720
我从什么从这个 CCTX 里面去取得这个 out quin context 

276
00:11:20,720 --> 00:11:22,740
就是因为这个 CCTX 啊

277
00:11:22,740 --> 00:11:26,470
就是我们刚才看到的这个 CCTX 

278
00:11:26,470 --> 00:11:30,620
这个 CCTX 里面目前是有一个 outgoing in context 对吧

279
00:11:30,620 --> 00:11:33,060
所以呢，我在这个蓝牙器里面呢

280
00:11:33,060 --> 00:11:36,280
唉，我先取到这个 outgoing context 

281
00:11:36,280 --> 00:11:39,220
从它里面把那个 metadata 先取出来

282
00:11:39,220 --> 00:11:42,380
是有一个什么 organization 大桥小对吧

283
00:11:42,380 --> 00:11:46,200
然后呢，我再往里面去追加两个 KY 

284
00:11:46,200 --> 00:11:48,710
trc id 和 uzi id 

285
00:11:48,710 --> 00:11:52,100
好，下面啊，你看下面这样的话

286
00:11:52,100 --> 00:11:57,470
我这个 method 里面已经有三条 alt going in 了是吧

287
00:11:57,470 --> 00:11:59,655
alt going in context 是三条 k value 

288
00:11:59,655 --> 00:12:01,330
然后呢，你看这里面啊

289
00:12:01,330 --> 00:12:02,750
我又去 new 了一个

290
00:12:02,750 --> 00:12:06,350
重新创建了一个全新的 outgoing in context 

291
00:12:06,350 --> 00:12:10,180
只不过这个呢，你看我是把这个 MD 给传进来的

292
00:12:10,180 --> 00:12:12,980
这样的话，我这个 CTX 里面

293
00:12:12,980 --> 00:12:19,160
这个 CTX 的 outgoing in context 里面是有三条 k value ，对吧

294
00:12:19,160 --> 00:12:21,060
然后我去执行这个 INVOKER 

295
00:12:21,060 --> 00:12:23,340
就执行真正的那个 GRPC 调用

296
00:12:23,340 --> 00:12:24,020
也就是说

297
00:12:24,020 --> 00:12:27,030
当你在执行真正的 gr pc 调用之前

298
00:12:27,030 --> 00:12:29,860
你必须要把你所有需要序列化

299
00:12:29,860 --> 00:12:33,720
需要传给对方 server 端的所有信息全部准备好

300
00:12:33,720 --> 00:12:36,140
并且呢，全部给传递进来

301
00:12:36,140 --> 00:12:39,120
那我们说我这两个追踪信息

302
00:12:39,120 --> 00:12:42,500
显然是需要传递给 server 等等

303
00:12:42,500 --> 00:12:45,760
你这两个追踪信息放到了 MD 里面

304
00:12:45,760 --> 00:12:49,660
而 MD 呢，放到这个 outgoing in context 里面

305
00:12:49,660 --> 00:12:53,820
而这个 CTX 里面它包含了这个 orgin context 

306
00:12:53,820 --> 00:12:56,950
所以呢，它会传递给我们的资料 B 端

307
00:12:56,950 --> 00:12:57,872
好

308
00:12:57,872 --> 00:12:59,910
这是传递追踪信息

309
00:12:59,910 --> 00:13:01,990
然后啊，我用空行是吧

310
00:13:01,990 --> 00:13:05,610
用空行把中间这一坨代码把它给分割出来

311
00:13:05,610 --> 00:13:07,290
中间这一坨代码在干嘛

312
00:13:07,290 --> 00:13:11,842
实际上就是在通过创建 span 向 JAGGER 去上报数据

313
00:13:11,842 --> 00:13:13,940
好，我们看一下这个 client 端

314
00:13:13,940 --> 00:13:16,750
那它是如何去创建这个 spend 的

315
00:13:16,750 --> 00:13:18,820
通过这个全局子 tracer 啊

316
00:13:18,820 --> 00:13:20,580
去启动一个 span 

317
00:13:20,580 --> 00:13:21,900
这个 span 的名称呢

318
00:13:21,900 --> 00:13:24,960
就以这个方法名作为 operate name 

319
00:13:24,960 --> 00:13:26,570
在我们的拦截器里面

320
00:13:26,570 --> 00:13:30,317
这个方法名是直接从参数这边传进来的啊

321
00:13:30,317 --> 00:13:31,860
然后指定了一些 tag 

322
00:13:31,860 --> 00:13:33,560
就是给这个 span 呢

323
00:13:33,560 --> 00:13:34,920
添加了一个 tag 啊

324
00:13:34,920 --> 00:13:37,550
key 是这个，这个 EXT 啊

325
00:13:37,550 --> 00:13:40,670
EXT 实际上是从 open tracing 这个包里面

326
00:13:40,670 --> 00:13:42,910
从这个 open tron 包里面来的啊

327
00:13:43,940 --> 00:13:46,880
刚才给大家看的这个 e x t component 

328
00:13:46,880 --> 00:13:48,460
它实际上就是一个常量

329
00:13:48,460 --> 00:13:49,800
就是一个字符串啊

330
00:13:49,800 --> 00:13:51,940
就是一个 component 这样一个字符串

331
00:13:51,940 --> 00:13:52,670
仅此而已

332
00:13:52,670 --> 00:13:55,040
value 呢，叫做 gr pc 客户端

333
00:13:55,040 --> 00:13:57,410
好，然后指定它是一个什么

334
00:13:57,410 --> 00:13:59,330
它是一个 span cat 是吧

335
00:13:59,330 --> 00:14:00,890
spend 的类型是什么呢

336
00:14:00,890 --> 00:14:02,710
是 r p c client 

337
00:14:02,710 --> 00:14:05,920
它是一个 r p c client 端这样的一个 span 

338
00:14:05,920 --> 00:14:10,480
那实际上它也会什么转变为一个 tag 啊

339
00:14:10,480 --> 00:14:11,580
付给这个 span 

340
00:14:11,580 --> 00:14:13,800
到时候我们在 GIGER 的 UI 里面呢

341
00:14:13,800 --> 00:14:16,340
会看到这个 span 里面有一个 tag 

342
00:14:16,340 --> 00:14:19,960
指明他是一个 RPC 的 cat 

343
00:14:19,960 --> 00:14:22,460
好，记得调用这个 finish 

344
00:14:22,460 --> 00:14:23,260
然后啊

345
00:14:23,260 --> 00:14:27,180
打算往这个 span 里面去添加一些其他的 tag 啊

346
00:14:27,180 --> 00:14:29,590
比方说我会把 MD 里面的

347
00:14:29,590 --> 00:14:32,125
因为 MD 当成一个 map 来使用

348
00:14:32,125 --> 00:14:33,960
MD 里面的所有信息呢

349
00:14:33,960 --> 00:14:37,550
全部添加到我的这个 SPT 里面去

350
00:14:37,550 --> 00:14:41,290
那这里面为什么 value 会要有一个取零呢

351
00:14:41,290 --> 00:14:44,655
实际上在这个 matt to date 它呢

352
00:14:44,655 --> 00:14:46,870
我们可以点开这个 MEDATE 看一看啊

353
00:14:46,870 --> 00:14:49,250
它是一个什么样的数据类型啊

354
00:14:49,250 --> 00:14:51,430
这个 MEDITY 啊，本质上是一个 map 

355
00:14:51,430 --> 00:14:54,360
只不过 the value 是一个切片啊

356
00:14:54,360 --> 00:14:56,640
所以说大家刚才在这看到

357
00:14:56,640 --> 00:14:59,460
它是取切片里面的首元素嘛

358
00:14:59,460 --> 00:15:01,870
那我们记得啊

359
00:15:01,870 --> 00:15:04,390
我们刚才在 web 接口里面的

360
00:15:04,390 --> 00:15:05,590
在开发端对吧

361
00:15:05,590 --> 00:15:08,070
你需要通过注入给它注入一个

362
00:15:08,070 --> 00:15:11,470
就是往这个呃 span 里面去

363
00:15:11,470 --> 00:15:13,950
给它注入一个什么 uber trace id 对吧

364
00:15:13,950 --> 00:15:16,230
那在加 IPC 里面也是一样的

365
00:15:16,230 --> 00:15:18,450
也需要调用这个 inject ，对吧

366
00:15:18,450 --> 00:15:19,110
inject 

367
00:15:19,110 --> 00:15:20,050
好

368
00:15:20,050 --> 00:15:22,170
通过调这个 inject 之后

369
00:15:22,170 --> 00:15:23,290
你会发现什么呢

370
00:15:23,290 --> 00:15:26,720
你会发现你再去便利这个 MD 的话

371
00:15:26,720 --> 00:15:30,640
你会发现 MD 里面多了一个 UBER 出3 D 啊

372
00:15:30,640 --> 00:15:33,820
本来在92行这一边辨认的时候还没有

373
00:15:33,820 --> 00:15:36,600
结果呢到九一百零三行就有了

374
00:15:36,600 --> 00:15:39,660
就是因为中间实现了一个 inject 

375
00:15:39,660 --> 00:15:43,690
好，这个 inject 本意他是要把这个 span context 啊

376
00:15:43,690 --> 00:15:46,402
注入到这个 carrier 里面去

377
00:15:46,402 --> 00:15:51,340
实际上核心实际上是想把这个 span context 里面的

378
00:15:51,340 --> 00:15:52,040
这些个

379
00:15:52,040 --> 00:15:53,260
matter data 啊

380
00:15:53,260 --> 00:15:54,200
进行一次注入

381
00:15:54,200 --> 00:15:57,167
要进行一个啊，远程传输嘛

382
00:15:57,167 --> 00:15:59,520
那这个地方它就不是使用的那个

383
00:15:59,520 --> 00:16:01,620
open prison 里面现成的 carry 

384
00:16:01,620 --> 00:16:04,840
是我们自己写的一个 carry 啊

385
00:16:04,840 --> 00:16:08,630
这个 carry 我们看一下它是一个什么 carry 啊

386
00:16:08,630 --> 00:16:14,040
其实啊，在 ober cut 里面有一个现成的叫做 text map carry 

387
00:16:14,040 --> 00:16:15,300
只不过这个 carry 呢

388
00:16:15,300 --> 00:16:19,180
它乘的数据类型是这样的一个 map 

389
00:16:19,180 --> 00:16:22,200
而我们今天我们的那个 MD 

390
00:16:22,200 --> 00:16:24,560
大家看到它是这样一种对吧

391
00:16:24,560 --> 00:16:26,460
它的这个虽然也是 map 

392
00:16:26,460 --> 00:16:29,040
但是这个 value 呢，是一个切片啊

393
00:16:29,040 --> 00:16:31,740
所以这种数据类型

394
00:16:31,740 --> 00:16:35,547
就需要特定的 carry 来进行承载

395
00:16:35,547 --> 00:16:36,410
好

396
00:16:36,410 --> 00:16:38,250
那么说作为 carry 对吧

397
00:16:38,250 --> 00:16:41,930
你必须实现这个 reader 和这个 RA 接口

398
00:16:41,930 --> 00:16:42,840
那所以呢

399
00:16:42,840 --> 00:16:46,070
就实现了 for each k 和这个 set 

400
00:16:46,070 --> 00:16:47,190
这两个方

401
00:16:47,190 --> 00:16:50,007
就是为了去实现这两个接口嘛

402
00:16:50,007 --> 00:16:51,340
这个 for each 啊

403
00:16:51,340 --> 00:16:52,220
便利接口

404
00:16:52,220 --> 00:16:55,000
便利接口实际上就是把这个 map 里面的数据

405
00:16:55,000 --> 00:16:56,110
进行一个便利嘛

406
00:16:56,110 --> 00:16:57,720
要取出每一对 k value 

407
00:16:57,720 --> 00:17:00,760
扔给这个 handler 回的函数进行处理

408
00:17:00,760 --> 00:17:04,720
那所谓的 set 就是往这个 app 里面去添加 k value 嘛

409
00:17:04,720 --> 00:17:05,460
只不过呢

410
00:17:05,460 --> 00:17:07,520
你把这个 value 传进来之后

411
00:17:07,520 --> 00:17:10,560
它实际上是要追加到这个切片里面去

412
00:17:10,560 --> 00:17:12,499
所以这边呢是 append 的

413
00:17:12,499 --> 00:17:16,420
好，这边是使用了一个自定义的 carry 啊

414
00:17:16,420 --> 00:17:17,540
然后注入之后

415
00:17:17,540 --> 00:17:21,099
这个 MD 里面会多出一个 UBERTRCD 

416
00:17:21,099 --> 00:17:24,192
然后进行一个 RPC 的调用

417
00:17:24,192 --> 00:17:24,770
好

418
00:17:24,770 --> 00:17:25,369
服务端

419
00:17:25,369 --> 00:17:30,090
这边核心功能全部封装到了这个拦截器里面

420
00:17:30,090 --> 00:17:32,655
我们看一下这个服务端的拦截器

421
00:17:32,655 --> 00:17:33,990
服务端拦截器的话

422
00:17:33,990 --> 00:17:34,790
首先一上来啊

423
00:17:34,790 --> 00:17:39,200
他先去从对方传过来的这个 context 里面呢，去取

424
00:17:39,200 --> 00:17:41,560
你看就是 from in common 对吧

425
00:17:41,560 --> 00:17:42,440
in common 对吧

426
00:17:42,440 --> 00:17:44,390
对方传给我的，好

427
00:17:44,390 --> 00:17:50,307
从这个 incoming in context 里面去取得 MATTDMD 

428
00:17:50,307 --> 00:17:52,430
然后呢，去便利啊

429
00:17:52,430 --> 00:17:54,520
这个地方他先去看了一眼

430
00:17:54,520 --> 00:17:56,820
这个 MD 里面到底有哪些数据啊

431
00:17:56,820 --> 00:18:00,720
实际上你能够看到这个 MD 里面有一条 key 

432
00:18:00,720 --> 00:18:03,070
是这个 uber tr 下 D 

433
00:18:03,070 --> 00:18:04,380
好，然后啊

434
00:18:04,380 --> 00:18:07,170
他就调用这个 extract 是吧

435
00:18:07,170 --> 00:18:08,170
反序列化嘛

436
00:18:08,170 --> 00:18:13,350
实际上是拿着这个 MD 里面的这个 UBERTRACID 

437
00:18:13,350 --> 00:18:15,190
去查询这个数据库

438
00:18:15,190 --> 00:18:17,840
取得 client 端的那个 span 

439
00:18:17,840 --> 00:18:18,260
好

440
00:18:18,260 --> 00:18:21,912
这样的话就会把 client 端的这个 span context 

441
00:18:21,912 --> 00:18:24,410
从那个这个里面给读出来

442
00:18:24,410 --> 00:18:26,700
并且呢，就进了反序列化

443
00:18:26,700 --> 00:18:31,440
好，然后我就要开始构建这个 server 端的 spend 啊

444
00:18:31,440 --> 00:18:34,380
server 端 spend 的话，指定 operation name 

445
00:18:34,380 --> 00:18:36,040
通过这个 info 点 full name 

446
00:18:36,040 --> 00:18:38,560
info 是直接从那个拦截器

447
00:18:38,560 --> 00:18:41,010
这个参数里面取过来的啊

448
00:18:41,010 --> 00:18:44,210
以这个方法名作为 spend operation name 

449
00:18:44,210 --> 00:18:46,790
然后呢，指定诶，父子关系是吧

450
00:18:46,790 --> 00:18:50,165
他是继承自这个 client 端的 span 

451
00:18:50,165 --> 00:18:53,550
并且指明了我是一个 rpc server 

452
00:18:53,550 --> 00:18:55,950
指定我的这个类型嘛，对吧

453
00:18:55,950 --> 00:18:58,890
然后给他啊添加了一个 tag 

454
00:18:58,890 --> 00:19:01,490
但下面还会继续的给它添一加 tag 

455
00:19:01,490 --> 00:19:03,270
就是把 MD 里面的信息呢

456
00:19:03,270 --> 00:19:06,890
全部作 tag 放到 server 端的这个 start 里面去

457
00:19:06,890 --> 00:19:09,610
如果是遇见 uber trace d 的话

458
00:19:09,610 --> 00:19:11,612
就跳过这一条 key 6

459
00:19:11,612 --> 00:19:15,600
好，最后通过这个 finish 把 span 呢上报给杰克

460
00:19:15,600 --> 00:19:19,230
最后调用核心的业务 handler 

461
00:19:19,230 --> 00:19:21,590
最终我们在 JK 的 UI 里面

462
00:19:21,590 --> 00:19:23,250
就能够看到这样一条对吧

463
00:19:23,250 --> 00:19:25,110
这是我们的 GRPC client span 

464
00:19:25,110 --> 00:19:27,650
这是我们的 j r p c server 端的 span 

465
00:19:27,650 --> 00:19:29,290
像这个吹下 D 

466
00:19:29,290 --> 00:19:31,610
就是通过 alt green contex

467
00:19:31,610 --> 00:19:35,170
从 client 这边传给 server 里边的

468
00:19:35,170 --> 00:19:36,950
这边会自动加一个 tag 

469
00:19:36,950 --> 00:19:38,490
spend your cat 等于 cat 

470
00:19:38,490 --> 00:19:41,150
这边呢是 spend your cat 等于 server 

471
00:19:41,150 --> 00:19:43,170
organization 等于大小小

472
00:19:43,170 --> 00:19:46,570
这个也是我们放在了那个 auto grain context 里面的数据
