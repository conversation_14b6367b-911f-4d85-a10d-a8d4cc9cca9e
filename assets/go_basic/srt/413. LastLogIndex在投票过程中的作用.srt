1
00:00:00,000 --> 00:00:02,190
这页 PPT 标题

2
00:00:02,190 --> 00:00:03,500
新的点上

3
00:00:03,500 --> 00:00:07,840
必然拥有旧 leader 上面的 committed log 

4
00:00:07,840 --> 00:00:09,130
一个日制是吧

5
00:00:09,130 --> 00:00:10,880
已经被提交了

6
00:00:10,880 --> 00:00:12,760
那么新的 leader 上面

7
00:00:12,760 --> 00:00:15,360
他必然也需要拥有这个日志

8
00:00:15,360 --> 00:00:19,800
因为已提交就代表着你向 client 做出了一个承诺

9
00:00:19,800 --> 00:00:22,860
这个数据已经成功的写入集群了

10
00:00:22,860 --> 00:00:26,435
client 不删集群是不能把他们弄丢失的

11
00:00:26,435 --> 00:00:29,480
那 rap 协议是如何保证这一点的呢

12
00:00:29,480 --> 00:00:30,967
我们来分析一下

13
00:00:30,967 --> 00:00:32,490
在总图里面呢

14
00:00:32,490 --> 00:00:35,310
那么这个 C 要想成为新的 leader 

15
00:00:35,310 --> 00:00:38,430
那么分区二里面它至少得有三个节点

16
00:00:38,430 --> 00:00:40,350
因为总计点数是五吗

17
00:00:40,350 --> 00:00:42,930
有三个才有可能成为地点

18
00:00:42,930 --> 00:00:47,300
而且还必须是全票通过 C 才能成为 leader 

19
00:00:47,300 --> 00:00:50,940
那假设节点 B 上面 index 等于七

20
00:00:50,940 --> 00:00:52,940
这个日日已经被提交了

21
00:00:52,940 --> 00:00:54,250
index 等于

22
00:00:54,250 --> 00:00:57,470
被提交就意味着这五个点里面

23
00:00:57,470 --> 00:01:00,970
至少三个上面已经拥有了 index 等于七

24
00:01:00,970 --> 00:01:01,960
这条日值

25
00:01:01,960 --> 00:01:03,190
也就意味着

26
00:01:03,190 --> 00:01:07,130
分区二里面至少有一个节点已经拥有了

27
00:01:07,130 --> 00:01:08,190
index 等于七

28
00:01:08,190 --> 00:01:09,080
至少是值

29
00:01:09,080 --> 00:01:10,280
那我们假设一下

30
00:01:10,280 --> 00:01:11,760
假设是几点

31
00:01:11,760 --> 00:01:15,510
D 上面它的最后一条日志是 index 等于七

32
00:01:15,510 --> 00:01:19,305
而 G 点 C 上面最后一条日志是 index 等于五

33
00:01:19,305 --> 00:01:22,240
然后 C 成为 leader clien

34
00:01:22,240 --> 00:01:24,940
把新的命令发给了 C 级点

35
00:01:24,940 --> 00:01:28,370
C 级点把限制广播给了 D 级点

36
00:01:28,370 --> 00:01:29,950
同时呢，会带啥

37
00:01:29,950 --> 00:01:32,020
prove index 等于五

38
00:01:32,020 --> 00:01:35,497
因为 C 上面最后一条值是 index 等于五嘛

39
00:01:35,497 --> 00:01:37,720
那么 D 收到这个信条之后

40
00:01:37,720 --> 00:01:40,300
他先找到 index 等于五这个位置

41
00:01:40,300 --> 00:01:43,142
然后呢，把新日志放到五后面

42
00:01:43,142 --> 00:01:46,770
而实际上在 D 上面本来就有

43
00:01:46,770 --> 00:01:49,830
index 等于六和 index 等于七这几条日志

44
00:01:49,830 --> 00:01:53,820
而且这几条日制已经是被标记为已提交了

45
00:01:53,820 --> 00:01:56,240
那现在由于发过来一个心跳

46
00:01:56,240 --> 00:01:59,980
导致呢，把六和七这两个 index 给覆盖了

47
00:01:59,980 --> 00:02:01,560
相当于是删除了吗

48
00:02:01,560 --> 00:02:03,550
那这显然是不允许的

49
00:02:03,550 --> 00:02:06,510
已经被提交的日志怎么能够被删除呢

50
00:02:06,510 --> 00:02:09,955
所以啊，我们的 JVC 的协议需要再完善一下

51
00:02:09,955 --> 00:02:11,710
就是当初这个节点

52
00:02:11,710 --> 00:02:15,230
C 他向 D 和 E 发送投票的时候

53
00:02:15,230 --> 00:02:18,950
D 和乙虽然说，诶，这个 STETERM 比我大

54
00:02:18,950 --> 00:02:22,560
但是呢， D 和 E 同时还要再判断一下

55
00:02:22,560 --> 00:02:27,680
C 上面它最大的那个日志 index 是否比我大

56
00:02:27,680 --> 00:02:31,260
比方说 C 上面 last log index 是五

57
00:02:31,260 --> 00:02:34,640
而 D 上面 last log index 是七五

58
00:02:34,640 --> 00:02:35,400
没有七大

59
00:02:35,400 --> 00:02:38,380
那这个时候 D 是要拒绝头条的

60
00:02:38,380 --> 00:02:40,350
不允许 C 成为 leader 

61
00:02:40,350 --> 00:02:43,810
那如果说 last log index 相等的话

62
00:02:43,810 --> 00:02:47,210
那这个时候是允许他成为新的 leader 的

63
00:02:47,210 --> 00:02:49,510
比方说 C 和 D 都是起

64
00:02:49,510 --> 00:02:51,845
那么这个时候 D 可以给 C 投票

65
00:02:51,845 --> 00:02:54,070
也就是说在分区二里面

66
00:02:54,070 --> 00:02:57,390
最终能够成为 leader 的那个节点

67
00:02:57,390 --> 00:03:01,160
它实际上拥有最大的 last log index 

68
00:03:01,160 --> 00:03:02,130
只有这样的话

69
00:03:02,130 --> 00:03:05,427
他才能在分区卷里面获得满票

70
00:03:05,427 --> 00:03:08,400
才能够获得超过5/2的票数

71
00:03:08,400 --> 00:03:09,870
才能够成为立的

72
00:03:09,870 --> 00:03:12,600
也就是说那个新 leader 

73
00:03:12,600 --> 00:03:16,280
它上面必然有 index 等于7 d tr 值

74
00:03:16,280 --> 00:03:18,630
这样就保证了我们的这个标题

75
00:03:18,630 --> 00:03:20,170
就是新 leader 上面

76
00:03:20,170 --> 00:03:23,790
它必须拥有旧 LIER 上面已经提交过的日志

77
00:03:23,790 --> 00:03:25,140
如何达到这个目的的

78
00:03:25,140 --> 00:03:27,800
就是因为我们多加了一个检查

79
00:03:27,800 --> 00:03:28,980
投票的时候

80
00:03:28,980 --> 00:03:33,520
不但要求 candidate the term 要大于等于自己头

81
00:03:33,520 --> 00:03:37,400
同时还要求 candidate 它的 last log index 

82
00:03:37,400 --> 00:03:41,040
要大于或者等于自己的 last log index 

83
00:03:41,040 --> 00:03:42,620
才能给他投票
