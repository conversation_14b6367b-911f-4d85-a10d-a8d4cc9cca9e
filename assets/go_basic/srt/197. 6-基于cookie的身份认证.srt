1
00:00:00,499 --> 00:00:01,700
上一节课我们讲到

2
00:00:01,700 --> 00:00:03,240
用户在修改密码时

3
00:00:03,240 --> 00:00:05,060
需要提交自己的用户 id 

4
00:00:05,060 --> 00:00:07,545
但实际上他并不知道自己的用户 id 

5
00:00:07,545 --> 00:00:08,580
同样道理

6
00:00:08,580 --> 00:00:10,960
用户在执行其他各项操作时

7
00:00:10,960 --> 00:00:15,530
比如说发表新闻、删除新闻、修改新闻

8
00:00:15,530 --> 00:00:17,090
其实都需要常用户 id 

9
00:00:17,090 --> 00:00:19,260
因为要确保你有这个权限嘛

10
00:00:19,260 --> 00:00:20,090
但用户呢

11
00:00:20,090 --> 00:00:21,902
又不知道自己用怀疑

12
00:00:21,902 --> 00:00:22,820
实际上啊

13
00:00:22,820 --> 00:00:26,340
用户 id 它是放在那个浏览器的 cookie 里面的

14
00:00:26,340 --> 00:00:27,660
对用户来说是透明的

15
00:00:27,660 --> 00:00:28,340
他不知道

16
00:00:28,340 --> 00:00:30,700
但实际上浏览器是知道的

17
00:00:30,700 --> 00:00:32,700
那整个过程呢，是这样子的

18
00:00:32,700 --> 00:00:34,060
就是我们之前啊

19
00:00:34,060 --> 00:00:36,855
在这个登录 HADER 是吧， login 

20
00:00:36,855 --> 00:00:38,840
那登录成功之后的话

21
00:00:38,840 --> 00:00:42,040
后端会浏览器去发送一个 cookie 

22
00:00:42,040 --> 00:00:44,767
cookie 里面呢，就包含了用户 id 

23
00:00:44,767 --> 00:00:46,770
而这个 cookie 啊，有个特点

24
00:00:46,770 --> 00:00:50,210
就是一旦浏览器它保存了这个 cookie 

25
00:00:50,210 --> 00:00:54,040
那么呢，浏览器后续提交的所有请求啊

26
00:00:54,040 --> 00:00:55,440
给后端提交的所有请求

27
00:00:55,440 --> 00:00:58,300
都会默认的把 cookie 再带回来

28
00:00:58,300 --> 00:01:00,780
再传给这个服务端

29
00:01:00,780 --> 00:01:03,400
所以呢，服务端就能够拿到这个用户 id 

30
00:01:03,400 --> 00:01:05,230
我们并不需要显示的

31
00:01:05,230 --> 00:01:06,750
在那个 JS 代码里

32
00:01:06,750 --> 00:01:08,635
去给用户 id 赋值

33
00:01:08,635 --> 00:01:12,330
回传。 cookie 是浏览器的一个默认操作

34
00:01:12,330 --> 00:01:14,397
程序员不需要做任何工作

35
00:01:14,397 --> 00:01:17,460
我们来把整个过程给大家详细的演示一下

36
00:01:17,460 --> 00:01:19,660
因为干这样讲感觉很抽象嘛

37
00:01:19,660 --> 00:01:21,330
这边是登录 handler 

38
00:01:21,330 --> 00:01:22,770
那么登录最后的话

39
00:01:22,770 --> 00:01:24,090
哎，如果到这是吧

40
00:01:24,090 --> 00:01:26,147
表示已经登录成功了吗

41
00:01:26,147 --> 00:01:28,420
那咱们给它返回一个 cookie 

42
00:01:28,420 --> 00:01:34,460
通过这个 JN 的 context 直接调 set cookie 啊

43
00:01:34,460 --> 00:01:36,400
可以给前端返回 cookie 

44
00:01:36,400 --> 00:01:38,110
需要传很多参数啊

45
00:01:38,110 --> 00:01:39,010
第一个 name 

46
00:01:39,010 --> 00:01:41,050
每一个 cookie 呢，需要起一个名称

47
00:01:41,050 --> 00:01:42,622
比如就叫 UID 吧

48
00:01:42,622 --> 00:01:45,780
第二个是对应的 value ，是一个字符串

49
00:01:45,780 --> 00:01:50,122
那咱们就把这个用户 id 转成字符串

50
00:01:50,122 --> 00:01:54,450
STRUCF 点 i to a 真实听话

51
00:01:54,450 --> 00:01:57,420
D 呢就是这个 user 2

52
00:01:57,420 --> 00:01:59,640
它是从库里面读出来的嘛

53
00:01:59,640 --> 00:02:01,360
它里面包含了 id 

54
00:02:02,460 --> 00:02:03,880
优化第

55
00:02:03,880 --> 00:02:06,700
好，第三个是这个 max edge 啊

56
00:02:06,700 --> 00:02:07,480
这个 cookie 啊

57
00:02:07,480 --> 00:02:09,180
能存活多长时间

58
00:02:09,180 --> 00:02:10,740
它是以秒为单位的

59
00:02:10,740 --> 00:02:16,140
咱们如果设置一个86400就一天嘛

60
00:02:16,140 --> 00:02:17,490
它只能存活一天

61
00:02:17,490 --> 00:02:18,670
那么一天以后的话

62
00:02:18,670 --> 00:02:21,572
浏览器会自动把这个库给清掉

63
00:02:21,572 --> 00:02:24,100
他这个登录信息就没有了啊

64
00:02:24,100 --> 00:02:26,422
就需要强制的再次登录

65
00:02:26,422 --> 00:02:29,610
然后是 pass cookie 有一个存放的路径啊

66
00:02:29,610 --> 00:02:31,892
咱们默认的话都使用根路径吧

67
00:02:31,892 --> 00:02:33,480
然后是 DOMI 啊

68
00:02:33,480 --> 00:02:35,460
这个主要是跟安全限制相关的

69
00:02:35,460 --> 00:02:37,507
那么咱们就使用 local host 

70
00:02:37,507 --> 00:02:38,350
什么意思呢

71
00:02:38,350 --> 00:02:40,210
就刚才我不是说那个浏览器

72
00:02:40,210 --> 00:02:43,400
它会自动的去回传这个 cookie 吗

73
00:02:43,400 --> 00:02:46,470
但是它也不是说所有 cookie 都回传

74
00:02:46,470 --> 00:02:49,460
它只回传本域名下面的 cookie 

75
00:02:49,460 --> 00:02:52,100
你比如说你登录百度啊

76
00:02:52,100 --> 00:02:53,852
百度给你返回一个 cookie 

77
00:02:53,852 --> 00:02:56,160
域名呢是百度点. com 

78
00:02:56,160 --> 00:03:00,750
然后呢，你去请求 QQ 的很多接口的话

79
00:03:00,750 --> 00:03:04,050
他是不会把百度的 cookie 回传给服务端的

80
00:03:04,050 --> 00:03:08,080
他只会把 QQ 点 com 下面的所有 cookie 

81
00:03:08,080 --> 00:03:10,820
回传给 QQ 的某一个接口

82
00:03:10,820 --> 00:03:12,832
所以这个是域名发挥作用

83
00:03:12,832 --> 00:03:15,390
然后是一个 secure 安全

84
00:03:15,390 --> 00:03:16,350
这个 secure 的话

85
00:03:16,350 --> 00:03:17,962
它实际上对应的是那个

86
00:03:17,962 --> 00:03:19,260
就这个 S 啊

87
00:03:19,260 --> 00:03:22,987
它实际就是 HTTPS 里面的那个 S 

88
00:03:22,987 --> 00:03:25,830
就是你是否要加那个 TLS 嘛

89
00:03:25,830 --> 00:03:26,790
就是说

90
00:03:26,790 --> 00:03:27,807
嗯

91
00:03:27,807 --> 00:03:31,322
cookie 里面呢，可能包含一些敏感的信息

92
00:03:31,322 --> 00:03:35,190
cookie 本质上是放到那个请求头衔头里面

93
00:03:35,190 --> 00:03:36,410
来进行传输的码

94
00:03:36,410 --> 00:03:40,870
你是打算直接通过铭文来传输这个请求头吗

95
00:03:40,870 --> 00:03:42,440
你就这样安全吗

96
00:03:42,440 --> 00:03:45,750
如果你觉得明文传输没啥问题

97
00:03:45,750 --> 00:03:49,485
那么呢，这个地方你就设置为 false 

98
00:03:49,485 --> 00:03:51,860
就是不需要走那个 HTPS 嘛

99
00:03:51,860 --> 00:03:54,980
如果你希望对整个应用层数据

100
00:03:54,980 --> 00:03:58,620
当然包含了 HTP 的请求头和请求体检都详体

101
00:03:58,620 --> 00:04:00,620
如果你希望全部加密的话

102
00:04:00,620 --> 00:04:04,430
那么就这个地方呢，就使用 true 啊

103
00:04:04,430 --> 00:04:06,195
我们的话就使用 false 吧

104
00:04:06,195 --> 00:04:07,870
因为如果你改成 true 的话

105
00:04:07,870 --> 00:04:08,990
那么我们到时候呢

106
00:04:08,990 --> 00:04:12,190
还需要启动那个 HTPS 的 server 端

107
00:04:12,190 --> 00:04:14,020
麻烦点最后一个啊

108
00:04:14,020 --> 00:04:17,459
就是这个 HTTP only 啊

109
00:04:17,459 --> 00:04:19,477
它对应的就是那个 JS 嘛

110
00:04:19,477 --> 00:04:21,910
如果这个 only 是 true 的话

111
00:04:21,910 --> 00:04:23,510
就意味着我们通过 JS 

112
00:04:23,510 --> 00:04:26,020
是不能够去修改这个 cookie 的

113
00:04:26,020 --> 00:04:28,060
如果我们使用 false 的话

114
00:04:28,060 --> 00:04:30,380
哎，使用 JS 呢，可以修改这个 cookie 

115
00:04:30,380 --> 00:04:32,380
就说 cookie 啊，已经存到本地了

116
00:04:32,380 --> 00:04:34,040
但是呢，我通过 JS 嘛

117
00:04:34,040 --> 00:04:35,160
可以去修改它

118
00:04:35,160 --> 00:04:37,900
比如说修改它的这个过期时间呢

119
00:04:37,900 --> 00:04:39,837
来秀他的这个 value 值啊

120
00:04:39,837 --> 00:04:41,690
那么我们如果改成 true 的话

121
00:04:41,690 --> 00:04:43,490
是不允许 JS 修改的

122
00:04:43,490 --> 00:04:46,250
如果我们有修改的需求怎么办

123
00:04:46,250 --> 00:04:52,030
比如说用户将来他要手动的退出登录是吧

124
00:04:52,030 --> 00:04:55,950
那默认这边是说一天之后需要重新登录吗

125
00:04:55,950 --> 00:04:57,170
还没到一天呢

126
00:04:57,170 --> 00:04:58,880
他自己要强制退出

127
00:04:58,880 --> 00:05:00,350
他自己强行退出

128
00:05:00,350 --> 00:05:02,210
实际上对应的就是说

129
00:05:02,210 --> 00:05:03,430
从浏览器里

130
00:05:03,430 --> 00:05:05,755
把这个 cookie 把它给删掉

131
00:05:05,755 --> 00:05:07,120
那谁删呢

132
00:05:07,120 --> 00:05:09,000
你这边设置成 true 了

133
00:05:09,000 --> 00:05:12,000
那 JS 它删不了或者不删可以

134
00:05:12,000 --> 00:05:14,140
你只要把这个过期时间

135
00:05:14,140 --> 00:05:16,260
设为过去的某一个时刻

136
00:05:16,260 --> 00:05:18,790
也可以让它立马过期嘛

137
00:05:18,790 --> 00:05:21,480
demo 提示 JS 也没有修改权限

138
00:05:21,480 --> 00:05:22,560
咋办呢

139
00:05:22,560 --> 00:05:23,750
没关系啊

140
00:05:23,750 --> 00:05:28,890
咱们通过后端再次调这个 context set 的 cookie 

141
00:05:28,890 --> 00:05:30,490
咱把这个 max edge 啊

142
00:05:30,490 --> 00:05:33,360
生命周期把它设置为一啊

143
00:05:33,360 --> 00:05:36,310
强行把这样一个 cookie 传给前端

144
00:05:36,310 --> 00:05:41,060
实际上就等价于让前端把这个困难删掉

145
00:05:41,060 --> 00:05:42,960
就没有走那个 JS 嘛

146
00:05:42,960 --> 00:05:44,820
完全做过后端的实现

147
00:05:44,820 --> 00:05:45,640
所以的话

148
00:05:45,640 --> 00:05:48,080
咱们这边可以写一个后端对应的

149
00:05:48,080 --> 00:05:51,080
这个退出登录的 header 啊

150
00:05:51,080 --> 00:05:52,187
也很简单

151
00:05:52,187 --> 00:05:54,250
跟上面这个几乎一模一样

152
00:05:54,250 --> 00:05:56,270
唯一变的地方就是

153
00:05:56,270 --> 00:05:57,790
咱们不需要关心 value 

154
00:05:57,790 --> 00:05:58,965
反正删除吧

155
00:05:58,965 --> 00:06:03,250
就重要的是要把这个 max edge 把它设置为负数

156
00:06:03,250 --> 00:06:04,270
好传给浏览器

157
00:06:04,270 --> 00:06:05,820
然后浏览器呢，把这个删掉

158
00:06:05,820 --> 00:06:09,670
这就是退出登录的背后试驾方案

159
00:06:09,670 --> 00:06:12,640
好，那么用户他是先调登录嘛，对吧

160
00:06:12,640 --> 00:06:13,540
先调登录

161
00:06:13,540 --> 00:06:16,362
然后呢，浏览器拿到这样一个 cookie 

162
00:06:16,362 --> 00:06:20,110
然后的话他去调这个修改密码

163
00:06:20,110 --> 00:06:21,122
这个接口

164
00:06:21,122 --> 00:06:22,100
那这样的话

165
00:06:22,100 --> 00:06:26,810
前端它不需要显示的传这个用户 id 了

166
00:06:26,810 --> 00:06:30,760
咱们来这边把这个用户 id 把它给它删掉啊

167
00:06:30,760 --> 00:06:31,747
不需要

168
00:06:31,747 --> 00:06:34,710
那么这边的话，这个用户 id 怎么来获取呢

169
00:06:34,710 --> 00:06:36,650
咱们从 cookie 里面来获取

170
00:06:36,650 --> 00:06:38,637
我这边写一个函数啊

171
00:06:38,637 --> 00:06:41,600
叫做 get ui i

172
00:06:41,600 --> 00:06:42,380
from 

173
00:06:43,600 --> 00:06:46,330
cookie 返回 UID 

174
00:06:46,330 --> 00:06:48,360
这个 cookie 呢，是那个浏览

175
00:06:48,360 --> 00:06:49,760
它默认给我传回来的

176
00:06:49,760 --> 00:06:53,100
我们在 JS 大八里面不需要显示的回传

177
00:06:53,100 --> 00:06:54,300
我作为服务端啊

178
00:06:54,300 --> 00:06:56,795
我可以去便利这些个 cookie 

179
00:06:56,795 --> 00:07:00,370
因为客户端给服务端可以传多个 cookie 嘛

180
00:07:00,370 --> 00:07:01,812
for range 

181
00:07:01,812 --> 00:07:04,710
咱们应该把这个 context 给传过来

182
00:07:04,710 --> 00:07:07,810
因为通过 context 才能够取得这个

183
00:07:07,810 --> 00:07:10,205
请求头和请求体嘛

184
00:07:10,205 --> 00:07:13,330
context 然后是 request 请求

185
00:07:13,330 --> 00:07:17,952
请求里面呢，可以直接拿到所有的 cookie 

186
00:07:17,952 --> 00:07:19,930
好去便利他们

187
00:07:19,930 --> 00:07:21,000
那便利的话

188
00:07:21,000 --> 00:07:23,360
如果说发现当前这个 cooki

189
00:07:23,360 --> 00:07:26,220
它的名称 name 等于，哎

190
00:07:26,220 --> 00:07:30,580
等于我之前设置好的这个 UID 的话

191
00:07:30,580 --> 00:07:33,330
我就只关心这个 cookie 

192
00:07:33,330 --> 00:07:34,920
好，等下的话

193
00:07:34,920 --> 00:07:38,260
那么它对应的那个 value 是吧

194
00:07:38,260 --> 00:07:41,322
这个 value 就应该是用户 id 嘛

195
00:07:41,322 --> 00:07:43,510
UID 大家可能会发生 error 啊

196
00:07:43,510 --> 00:07:45,220
就是你要转嘛

197
00:07:45,220 --> 00:07:49,505
STRUCV 点 a to i 字符串转成整数

198
00:07:49,505 --> 00:07:50,630
把这个

199
00:07:52,070 --> 00:07:55,645
cookie 的 value 转成 EYD 

200
00:07:55,645 --> 00:07:57,690
如果我说这个 L 等于空的话

201
00:07:57,690 --> 00:07:59,240
说明转成功了

202
00:07:59,240 --> 00:08:01,690
哎，那么呢，我就返回什么

203
00:08:01,690 --> 00:08:04,860
返回这个 UID 就可以了

204
00:08:04,860 --> 00:08:07,040
好，那么其他情况啊

205
00:08:07,040 --> 00:08:08,960
不管是哪一步出问题了

206
00:08:08,960 --> 00:08:10,720
一律返回零零

207
00:08:10,720 --> 00:08:13,820
就表示我这个提取失败了

208
00:08:13,820 --> 00:08:16,640
没有从 QQ 里面来拿到 UID 

209
00:08:16,640 --> 00:08:18,620
所以的话呢，我们这边啊

210
00:08:18,620 --> 00:08:19,740
就可以来这样搞一下

211
00:08:19,740 --> 00:08:23,790
UID 等于我调下面写好的这个函数

212
00:08:23,790 --> 00:08:26,115
把 context 传进去

213
00:08:26,115 --> 00:08:27,940
那如果说这一步啊

214
00:08:27,940 --> 00:08:33,294
从 cookie 里面没有提取出正常的 UID 的话

215
00:08:33,294 --> 00:08:34,659
那么我们应该怎么样

216
00:08:34,659 --> 00:08:37,980
我们应该给它返回一个权限不够，对吧

217
00:08:37,980 --> 00:08:38,900
你没有登录的话

218
00:08:38,900 --> 00:08:41,209
你就不能够去修改密码吧

219
00:08:41,209 --> 00:08:42,700
这边是 forbidden 啊

220
00:08:42,700 --> 00:08:44,680
403，请先登录

221
00:08:44,680 --> 00:08:45,370
好

222
00:08:45,370 --> 00:08:47,100
如果有用户 id 的话

223
00:08:47,100 --> 00:08:48,700
那么咱们直接把用户 i

224
00:08:48,700 --> 00:08:50,100
就可以扔给这个

225
00:08:50,100 --> 00:08:52,035
修改密码的函数嘛

226
00:08:52,035 --> 00:08:53,160
好，所以这样的话

227
00:08:53,160 --> 00:08:56,420
咱们是从 cookie 里面来获得这个 UID 

228
00:08:56,420 --> 00:08:58,200
我们写几个前端页面

229
00:08:58,200 --> 00:09:00,385
把这个完整流程走一下

230
00:09:00,385 --> 00:09:04,240
那前端页面的话应该放到 views 目录下

231
00:09:04,240 --> 00:09:07,560
咱们放到 HTML 这个目录下

232
00:09:07,560 --> 00:09:09,700
那说到跟前段相关的

233
00:09:09,700 --> 00:09:14,540
我们就使用最原始的 HTML 加 JAVASCRIPT 啊

234
00:09:14,540 --> 00:09:15,900
咱们也不使用什么 react 

235
00:09:15,900 --> 00:09:17,345
什么 view 1种框架了

236
00:09:17,345 --> 00:09:18,470
我也不熟悉

237
00:09:18,470 --> 00:09:20,240
用户修改密码之前

238
00:09:20,240 --> 00:09:21,940
他第一步需要先注册

239
00:09:21,940 --> 00:09:22,840
然后登录

240
00:09:22,840 --> 00:09:24,132
然后才要修改

241
00:09:24,132 --> 00:09:26,270
所以我需要搞三个页面

242
00:09:27,270 --> 00:09:29,190
这三个页面写好之后的话

243
00:09:29,190 --> 00:09:32,010
我们把整个 JN 的 main 函数写一下

244
00:09:32,010 --> 00:09:33,435
把它跑起来

245
00:09:33,435 --> 00:09:36,810
我们来到最外层的 main 点

246
00:09:36,810 --> 00:09:38,550
勾面函数嘛

247
00:09:38,550 --> 00:09:42,260
text 名称必须是 funk min 

248
00:09:42,260 --> 00:09:45,990
咱们先来写一下跟初始化相关

249
00:09:45,990 --> 00:09:48,360
funk in it 

250
00:09:48,360 --> 00:09:52,500
那么还记得当初我们在写那个单元测试时候

251
00:09:52,500 --> 00:09:54,560
user test 对吧

252
00:09:54,560 --> 00:09:57,180
咱们故意的去初始化了一个 log 

253
00:09:57,180 --> 00:09:59,872
和一个虚库连接

254
00:09:59,872 --> 00:10:04,215
所以呢，这个也是我们 main 函数的初始化工作

255
00:10:04,215 --> 00:10:07,380
那只不过对你的路径需要改一改

256
00:10:07,380 --> 00:10:09,230
当初是相对单色文件

257
00:10:09,230 --> 00:10:12,812
现在是相对我们执行 go run 的路径

258
00:10:12,812 --> 00:10:16,682
这个直接是当前目录下找这个 log 

259
00:10:16,682 --> 00:10:21,350
KF 的话，我们是在当前目录下找到 post 

260
00:10:21,350 --> 00:10:23,112
然后去找 CONF 

261
00:10:23,112 --> 00:10:26,692
当前目录下找 log 目录

262
00:10:26,692 --> 00:10:30,230
在 main 里面一上来先去执行初始化

263
00:10:30,230 --> 00:10:33,920
然后去创建 GN 的 engine 

264
00:10:33,920 --> 00:10:36,425
GN 点 default 

265
00:10:36,425 --> 00:10:40,870
最后是通过 run 把它给跑起来

266
00:10:40,870 --> 00:10:44,950
咱们直接使用 local host 端口

267
00:10:44,950 --> 00:10:47,685
5678跑起来

268
00:10:47,685 --> 00:10:50,290
然后刚才不是搞了一些相关页面吗

269
00:10:50,290 --> 00:10:52,250
那么这些 ETML 文件的话

270
00:10:52,250 --> 00:10:55,592
咱们就通过静态资源把它给加载起来

271
00:10:55,592 --> 00:10:59,450
我直接放这儿看一下这几行代码啊

272
00:10:59,450 --> 00:11:02,460
那么这一行代码的意思是说

273
00:11:02,460 --> 00:11:06,640
我在服务器端的实际路径呢，是这个路径

274
00:11:06,640 --> 00:11:09,680
那么我要把这个路径映射为

275
00:11:09,680 --> 00:11:10,900
就是前端的话

276
00:11:10,900 --> 00:11:13,490
你直接请求 JS 这个路径

277
00:11:13,490 --> 00:11:16,850
它背后对应的是服务器上面的这个逻辑

278
00:11:16,850 --> 00:11:19,722
好， CSS 也是一样的道理

279
00:11:19,722 --> 00:11:21,220
然后的话就是

280
00:11:21,220 --> 00:11:24,000
如果前端直接请求根路径下的

281
00:11:24,000 --> 00:11:25,380
这个图片文件的话

282
00:11:25,380 --> 00:11:28,797
它对应的服务端是这个图片文件

283
00:11:28,797 --> 00:11:32,270
而这个图片文件它实际上是那个浏览器

284
00:11:32,270 --> 00:11:35,415
默认的就是每一个标签页啊

285
00:11:35,415 --> 00:11:36,930
最上面的标签

286
00:11:36,930 --> 00:11:38,780
那不是会有个图标吗

287
00:11:38,780 --> 00:11:41,567
就类似于我们看百度这个网

288
00:11:41,567 --> 00:11:42,780
这个地方对吧

289
00:11:42,780 --> 00:11:45,000
标签这个地方有一个百度熊掌

290
00:11:45,000 --> 00:11:46,700
那这个图片是怎么来的

291
00:11:46,700 --> 00:11:50,047
这个图片实际上它对应的就是每个网站呢

292
00:11:50,047 --> 00:11:53,690
它需要在根目录下提供这样一个图片啊

293
00:11:53,690 --> 00:11:56,580
所以这个图片我是之前就准备好了

294
00:11:56,580 --> 00:12:00,230
放到了我的这个 views 1密接下

295
00:12:00,230 --> 00:12:04,160
哎，就是这张图片不需要很大

296
00:12:04,160 --> 00:12:06,935
反正将来展出来很小嘛

297
00:12:06,935 --> 00:12:09,540
然后呢，通过 load HTML 啊

298
00:12:09,540 --> 00:12:13,135
我把整个 HTML 目录下面的所有文件

299
00:12:13,135 --> 00:12:14,520
都进行了加载

300
00:12:14,520 --> 00:12:16,032
那这样的话呢

301
00:12:16,032 --> 00:12:19,090
我到时候去使用某一个 HTML 文件

302
00:12:19,090 --> 00:12:20,390
就不需要带路径了

303
00:12:20,390 --> 00:12:22,785
直接使用文件名称就可以了

304
00:12:22,785 --> 00:12:24,770
好，比如现在我要定义路由

305
00:12:24,770 --> 00:12:28,020
那我就要去使用这些静态的前端页面

306
00:12:28,020 --> 00:12:29,875
通过 get 

307
00:12:29,875 --> 00:12:33,370
比如说他来请求这个 login 这个目录的话

308
00:12:33,370 --> 00:12:36,867
那么我对应的 handler 是这样子的

309
00:12:36,867 --> 00:12:41,490
通过 context 我直接给它返回一个前端的页面

310
00:12:41,490 --> 00:12:43,560
状态码是200

311
00:12:43,560 --> 00:12:45,000
对应的文件呢

312
00:12:45,000 --> 00:12:50,940
就是我在这个目录下已经写好的啊

313
00:12:50,940 --> 00:12:53,242
login 点 HTML 

314
00:12:53,242 --> 00:12:54,170
后面的话

315
00:12:54,170 --> 00:12:54,370
如果

316
00:12:54,370 --> 00:12:57,710
如果你有一些参数需要往前页面填充的话

317
00:12:57,710 --> 00:12:59,660
可以通过 map 给他带上

318
00:12:59,660 --> 00:13:00,200
这里面呢

319
00:13:00,200 --> 00:13:02,380
我们不需要填充任何数据啊

320
00:13:02,380 --> 00:13:04,197
直接传一个 new 就可以了

321
00:13:04,197 --> 00:13:05,170
同理啊

322
00:13:05,170 --> 00:13:07,210
关于注册、关于修改密码

323
00:13:07,210 --> 00:13:10,820
都是给他返回我们写好的前端页面

324
00:13:10,820 --> 00:13:16,260
register 对应的文件名是 user register 

325
00:13:16,260 --> 00:13:17,360
修改密

326
00:13:17,360 --> 00:13:21,967
我们定义好这个路径叫做 modify pass 

327
00:13:21,967 --> 00:13:25,590
咱们的文件名是 update pass 

328
00:13:25,590 --> 00:13:29,060
这些只是第一次打开这个页面啊

329
00:13:29,060 --> 00:13:31,060
比方说你在这个登录页面上

330
00:13:31,060 --> 00:13:32,120
在登录页面

331
00:13:32,120 --> 00:13:33,840
你输入用户密码之后

332
00:13:33,840 --> 00:13:35,630
你点那个登录按钮

333
00:13:35,630 --> 00:13:36,920
点 DOI 之后

334
00:13:36,920 --> 00:13:39,200
它对应的是另外一个接口啊

335
00:13:39,200 --> 00:13:40,460
注意区分开

336
00:13:40,460 --> 00:13:41,320
什么意思呢

337
00:13:41,320 --> 00:13:44,840
就是说我再定义一个路径是 post 

338
00:13:44,840 --> 00:13:45,640
它是谁

339
00:13:45,640 --> 00:13:47,110
它是 login 

340
00:13:47,110 --> 00:13:49,860
下面的话，如果你点了提交按钮的话

341
00:13:49,860 --> 00:13:51,980
对应的是 submit ，哎

342
00:13:51,980 --> 00:13:53,152
对应这个逻辑

343
00:13:53,152 --> 00:13:55,150
那这个路径它对应的 handler 呢

344
00:13:55,150 --> 00:13:57,530
就是我们之前写好的

345
00:13:57,530 --> 00:13:59,190
来到 handler 里面

346
00:13:59,190 --> 00:14:01,260
我们之前写好的这个

347
00:14:01,260 --> 00:14:04,280
这个啊 login ，这个 HLER 

348
00:14:04,280 --> 00:14:09,560
所以就是 handle 这个包下面的 login 

349
00:14:09,560 --> 00:14:13,025
同理，比如说你在这个修改密码

350
00:14:13,025 --> 00:14:16,910
这个 Y 上面点击了那个提交按钮之后

351
00:14:16,910 --> 00:14:19,127
它对应的是另外一个路径

352
00:14:19,127 --> 00:14:20,990
那么它对应的 handler 呢

353
00:14:20,990 --> 00:14:25,580
就是 handler 包下面的 update password 这个接口

354
00:14:25,580 --> 00:14:31,040
那么当你请求了这个登录 SOBEAT 这个接口之后

355
00:14:31,040 --> 00:14:34,860
正常情况下浏览器会收到一个 cookie 

356
00:14:34,860 --> 00:14:36,905
cookie 里面包含了用户 id 

357
00:14:36,905 --> 00:14:41,080
然后大 van 去去提交这个接口的时候呢

358
00:14:41,080 --> 00:14:43,280
他会把那个带有用户 id 的 cookie 

359
00:14:43,280 --> 00:14:45,445
再回传给服务端

360
00:14:45,445 --> 00:14:48,750
我们先把这个 GI 的 server 先跑起来

361
00:14:48,750 --> 00:14:50,235
post 

362
00:14:50,235 --> 00:14:53,780
好，这边已经说开始监听55678了

363
00:14:53,780 --> 00:14:58,455
那么我们率先去访问 login 这个页面

364
00:14:58,455 --> 00:15:00,980
请求 local host 5678

365
00:15:00,980 --> 00:15:04,330
然后是 login 这个页面啊

366
00:15:04,330 --> 00:15:05,970
之前我已经注册了些用户了

367
00:15:05,970 --> 00:15:07,830
然后我直接登录吧

368
00:15:07,830 --> 00:15:13,110
用户名、密码登录啊

369
00:15:13,110 --> 00:15:15,430
这个是要跳出那个新闻列表页

370
00:15:15,430 --> 00:15:16,910
新闻列表咱们还没有开发

371
00:15:16,910 --> 00:15:17,870
所以这边是34

372
00:15:17,870 --> 00:15:19,380
这个不用管，没关系啊

373
00:15:19,380 --> 00:15:21,360
我们重点关心的是什么呢

374
00:15:21,360 --> 00:15:22,700
我们重点关心的是

375
00:15:22,700 --> 00:15:25,192
我们有没有收到这样一个 cookie 

376
00:15:25,192 --> 00:15:28,000
我们按住这个 F 12啊

377
00:15:28,000 --> 00:15:30,080
打开这个开发者工具

378
00:15:30,080 --> 00:15:33,907
然后这边我们来到这个应用程序这

379
00:15:33,907 --> 00:15:36,180
然后左侧啊，我看到有存储

380
00:15:36,180 --> 00:15:38,197
存储这边有 cookie 对吧

381
00:15:38,197 --> 00:15:39,710
那么从 cookie 这呢

382
00:15:39,710 --> 00:15:41,370
咱们发现有一个什么

383
00:15:41,370 --> 00:15:42,910
有一个 UID 啊

384
00:15:42,910 --> 00:15:45,180
UID 是 cookie 的名称

385
00:15:45,180 --> 00:15:47,035
value 呢是五

386
00:15:47,035 --> 00:15:51,750
因为这个 UID 就是我们当初在代码里面

387
00:15:51,750 --> 00:15:54,200
它登录成功之后

388
00:15:54,200 --> 00:15:57,900
咱们给他设置的就叫 UID 吗

389
00:15:57,900 --> 00:16:00,630
那这个 UID 应该等于五啊

390
00:16:00,630 --> 00:16:02,730
这个是我们设置的 DOI 对吧

391
00:16:02,730 --> 00:16:03,460
pass 

392
00:16:03,460 --> 00:16:05,390
然后是那个过期时间

393
00:16:05,390 --> 00:16:07,300
这个是一天嘛

394
00:16:07,300 --> 00:16:09,030
现在是2月28号

395
00:16:09,030 --> 00:16:10,950
那明天就是3月1号了啊

396
00:16:10,950 --> 00:16:12,645
一天就过期

397
00:16:12,645 --> 00:16:15,770
然后是 htp only 等于对号

398
00:16:15,770 --> 00:16:16,690
对号是 true 啊

399
00:16:16,690 --> 00:16:18,340
true ， true 的话看看啊

400
00:16:18,340 --> 00:16:23,760
我们当初设置的确实就是出一的前端 JS 

401
00:16:23,760 --> 00:16:26,510
它不能够去修改这个 cookie 

402
00:16:26,510 --> 00:16:29,300
然后我们修改这个 URL 啊

403
00:16:29,300 --> 00:16:34,132
咱们来请求一下那个 modify 修改密码这个页面

404
00:16:34,132 --> 00:16:37,310
好，我输入那个旧密码

405
00:16:37,310 --> 00:16:39,560
再输入新密码

406
00:16:39,560 --> 00:16:42,915
然后点击修改按钮

407
00:16:42,915 --> 00:16:45,532
竟然说密码修改成功

408
00:16:45,532 --> 00:16:47,800
这证明了我们的后端

409
00:16:47,800 --> 00:16:51,880
确实从那个库里面取到了 UID 

410
00:16:51,880 --> 00:16:54,360
我们实现这个 log out hander 

411
00:16:54,360 --> 00:16:56,120
但是咱们没有把它用起来啊

412
00:16:56,120 --> 00:16:57,710
来到 main 函数里边

413
00:16:57,710 --> 00:17:04,120
给他搞一个路由 engine 点 get 吧

414
00:17:04,120 --> 00:17:06,970
好，如果访问 logos 的话

415
00:17:06,970 --> 00:17:09,210
那么它对应的 handler 呢

416
00:17:10,339 --> 00:17:12,280
就是我们的 log out 

417
00:17:12,280 --> 00:17:15,340
这个 HLER 我们停掉

418
00:17:15,340 --> 00:17:18,062
把服务端重新再起一下

419
00:17:18,062 --> 00:17:19,310
打开浏览器

420
00:17:19,310 --> 00:17:20,890
我们再看一下应用程

421
00:17:20,890 --> 00:17:23,650
这边这个 cookie 还在对吧

422
00:17:23,650 --> 00:17:24,720
这个 cookie 一直在

423
00:17:24,720 --> 00:17:26,920
它是一个浏览器把它保存了吗

424
00:17:26,920 --> 00:17:27,079
啊

425
00:17:27,079 --> 00:17:28,720
跟你服务端有没有挂掉

426
00:17:28,720 --> 00:17:30,530
有没有重启没有丝毫关系

427
00:17:30,530 --> 00:17:32,920
只要这个到后期时期还没到

428
00:17:32,920 --> 00:17:34,240
他就一直在

429
00:17:34,240 --> 00:17:36,745
那么怎么能让他删掉呢

430
00:17:36,745 --> 00:17:40,420
那既然说刚才我们定义的路由是 logout 

431
00:17:40,420 --> 00:17:43,660
那我们直接去请求 log out 是吧

432
00:17:43,660 --> 00:17:44,975
通过 get 嘛

433
00:17:44,975 --> 00:17:47,110
通过 get 请求这个路径

434
00:17:47,110 --> 00:17:49,000
那么直接在 URL 里面呢

435
00:17:49,000 --> 00:17:52,320
去请求 logo out 这个路径

436
00:17:52,320 --> 00:17:54,400
我们注意观察一下这个 cookie 啊

437
00:17:54,400 --> 00:17:55,990
UID 还在不在

438
00:17:55,990 --> 00:17:57,100
好，注意看啊

439
00:17:57,100 --> 00:17:58,490
按一下回车

440
00:17:58,490 --> 00:18:01,905
你看这个 cookie 就没了

441
00:18:01,905 --> 00:18:05,160
那这样的话相当于是你已经退登录了嘛

442
00:18:05,160 --> 00:18:11,310
那如果你再去打开这个修改密码

443
00:18:11,310 --> 00:18:13,140
这个页面啊

444
00:18:13,140 --> 00:18:16,520
你再去输入你的旧密码

445
00:18:16,520 --> 00:18:17,810
输入新密码的话

446
00:18:17,810 --> 00:18:19,650
因为你没有用花 id 吗

447
00:18:19,650 --> 00:18:22,050
你点击这个按钮

448
00:18:22,050 --> 00:18:24,750
他说请先登录

449
00:18:24,750 --> 00:18:27,340
他从 cookie 里面拿不到那个用户 id 了
