1
00:00:00,800 --> 00:00:04,280
来看一下白酒或者 boost 这两种 KV 数据库

2
00:00:04,280 --> 00:00:07,800
他们提供出来的 API 接口长什么样子啊

3
00:00:07,800 --> 00:00:09,470
这节课仅作为了解

4
00:00:09,470 --> 00:00:12,440
因为如果工作当中你真的需要使用的话

5
00:00:12,440 --> 00:00:15,385
你可以直接把我这个代码拷贝过去就可以了

6
00:00:15,385 --> 00:00:16,990
我们来看一下 boot 

7
00:00:16,990 --> 00:00:20,460
那 boot 呢，这边使用的是这个库啊

8
00:00:20,460 --> 00:00:23,600
它是直接从 ETCD 里面搞出来的

9
00:00:23,600 --> 00:00:25,280
一个分支吧

10
00:00:25,280 --> 00:00:26,370
bot 

11
00:00:26,370 --> 00:00:29,710
好，呃，第十行呢，定义了一个 error 

12
00:00:29,710 --> 00:00:31,670
叫做数据不存在啊

13
00:00:31,670 --> 00:00:33,600
就这是这样的一个全局变量

14
00:00:33,600 --> 00:00:37,080
那我们认为数据不存在也是一种 error 

15
00:00:37,080 --> 00:00:39,820
这种思想在构变里面是非常常见的

16
00:00:39,820 --> 00:00:43,460
你比方说啊，我们在读那个 MYSQL 的时候啊

17
00:00:43,460 --> 00:00:44,640
GOM 里面

18
00:00:44,640 --> 00:00:47,700
GOM 里面它有一种 error 

19
00:00:47,700 --> 00:00:49,580
叫做

20
00:00:49,580 --> 00:00:53,120
好像是叫做什么 error 啊

21
00:00:53,120 --> 00:00:56,390
record not found 是吧

22
00:00:56,390 --> 00:00:57,530
就是你传一个

23
00:00:57,530 --> 00:00:58,770
比方说传一个 id 进去

24
00:00:58,770 --> 00:01:00,530
那这个 id 第二个就不存在

25
00:01:00,530 --> 00:01:01,810
就对应的记录不存在嘛

26
00:01:01,810 --> 00:01:03,390
它会返回这样一个 error 

27
00:01:03,390 --> 00:01:05,570
再比方说 REDIS 里面

28
00:01:05,570 --> 00:01:06,720
REDIS 里面呢

29
00:01:06,720 --> 00:01:09,260
你搞了一个根本不存在的一个 key 啊

30
00:01:09,260 --> 00:01:13,020
它也会返回 redis new 这样一种 error 

31
00:01:13,020 --> 00:01:14,720
所以呢，哎，我们认

32
00:01:14,720 --> 00:01:17,160
如果你去读这个 KV 数据库的话

33
00:01:17,160 --> 00:01:18,760
那对应的 K 不存在

34
00:01:18,760 --> 00:01:22,370
也我们也给他返回这样一个 NO gerror 

35
00:01:24,210 --> 00:01:27,540
好，这边这个 boot 是我自己写的一个结构体啊

36
00:01:27,540 --> 00:01:30,900
最核心的成员就是这个 boot 点 dB 啊

37
00:01:30,900 --> 00:01:34,560
就从这个第三方库这搞过来的个 bot 点 dB 啊

38
00:01:34,560 --> 00:01:35,660
后面这两个呢

39
00:01:35,660 --> 00:01:37,950
呃，是不太重要吧

40
00:01:37,950 --> 00:01:39,070
这个 pass 是说

41
00:01:39,070 --> 00:01:42,850
那么它存储在本地的哪一个目录下数据嘛

42
00:01:42,850 --> 00:01:44,580
这个 bucket ，呃

43
00:01:44,580 --> 00:01:45,880
bot 的一个特点

44
00:01:45,880 --> 00:01:48,920
就是说你可以把一个 bot 理解为是一个库嘛

45
00:01:48,920 --> 00:01:50,640
那这个库里有很多表对吧

46
00:01:50,640 --> 00:01:51,830
那这个表呢

47
00:01:51,830 --> 00:01:54,120
你需要指定这个表的名称啊

48
00:01:54,120 --> 00:01:55,942
它是一个字符串啊

49
00:01:55,942 --> 00:01:58,360
好，这是一个

50
00:01:58,360 --> 00:02:00,110
呃， builder 模式啊

51
00:02:00,110 --> 00:02:03,890
就是你要给这个什么啊， pass 啊、 bucket 啊

52
00:02:03,890 --> 00:02:04,450
赋值嘛

53
00:02:04,450 --> 00:02:06,650
通过这个位置函数啊

54
00:02:06,650 --> 00:02:10,330
这个 with 函数呢，返回的还是 bot 本身啊

55
00:02:10,330 --> 00:02:11,250
builder 模式

56
00:02:13,840 --> 00:02:14,760
要注释一下

57
00:02:14,760 --> 00:02:17,480
这是 builder 模式

58
00:02:22,310 --> 00:02:25,150
好像有一个这个打开函数啊

59
00:02:25,150 --> 00:02:27,115
我要打开这个数据库

60
00:02:27,115 --> 00:02:30,450
他首先拿到对应存储数据的目录

61
00:02:30,450 --> 00:02:32,290
然后通过这个 bot 啊

62
00:02:32,290 --> 00:02:33,850
这个 bot 是那个第三方库啊

63
00:02:33,850 --> 00:02:35,310
第三方库过来的这个 bot 

64
00:02:35,310 --> 00:02:37,960
好在这个包上面调用 open 函数

65
00:02:37,960 --> 00:02:39,940
把那个路径呢给传进来

66
00:02:39,940 --> 00:02:44,340
这个0 O 600是那个文件的权限设置吗

67
00:02:44,340 --> 00:02:45,760
那这个零欧啊

68
00:02:45,760 --> 00:02:48,380
这个零欧在构建里面的这个0 O 

69
00:02:48,380 --> 00:02:49,900
就是数字前面对吧

70
00:02:49,900 --> 00:02:52,240
600前面加这样一个0 O 

71
00:02:52,240 --> 00:02:54,320
0 O 表示是说八进制

72
00:02:54,320 --> 00:02:56,620
也就是说它只是我们啊

73
00:02:56,620 --> 00:02:59,900
后面这个600可不是你十进制里面的600

74
00:02:59,900 --> 00:03:02,262
是八进制里面的600

75
00:03:02,262 --> 00:03:06,630
它转成十进制之后应该是384啊

76
00:03:06,630 --> 00:03:10,110
那所以这个八经的前缀呢，用0 O 表示

77
00:03:10,110 --> 00:03:11,930
其实也可以把 O 删掉

78
00:03:11,930 --> 00:03:13,170
也可以用零表示

79
00:03:13,170 --> 00:03:14,670
也就是说这个零啊

80
00:03:14,670 --> 00:03:17,000
它其实也是一个代表八进制

81
00:03:17,000 --> 00:03:20,120
所以当你看到600的时候

82
00:03:20,120 --> 00:03:21,400
不要以为它就是600

83
00:03:21,400 --> 00:03:24,440
它实际上是384啊

84
00:03:24,440 --> 00:03:26,720
所以说，呃，为了避免混淆呢

85
00:03:26,720 --> 00:03:28,800
我们一般再加个 O 是吧

86
00:03:28,800 --> 00:03:30,490
这样的话你会更加的

87
00:03:30,490 --> 00:03:32,540
呃，警惕一些吧

88
00:03:32,540 --> 00:03:33,820
这是个八进制啊

89
00:03:33,820 --> 00:03:35,880
他第一次打开这个库之后的话

90
00:03:35,880 --> 00:03:39,620
会先去调用一个 create bucket 啊

91
00:03:39,620 --> 00:03:42,200
你不是会传一个表名吗

92
00:03:42,200 --> 00:03:43,420
啊， bucket 名称啊

93
00:03:43,420 --> 00:03:44,640
他要把这个 bucket 呢

94
00:03:44,640 --> 00:03:46,457
先创建一把

95
00:03:46,457 --> 00:03:49,010
所以这是一个初始化的工作吧

96
00:03:49,010 --> 00:03:51,127
放在 open 函数里面

97
00:03:51,127 --> 00:03:54,880
这是那个 get 成员变量啊

98
00:03:54,880 --> 00:03:57,487
直接把这个成员变量返回就可以了

99
00:03:57,487 --> 00:04:00,650
然后我们看看那些核心的什么 set get 

100
00:04:00,650 --> 00:04:02,020
它是怎么实现的

101
00:04:02,020 --> 00:04:03,990
对于 site 而言呢

102
00:04:03,990 --> 00:04:07,370
你把一个 KV 想写进 dB 里面去

103
00:04:07,370 --> 00:04:10,350
它是通过调这个 dB 啊

104
00:04:10,350 --> 00:04:12,710
这个 dB 就是我们上面写的这个

105
00:04:14,050 --> 00:04:15,750
bot 点 DB 好

106
00:04:15,750 --> 00:04:17,430
他在这个 dB 上面呢

107
00:04:17,430 --> 00:04:19,370
调用了一个 update 函数

108
00:04:19,370 --> 00:04:23,330
update 呢，又接受另外一个函数作为参数，对吧

109
00:04:23,330 --> 00:04:27,380
你看这个地方频繁的出现用函数作为参数

110
00:04:27,380 --> 00:04:28,840
这种情况啊

111
00:04:28,840 --> 00:04:30,112
好

112
00:04:30,112 --> 00:04:31,500
关键是啊

113
00:04:31,500 --> 00:04:34,070
这个函数参数内部

114
00:04:34,070 --> 00:04:35,490
它的参数又是一个什么

115
00:04:35,490 --> 00:04:37,970
又是一个事物 transaction 啊

116
00:04:37,970 --> 00:04:39,777
boot 里面的定义的一个事物

117
00:04:39,777 --> 00:04:41,450
他通过这个事物呢

118
00:04:41,450 --> 00:04:44,000
拿到这个 bucket 啊

119
00:04:44,000 --> 00:04:45,580
在 bucket 上面呢

120
00:04:45,580 --> 00:04:46,740
去掉这个 put 

121
00:04:46,740 --> 00:04:49,140
也就是它核心是通过 put 函数

122
00:04:49,140 --> 00:04:51,395
把 KV 给写进去的

123
00:04:51,395 --> 00:04:53,220
这是写入单条

124
00:04:53,220 --> 00:04:55,480
那写入多条呢， BT 对吧

125
00:04:55,480 --> 00:04:56,620
批量的写入啊

126
00:04:56,620 --> 00:04:59,690
批量写入的话跟单调协欧其实很类似了啊

127
00:04:59,690 --> 00:05:03,670
无非就是说啊，它上面是通过 update 

128
00:05:03,670 --> 00:05:06,210
这边呢是通过 batch 啊

129
00:05:06,210 --> 00:05:08,840
还是穿这样一个回调函数进来

130
00:05:08,840 --> 00:05:11,670
你不是多个 key 和多个 value 嘛

131
00:05:11,670 --> 00:05:15,130
这边呢，就去便利这些个 key value 啊

132
00:05:15,130 --> 00:05:16,660
对于每一个 KP 而言

133
00:05:16,660 --> 00:05:20,007
还是调用这个 put 函数吸进去

134
00:05:20,007 --> 00:05:22,920
这是写，那读呢

135
00:05:22,920 --> 00:05:24,912
读它是通过这个 view 

136
00:05:24,912 --> 00:05:26,910
写是通过 update 对吧

137
00:05:26,910 --> 00:05:28,280
都是通过 view 啊

138
00:05:28,280 --> 00:05:31,872
在 DEB 上面调用这个 view 

139
00:05:31,872 --> 00:05:36,430
好， view 这边呢，是通过这个事物拿到8 KET 

140
00:05:36,430 --> 00:05:38,690
然后拿到这个 get 函数啊

141
00:05:38,690 --> 00:05:40,010
把 key 给传进去

142
00:05:40,010 --> 00:05:42,280
根据 get 获得对应的 value 

143
00:05:42,280 --> 00:05:43,880
那如果 value 为空的话

144
00:05:43,880 --> 00:05:45,800
会返回我们最上面

145
00:05:45,800 --> 00:05:47,280
定义好的那个全局变量啊

146
00:05:47,280 --> 00:05:48,960
那个 error ， NO data error 

147
00:05:50,060 --> 00:05:51,100
批量读啊

148
00:05:51,100 --> 00:05:53,400
批量读的话还是调的这个 batch 

149
00:05:53,400 --> 00:05:54,500
batch 啊

150
00:05:54,500 --> 00:05:58,507
内部呢，是通过调这个 get 删除

151
00:05:58,507 --> 00:06:00,970
删除的话跟那个 size 就很类似了

152
00:06:00,970 --> 00:06:01,970
反正都是写嘛

153
00:06:01,970 --> 00:06:04,250
也是通过调 update 

154
00:06:04,250 --> 00:06:09,680
那内部呢，是通过调 delete 来实现批量删除啊， batch 

155
00:06:09,680 --> 00:06:12,180
然后遍历每一个 key 啊

156
00:06:12,180 --> 00:06:13,420
对每一个 key 来说

157
00:06:13,420 --> 00:06:17,140
还是调用 delete 来进行删除

158
00:06:17,140 --> 00:06:19,980
has 子来判断某一个 key 是否存在

159
00:06:19,980 --> 00:06:23,210
它本质上是掉的那个 get 啊

160
00:06:23,210 --> 00:06:25,210
只不过他先通过调 get 

161
00:06:25,210 --> 00:06:27,810
然后呢来看看这个 value 是否空

162
00:06:27,810 --> 00:06:28,730
如果 value 为空的话

163
00:06:28,730 --> 00:06:29,930
还返回说不存在

164
00:06:29,930 --> 00:06:31,450
返回一个 false 

165
00:06:32,780 --> 00:06:34,020
遍地 dB 啊

166
00:06:34,020 --> 00:06:36,080
变异的 dB 里面的每一条 k value 

167
00:06:36,080 --> 00:06:37,460
它是通过 view 

168
00:06:37,460 --> 00:06:38,880
因为你是读嘛

169
00:06:38,880 --> 00:06:40,192
通过 view 啊

170
00:06:40,192 --> 00:06:43,230
六的话，那还是啊，先拿到这个 bucket 

171
00:06:43,230 --> 00:06:45,180
然后在 bucket 上面

172
00:06:45,180 --> 00:06:46,330
这个箱盖是什么

173
00:06:46,330 --> 00:06:50,530
这个箱盖是我们之前讲过的迭代器模式

174
00:06:50,530 --> 00:06:53,442
迭代器模式啊，一推特

175
00:06:53,442 --> 00:06:55,360
当然他这个叫法可能不一样

176
00:06:55,360 --> 00:06:57,440
这个地方它叫做什么游标啊

177
00:06:57,440 --> 00:06:58,240
cursor 

178
00:06:58,240 --> 00:06:59,500
你可以把它想象为

179
00:06:59,500 --> 00:07:02,020
是我们之前讲过的那个 iterator 

180
00:07:02,020 --> 00:07:05,140
然后呢，这个 C 是一个带 G 嘛

181
00:07:05,140 --> 00:07:09,520
在 C 上面不停的调用 next 、 next 、 next ，哎

182
00:07:09,520 --> 00:07:11,702
来实现这个电力

183
00:07:11,702 --> 00:07:13,500
之前我们是看到说

184
00:07:13,500 --> 00:07:14,940
我们把这个 next 呢

185
00:07:14,940 --> 00:07:18,320
放在这个 for 循环体的最后一行代码

186
00:07:18,320 --> 00:07:19,080
而这里面呢

187
00:07:19,080 --> 00:07:23,430
它是把这个 next 放到了第二个分号后面，对吧

188
00:07:23,430 --> 00:07:24,710
就是第三部分嘛

189
00:07:24,710 --> 00:07:28,920
那这个根就是我把这个直接剪切下来

190
00:07:28,920 --> 00:07:31,150
直接放在这里，对吧

191
00:07:31,150 --> 00:07:32,855
这完全等价的了

192
00:07:32,855 --> 00:07:34,530
只不过注意一下它这个地方

193
00:07:34,530 --> 00:07:37,370
这个 next 它返回的不是一个数据

194
00:07:37,370 --> 00:07:40,650
而是一个 key 和一个 value 

195
00:07:42,260 --> 00:07:44,040
拿到 K 1226之后的话

196
00:07:44,040 --> 00:07:45,540
你再扔给这个 FN 

197
00:07:45,540 --> 00:07:47,650
这个 FN 是你传进来

198
00:07:47,650 --> 00:07:49,050
这个回调函数

199
00:07:49,050 --> 00:07:51,630
就是你拿到一对 key 了之后

200
00:07:51,630 --> 00:07:53,530
你想怎么处置它啊

201
00:07:53,530 --> 00:07:56,720
由外部调用方传进来

202
00:07:56,720 --> 00:08:01,115
大部分情况下都是直接去打印这个 KYL 啊

203
00:08:01,115 --> 00:08:05,170
也可能是基于这个 key value 进行一些统计工作

204
00:08:05,170 --> 00:08:08,410
比方说我已经把所有数据啊

205
00:08:08,410 --> 00:08:10,130
所有的视频数据

206
00:08:10,130 --> 00:08:13,240
都存到我的 K 1个数据库里面去了

207
00:08:13,240 --> 00:08:15,260
那么呢，我想把整个数据库遍历一遍

208
00:08:15,260 --> 00:08:16,620
我想统计一下

209
00:08:16,620 --> 00:08:21,770
比方说播放量在1万以上的视频有几个对吧

210
00:08:21,770 --> 00:08:24,550
可以通过这个变 B 的函数吗

211
00:08:24,550 --> 00:08:26,930
那么你那个所谓的呃

212
00:08:26,930 --> 00:08:28,910
if 什么播放量大于一半

213
00:08:28,910 --> 00:08:30,900
就让某一个数据加加

214
00:08:30,900 --> 00:08:31,990
这些操作

215
00:08:31,990 --> 00:08:35,950
就完全可以封装在 FN 这个函数里面

216
00:08:37,419 --> 00:08:40,360
好下面一个是说只关心 K 啊

217
00:08:40,360 --> 00:08:41,419
只关心 K 

218
00:08:42,659 --> 00:08:46,540
最后呢，是把这个 dB 给关闭掉， close 掉

219
00:08:47,630 --> 00:08:50,930
因为大部分的这种本地型的 KV 数据库的话

220
00:08:50,930 --> 00:08:54,320
它只允许单进程去使用啊

221
00:08:54,320 --> 00:08:56,760
如果你这个 go 进程没有把它关闭了的话

222
00:08:56,760 --> 00:08:58,060
那么另外一个进程呢

223
00:08:58,060 --> 00:08:59,720
或者说下次你重启的时候

224
00:08:59,720 --> 00:09:04,525
你会发现那个数据文件它直接不让你用

225
00:09:04,525 --> 00:09:05,820
这是 boot 

226
00:09:05,820 --> 00:09:08,880
那 badger 对应的代码在这个地方啊

227
00:09:08,880 --> 00:09:13,100
那其实 badger 跟这个 boot API 很类似了

228
00:09:13,100 --> 00:09:15,120
因为都是 KV 型数据库嘛

229
00:09:15,120 --> 00:09:17,540
所以你看我们大概瞟一眼啊

230
00:09:17,540 --> 00:09:20,160
你看它这里面的话，呃

231
00:09:20,160 --> 00:09:22,940
也是用的所谓的什么事物对吧

232
00:09:22,940 --> 00:09:25,100
也也是什么 update 

233
00:09:25,100 --> 00:09:29,262
然后读的话也是使用的 view 

234
00:09:29,262 --> 00:09:31,230
那其实我们也不需要去关心

235
00:09:31,230 --> 00:09:33,410
它里面具体的这个语法怎么写的

236
00:09:33,410 --> 00:09:34,130
如果真的需要的话

237
00:09:34,130 --> 00:09:36,370
你可以直接把我这个代码给拷过去

238
00:09:36,370 --> 00:09:37,610
这样的话对你而言

239
00:09:37,610 --> 00:09:40,290
你只需要去关心这些什么

240
00:09:40,290 --> 00:09:42,890
get set 函数该怎么传就可以了
