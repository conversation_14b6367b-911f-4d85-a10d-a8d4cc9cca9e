1
00:00:00,560 --> 00:00:02,880
这节课讲一讲分布式锁

2
00:00:02,880 --> 00:00:04,860
什么时候会用到分布式锁呢

3
00:00:04,860 --> 00:00:06,200
根据我的经验来看

4
00:00:06,200 --> 00:00:08,480
通常是定时任务啊

5
00:00:08,480 --> 00:00:11,810
比方说我们有一个每天凌晨需要执行的

6
00:00:11,810 --> 00:00:12,880
一个定时任务

7
00:00:12,880 --> 00:00:13,990
大家会怎么做呢

8
00:00:13,990 --> 00:00:16,190
可能你会说我写一个 main 函数

9
00:00:16,190 --> 00:00:19,930
然后 go build 把它打包成一个可执行文件

10
00:00:19,930 --> 00:00:22,540
然后通过 LINUX 的 chrome tab ，对吧

11
00:00:22,540 --> 00:00:23,540
写一个 chrome tab 

12
00:00:23,540 --> 00:00:25,867
然后每天凌晨去执行它

13
00:00:25,867 --> 00:00:27,620
那这种方式的话

14
00:00:27,620 --> 00:00:29,110
存在两个问题

15
00:00:29,110 --> 00:00:31,050
第一个问题是说，呃

16
00:00:31,050 --> 00:00:33,800
一旦我们在迁移服务器的时候

17
00:00:33,800 --> 00:00:36,952
很可能会把这个孔 tab 把它给漏掉

18
00:00:36,952 --> 00:00:38,220
第二个问题是说

19
00:00:38,220 --> 00:00:39,180
很多情况下

20
00:00:39,180 --> 00:00:40,610
尤其在大公司里面

21
00:00:40,610 --> 00:00:44,110
我们普通的开发人员是没有这个权限的

22
00:00:44,110 --> 00:00:46,990
也就是说他不给你服务器的这个权限

23
00:00:46,990 --> 00:00:51,280
你不能去服务器上自己去创建一个 context 命令

24
00:00:51,280 --> 00:00:52,740
那我的习惯是说

25
00:00:52,740 --> 00:00:54,020
我会把这个任务啊

26
00:00:54,020 --> 00:00:56,380
直接放到我的微服务里面去

27
00:00:56,380 --> 00:00:57,820
通过我的 go 代码是吧

28
00:00:57,820 --> 00:01:00,120
去起一个定时任务

29
00:01:00,120 --> 00:01:02,840
但这种方式在另外一个问

30
00:01:02,840 --> 00:01:03,340
就是

31
00:01:03,340 --> 00:01:07,000
我们的微服务是部署在多台服务器上面的

32
00:01:07,000 --> 00:01:09,280
如果你在你的竞争里面

33
00:01:09,280 --> 00:01:10,860
起了这样一个定义任务的话

34
00:01:10,860 --> 00:01:13,020
那么他会多台服务器

35
00:01:13,020 --> 00:01:15,320
同时去执行这个定时任务

36
00:01:15,320 --> 00:01:18,015
很多时候这个是不符合预期的

37
00:01:18,015 --> 00:01:20,680
所以呢，就需要来一把锁，对吧

38
00:01:20,680 --> 00:01:21,680
分布式嘛

39
00:01:21,680 --> 00:01:23,580
那大家在同一个时间点上

40
00:01:23,580 --> 00:01:25,470
都去争抢这一把锁

41
00:01:25,470 --> 00:01:26,490
谁获得

42
00:01:26,490 --> 00:01:28,330
谁就去执行这个电线任务

43
00:01:28,330 --> 00:01:29,110
没获得

44
00:01:29,110 --> 00:01:31,607
你就什么都不做就可以了

45
00:01:31,607 --> 00:01:32,520
当然了

46
00:01:32,520 --> 00:01:35,060
分布式锁的实现有很多种了

47
00:01:35,060 --> 00:01:37,940
我们前面讲过 REDIS 可以实现啊

48
00:01:37,940 --> 00:01:39,260
LUKEEPER 也可以实现

49
00:01:39,260 --> 00:01:42,260
甚至通过 MYSQL 也可以实现分布式锁

50
00:01:42,260 --> 00:01:43,020
那今天呢

51
00:01:43,020 --> 00:01:43,920
我们讲一

52
00:01:43,920 --> 00:01:47,415
基于 ETCD 的实现方案

53
00:01:47,415 --> 00:01:50,630
好，大家看这一段代码啊

54
00:01:50,630 --> 00:01:52,600
如果想实现锁的话呢

55
00:01:52,600 --> 00:01:56,395
第一步是要先创建一个 session 

56
00:01:56,395 --> 00:01:58,700
这个 concurrency 是怎么来的

57
00:01:58,700 --> 00:01:59,840
这个 concurrency 

58
00:01:59,840 --> 00:02:03,320
它也是从 EDCD 这个包下面出来的

59
00:02:03,320 --> 00:02:04,360
一个 concurrency 啊

60
00:02:04,360 --> 00:02:07,120
你把这个 EDCD 的 client 传进来

61
00:02:07,120 --> 00:02:09,069
会创建一个 session 

62
00:02:09,069 --> 00:02:13,080
然后呢，你去创建一个什么 new 一个锁

63
00:02:13,080 --> 00:02:14,600
这个 new text 就是锁嘛

64
00:02:14,600 --> 00:02:15,260
new 一个锁

65
00:02:15,260 --> 00:02:17,700
然后把这个 station 传进来

66
00:02:17,700 --> 00:02:20,590
然后把锁的名称给传进来是吧

67
00:02:20,590 --> 00:02:21,710
你多个携程

68
00:02:21,710 --> 00:02:24,030
多个进程去争抢同一把锁

69
00:02:24,030 --> 00:02:27,050
这个锁就是由它的这个锁的名

70
00:02:27,050 --> 00:02:27,710
来进行

71
00:02:27,710 --> 00:02:28,590
唯一的标识嘛

72
00:02:28,590 --> 00:02:30,710
实际上就是一个字符串

73
00:02:30,710 --> 00:02:34,930
那实际上就是这个 EDCD 上面的一个 K 

74
00:02:34,930 --> 00:02:36,207
仅此而已

75
00:02:36,207 --> 00:02:38,720
好，这样的话呢，就会返回一把锁

76
00:02:38,720 --> 00:02:43,250
你通过调这个 try lock 去尝试去获得这个锁

77
00:02:43,250 --> 00:02:44,550
注意是 TRILOCK 啊

78
00:02:44,550 --> 00:02:45,770
他只是试一下而已

79
00:02:45,770 --> 00:02:47,700
但他并没那么执着啊

80
00:02:47,700 --> 00:02:49,330
如果说他发现，诶

81
00:02:49,330 --> 00:02:50,650
我获得不到锁

82
00:02:50,650 --> 00:02:54,007
所以已经被其他西城或者京城持有了

83
00:02:54,007 --> 00:02:55,080
那他也不执着

84
00:02:55,080 --> 00:02:56,160
他很快就放弃了

85
00:02:56,160 --> 00:02:59,040
他直接就他立即会返回一个什么

86
00:02:59,040 --> 00:03:00,325
会返回一个 error 

87
00:03:00,325 --> 00:03:01,860
返回一个什么 error 呢

88
00:03:01,860 --> 00:03:04,700
会返回一个 concurrency errolocked 

89
00:03:04,700 --> 00:03:06,100
那会返回这样一个 error 

90
00:03:06,100 --> 00:03:08,900
就表示说所被其他人秀了嘛

91
00:03:08,900 --> 00:03:11,135
我就放弃了

92
00:03:11,135 --> 00:03:11,740
好

93
00:03:11,740 --> 00:03:14,360
那如果说它返回的这个 L 路

94
00:03:14,360 --> 00:03:16,140
不是这个 L 的话

95
00:03:16,140 --> 00:03:17,747
是其他 L 的话

96
00:03:17,747 --> 00:03:20,450
那很可能是发生了某种系统性故障

97
00:03:20,450 --> 00:03:23,310
比方说 EEDCD 是不是挂了对吧

98
00:03:23,310 --> 00:03:24,770
对于这种 LL 的话

99
00:03:24,770 --> 00:03:27,250
我们显然是需要打一个日日啊

100
00:03:27,250 --> 00:03:29,210
把这个错信息给打出来啊

101
00:03:29,210 --> 00:03:30,930
如果是这种 L 的话

102
00:03:30,930 --> 00:03:33,390
你也不需要打什么错误日志

103
00:03:33,390 --> 00:03:36,180
那你就直接放个 G 就可以了

104
00:03:36,180 --> 00:03:39,200
好，整个 try lock 这个函数啊

105
00:03:39,200 --> 00:03:42,320
它就会把这个锁以及这个 session 

106
00:03:42,320 --> 00:03:44,810
进行一个返回啊

107
00:03:44,810 --> 00:03:46,970
返回如果说获得到锁了

108
00:03:46,970 --> 00:03:48,050
就返回这个 true 

109
00:03:48,050 --> 00:03:49,110
如果没有获得锁

110
00:03:49,110 --> 00:03:50,930
返回 false 啊

111
00:03:50,930 --> 00:03:52,800
这是封装了一个函数

112
00:03:52,800 --> 00:03:56,110
那么在上层怎么去用这个函数呢

113
00:03:56,110 --> 00:03:58,260
比方说我们写了一个定制任务

114
00:03:58,260 --> 00:03:59,660
在这个定义式任务里面

115
00:03:59,660 --> 00:04:02,180
怎么去调这个 TRL 口这个函数呢

116
00:04:02,180 --> 00:04:02,640
哦

117
00:04:02,640 --> 00:04:05,200
下面写了一个模拟定时任务的

118
00:04:05,200 --> 00:04:07,880
这样一个函数啊

119
00:04:09,080 --> 00:04:14,160
啊，首先呢，我会去创建一个 context 

120
00:04:14,160 --> 00:04:16,130
可以往这个 context 里面呢

121
00:04:16,130 --> 00:04:18,810
去传一个啊

122
00:04:18,810 --> 00:04:21,050
传一个参数吧

123
00:04:21,050 --> 00:04:22,250
这个参数名叫 id 

124
00:04:22,250 --> 00:04:25,530
参数值就是这个 routine 的 id 

125
00:04:25,530 --> 00:04:29,240
这个 routine id 是从外围传进来的

126
00:04:29,240 --> 00:04:30,150
什么意思呢

127
00:04:30,150 --> 00:04:32,500
就是将来呀，我这个函数啊

128
00:04:32,500 --> 00:04:35,240
test tlock 这个函数加上这个函数

129
00:04:35,240 --> 00:04:36,020
大家看一下

130
00:04:36,020 --> 00:04:38,520
在我的这个 main 函数里面

131
00:04:38,520 --> 00:04:40,640
我是要多次去调用的

132
00:04:40,640 --> 00:04:44,390
而且是单独放在一个集成里面去调用的

133
00:04:44,390 --> 00:04:45,630
那传的时候呢

134
00:04:45,630 --> 00:04:48,290
传 context 和 e DC d clien

135
00:04:48,290 --> 00:04:50,380
传这个锁的名称之外

136
00:04:50,380 --> 00:04:53,930
每一个携程会传一个 id 号啊

137
00:04:53,930 --> 00:04:54,790
123

138
00:04:54,790 --> 00:04:59,177
这个123就是给这个协程负的一个编号

139
00:04:59,177 --> 00:05:00,400
但是这个编号啊

140
00:05:00,400 --> 00:05:01,780
其实在构元里面

141
00:05:01,780 --> 00:05:05,800
你并不能够直接给一个携程去附一个

142
00:05:05,800 --> 00:05:07,250
所谓的 id 编号

143
00:05:07,250 --> 00:05:09,980
实际上它是通过什么

144
00:05:09,980 --> 00:05:13,540
我是通过给这个 contex

145
00:05:13,540 --> 00:05:16,080
往 context 里面传了一个参数

146
00:05:16,080 --> 00:05:17,320
id 参数啊

147
00:05:17,320 --> 00:05:19,990
把这个 id 给他传进来的

148
00:05:19,990 --> 00:05:20,690
好

149
00:05:20,690 --> 00:05:21,400
然后呢

150
00:05:21,400 --> 00:05:24,160
你把这个 context 会传递给上面

151
00:05:24,160 --> 00:05:26,660
刚刚写好的这个传 log 函数嘛

152
00:05:26,660 --> 00:05:26,900
对吧

153
00:05:26,900 --> 00:05:28,412
去获得这个锁

154
00:05:28,412 --> 00:05:31,450
如果这个布尔值 success 为 true 的话

155
00:05:31,450 --> 00:05:34,700
说明成功的获得到了锁，对吧

156
00:05:34,700 --> 00:05:35,240
好

157
00:05:35,240 --> 00:05:37,000
如果说获得锁的话呢

158
00:05:37,000 --> 00:05:39,370
我会去什么这个 sleep 啊

159
00:05:39,370 --> 00:05:44,120
这个 sleep 就是在模拟我们正常的定时任务里面

160
00:05:44,120 --> 00:05:45,760
该做哪些事情啊

161
00:05:45,760 --> 00:05:48,640
你就把它放到 slip 里边去执行就可以了

162
00:05:48,640 --> 00:05:50,570
执行完之后你需要什么

163
00:05:50,570 --> 00:05:52,410
需要把锁给释放掉是吧

164
00:05:52,410 --> 00:05:56,860
所以呢，需要通过这个 new text unlock 把锁给释放掉

165
00:05:56,860 --> 00:05:58,790
好，把所释放之后

166
00:05:58,790 --> 00:06:02,220
你当初不是还创建了一个 session 嘛，对吧

167
00:06:02,220 --> 00:06:04,640
这个地方还是需要把这个 session 

168
00:06:04,640 --> 00:06:06,780
同时把它给关闭掉啊

169
00:06:06,780 --> 00:06:10,122
该关闭的、该释放的全部关闭释放

170
00:06:10,122 --> 00:06:10,810
好

171
00:06:10,810 --> 00:06:12,810
如果你没有或 DOS 的话

172
00:06:12,810 --> 00:06:14,560
那你就什么都不做了，对吧

173
00:06:14,560 --> 00:06:18,140
这里面我们往 context 里面放了这样一个 id 

174
00:06:18,140 --> 00:06:18,920
参数的话

175
00:06:18,920 --> 00:06:20,920
那么当你去调用这个 TRILOCK 的时候

176
00:06:20,920 --> 00:06:22,060
在 TRILOG 里

177
00:06:22,060 --> 00:06:25,980
你可以通过 CTX 什么 context value 啊

178
00:06:25,980 --> 00:06:28,760
通过这种方式把这个 id 对应的值

179
00:06:28,760 --> 00:06:30,140
就是把这个值呢

180
00:06:30,140 --> 00:06:31,300
可以把它给取出来

181
00:06:31,300 --> 00:06:33,730
然后你可以进行一个日志的打印嘛

182
00:06:33,730 --> 00:06:35,280
这种方式 try log 

183
00:06:35,280 --> 00:06:38,040
就是说如果所被其他人所持有

184
00:06:38,040 --> 00:06:40,680
那么呢，我这边就立即返回下面

185
00:06:40,680 --> 00:06:43,940
还有一种就是如果说锁被其他人所撤的话

186
00:06:43,940 --> 00:06:45,990
我就一直等死等，对吧

187
00:06:45,990 --> 00:06:47,290
直到别人把锁释放了

188
00:06:47,290 --> 00:06:49,420
我赶紧再去抢这把锁

189
00:06:49,420 --> 00:06:51,960
诶，通过这个 lock 就可以了

190
00:06:51,960 --> 00:06:52,910
上面是什么

191
00:06:52,910 --> 00:06:55,070
上面是 try lock 啊

192
00:06:55,070 --> 00:06:57,460
下面呢，是这个 lock 

193
00:06:57,460 --> 00:06:58,842
仅此而已

194
00:06:58,842 --> 00:07:01,300
好，它代码跟上面代码是一模一样的

195
00:07:01,300 --> 00:07:02,280
一个是说

196
00:07:02,280 --> 00:07:03,800
呃，立即返回

197
00:07:03,800 --> 00:07:05,612
一个是说死等

198
00:07:05,612 --> 00:07:08,210
还有情况是你不想死等

199
00:07:08,210 --> 00:07:11,050
但是呢，你也不想立即放弃啊

200
00:07:11,050 --> 00:07:12,790
你想等一段时间

201
00:07:12,790 --> 00:07:15,450
比方说虽然当前获得不了锁

202
00:07:15,450 --> 00:07:17,740
但是呢，你想等个5分钟啊

203
00:07:17,740 --> 00:07:19,830
5分钟之后还获得不到锁

204
00:07:19,830 --> 00:07:20,770
你才放弃

205
00:07:20,770 --> 00:07:23,930
那么这个给等待设置一个超时时间

206
00:07:23,930 --> 00:07:24,690
怎么设呢

207
00:07:24,690 --> 00:07:25,730
也是可以的

208
00:07:25,730 --> 00:07:30,440
这是我们在调用 lock 函数去上锁的时候

209
00:07:30,440 --> 00:07:31,920
不要传一个 context 吗

210
00:07:31,920 --> 00:07:33,600
诶，你给这个 context 

211
00:07:33,600 --> 00:07:34,920
你看这个 context 是吧

212
00:07:34,920 --> 00:07:38,560
你给这个 context 设置一个超时时间

213
00:07:38,560 --> 00:07:39,930
比方说设置了100 ms 

214
00:07:39,930 --> 00:07:40,730
这样的话呢

215
00:07:40,730 --> 00:07:44,580
这个如果说所被其他人所持有

216
00:07:44,580 --> 00:07:45,620
那么你等的话

217
00:07:45,620 --> 00:07:48,430
最多只会等100 ms 啊

218
00:07:48,430 --> 00:07:50,690
如果100 ml 之后还是没有获得锁

219
00:07:50,690 --> 00:07:52,350
就会发生一个什么

220
00:07:52,350 --> 00:07:56,425
发生一个 deadline exit 这样一个错误

221
00:07:56,425 --> 00:07:58,230
所以一共是三种方式

222
00:07:58,230 --> 00:08:00,570
第一种方式是说如果获得不到锁

223
00:08:00,570 --> 00:08:01,880
就立即返回

224
00:08:01,880 --> 00:08:03,140
第二种方式是说

225
00:08:03,140 --> 00:08:04,280
如果获得不到手

226
00:08:04,280 --> 00:08:06,510
我就死等，一直等下去

227
00:08:06,510 --> 00:08:08,500
第三种方式是说

228
00:08:08,500 --> 00:08:10,380
如果获得不到锁，我就等

229
00:08:10,380 --> 00:08:12,870
但是呢，这个等是有一个超时时间的

230
00:08:12,870 --> 00:08:14,990
好，我们把这三种方式呢

231
00:08:14,990 --> 00:08:17,545
呃，转起来看看这个结果

232
00:08:17,545 --> 00:08:19,550
第一种方式我们发现呢

233
00:08:19,550 --> 00:08:21,960
7×2是他率先获导锁

234
00:08:21,960 --> 00:08:23,960
这样的话切成一和三

235
00:08:23,960 --> 00:08:25,220
他们没有获得所嘛

236
00:08:25,220 --> 00:08:27,680
所以呢，他们就立刻就退出了

237
00:08:27,680 --> 00:08:30,250
他们说所被其他人所持有啊

238
00:08:30,250 --> 00:08:31,470
最后呢，我发现二呢

239
00:08:31,470 --> 00:08:33,475
他把锁给释放了

240
00:08:33,475 --> 00:08:35,169
这种方式四等

241
00:08:35,169 --> 00:08:38,609
四等的话，率先由二他获得了锁

242
00:08:38,609 --> 00:08:42,227
然后呢，只有当二把锁释放掉之后

243
00:08:42,227 --> 00:08:45,400
因为一在因为一和三在四等嘛，对吧

244
00:08:45,400 --> 00:08:47,080
那这个时候一比较幸运

245
00:08:47,080 --> 00:08:48,287
他获得了数

246
00:08:48,287 --> 00:08:50,260
那三呢，在四等

247
00:08:50,260 --> 00:08:52,730
就当一他把锁释放掉之后

248
00:08:52,730 --> 00:08:54,305
三才获得了锁

249
00:08:54,305 --> 00:08:57,650
所以说如果每一个人都去自动的话

250
00:08:57,650 --> 00:09:01,480
最终的效果就是大家串行执行是吧

251
00:09:01,480 --> 00:09:03,380
只有你实施放火锁之后

252
00:09:03,380 --> 00:09:05,920
才有可能下一个人获得。所

253
00:09:05,920 --> 00:09:06,640
好

254
00:09:06,640 --> 00:09:07,980
第三种方式

255
00:09:07,980 --> 00:09:09,020
一比较幸运

256
00:09:09,020 --> 00:09:10,775
他率先获得了锁

257
00:09:10,775 --> 00:09:11,960
然后二呢

258
00:09:11,960 --> 00:09:13,680
它在指定时间内

259
00:09:13,680 --> 00:09:15,220
因为他设置了一个超时时间嘛，对吧

260
00:09:15,220 --> 00:09:16,660
在超时时时间之内

261
00:09:16,660 --> 00:09:17,840
他并没有获得锁

262
00:09:17,840 --> 00:09:19,150
所以呢，二就放弃了

263
00:09:19,150 --> 00:09:22,000
那三呢，也是在这个指定时间之内

264
00:09:22,000 --> 00:09:24,580
就那个100 ms 之内没有获得锁三呢

265
00:09:24,580 --> 00:09:25,380
也放弃

266
00:09:25,380 --> 00:09:28,410
最后一他才把锁给释放掉
