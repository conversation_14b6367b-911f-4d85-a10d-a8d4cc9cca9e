1
00:00:00,720 --> 00:00:02,440
上一节课我们讲了

2
00:00:02,440 --> 00:00:03,700
如何用 EDCD 

3
00:00:03,700 --> 00:00:07,750
来充当一个全局的参数配置中心

4
00:00:07,750 --> 00:00:09,930
那理解了那次课代码

5
00:00:09,930 --> 00:00:13,350
今天这次课服务的注册与发现就很好理解了

6
00:00:13,350 --> 00:00:15,710
你可以直接把服务类比到

7
00:00:15,710 --> 00:00:19,220
我们上次课讲的那个全局配置参数啊

8
00:00:19,220 --> 00:00:20,410
原理比较简单

9
00:00:20,410 --> 00:00:21,730
只不过今天这节课呢

10
00:00:21,730 --> 00:00:24,210
我们讲代码花的时间会多一些

11
00:00:24,210 --> 00:00:28,610
好，我们来看看如何去实现辅助测发现

12
00:00:28,610 --> 00:00:30,270
那其实是这样子的

13
00:00:30,270 --> 00:00:33,670
我们一个服务会布在多台服务器上面嘛，对吧

14
00:00:33,670 --> 00:00:36,730
那每台服务器配一个 IP 以及对应的端口号

15
00:00:36,730 --> 00:00:38,400
好，那个 IP 对应断块

16
00:00:38,400 --> 00:00:40,490
你就理解这里面的 server 

17
00:00:40,490 --> 00:00:41,245
好

18
00:00:41,245 --> 00:00:43,780
这个 server 它一旦记起来之后啊

19
00:00:43,780 --> 00:00:45,770
这服务启动成功之后呢

20
00:00:45,770 --> 00:00:48,490
他需要去向这个服务中心

21
00:00:48,490 --> 00:00:51,370
这个服务中心其实就是 ETCD 

22
00:00:51,370 --> 00:00:55,190
它需要向 EDCD 去写入一个 key 

23
00:00:55,190 --> 00:00:56,350
通过 put 是吧

24
00:00:56,350 --> 00:00:57,730
去写一个 key 

25
00:00:57,730 --> 00:00:59,020
这个 K 是什么呢

26
00:00:59,020 --> 00:01:02,637
这个 K 的取法啊，名称很有讲究

27
00:01:02,637 --> 00:01:03,630
比方说啊

28
00:01:03,630 --> 00:01:06,850
我们这个服务名称叫 hello service 

29
00:01:06,850 --> 00:01:08,430
好， hello service 是吧

30
00:01:08,430 --> 00:01:12,050
你做一个这个东西看上去像是个目录啊

31
00:01:12,050 --> 00:01:13,690
但它实际上就是一个字符串

32
00:01:13,690 --> 00:01:15,820
只不过我们是通过斜线

33
00:01:15,820 --> 00:01:18,430
将它字符串分割成了多个部分嘛，对吧

34
00:01:18,430 --> 00:01:21,137
第一部分是你这个服务的名称

35
00:01:21,137 --> 00:01:22,120
第二部分呢

36
00:01:22,120 --> 00:01:25,320
就是你这个 server 本机的 IP 和端口号

37
00:01:25,320 --> 00:01:30,100
你就把这个字母串作为 K 写进 etc 里面

38
00:01:30,100 --> 00:01:31,620
据说 value 可以置空

39
00:01:31,620 --> 00:01:33,382
我们不关心 value 

40
00:01:33,382 --> 00:01:37,210
好，那第二台服务它启动好之后

41
00:01:37,210 --> 00:01:40,170
他也要把自己的 IP 和端口号

42
00:01:40,170 --> 00:01:43,120
写到 etc 里面去啊

43
00:01:43,120 --> 00:01:43,660
当然了

44
00:01:43,660 --> 00:01:46,730
当这个 server 它在终止的时候

45
00:01:46,730 --> 00:01:49,170
比方说他收到一个 Q 信号对吧

46
00:01:49,170 --> 00:01:51,110
他需要把自己给停掉

47
00:01:51,110 --> 00:01:53,030
在零停掉之前

48
00:01:53,030 --> 00:01:58,047
他还需要去 EDC 上把自己呢这个 K 给删掉

49
00:01:58,047 --> 00:02:01,840
好，这是作为服务方需要做这么一件事情啊

50
00:02:01,840 --> 00:02:05,542
那作为客户端这个 RPC 的调用方

51
00:02:05,542 --> 00:02:09,759
他呢，他需要去查询这个 ETCD 

52
00:02:09,759 --> 00:02:12,960
按照这个服务的名称作为前缀啊

53
00:02:12,960 --> 00:02:14,320
他要去监听

54
00:02:14,320 --> 00:02:18,730
凡是以这个服务名称作为前缀的这些个 K 啊

55
00:02:18,730 --> 00:02:21,130
怕把这些 K 全部给读出来

56
00:02:21,130 --> 00:02:24,930
因为我们知道 ETCD 它本身支持什么

57
00:02:24,930 --> 00:02:27,370
支持按前缀查询，对吧

58
00:02:27,370 --> 00:02:29,820
这样的话他会把这些个 K 呢

59
00:02:29,820 --> 00:02:30,960
全部给读下来

60
00:02:30,960 --> 00:02:33,130
那这样的话他要知道哦

61
00:02:33,130 --> 00:02:34,330
这个 hello service 

62
00:02:34,330 --> 00:02:37,550
目前有三台服务器都可以提供这个服务

63
00:02:37,550 --> 00:02:40,300
那么呢，我采用某种服务的均衡算法

64
00:02:40,300 --> 00:02:44,295
随机的选择一台服务去调用就可以了

65
00:02:44,295 --> 00:02:46,050
好，这是 get 

66
00:02:46,050 --> 00:02:48,700
但是呢，除了 get 之外

67
00:02:48,700 --> 00:02:49,600
作为客户端

68
00:02:49,600 --> 00:02:53,080
还要去向 ETCD 上面的 JC

69
00:02:53,080 --> 00:02:55,152
安装一个监听器

70
00:02:55,152 --> 00:02:56,330
因为你不知道

71
00:02:56,330 --> 00:02:57,850
说不定过了一会儿对吧

72
00:02:57,850 --> 00:03:00,030
过了一会儿某台服务器挂了，对吧

73
00:03:00,030 --> 00:03:01,370
也可能过了一会儿

74
00:03:01,370 --> 00:03:04,130
一个 server 4加进来了都有可能

75
00:03:04,130 --> 00:03:07,690
所以呢，你就直接去监听这个 hello servic

76
00:03:07,690 --> 00:03:08,630
这样一个前缀啊

77
00:03:08,630 --> 00:03:12,370
不管他是挂了还是新的成员加进来了

78
00:03:12,370 --> 00:03:15,240
作为 client 都能够及时的感知到

79
00:03:15,240 --> 00:03:16,090
这样的话

80
00:03:16,090 --> 00:03:19,150
client 在下一次发起 RP 请的时候

81
00:03:19,150 --> 00:03:20,050
他就知道哦

82
00:03:20,050 --> 00:03:22,090
我应该从哪个集合里面去

83
00:03:22,090 --> 00:03:24,890
随机的选一个 server 进行调用

84
00:03:24,890 --> 00:03:28,150
这里面还有一点就是作为 server 端啊

85
00:03:28,150 --> 00:03:29,190
作为 server 端

86
00:03:29,190 --> 00:03:31,490
如果是他接触到一个 Q 信号

87
00:03:31,490 --> 00:03:32,780
它在零推出之前

88
00:03:32,780 --> 00:03:36,000
他可以主动的去把自己给删掉嘛

89
00:03:36,000 --> 00:03:37,900
那这个过程实际上就是注销

90
00:03:37,900 --> 00:03:39,520
是把自己给注销掉

91
00:03:39,520 --> 00:03:43,990
但是啊，但是有可能发的是一个 Q 9型号

92
00:03:43,990 --> 00:03:46,770
它没有机会去主动注销自己

93
00:03:46,770 --> 00:03:47,722
怎么办呢

94
00:03:47,722 --> 00:03:48,260
是吧

95
00:03:48,260 --> 00:03:48,860
这样的话

96
00:03:48,860 --> 00:03:52,340
那 etc 上面就一直会有他这个 key 

97
00:03:52,340 --> 00:03:54,620
那客户端就会去调查

98
00:03:54,620 --> 00:03:56,662
那发现都调不通，对吧

99
00:03:56,662 --> 00:03:58,130
所以这里面有个小技巧

100
00:03:58,130 --> 00:03:58,950
就是呢

101
00:03:58,950 --> 00:04:01,790
每一个 server 再去向 EETCD 

102
00:04:01,790 --> 00:04:03,790
注册自己的时候啊

103
00:04:03,790 --> 00:04:06,310
他这个 K 啊，它会自带一个有效期

104
00:04:06,310 --> 00:04:09,370
实际上就是借助于租约的方法啊

105
00:04:09,370 --> 00:04:11,710
这个租约呢，给他设置一个有效期

106
00:04:11,710 --> 00:04:13,090
比方说这个

107
00:04:13,090 --> 00:04:14,420
这个租期会比较短

108
00:04:14,420 --> 00:04:15,400
比方说两秒

109
00:04:15,400 --> 00:04:17,040
那两秒钟之后

110
00:04:17,040 --> 00:04:21,197
就意味着说这个 K 它会自动的被删掉

111
00:04:21,197 --> 00:04:23,310
那意味着说你两秒钟之后

112
00:04:23,310 --> 00:04:25,210
你必须记着两秒之

113
00:04:25,210 --> 00:04:27,820
你要赶紧再去什么再去续约啊

114
00:04:27,820 --> 00:04:28,800
把自己给叙事

115
00:04:28,800 --> 00:04:30,380
每隔两秒你就要续词

116
00:04:30,380 --> 00:04:31,730
每隔两秒就要叙事

117
00:04:31,730 --> 00:04:35,030
假如说某一次这个 server 3他挂了

118
00:04:35,030 --> 00:04:38,010
他挂了他自然就不会去续约嘛，对吧

119
00:04:38,010 --> 00:04:41,280
那这个 server 3最多连码之后

120
00:04:41,280 --> 00:04:44,000
就会自动的从这个上面被删掉

121
00:04:44,000 --> 00:04:45,950
那 client 呢，感知到哦

122
00:04:45,950 --> 00:04:47,090
一个 server 没了

123
00:04:47,090 --> 00:04:49,577
那下次呢，就不会去掉这个 server 了

124
00:04:49,577 --> 00:04:52,980
购代码之间我们划分成了客户端和服务端

125
00:04:52,980 --> 00:04:55,100
以及有一个公共的代码啊

126
00:04:55,100 --> 00:04:58,830
那客户端和服务端都会有一个服务中心

127
00:04:58,830 --> 00:05:01,110
因为服务中心在两端

128
00:05:01,110 --> 00:05:03,110
所负责的功能是不一样的

129
00:05:03,110 --> 00:05:03,990
在服务端

130
00:05:03,990 --> 00:05:04,990
这个 service hub 

131
00:05:04,990 --> 00:05:08,950
主要是给 server 提供一个注册和注销的接口

132
00:05:08,950 --> 00:05:10,120
而在 CD 端

133
00:05:10,120 --> 00:05:13,100
这个 service club 主要是帮他去查询

134
00:05:13,100 --> 00:05:14,907
有哪些存活的 server 

135
00:05:14,907 --> 00:05:17,090
common 里面放的是 cat 跟 server 

136
00:05:17,090 --> 00:05:19,322
他们公共需要读取的内容

137
00:05:19,322 --> 00:05:22,820
我们先看一下这个 common 点 go 里面啊

138
00:05:22,820 --> 00:05:25,490
首先不管是客户端还是服务端

139
00:05:25,490 --> 00:05:27,690
他们连的是同一个注册中心

140
00:05:27,690 --> 00:05:30,745
连的是同一个 ETCD 集群

141
00:05:30,745 --> 00:05:31,410
嗯

142
00:05:31,410 --> 00:05:34,670
然后呢，我们说那个每一个 serve

143
00:05:34,670 --> 00:05:37,480
它对应的 EDDK 那个 key 呢

144
00:05:37,480 --> 00:05:39,000
实际上是需要有一个前缀的

145
00:05:39,000 --> 00:05:41,640
因为通过前缀来区分不同的右嘛

146
00:05:41,640 --> 00:05:44,850
啊，比方说我们所有的微负注册中心

147
00:05:44,850 --> 00:05:49,560
全部施以这样一个字符串左前缀

148
00:05:49,560 --> 00:05:51,720
然后呢，下一级

149
00:05:51,720 --> 00:05:54,297
下一级就是具体的服务的名称

150
00:05:54,297 --> 00:05:57,650
再下一级就是具体的那个 server 的 IP 和端口号

151
00:05:57,650 --> 00:06:01,320
也就是说在我们的 ETTT 上面存储的 K 

152
00:06:01,320 --> 00:06:03,420
我举一个具体的 K 的例子啊

153
00:06:03,420 --> 00:06:04,240
是这样子的

154
00:06:04,240 --> 00:06:06,610
哎，你比方说下一级是什么

155
00:06:06,610 --> 00:06:07,730
hello service 啊

156
00:06:07,730 --> 00:06:09,000
你的服务的名称

157
00:06:09,000 --> 00:06:11,257
再下面一级呢

158
00:06:11,257 --> 00:06:13,520
是你的这个 IP 的名称啊

159
00:06:13,520 --> 00:06:15,500
IP 的名称叫 IP 1吧

160
00:06:15,500 --> 00:06:16,772
好

161
00:06:16,772 --> 00:06:19,010
下面还有一个 IP 2，对吧

162
00:06:19,010 --> 00:06:20,690
那可能下面还有一个 IP 3

163
00:06:21,770 --> 00:06:22,760
ip 3

164
00:06:22,760 --> 00:06:25,670
这只是 hello service 这一个服务而已

165
00:06:25,670 --> 00:06:26,990
当然我们在公司内部

166
00:06:26,990 --> 00:06:28,550
可能有很多个微服务嘛

167
00:06:28,550 --> 00:06:28,930
对吧

168
00:06:28,930 --> 00:06:30,230
那除了 hello service 

169
00:06:30,230 --> 00:06:32,190
可能还有其他 service 啊

170
00:06:32,190 --> 00:06:35,000
这是他的 key 的形式啊

171
00:06:35,000 --> 00:06:36,770
这边还写了一个方法

172
00:06:36,770 --> 00:06:39,210
主要是获取这个内网的 IP 

173
00:06:39,210 --> 00:06:43,490
就是因为我们的 server 再去向注册中心

174
00:06:43,490 --> 00:06:44,510
注册自己的时候

175
00:06:44,510 --> 00:06:47,510
他需要把自己的 IP 告诉注册中心对吧

176
00:06:47,510 --> 00:06:50,110
那他需要获取自己的这个内网 IP 吗

177
00:06:50,110 --> 00:06:51,180
为什么是内网 IP 呢

178
00:06:51,180 --> 00:06:53,160
因为我们一般这种加 PC 的话

179
00:06:53,160 --> 00:06:54,580
都是在公司内部

180
00:06:54,580 --> 00:06:56,420
在内网上去互相调用嘛

181
00:06:56,420 --> 00:06:58,472
所以你也不需要知道他的外网 IP 

182
00:06:58,472 --> 00:07:00,830
好，然后我们看这个 server 里面

183
00:07:00,830 --> 00:07:03,150
在 server 里面我们看这个 surface hub 

184
00:07:03,150 --> 00:07:07,400
它需要实现注册和注销这两个核心功能啊

185
00:07:07,400 --> 00:07:09,040
看一下这个 surface hub 

186
00:07:10,170 --> 00:07:12,650
surface hub 定义为一个结构体

187
00:07:12,650 --> 00:07:13,110
它呢

188
00:07:13,110 --> 00:07:16,220
也要有一个向 EEDCD 

189
00:07:16,220 --> 00:07:17,900
进行连接的一个 client 

190
00:07:17,900 --> 00:07:21,420
然后会有一个上报心跳的这样一个频率

191
00:07:21,420 --> 00:07:23,140
这个是一个整数啊

192
00:07:23,140 --> 00:07:24,930
它实际上就是说

193
00:07:24,930 --> 00:07:26,450
哎，每隔几秒钟

194
00:07:26,450 --> 00:07:29,170
我需要主动的向注册中心上报一下

195
00:07:29,170 --> 00:07:30,650
自己保持一下心跳嘛

196
00:07:30,650 --> 00:07:34,820
实际上也就等价于那个租约的那个倒计时间

197
00:07:34,820 --> 00:07:36,327
单位是秒

198
00:07:36,327 --> 00:07:39,220
好，这边呢，搞了一个全局变量啊

199
00:07:39,220 --> 00:07:40,140
service hub 对吧

200
00:07:40,140 --> 00:07:41,925
service tub ，它是一个

201
00:07:41,925 --> 00:07:44,200
额，不可导出的小写开头的嘛

202
00:07:44,200 --> 00:07:47,840
所以呢，下面需要有一个对应的函数

203
00:07:47,840 --> 00:07:50,957
构造函数来获得这个全局的这个变量

204
00:07:50,957 --> 00:07:52,630
而同时这个全局变量呢

205
00:07:52,630 --> 00:07:54,130
由于在这个构造函数里面呢

206
00:07:54,130 --> 00:07:54,970
他需要去什么

207
00:07:54,970 --> 00:07:58,210
他需要去连接到我的这个 ETCD 

208
00:07:58,210 --> 00:08:00,850
那这个连接操作显然只需要连一次嘛

209
00:08:00,850 --> 00:08:03,190
所以呢，我把它搞成一个单例模式

210
00:08:03,190 --> 00:08:04,330
那单例模式的话

211
00:08:04,330 --> 00:08:07,810
核心是通过一个 once 来实现

212
00:08:07,810 --> 00:08:11,000
好，无非是想构造这样一个结构体嘛

213
00:08:11,000 --> 00:08:14,820
无非是想给它这两个值赋值嘛

214
00:08:14,820 --> 00:08:18,840
那么这个心跳频率好，复制

215
00:08:18,840 --> 00:08:20,600
直接通过参数给它传进来

216
00:08:20,600 --> 00:08:22,560
关键是第一个，这个 client 

217
00:08:22,560 --> 00:08:25,350
这个 canst 的话就通过 ETCD 啊

218
00:08:25,350 --> 00:08:26,290
这个 new 是吧

219
00:08:26,290 --> 00:08:28,310
去创建这个连接嘛

220
00:08:28,310 --> 00:08:30,850
就连上我们的 ETD 服务器了

221
00:08:30,850 --> 00:08:32,990
好，下面来个核心函数

222
00:08:32,990 --> 00:08:33,970
一个是注册

223
00:08:33,970 --> 00:08:35,090
一个是注销啊

224
00:08:35,090 --> 00:08:37,450
这两个函数是给谁调用的

225
00:08:37,450 --> 00:08:40,200
是给我们的无房调用了对吧

226
00:08:40,200 --> 00:08:42,829
服务房他要去调用注册跟注销吗

227
00:08:42,829 --> 00:08:45,032
好，看一下怎么注册啊

228
00:08:45,032 --> 00:08:46,070
他注册的话

229
00:08:46,070 --> 00:08:49,550
首先需要把这个 service 名称给传进来

230
00:08:49,550 --> 00:08:51,910
就说我提供的是什么服务

231
00:08:51,910 --> 00:08:56,360
我提供的是 hello service 还是 login service 对吧

232
00:08:56,360 --> 00:08:57,310
副名称

233
00:08:57,310 --> 00:08:57,740
好

234
00:08:57,740 --> 00:08:58,860
然后是这个 and point 

235
00:08:58,860 --> 00:09:02,830
就是这个 server 端它自己的 IP 和端口号

236
00:09:02,830 --> 00:09:05,240
list 的 id 是那个租约 id 啊

237
00:09:05,240 --> 00:09:07,000
第一次注册的时候

238
00:09:07,000 --> 00:09:08,340
这个租约 id 是零

239
00:09:08,340 --> 00:09:09,980
因为第一次的时候还没有租约啊

240
00:09:09,980 --> 00:09:11,300
直接传零就可以了

241
00:09:11,300 --> 00:09:12,380
好，我们看一下啊

242
00:09:12,380 --> 00:09:13,420
第一次进来啊

243
00:09:13,420 --> 00:09:15,200
这个租 VID 肯定是零嘛

244
00:09:15,200 --> 00:09:17,102
好，走这个 E 分之

245
00:09:17,102 --> 00:09:18,010
他第一步呢

246
00:09:18,010 --> 00:09:20,150
他先去创建一个租约啊

247
00:09:20,150 --> 00:09:22,030
他在创建这个租约的时候呢

248
00:09:22,030 --> 00:09:24,250
需要指定这个租约的到期时间

249
00:09:25,420 --> 00:09:27,460
然后呢，他拿着这个租

250
00:09:27,460 --> 00:09:29,560
就准备把自己的

251
00:09:29,560 --> 00:09:31,740
把自己的这个 IP 啊

252
00:09:31,740 --> 00:09:33,935
放到这个 etc 上面

253
00:09:33,935 --> 00:09:35,760
好，这个 key 是什么呢

254
00:09:35,760 --> 00:09:39,600
这个 key 它包含了我的这个前缀啊

255
00:09:39,600 --> 00:09:42,520
就是大家看刚才看到的啊

256
00:09:42,520 --> 00:09:44,560
这个前缀对吧

257
00:09:44,560 --> 00:09:47,980
然后呢，再加上具体的服务的名称

258
00:09:47,980 --> 00:09:49,920
就是你这边传进来的服务名称

259
00:09:49,920 --> 00:09:51,320
再加上 and point 

260
00:09:51,320 --> 00:09:55,220
and point 就是你自己的这个 IP 和端口号嘛

261
00:09:55,220 --> 00:09:57,490
啊，服务端自己的 IP 、端口号啊

262
00:09:57,490 --> 00:09:58,750
把它们呢构成 K 

263
00:09:58,750 --> 00:09:59,090
这样的话

264
00:09:59,090 --> 00:10:02,540
把这个 K 放到我们的 ETCD 上面去

265
00:10:02,540 --> 00:10:03,960
通过 put 放上去

266
00:10:03,960 --> 00:10:06,875
value 我们不关心这空就可以了

267
00:10:06,875 --> 00:10:10,060
好，这是第一次啊，去注册自己

268
00:10:10,060 --> 00:10:13,990
但是呢，这个注册我们说第一次注算之后

269
00:10:13,990 --> 00:10:15,310
后面是吧

270
00:10:15,310 --> 00:10:16,590
因为他会到期嘛

271
00:10:16,590 --> 00:10:19,350
所以你要每隔这么几秒

272
00:10:19,350 --> 00:10:21,670
你要去把他把自己给续一次对吧

273
00:10:21,670 --> 00:10:22,537
续一次

274
00:10:22,537 --> 00:10:24,080
所以呢，这里面啊

275
00:10:24,080 --> 00:10:27,310
假如说你后面来给自己续命的时候

276
00:10:27,310 --> 00:10:30,430
你把这个类似 id 给我带过来

277
00:10:30,430 --> 00:10:31,350
因为第一次的话

278
00:10:31,350 --> 00:10:33,390
他会把这个 release id 返回嘛

279
00:10:33,390 --> 00:10:36,140
你看会把这个租个 id 返回好

280
00:10:36,140 --> 00:10:37,880
你把这个足 VID 带过来

281
00:10:37,880 --> 00:10:39,160
带过来之后的话

282
00:10:39,160 --> 00:10:42,190
哎，第二次这个足 vii id 就大于零了嘛

283
00:10:42,190 --> 00:10:45,040
这个时候呢，我直接拿着这个租 vii id 诶

284
00:10:45,040 --> 00:10:46,800
给你什么 keep live once 

285
00:10:46,800 --> 00:10:48,040
但注意是 once 啊

286
00:10:48,040 --> 00:10:50,190
就指续命只续一次

287
00:10:50,190 --> 00:10:51,430
而不是永久续

288
00:10:51,430 --> 00:10:52,290
只续一次

289
00:10:52,290 --> 00:10:53,210
好，续一次

290
00:10:53,210 --> 00:10:53,810
这样的话呢

291
00:10:53,810 --> 00:10:57,700
哎，你就又可以再存活这么长时间

292
00:10:57,700 --> 00:11:00,370
所以啊，这个 register 啊

293
00:11:00,370 --> 00:11:02,610
这个函数肯定是要周期性的

294
00:11:02,610 --> 00:11:04,470
就是每隔这么多秒

295
00:11:04,470 --> 00:11:08,450
你就要周期性的调一次这个 register 函数

296
00:11:09,550 --> 00:11:14,180
好，这就是服务端给自己去啊，注册啊

297
00:11:14,180 --> 00:11:17,260
如果说服务端发现有人想让他终止的话

298
00:11:17,260 --> 00:11:20,460
那他在临终之前需要把自己给注销掉

299
00:11:20,460 --> 00:11:21,520
怎么注销呢

300
00:11:21,520 --> 00:11:22,700
跟上面是一样的啊

301
00:11:22,700 --> 00:11:25,060
第一步是先把这个 EETCD 

302
00:11:25,060 --> 00:11:27,167
这个 K 先组织好对吧

303
00:11:27,167 --> 00:11:30,430
第一步是那个前缀加上 service 名称

304
00:11:30,430 --> 00:11:31,950
加上自己的 IP 和端口号

305
00:11:31,950 --> 00:11:33,750
然后呢，通过 delete ，诶

306
00:11:33,750 --> 00:11:36,117
把这个 key 删掉就可以了

307
00:11:36,117 --> 00:11:40,200
所以所谓的注册就是把这 K 放上去

308
00:11:40,200 --> 00:11:43,140
所谓的注销就是把这 K 呢，把这个删掉

309
00:11:43,140 --> 00:11:44,180
那仅此而已

310
00:11:44,180 --> 00:11:47,980
好，我们看一下具体的一个 GRPC server 

311
00:11:47,980 --> 00:11:49,160
它的完整代码

312
00:11:49,160 --> 00:11:51,130
好，我们直接看一个具体例子

313
00:11:51,130 --> 00:11:56,180
我们还是以之前讲过的这样一个 hello 服啊

314
00:11:56,180 --> 00:11:59,140
hello 服，你看这边定义了一个 port 文件嘛

315
00:11:59,140 --> 00:12:01,020
那么这个 hello service 对吧

316
00:12:01,020 --> 00:12:02,740
它里面有两个方法啊

317
00:12:02,740 --> 00:12:06,060
好，我们自己写了一个 server 啊

318
00:12:06,060 --> 00:12:07,140
my server 对吧

319
00:12:07,140 --> 00:12:09,540
然后他要去实现这个 login 和 say hello 

320
00:12:09,540 --> 00:12:11,420
这两个方法里面的实

321
00:12:11,420 --> 00:12:12,190
写的很简单

322
00:12:12,190 --> 00:12:13,730
我们不关心里面的具体实践

323
00:12:13,730 --> 00:12:16,530
只要关心它能够调通就可以，是吧

324
00:12:16,530 --> 00:12:18,150
那注意这里面 myself 呢

325
00:12:18,150 --> 00:12:20,620
你需要去继承这个类

326
00:12:20,620 --> 00:12:22,367
好，看一下幂函数

327
00:12:22,367 --> 00:12:23,300
main 函数呢

328
00:12:23,300 --> 00:12:25,660
第一个是通过这个 flag pass 啊

329
00:12:25,660 --> 00:12:27,120
这个是因为我们将来啊

330
00:12:27,120 --> 00:12:29,040
我们将来这个服务端

331
00:12:29,040 --> 00:12:31,280
我们要模拟一个分布式的

332
00:12:31,280 --> 00:12:33,860
我们的服务是布在多台服务器上面

333
00:12:33,860 --> 00:12:35,380
模拟这样一个分布式场景

334
00:12:35,380 --> 00:12:37,800
但是呢，我们只有一台服务器咋办呢

335
00:12:37,800 --> 00:12:40,220
所以我们只能是在一台服务器上面

336
00:12:40,220 --> 00:12:42,570
通过不同的端口号啊

337
00:12:42,570 --> 00:12:43,610
来起多个服务啊

338
00:12:43,610 --> 00:12:46,720
所以呢，这个端口号就没有写死在代码里面

339
00:12:46,720 --> 00:12:50,980
而是通过外围的这个参数给传进来的啊

340
00:12:50,980 --> 00:12:53,180
通过这个 flag 传进来

341
00:12:53,180 --> 00:12:54,150
到时候你看

342
00:12:54,150 --> 00:12:57,330
到时候我一起的时候是通过 go run 

343
00:12:57,330 --> 00:12:57,550
哎

344
00:12:57,550 --> 00:12:59,070
通过杠 pod 对吧

345
00:12:59,070 --> 00:13:00,870
你带一个额外参数啊

346
00:13:00,870 --> 00:13:03,520
我传个三个不同的的端口号

347
00:13:03,520 --> 00:13:05,940
这样的话呢，我就可以在本地

348
00:13:05,940 --> 00:13:08,660
相当于是我起了三个服务嘛

349
00:13:10,720 --> 00:13:13,600
拿到这个端口号之后啊

350
00:13:13,600 --> 00:13:16,800
这个端口号就通过这个啊， service port 

351
00:13:16,800 --> 00:13:19,260
就是你最上面搞的这个 service port 嘛

352
00:13:19,260 --> 00:13:20,585
好

353
00:13:20,585 --> 00:13:23,710
我呢，需要去监听这个端口号

354
00:13:23,710 --> 00:13:25,550
然后呢，哎， new 一个 server 

355
00:13:25,550 --> 00:13:28,670
然后呢，绑定这个接口对应的具体实践是吧

356
00:13:28,670 --> 00:13:32,800
上面代码跟我们以往写一个 jp server 1模一样

357
00:13:32,800 --> 00:13:33,760
没有任何区别

358
00:13:33,760 --> 00:13:35,595
关键下面对吧

359
00:13:35,595 --> 00:13:37,300
下面你最下面一行

360
00:13:37,300 --> 00:13:39,547
不是打算把服务给启动吗

361
00:13:39,547 --> 00:13:40,230
注意啊

362
00:13:40,230 --> 00:13:42,790
你这个服务启动如果启动成功的话

363
00:13:42,790 --> 00:13:44,880
它是一个阻塞状态，对吧

364
00:13:44,880 --> 00:13:46,670
它会阻塞到第76行

365
00:13:46,670 --> 00:13:47,930
永不退出啊

366
00:13:47,930 --> 00:13:48,450
所以说呢

367
00:13:48,450 --> 00:13:51,260
你必须在执行这一行代码之

368
00:13:51,260 --> 00:13:52,530
就去向什么

369
00:13:52,530 --> 00:13:56,440
向那个注册中心注册自己啊

370
00:13:56,440 --> 00:13:59,142
好，看下怎么去注册自己啊

371
00:13:59,142 --> 00:14:00,240
注册自己的话

372
00:14:00,240 --> 00:14:04,360
第一步你需要先获得到自己的这个内网 IP 

373
00:14:04,360 --> 00:14:04,800
对吧

374
00:14:04,800 --> 00:14:08,990
然后呢，你定一个你的这个心跳周期是三秒钟

375
00:14:08,990 --> 00:14:11,900
然后呢，去获得那个 surface hub 

376
00:14:11,900 --> 00:14:14,470
我们说这个是个什么单例模式嘛，对吧

377
00:14:14,470 --> 00:14:15,850
去获得 surface hub 啊

378
00:14:15,850 --> 00:14:18,480
你把 ETCD 的这个地址传进来

379
00:14:18,480 --> 00:14:20,580
把你的新角周期传进来

380
00:14:20,580 --> 00:14:22,060
获得这个注册中心

381
00:14:22,060 --> 00:14:26,120
然后呢，通过注册中心调这个 register ，哎

382
00:14:26,120 --> 00:14:28,320
把自己的给注册进去，对吧

383
00:14:28,320 --> 00:14:29,260
你看注册的时候

384
00:14:29,260 --> 00:14:32,162
你需要传你提供的是什么服务

385
00:14:32,162 --> 00:14:35,790
你自己的 IP 和你自己的这个端口号

386
00:14:35,790 --> 00:14:39,692
第一次注册那个租 UID 传零就可以了

387
00:14:39,692 --> 00:14:41,660
好，那第一次注算之后

388
00:14:41,660 --> 00:14:46,030
后面的话你还要周期性的反复的去注册自己

389
00:14:46,030 --> 00:14:47,670
也就是说去上报心跳嘛

390
00:14:47,670 --> 00:14:50,690
所以呢，这里面开了一个携程，对吧

391
00:14:50,690 --> 00:14:52,700
开了一个无限循环

392
00:14:52,700 --> 00:14:58,140
他呢，会，你看他会每次都反复的去注册自己

393
00:14:58,140 --> 00:15:01,190
而且这个时候他是会把这个租 UID 

394
00:15:01,190 --> 00:15:03,730
因为租 UIDD 4已经拿到了嘛

395
00:15:03,730 --> 00:15:05,845
把租 UID 呢给传过来

396
00:15:05,845 --> 00:15:07,570
相当于每次是续命嘛

397
00:15:07,570 --> 00:15:08,937
keep alive once 

398
00:15:08,937 --> 00:15:12,360
好，那每次注册好之后呢

399
00:15:12,360 --> 00:15:13,320
休息一段时间

400
00:15:13,320 --> 00:15:15,980
其实这个时间就是这个心跳时间

401
00:15:15,980 --> 00:15:17,060
只不过呢

402
00:15:17,060 --> 00:15:19,410
他比那个新调时

403
00:15:19,410 --> 00:15:20,990
稍微的短了那么一点点

404
00:15:20,990 --> 00:15:22,570
因为他害怕过期嘛，对吧

405
00:15:22,570 --> 00:15:23,450
短了一点点啊

406
00:15:23,450 --> 00:15:25,700
确保我能够在到期之前

407
00:15:25,700 --> 00:15:28,315
能够把自己给再续以自命

408
00:15:28,315 --> 00:15:29,660
好，这是一个

409
00:15:29,660 --> 00:15:31,400
因为这边是一个无限循环嘛

410
00:15:31,400 --> 00:15:31,600
所以呢

411
00:15:31,600 --> 00:15:34,620
你必须放到一个单独的子星里面去执行

412
00:15:34,620 --> 00:15:35,940
好，同时呢

413
00:15:35,940 --> 00:15:38,360
这边又开辟了一个单独的子集成

414
00:15:38,360 --> 00:15:39,640
这个主要是什么呢

415
00:15:39,640 --> 00:15:43,140
主要是说当通过 Q 啊

416
00:15:43,140 --> 00:15:47,482
你给我发送 SKT 和 st 个 term 这两个信号的时候

417
00:15:47,482 --> 00:15:49,650
这是操作系统层面的知识啦

418
00:15:49,650 --> 00:15:51,670
就是如果说你给我发送 Q 9的话

419
00:15:51,670 --> 00:15:54,610
那我们的进程收到这样一个信号之后

420
00:15:54,610 --> 00:15:56,830
他是无条件的必须立即结束

421
00:15:56,830 --> 00:15:59,600
但是如果是发送 SKT 和 sk term 

422
00:15:59,600 --> 00:16:00,660
这两个型号的话呢

423
00:16:00,660 --> 00:16:02,160
那作为应用程序

424
00:16:02,160 --> 00:16:04,610
它是可以有所选择的

425
00:16:04,610 --> 00:16:05,650
就说他可以知道啊

426
00:16:05,650 --> 00:16:07,370
我应该做什么事情

427
00:16:07,370 --> 00:16:10,440
那我甚至可以选择不退出对吧

428
00:16:10,440 --> 00:16:12,750
所以呢，他可以选择啊

429
00:16:12,750 --> 00:16:14,230
我收到这个信号之后

430
00:16:14,230 --> 00:16:16,390
我就去什么注销自己

431
00:16:16,390 --> 00:16:18,090
你看 UNREGISTER 啊

432
00:16:18,090 --> 00:16:19,690
我把自己呢给注销掉

433
00:16:19,690 --> 00:16:23,170
然后我才去执行这个退出操作啊

434
00:16:23,170 --> 00:16:25,460
退出操作好

435
00:16:25,460 --> 00:16:27,370
然后正式的去把启动服

436
00:16:27,370 --> 00:16:29,630
但这个启动服务也可能会失败

437
00:16:29,630 --> 00:16:31,030
最常见的就是什么

438
00:16:31,030 --> 00:16:33,090
就是端口号被占用了

439
00:16:33,090 --> 00:16:34,130
导致你起不来

440
00:16:34,130 --> 00:16:35,190
起不来的话

441
00:16:35,190 --> 00:16:38,550
诶，这个地方还是需要把自己给注销掉

442
00:16:38,550 --> 00:16:40,860
因为你在第一行代码之前

443
00:16:40,860 --> 00:16:42,100
已经去注册自己了

444
00:16:42,100 --> 00:16:43,400
结果发现起不来，起不

445
00:16:43,400 --> 00:16:46,240
还是要去把把自己给注销掉

446
00:16:48,150 --> 00:16:50,050
啊，这就是我们是吧

447
00:16:50,050 --> 00:16:51,630
服务端的 service hub 

448
00:16:51,630 --> 00:16:54,530
然后一个具体的服务端他需要做的事情

449
00:16:54,530 --> 00:16:57,627
好，然后看一下我们的客户端

450
00:16:57,627 --> 00:16:58,400
客户端

451
00:16:58,400 --> 00:17:00,360
我们看下客户端这个 service hub 

452
00:17:00,360 --> 00:17:02,000
需要提供什么功能啊

453
00:17:02,000 --> 00:17:03,560
客户端的这个 service hub 

454
00:17:03,560 --> 00:17:06,495
它需要提供的核心就一个功能

455
00:17:06,495 --> 00:17:11,700
就这个就是获得 server 的地址啊

456
00:17:11,700 --> 00:17:12,740
你起了三个 server 

457
00:17:12,740 --> 00:17:14,800
你要把这三个 server 地址呢告诉我

458
00:17:14,800 --> 00:17:15,880
好，我们看一下啊

459
00:17:15,880 --> 00:17:18,460
为了哈，为了提供这样一个函数啊

460
00:17:18,460 --> 00:17:21,458
它上面写了几个那个旧函数

461
00:17:21,458 --> 00:17:22,308
好

462
00:17:22,308 --> 00:17:23,680
我们看一下 can 端

463
00:17:23,680 --> 00:17:25,180
它这个 service club 呀

464
00:17:25,180 --> 00:17:26,880
它需要有一个啊

465
00:17:26,880 --> 00:17:29,520
client 去连接上我们的 ETCD 

466
00:17:29,520 --> 00:17:31,780
需要去连接上我们的注册中心吧

467
00:17:31,780 --> 00:17:35,100
另外还需要有一个 and point cash 

468
00:17:35,100 --> 00:17:37,700
这个呢，主要就是维护什么呢

469
00:17:37,700 --> 00:17:41,580
维护我们的服务端有几个 server 

470
00:17:41,580 --> 00:17:42,840
它是一个 map 对吧

471
00:17:42,840 --> 00:17:45,842
这个 map 的 key 就是那个 service name 

472
00:17:45,842 --> 00:17:48,170
而 value 呢，是一个切片

473
00:17:48,170 --> 00:17:51,365
就是每一个 server 对应的 IP 跟端口号嘛

474
00:17:51,365 --> 00:17:53,770
好，然后还有一个 watched 

475
00:17:53,770 --> 00:17:55,980
这个 watch 我们待会下面去讲啊

476
00:17:55,980 --> 00:17:58,090
不是一个关键的参数

477
00:17:58,090 --> 00:18:00,540
好，跟服装一样对吧

478
00:18:00,540 --> 00:18:03,640
这里面也是通过一个不可导出的全局变量

479
00:18:03,640 --> 00:18:06,660
然后通过一个单例的构造函数

480
00:18:06,660 --> 00:18:09,680
来获得这个 service hos 

481
00:18:09,680 --> 00:18:11,040
好，看下这个

482
00:18:11,040 --> 00:18:14,690
这个就是获得你把 service name 呢传进来

483
00:18:14,690 --> 00:18:18,090
比方说你把 hello service 或者把什么 login service 传进来

484
00:18:18,090 --> 00:18:19,520
我给你返回，诶

485
00:18:19,520 --> 00:18:22,550
有哪些服务器目前可以提供这个服务

486
00:18:22,550 --> 00:18:23,730
返回一个切片

487
00:18:23,730 --> 00:18:25,367
看下怎么做啊

488
00:18:25,367 --> 00:18:28,360
首先还是要根据这个 service 的名称呢

489
00:18:28,360 --> 00:18:31,620
去构造好这样一个 key 的前缀啊

490
00:18:31,620 --> 00:18:32,300
K 的前缀

491
00:18:32,300 --> 00:18:34,780
然后呢，哎，我要去 get 对吧

492
00:18:34,780 --> 00:18:37,040
你看这里面是 with prefix 

493
00:18:37,040 --> 00:18:41,470
也就是说我按照前缀去 ETCD 上面

494
00:18:41,470 --> 00:18:43,990
这个 client 是 e t CD client 对吧

495
00:18:43,990 --> 00:18:45,450
去 ETC 上面获得

496
00:18:45,450 --> 00:18:48,390
凡是满足这个前缀的所有 K 

497
00:18:48,390 --> 00:18:51,130
你看这里面就便利所有的 key 嘛，对吧

498
00:18:51,130 --> 00:18:55,050
把每一个 K 啊，都放到我的这个集合里面去

499
00:18:56,090 --> 00:18:58,530
那这个集合我最后就返回吗

500
00:18:58,530 --> 00:19:00,090
返回这个集合

501
00:19:00,090 --> 00:19:00,810
好

502
00:19:00,810 --> 00:19:03,210
这样的话就知道有哪些个服务器

503
00:19:03,210 --> 00:19:06,005
目前可以提供这个服务了

504
00:19:06,005 --> 00:19:07,790
下面还有一个 watch 

505
00:19:07,790 --> 00:19:09,630
就是安装监听器嘛，对吧

506
00:19:09,630 --> 00:19:10,330
安装监听器

507
00:19:10,330 --> 00:19:12,510
好，安装监听器的话呢

508
00:19:12,510 --> 00:19:14,755
这边啊，它会有一个

509
00:19:14,755 --> 00:19:17,530
哎，我们上面讲到这个边对吧

510
00:19:17,530 --> 00:19:20,217
这个 watch watched 就是说呀

511
00:19:20,217 --> 00:19:23,260
就因为这个安装监听器这个函数啊

512
00:19:23,260 --> 00:19:25,780
有可能会被反复的调用啊

513
00:19:25,780 --> 00:19:29,540
所以呢，我们就用一个 map 来判断一下

514
00:19:29,540 --> 00:19:31,520
就第一次安装监听的时候呢

515
00:19:31,520 --> 00:19:33,340
我把你放到这个 map 里面去

516
00:19:33,340 --> 00:19:35,880
第二次如果说你真的反复调用了

517
00:19:35,880 --> 00:19:38,390
我一看 map 里面已经有了

518
00:19:38,390 --> 00:19:40,290
哎，就不会去重复监听了嘛

519
00:19:40,290 --> 00:19:42,930
所以这个 watch 是这样一个功能啊

520
00:19:42,930 --> 00:19:44,920
所以呢，它通过是吧

521
00:19:44,920 --> 00:19:47,530
哎，通过 load wall store 

522
00:19:47,530 --> 00:19:50,650
也就是说他先去按照这个服务名称

523
00:19:50,650 --> 00:19:52,580
先去查询一下这个 map 

524
00:19:52,580 --> 00:19:53,870
如果有的话是吧

525
00:19:53,870 --> 00:19:56,030
如果有的话就直接返回了

526
00:19:56,030 --> 00:19:57,230
如果没有的话

527
00:19:57,230 --> 00:20:00,620
就把呢，就把你给加到这个 map 里面去

528
00:20:00,620 --> 00:20:02,700
所以这个是 load word store 

529
00:20:02,700 --> 00:20:04,510
它的这个功能啊

530
00:20:04,510 --> 00:20:06,320
如果没有就放进去

531
00:20:06,320 --> 00:20:07,400
如果有的话

532
00:20:07,400 --> 00:20:09,500
这个 EXEIS 就 true 嘛

533
00:20:09,500 --> 00:20:11,510
好，看一下啊

534
00:20:11,510 --> 00:20:12,590
如何去

535
00:20:12,590 --> 00:20:14,130
就如果说没有的话

536
00:20:14,130 --> 00:20:16,360
就不会执行这个 return 

537
00:20:16,360 --> 00:20:17,420
就会往下执行

538
00:20:17,420 --> 00:20:18,800
那我就去监听嘛

539
00:20:18,800 --> 00:20:19,700
看下怎么监听

540
00:20:19,700 --> 00:20:22,740
首先呢，还是获得这样一个 key 的前缀

541
00:20:22,740 --> 00:20:25,550
然后呢，你看当初你这个 get 对吧

542
00:20:25,550 --> 00:20:28,780
可以按照前缀去获取一大把

543
00:20:28,780 --> 00:20:30,490
这个地方呢，监听

544
00:20:30,490 --> 00:20:33,750
也可以按照前缀去监听一大把

545
00:20:33,750 --> 00:20:37,007
凡是有这个前缀的全部执行

546
00:20:37,007 --> 00:20:38,110
真听

547
00:20:38,110 --> 00:20:38,820
好

548
00:20:38,820 --> 00:20:43,290
那监听一旦这些个 K 它们发生更新啊

549
00:20:43,290 --> 00:20:44,990
新增啊、删除啊

550
00:20:44,990 --> 00:20:46,430
发生这些事件的时候

551
00:20:46,430 --> 00:20:49,310
这些事件全部会放到这个 CH 

552
00:20:49,310 --> 00:20:50,850
放到这个 channel 里面去

553
00:20:50,850 --> 00:20:54,250
这边呢，我就不停的去读这个 channel 啊

554
00:20:54,250 --> 00:20:57,580
这个 for 循环它是一个永不退出的无限循环

555
00:20:57,580 --> 00:21:00,740
所以呢，你需要单独的开辟一个子线

556
00:21:00,740 --> 00:21:02,112
去执行它

557
00:21:02,112 --> 00:21:04,600
好，我取出这些事件啊

558
00:21:04,600 --> 00:21:07,150
那么其实我也不关心是什么事情了

559
00:21:07,150 --> 00:21:09,810
不管你是更新时间还是删除事件啊

560
00:21:09,810 --> 00:21:12,320
只要有事情发生怎么样哈

561
00:21:12,320 --> 00:21:14,280
我就盲目的啊

562
00:21:14,280 --> 00:21:17,760
我就全部会直接全量的再去调

563
00:21:17,760 --> 00:21:19,447
上面写好的这个函数

564
00:21:19,447 --> 00:21:20,570
这个函数啊

565
00:21:20,570 --> 00:21:24,380
再去跟那个 ETCD 同步一次啊

566
00:21:24,380 --> 00:21:26,880
因为我之所以跟 etc 的同步

567
00:21:26,880 --> 00:21:31,797
无非就是想更新我本地的这个变量嘛，对吧

568
00:21:31,797 --> 00:21:35,710
哎，我这个 service 对应的是这三台服务器

569
00:21:35,710 --> 00:21:38,590
我另外一个 service 对应的是另外五台

570
00:21:38,590 --> 00:21:40,070
五台服务器对吧

571
00:21:40,070 --> 00:21:43,310
所以全部是在这个 map 里面进行维护的

572
00:21:43,310 --> 00:21:46,530
现在我发现某一个 servic

573
00:21:46,530 --> 00:21:48,690
他们的 key 有变化

574
00:21:48,690 --> 00:21:52,430
那我直接把以这个为前缀的所有 K 

575
00:21:52,430 --> 00:21:53,550
全部再同步一遍

576
00:21:53,550 --> 00:21:55,675
就跟我本地的

577
00:21:55,675 --> 00:21:58,530
跟我本地的这个 end point cash 

578
00:21:58,530 --> 00:22:00,760
这个 map 整体同步一次嘛

579
00:22:00,760 --> 00:22:03,960
所以说上面两个都是那个 seed 的

580
00:22:03,960 --> 00:22:05,120
都是小写开头的

581
00:22:05,120 --> 00:22:05,930
那么最终

582
00:22:05,930 --> 00:22:12,320
最终他提供出去的是这个 get service and point with k with cash 

583
00:22:12,320 --> 00:22:14,387
好，看一下啊

584
00:22:14,387 --> 00:22:17,300
呃，就说在 client 里面

585
00:22:17,300 --> 00:22:19,260
它实际上会去调用这个函数嘛

586
00:22:19,260 --> 00:22:21,100
你把 service 名称传进来

587
00:22:21,100 --> 00:22:23,760
返回对应的那个 server 的地址

588
00:22:23,760 --> 00:22:27,100
那第一步呢，他会先去监听啊

589
00:22:27,100 --> 00:22:31,560
监听以这个 service 名称为前缀的 SK 去监听

590
00:22:31,560 --> 00:22:32,495
当然了

591
00:22:32,495 --> 00:22:34,800
只有第一次监听会真正的监听

592
00:22:34,800 --> 00:22:35,540
第二次的话

593
00:22:35,540 --> 00:22:38,070
实际上在这个地方就返回了嘛

594
00:22:38,070 --> 00:22:39,697
啊，实际上就是说

595
00:22:39,697 --> 00:22:41,910
如果之前从来没有监听过

596
00:22:41,910 --> 00:22:43,170
先监听一次啊

597
00:22:43,170 --> 00:22:43,930
以后的话

598
00:22:43,930 --> 00:22:46,210
其实这一行就不怎么执行了

599
00:22:46,210 --> 00:22:47,852
就直接执行下面的啊

600
00:22:47,852 --> 00:22:52,070
好，那么这个服务它所对应的那几台 server 

601
00:22:52,070 --> 00:22:54,990
不是全放在我本地的这个 map 里面了吗

602
00:22:54,990 --> 00:22:58,167
我直接从 map 里面把它给读出来

603
00:22:58,167 --> 00:23:00,330
直接返回不就可以了吗

604
00:23:00,330 --> 00:23:02,420
那假如说发现我本地的

605
00:23:02,420 --> 00:23:03,420
就第一次的时候是吧

606
00:23:03,420 --> 00:23:05,420
第一次你调这个函数的时候

607
00:23:05,420 --> 00:23:09,330
那这个 surface name 它还不在我本地的这个 map 里面

608
00:23:09,330 --> 00:23:09,970
咋办呢

609
00:23:09,970 --> 00:23:12,252
那我就去执行上面的这个

610
00:23:12,252 --> 00:23:15,560
真正的去读 ETCD 的那个操作，对吧

611
00:23:15,560 --> 00:23:17,060
从 ETCT 上面

612
00:23:17,060 --> 00:23:20,260
把对应的那个 server 地址给读下来

613
00:23:20,260 --> 00:23:24,072
然后呢，放到我本地的这个缓存里面去

614
00:23:24,072 --> 00:23:27,070
然后把这些服务端地址呢，返回

615
00:23:27,070 --> 00:23:30,330
好，我们看这个 client 对吧

616
00:23:30,330 --> 00:23:34,150
client 啊，他打算去掉那个 hello service 

617
00:23:34,150 --> 00:23:36,680
好，我们看一下这个 main 函

618
00:23:36,680 --> 00:23:39,220
这边呢，是反复的去掉这个 RPC 

619
00:23:39,220 --> 00:23:40,500
那 RPC 里面呢

620
00:23:40,500 --> 00:23:41,120
第一步啊

621
00:23:41,120 --> 00:23:44,040
它去获得这个 GRPC 的 client 

622
00:23:44,040 --> 00:23:49,050
然后呢，通过这个 j IP c cat 去调一个 login 

623
00:23:49,050 --> 00:23:52,200
login 的话就是我们在那个 PROTO 文件里面

624
00:23:52,200 --> 00:23:55,150
PROTO 文件里面不是指定了两个方法可以调吗

625
00:23:55,150 --> 00:23:56,850
那这里面作为演示啊

626
00:23:56,850 --> 00:23:59,640
就仅仅是去调了一下这个 login 

627
00:23:59,640 --> 00:24:01,050
我们不关心这个结果啊

628
00:24:01,050 --> 00:24:02,690
我们只关心我们核心

629
00:24:02,690 --> 00:24:04,980
关心的是这行代码

630
00:24:04,980 --> 00:24:07,890
也就是说呀，他实际上他每次啊

631
00:24:07,890 --> 00:24:11,290
他每次在执行这个 RPCCC 调用之前啊

632
00:24:11,290 --> 00:24:14,320
他都要去获得一把这个 client 

633
00:24:14,320 --> 00:24:16,050
在之前的代码里面呢

634
00:24:16,050 --> 00:24:17,670
我们这个 client 是写死的

635
00:24:17,670 --> 00:24:18,810
只有一个 client 

636
00:24:18,810 --> 00:24:20,110
但今天不一样

637
00:24:20,110 --> 00:24:23,100
今天我们的服务是有多台服务

638
00:24:23,100 --> 00:24:27,010
所以呢，这个 client 它可能是三个里面的某一个

639
00:24:27,010 --> 00:24:30,840
那如何去获得这个 client 呢

640
00:24:30,840 --> 00:24:35,170
其实啊，由于每一个 server 它都对应一个 content 嘛，对吧

641
00:24:35,170 --> 00:24:37,540
所以呢，我在这个地方啊

642
00:24:37,540 --> 00:24:39,367
搞了一个全局变量

643
00:24:39,367 --> 00:24:41,460
实际上这个全局变量里面呢

644
00:24:41,460 --> 00:24:43,910
就会维护三个 server 

645
00:24:43,910 --> 00:24:44,850
它们对应的 CLI 

646
00:24:44,850 --> 00:24:45,770
他是个 map 吧

647
00:24:45,770 --> 00:24:49,017
是 map 的 key 就是那个 server 的 IP 

648
00:24:49,017 --> 00:24:52,040
value 就是它对应的这个

649
00:24:53,240 --> 00:24:54,890
IDL 的 client 

650
00:24:54,890 --> 00:24:56,750
我们看一下啊

651
00:24:56,750 --> 00:24:57,760
首先啊

652
00:24:57,760 --> 00:25:01,780
我还是通过单例模式去获得这个 service hub 啊

653
00:25:01,780 --> 00:25:02,360
第二步呢

654
00:25:02,360 --> 00:25:04,940
我借助这个 surface hub ，哎，去获

655
00:25:04,940 --> 00:25:08,180
我把这个 hello service 这个服务名称传进来

656
00:25:08,180 --> 00:25:11,910
你告诉我目前哪几台 server 可以提供这个服务

657
00:25:11,910 --> 00:25:13,140
好，他告诉你啊

658
00:25:13,140 --> 00:25:15,390
有比方说有三台 server 对吧

659
00:25:15,390 --> 00:25:16,330
有三个 server 

660
00:25:16,330 --> 00:25:19,010
然后呢，我通过随机数，对吧

661
00:25:19,010 --> 00:25:21,740
随机数我随机的选择了一个 server 

662
00:25:21,740 --> 00:25:22,920
也就是说在这个地方

663
00:25:22,920 --> 00:25:24,840
我们是通过 client 

664
00:25:24,840 --> 00:25:28,060
来完成所谓的负载均衡功能啊

665
00:25:28,060 --> 00:25:31,180
这里面采用了一种最简单的负载均衡算法

666
00:25:31,180 --> 00:25:33,680
就是随机法对吧

667
00:25:33,680 --> 00:25:35,550
随机的选这台 server 

668
00:25:35,550 --> 00:25:37,470
我把这个 server 呢进行一个打印

669
00:25:37,470 --> 00:25:39,370
这个是为了方便我们调试啊

670
00:25:39,370 --> 00:25:42,060
好，这个 server 存的是什么

671
00:25:42,060 --> 00:25:43,900
存的是那个 IP 和端口号对吧

672
00:25:43,900 --> 00:25:47,690
然后呢，哎，我先去本地的这个缓存里面

673
00:25:47,690 --> 00:25:50,330
Mac 里面把这个 IP 端口号传进

674
00:25:50,330 --> 00:25:50,890
作为 K 

675
00:25:50,890 --> 00:25:54,530
我看看有没有对应的这个 client 

676
00:25:54,530 --> 00:25:56,670
就是这个 hello surface client 啊

677
00:25:56,670 --> 00:25:57,350
如果也有了

678
00:25:57,350 --> 00:25:58,270
那我直接返回嘛

679
00:25:58,270 --> 00:25:59,672
直接就可以用了

680
00:25:59,672 --> 00:26:01,200
第一次的话肯定没有，对吧

681
00:26:01,200 --> 00:26:02,180
没有的话那怎么办

682
00:26:02,180 --> 00:26:02,780
没有的话

683
00:26:02,780 --> 00:26:05,850
哎，我就要通过这个 IP 和端口号对吧

684
00:26:05,850 --> 00:26:08,110
给它进行一个拨号连接嘛

685
00:26:08,110 --> 00:26:09,550
就是我们以往代码里面

686
00:26:09,550 --> 00:26:11,690
第一步不都是要通过这个 jp style 

687
00:26:11,690 --> 00:26:14,370
去连接到这个服务端吗

688
00:26:14,370 --> 00:26:16,950
连接创建好这个连接之后，对吧

689
00:26:16,950 --> 00:26:18,670
我再通过这个 new 是吧

690
00:26:18,670 --> 00:26:23,332
去根据这个连接去生成这样一个加配 C 的 client 

691
00:26:23,332 --> 00:26:24,890
好，然后把这个 cat 呢

692
00:26:24,890 --> 00:26:27,670
再放到我本地的这个缓存里面去

693
00:26:27,670 --> 00:26:28,350
这样的话

694
00:26:28,350 --> 00:26:30,985
我下次可以从缓存里面直接取出来吗

695
00:26:30,985 --> 00:26:32,827
然后返回这个 cat 

696
00:26:32,827 --> 00:26:37,020
然后我就可以拿着这个 client 去调我的这个 login 

697
00:26:37,020 --> 00:26:39,940
或者是调那个 say hello 那个方法了

698
00:26:39,940 --> 00:26:44,160
OK ，这就是我们客户端啊

699
00:26:44,160 --> 00:26:45,900
就当我们有 DOTA 4的时候

700
00:26:45,900 --> 00:26:49,020
那我们客户端在执行这个 RPC 调用之前

701
00:26:49,020 --> 00:26:53,280
它需要先确定我本次调用到底应该去调谁

702
00:26:53,280 --> 00:26:53,660
对吧

703
00:26:53,660 --> 00:26:55,920
到底应该用哪一个 client 

704
00:26:55,920 --> 00:26:57,145
好

705
00:26:57,145 --> 00:26:59,360
然后我在这个终端这边呢

706
00:26:59,360 --> 00:27:00,680
你看啊

707
00:27:00,680 --> 00:27:02,452
在我的终端这边

708
00:27:02,452 --> 00:27:04,470
这一共是起了四个终端啊

709
00:27:04,470 --> 00:27:07,590
前三个终端是对应的 server 啊

710
00:27:07,590 --> 00:27:08,830
因为我们刚才也看到

711
00:27:08,830 --> 00:27:13,880
其实我是打算用三个不同端口号起三个 server 的

712
00:27:13,880 --> 00:27:15,110
但是目前呢

713
00:27:15,110 --> 00:27:17,790
中间这个也就是5679

714
00:27:17,790 --> 00:27:19,570
这个 server 我还没有起

715
00:27:19,570 --> 00:27:23,050
我只起了5678和5680这两个 server 

716
00:27:23,050 --> 00:27:25,590
同时第四个终端是我的 cat 

717
00:27:25,590 --> 00:27:27,315
我的 cat 已经提起来了

718
00:27:27,315 --> 00:27:29,450
那么由于刚才在 kt 里面

719
00:27:29,450 --> 00:27:33,490
我每次去获得这个服务端 IP 的时候是吧

720
00:27:33,490 --> 00:27:36,670
我会把这个服务端 IP 和端口号打出来嘛

721
00:27:36,670 --> 00:27:39,150
那目前大家看到里面有什么

722
00:27:39,150 --> 00:27:41,550
5678和5680对吧

723
00:27:41,550 --> 00:27:43,810
5678和5680这两个端口号

724
00:27:43,810 --> 00:27:45,330
它是能够获取到的

725
00:27:45,330 --> 00:27:46,850
每次都随机选择一个吗

726
00:27:48,040 --> 00:27:49,000
然后注意再看

727
00:27:49,000 --> 00:27:51,420
这个时候我打算把中间这台啊

728
00:27:51,420 --> 00:27:54,440
也就是说这个，我的这个55679

729
00:27:54,440 --> 00:27:57,590
我打算把它给提起来看一下啊

730
00:27:57,590 --> 00:27:58,850
我起了，我要起了啊

731
00:27:58,850 --> 00:27:59,860
启动一下

732
00:27:59,860 --> 00:28:01,190
好，启动一下

733
00:28:01,190 --> 00:28:02,110
记下来之后

734
00:28:02,110 --> 00:28:04,640
大家注意看我的 client 端

735
00:28:04,640 --> 00:28:07,760
你看这个他会多出这样一条日志

736
00:28:07,760 --> 00:28:10,620
他说这个这个服务对应的 server 

737
00:28:10,620 --> 00:28:13,850
你看这个 server 有三台对吧

738
00:28:13,850 --> 00:28:18,050
5678、5679和5680啊

739
00:28:18,050 --> 00:28:20,660
这行日志是在哪打出来的

740
00:28:20,660 --> 00:28:23,445
在我 content on surface hub 的这个地方

741
00:28:23,445 --> 00:28:28,880
你看就是我监听到它有一个更新世界的时候

742
00:28:28,880 --> 00:28:29,540
我会去

743
00:28:29,540 --> 00:28:31,200
就是我在这个地方啊

744
00:28:31,200 --> 00:28:34,080
我不是监听到它有一个事件变化吗

745
00:28:34,080 --> 00:28:36,780
我会去重新去调这个 get 是吧

746
00:28:36,780 --> 00:28:40,040
service and point 。在调这个方法的时候呢

747
00:28:40,040 --> 00:28:42,920
在这个地方他会去打这样一个日志啊

748
00:28:42,920 --> 00:28:45,760
他说刷新了这个服务对应的 server 对吧

749
00:28:45,760 --> 00:28:48,780
那这个 hello service 它对应的 server 呢，变成了三台

750
00:28:48,780 --> 00:28:50,592
之前一直是两台

751
00:28:50,592 --> 00:28:52,300
我们再看一下

752
00:28:52,300 --> 00:28:55,690
那么对应的这个 cat 

753
00:28:55,690 --> 00:29:00,460
你看这个时候 cat 他已经可以取到5679

754
00:29:00,460 --> 00:29:01,530
这个端口号了

755
00:29:01,530 --> 00:29:02,750
这是个新的，对吧

756
00:29:02,750 --> 00:29:05,390
那比方说啊，我们再把第一个，把这个停掉

757
00:29:05,390 --> 00:29:08,762
也是我们把这个5678这个停掉啊

758
00:29:08,762 --> 00:29:11,360
CTRL 加 S 把它扣掉

759
00:29:11,360 --> 00:29:12,600
那 Q 掉之后

760
00:29:12,600 --> 00:29:14,700
我们说这个 server 端是吧

761
00:29:14,700 --> 00:29:18,680
你给我发送这个 CTRLC 这个信号的时候

762
00:29:18,680 --> 00:29:21,040
我会去打印这么一条一只蛇

763
00:29:21,040 --> 00:29:24,640
他说，诶，我接收到 in interrot 这样个信号

764
00:29:24,640 --> 00:29:25,585
然后呢

765
00:29:25,585 --> 00:29:29,740
他把这个对应的5678把它给注销掉了

766
00:29:29,740 --> 00:29:32,360
因为他会去调这个注销函数嘛

767
00:29:32,360 --> 00:29:37,080
在注销函数里面会去打印这样一个预知啊

768
00:29:37,080 --> 00:29:38,620
所以把谁给注销了，对吧

769
00:29:38,620 --> 00:29:43,087
好，注销之后我们再来看一下我们的客户端

770
00:29:43,087 --> 00:29:44,910
这个时候我们的客户

771
00:29:44,910 --> 00:29:46,290
就只能获取到

772
00:29:46,290 --> 00:29:48,530
5679和5680了

773
00:29:48,530 --> 00:29:51,255
他获取不到这个5678了

774
00:29:51,255 --> 00:29:52,110
对吧

775
00:29:52,110 --> 00:29:56,050
所以说每次服务端有新的成员加入

776
00:29:56,050 --> 00:29:57,880
和老成员退出

777
00:29:57,880 --> 00:30:00,980
那客户端里边他都能够及时的感知到

778
00:30:00,980 --> 00:30:05,100
他能够立即去更新本地的那个 server 缓存

779
00:30:05,100 --> 00:30:08,020
最后我们再来补充一个小的知识点

780
00:30:08,020 --> 00:30:09,730
就是我们上一节课

781
00:30:09,730 --> 00:30:11,870
在讲那个配置中心的时候

782
00:30:11,870 --> 00:30:14,590
我们搞了一个全局的配置中心啊

783
00:30:14,590 --> 00:30:18,150
它是一个，你看这个全局配置是一个大写 CONFIG 

784
00:30:18,150 --> 00:30:18,890
这样的话呢

785
00:30:18,890 --> 00:30:22,920
我们就可以在任何地方去使用这个全局配置

786
00:30:22,920 --> 00:30:26,920
configure 就没有给他去搞一个什么单例的构造函数

787
00:30:26,920 --> 00:30:28,590
而今天这个课里面呢

788
00:30:28,590 --> 00:30:32,830
我们故意把这个注册中心搞成小写

789
00:30:32,830 --> 00:30:33,440
这样的话

790
00:30:33,440 --> 00:30:36,950
你就通过这样一个单例的构造函数去获取它

791
00:30:36,950 --> 00:30:39,910
那么这两种方式有什么区别呢

792
00:30:39,910 --> 00:30:44,000
看上去这个用一个全局的可导出的这个变量

793
00:30:44,000 --> 00:30:46,710
用起来代码接起来会更简单一点

794
00:30:46,710 --> 00:30:48,190
而通过这种单例模式的话

795
00:30:48,190 --> 00:30:50,970
你还得单独的写这样一个构造函数

796
00:30:50,970 --> 00:30:52,722
麻烦一点，对吧

797
00:30:52,722 --> 00:30:54,960
那其实它们的区别在于

798
00:30:54,960 --> 00:30:57,800
你通过单列的这种构造函数的话

799
00:30:57,800 --> 00:31:01,680
实际上是一种什么叫做懒加载啊

800
00:31:01,680 --> 00:31:02,700
就说你看啊

801
00:31:02,700 --> 00:31:05,810
上节课我们这个全局的配置

802
00:31:05,810 --> 00:31:06,940
你必须什么

803
00:31:06,940 --> 00:31:09,660
你必须去调这个 init global config 

804
00:31:09,660 --> 00:31:10,860
必须去调这个函数

805
00:31:10,860 --> 00:31:13,250
而且是在你的 main 函数

806
00:31:13,250 --> 00:31:15,180
在我们的 main 函数一进来

807
00:31:15,180 --> 00:31:16,860
在系统初始化的时候

808
00:31:16,860 --> 00:31:19,140
一定要去记得调这个函数

809
00:31:19,140 --> 00:31:21,922
给我们的这个全局变

810
00:31:21,922 --> 00:31:24,550
configure 进行一次赋值啊

811
00:31:24,550 --> 00:31:26,350
必须在系统初始化的时候

812
00:31:26,350 --> 00:31:28,970
必须做这个复制操作

813
00:31:28,970 --> 00:31:30,430
但这样有一个问题

814
00:31:30,430 --> 00:31:33,470
就是说你虽然给这个全局变量赋值了

815
00:31:33,470 --> 00:31:37,190
但你的程序可能运行了一个小时之后啊

816
00:31:37,190 --> 00:31:39,225
才真正的需要去

817
00:31:39,225 --> 00:31:40,370
需要去这个

818
00:31:40,370 --> 00:31:42,050
用这个 CONFIG 对吧

819
00:31:42,050 --> 00:31:45,410
那这一小时之间实际上就是浪费了嘛，对吧

820
00:31:45,410 --> 00:31:47,987
你就占了你的一点点内存

821
00:31:47,987 --> 00:31:51,820
而且呢，就是你需要始终去记得啊

822
00:31:51,820 --> 00:31:57,160
记得在 main 函数入口日去执行这个初始函数

823
00:31:57,160 --> 00:32:00,250
你很可能会把这个调用啊

824
00:32:00,250 --> 00:32:02,230
忘了，忘记去调用它

825
00:32:02,230 --> 00:32:03,530
但是单例好

826
00:32:03,530 --> 00:32:05,750
单例函数的好处就在于说

827
00:32:05,750 --> 00:32:09,570
单例的在说你在那个系统初始化的时候呢

828
00:32:09,570 --> 00:32:13,580
你不需要去显示的去立即调用这个函数

829
00:32:13,580 --> 00:32:14,560
你只需要什么

830
00:32:14,560 --> 00:32:18,130
你只需要在你打算去用它的时候

831
00:32:18,130 --> 00:32:19,700
再去初始化它啊

832
00:32:19,700 --> 00:32:24,120
比方说你打算去用这个 register 或者 UNREGISTER 

833
00:32:24,120 --> 00:32:24,940
这个函数了

834
00:32:24,940 --> 00:32:28,770
这个时候你再去创建它的这个实例就可以了

835
00:32:28,770 --> 00:32:29,970
因为你调用它了嘛

836
00:32:29,970 --> 00:32:30,890
你可能去创建嘛

837
00:32:30,890 --> 00:32:32,150
那在用之

838
00:32:32,150 --> 00:32:35,100
你一直都不需要去框架的实力

839
00:32:35,100 --> 00:32:37,010
对全局的配置

840
00:32:37,010 --> 00:32:39,730
这个 int 函数你只能调一次

841
00:32:39,730 --> 00:32:42,010
但是我们的这个钩子函数对吧

842
00:32:42,010 --> 00:32:43,170
构造函数的话

843
00:32:43,170 --> 00:32:44,860
你可以反复的调好几次

844
00:32:44,860 --> 00:32:47,050
因为它内部还是可以通过 once 

845
00:32:47,050 --> 00:32:49,370
来确保你底层只执行一次
