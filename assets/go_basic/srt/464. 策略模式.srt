1
00:00:00,080 --> 00:00:02,140
作为 surface hub 服务中心

2
00:00:02,140 --> 00:00:04,160
它还需要提供另外一个功能

3
00:00:04,160 --> 00:00:05,750
就是负载均衡

4
00:00:05,750 --> 00:00:09,600
因为之前我们提供的这个 get service and points 

5
00:00:09,600 --> 00:00:11,400
它返回的是一个什么

6
00:00:11,400 --> 00:00:14,312
是一个 server 构成的集合

7
00:00:14,312 --> 00:00:15,900
那么对于调用方而言

8
00:00:15,900 --> 00:00:17,780
他说你最好直接给我

9
00:00:17,780 --> 00:00:19,940
返回一台服务器就行了

10
00:00:19,940 --> 00:00:23,400
我刚好可以跟他进行接口调用通信啊

11
00:00:23,400 --> 00:00:24,420
你不要给我放多个

12
00:00:24,420 --> 00:00:25,320
放放多个的话

13
00:00:25,320 --> 00:00:28,397
我还得自己去做这个负载均衡嘛

14
00:00:28,397 --> 00:00:31,310
所以能不能让这个 surface hub 

15
00:00:31,310 --> 00:00:34,592
直接把这个负载均衡给我做了，对吧

16
00:00:34,592 --> 00:00:35,820
OK ，所以呢

17
00:00:35,820 --> 00:00:37,440
就有了下面这个函数啊

18
00:00:37,440 --> 00:00:41,315
就是它只返回某一个 end point 

19
00:00:41,315 --> 00:00:42,070
好

20
00:00:42,070 --> 00:00:45,920
但是呢，我们这个负载均衡算法实现

21
00:00:45,920 --> 00:00:50,430
本身又不想在这个 surface hub 里面去做啊

22
00:00:50,430 --> 00:00:53,550
surface hub 说这个算法我也不想自己实现

23
00:00:53,550 --> 00:00:56,550
他希望丢给外部去实现

24
00:00:56,550 --> 00:01:01,550
比方说你在这个调这个 get surface and point 

25
00:01:01,550 --> 00:01:02,430
这个函数的时候

26
00:01:02,430 --> 00:01:04,349
你能不能给我传一个

27
00:01:04,349 --> 00:01:06,230
比方说这个回调函数啊

28
00:01:06,230 --> 00:01:09,070
我按照你那个函数指定的方式来实现

29
00:01:09,070 --> 00:01:10,070
这个选择

30
00:01:10,070 --> 00:01:11,390
某一台服务器啊

31
00:01:11,390 --> 00:01:12,237
比方说啊

32
00:01:12,237 --> 00:01:12,960
举个例子

33
00:01:12,960 --> 00:01:16,770
比方说我这边定义一个 function 

34
00:01:16,770 --> 00:01:20,910
我这边定义一个 load balancer 

35
00:01:22,160 --> 00:01:23,280
function 

36
00:01:23,280 --> 00:01:25,972
它呢就是一个 funk 

37
00:01:25,972 --> 00:01:28,090
参数呢，是一个切片

38
00:01:28,090 --> 00:01:31,630
然后呢，我从这个切片里面选择某一个元素

39
00:01:31,630 --> 00:01:33,805
好，选择一个元素

40
00:01:33,805 --> 00:01:35,890
这样的话呢，我这个地方啊

41
00:01:35,890 --> 00:01:37,950
我再加一个参数

42
00:01:37,950 --> 00:01:41,040
叫做 load balance function 

43
00:01:41,040 --> 00:01:44,167
helload balancer function 

44
00:01:44,167 --> 00:01:46,520
OK ，这样是吧

45
00:01:46,520 --> 00:01:50,770
我这个函数呢，要求拿另外一个函数

46
00:01:50,770 --> 00:01:52,610
另外一个函数作为入财

47
00:01:52,610 --> 00:01:54,220
那这样的话呢

48
00:01:55,670 --> 00:01:57,180
OK ，我写一下啊

49
00:01:57,180 --> 00:01:58,270
return 

50
00:01:59,370 --> 00:02:02,250
好， return ，我先去调用我自己的

51
00:02:02,250 --> 00:02:04,257
就上面这个函数，对吧

52
00:02:04,257 --> 00:02:06,200
好，先调我自己的

53
00:02:06,200 --> 00:02:08,740
上面这个函数也就是它

54
00:02:10,880 --> 00:02:11,840
ok 

55
00:02:11,840 --> 00:02:12,520
好

56
00:02:12,520 --> 00:02:15,320
我自己的这个函数返回的是一个切片嘛，对吧

57
00:02:15,320 --> 00:02:19,200
那我再把这个切片再扔给这个函数

58
00:02:19,200 --> 00:02:21,700
也就是扔给这个 l BF 对吧

59
00:02:21,700 --> 00:02:24,420
扔给 l b f l BF 

60
00:02:26,740 --> 00:02:28,180
好，这样

61
00:02:28,180 --> 00:02:28,540
好

62
00:02:28,540 --> 00:02:30,170
这样的话不就实现了这

63
00:02:30,170 --> 00:02:33,580
从众多里面选择一个这个功能吗

64
00:02:33,580 --> 00:02:36,460
好，所谓的这个策略模式啊

65
00:02:36,460 --> 00:02:38,007
他就是

66
00:02:38,007 --> 00:02:40,310
我把这个具体的实现方案呢

67
00:02:40,310 --> 00:02:42,030
我不在我内部写

68
00:02:42,030 --> 00:02:43,737
而丢给外部

69
00:02:43,737 --> 00:02:46,140
OK ，那刚才我们是通过什么

70
00:02:46,140 --> 00:02:48,700
通过这个传函数的方式啊

71
00:02:48,700 --> 00:02:50,840
传函数的方式来完成这个功能

72
00:02:50,840 --> 00:02:52,860
那么在真正的策略模式里面呢

73
00:02:52,860 --> 00:02:55,000
也就是所谓的设计模式嘛

74
00:02:55,000 --> 00:02:59,840
他们一般他们都肯定不会去走购员这种啊

75
00:02:59,840 --> 00:03:00,850
函数方式嘛

76
00:03:00,850 --> 00:03:02,770
他那么肯定是种种面向对象啊

77
00:03:02,770 --> 00:03:03,990
这个接口这种方式

78
00:03:03,990 --> 00:03:07,240
所以呢，我们把这个形式稍微的转换一下

79
00:03:07,240 --> 00:03:08,340
因为我们之前讲过

80
00:03:08,340 --> 00:03:13,100
本质上你这个函数类型和接口是一致的嘛

81
00:03:13,100 --> 00:03:15,200
所以我们如果把它转成接口的话

82
00:03:15,200 --> 00:03:17,950
就是这样一种形式啊

83
00:03:17,950 --> 00:03:20,830
这边呢，我专门定义了一个接口

84
00:03:20,830 --> 00:03:21,990
那这个接口里面呢

85
00:03:21,990 --> 00:03:23,450
必须实现一个方法

86
00:03:23,450 --> 00:03:26,890
就是你传进来多个 and point 

87
00:03:26,890 --> 00:03:30,147
我跟呢，给你选中一个 and point 

88
00:03:30,147 --> 00:03:30,780
好

89
00:03:30,780 --> 00:03:31,440
这边呢

90
00:03:31,440 --> 00:03:34,900
就随便实现两个最简单的复杂均衡策略啊

91
00:03:34,900 --> 00:03:36,960
一个是让我们的 robin 

92
00:03:36,960 --> 00:03:38,920
一个是这个 random 是吧

93
00:03:38,920 --> 00:03:40,440
轮询法和随机法

94
00:03:40,440 --> 00:03:41,300
他们两个呢

95
00:03:41,300 --> 00:03:44,835
都要去实现这个 take 函数

96
00:03:44,835 --> 00:03:47,000
来看一下轮询法嘛

97
00:03:47,000 --> 00:03:49,080
轮询，轮询就说我第一次的话

98
00:03:49,080 --> 00:03:50,420
我请求 DA 服务器

99
00:03:50,420 --> 00:03:52,360
第二次呢，请求 DA 服务器是吧

100
00:03:52,360 --> 00:03:53,100
以此类推

101
00:03:53,100 --> 00:03:55,950
如果说你比方说总共有三台服务器

102
00:03:55,950 --> 00:03:58,030
那么第四次请求呢，就返回来

103
00:03:58,030 --> 00:03:59,870
还是请求 DA 服务器嘛

104
00:03:59,870 --> 00:04:01,350
落实到代码层面

105
00:04:01,350 --> 00:04:04,310
实际上就说让那个第几次啊

106
00:04:04,310 --> 00:04:07,430
次数直接对我这个服务器的总函数

107
00:04:07,430 --> 00:04:09,110
求一个模就可以了

108
00:04:09,110 --> 00:04:10,440
好，所以呢

109
00:04:10,440 --> 00:04:12,320
这个地方啊，就是你每次 take 

110
00:04:12,320 --> 00:04:14,480
每次 take 都是返回的一台嘛

111
00:04:14,480 --> 00:04:16,540
所以你需要有一个状态变量

112
00:04:16,540 --> 00:04:19,500
来记录当前是第几次请求啊

113
00:04:19,500 --> 00:04:21,720
所以它有一个 AACC 啊

114
00:04:21,720 --> 00:04:25,397
来记录一下我已经累计请求多少次了

115
00:04:25,397 --> 00:04:29,110
好，对于某一次 take 而言，好

116
00:04:29,110 --> 00:04:32,992
那我呢，先对这个累积次数加一

117
00:04:32,992 --> 00:04:34,900
一定要传这个指针啊

118
00:04:34,900 --> 00:04:36,180
不然的话这个 AACC 不会变

119
00:04:36,180 --> 00:04:36,790
好

120
00:04:36,790 --> 00:04:39,160
累积次数加一，加一之后的话

121
00:04:39,160 --> 00:04:40,180
这样这个 N 呢

122
00:04:40,180 --> 00:04:43,990
对总的服务的台数求个模

123
00:04:43,990 --> 00:04:46,100
这样的话得到一个下标嘛

124
00:04:46,100 --> 00:04:49,070
哎，我直接返回对应的下标就可以了啊

125
00:04:49,070 --> 00:04:50,870
你给我传进来的是一个切片

126
00:04:50,870 --> 00:04:54,330
那我从切片里面选中了这台服务器返回

127
00:04:54,330 --> 00:04:56,250
这就是轮询法

128
00:04:56,250 --> 00:04:58,765
那轮询法它可以保证

129
00:04:58,765 --> 00:05:00,620
呃，绝对的公平

130
00:05:00,620 --> 00:05:03,220
对每一台服务器被命中的次数

131
00:05:03,220 --> 00:05:05,230
是绝对是一样的

132
00:05:05,230 --> 00:05:06,640
这里面无非是要注意一点

133
00:05:06,640 --> 00:05:08,160
就是我们这个加一呢

134
00:05:08,160 --> 00:05:11,020
是要考虑到这个并发安全性

135
00:05:11,020 --> 00:05:13,140
就是在高并发的情况之下

136
00:05:13,140 --> 00:05:15,590
这个 take 啊，会被并发的调用

137
00:05:15,590 --> 00:05:20,142
所以呢，这个加一一定要通过这个 atomic 原子操作

138
00:05:20,142 --> 00:05:22,160
那随机法就更简单了

139
00:05:22,160 --> 00:05:24,080
随机法对任意次请求而言

140
00:05:24,080 --> 00:05:26,500
反正我就生成一个随机数嘛

141
00:05:26,500 --> 00:05:26,720
啊

142
00:05:26,720 --> 00:05:27,480
让这个随机数

143
00:05:27,480 --> 00:05:31,030
它位于零到这个服务的总台数之间啊

144
00:05:31,030 --> 00:05:34,030
我去选中这台服务器就可以了

145
00:05:34,030 --> 00:05:36,410
所以机法直接说，从概率上讲

146
00:05:36,410 --> 00:05:39,030
那每一台服务器被选中了，呃

147
00:05:39,030 --> 00:05:39,830
概率是一样的

148
00:05:39,830 --> 00:05:42,330
但是实际上它可能有一些出入啊

149
00:05:42,330 --> 00:05:42,770
好

150
00:05:42,770 --> 00:05:44,770
那如果把这个策略

151
00:05:44,770 --> 00:05:47,670
全部通过接口种形式来实现的话

152
00:05:47,670 --> 00:05:50,590
那么我们这边这个函数就可以是吧

153
00:05:50,590 --> 00:05:51,970
就可以改造一下

154
00:05:53,290 --> 00:05:55,930
好，那就改造成什么呢

155
00:05:55,930 --> 00:05:57,010
改造成

156
00:05:58,110 --> 00:06:01,330
刚才不是搞了一个这个负载均衡的接口吗

157
00:06:01,330 --> 00:06:02,490
那我把这个接口

158
00:06:02,490 --> 00:06:05,550
直接注入到我这个 surface hub 里面来

159
00:06:05,550 --> 00:06:07,755
你看到这里边是吧

160
00:06:07,755 --> 00:06:09,730
好，我把这个负载均衡呢

161
00:06:09,730 --> 00:06:13,370
直接作为我 surface hub 的一个成员变量啊

162
00:06:13,370 --> 00:06:14,930
这就是策略模式啊

163
00:06:14,930 --> 00:06:17,590
就是说我完成这个负载均衡呢

164
00:06:17,590 --> 00:06:20,060
它可能有多种不同的实现

165
00:06:20,060 --> 00:06:21,400
那么作为 service hub 

166
00:06:21,400 --> 00:06:23,040
它不关心每一种事件是什么

167
00:06:23,040 --> 00:06:25,880
它这个平台放的只是一个接口啊

168
00:06:25,880 --> 00:06:26,560
只是接口

169
00:06:26,560 --> 00:06:29,620
到时候你去这个 surface hu

170
00:06:29,620 --> 00:06:31,280
它的构造函数里面

171
00:06:31,280 --> 00:06:32,880
在 surface hub 构造函数里面

172
00:06:32,880 --> 00:06:36,552
你指定一个具体的策略就可以了

173
00:06:36,552 --> 00:06:39,710
所以在这个地方我们直接通过啊

174
00:06:39,710 --> 00:06:42,240
通过调这个 self face hub 

175
00:06:42,240 --> 00:06:44,680
它的这个成员变量 load balancer 

176
00:06:44,680 --> 00:06:47,400
通过这个 load balancer 它的 take 函数，哎

177
00:06:47,400 --> 00:06:49,357
我就实现了负载均衡

178
00:06:49,357 --> 00:06:53,130
最后我们再来品一品这个策略模式啊

179
00:06:53,130 --> 00:06:54,517
细品策略模式

180
00:06:54,517 --> 00:06:55,660
虽然说啊

181
00:06:55,660 --> 00:06:57,500
在描述策略模式时

182
00:06:57,500 --> 00:06:59,380
用了一大段文字来讲

183
00:06:59,380 --> 00:07:00,340
但实际上

184
00:07:00,340 --> 00:07:03,680
如果你根本就没有学过策略模式的话

185
00:07:03,680 --> 00:07:04,620
那在这个地方

186
00:07:04,620 --> 00:07:07,310
我觉得你也能够很自然而然的想到

187
00:07:07,310 --> 00:07:08,000
这种方法

188
00:07:08,000 --> 00:07:10,400
无非就是说选中某一台服务器

189
00:07:10,400 --> 00:07:13,050
它有多种不同的策略可供选择

190
00:07:13,050 --> 00:07:14,840
于是乎呢，这个地方对吧

191
00:07:14,840 --> 00:07:17,640
于是乎这个地方你可以传一

192
00:07:17,640 --> 00:07:19,740
load balanced interface 

193
00:07:21,940 --> 00:07:22,520
是吧

194
00:07:22,520 --> 00:07:25,305
你可以传一个接口参数过来嘛

195
00:07:25,305 --> 00:07:26,800
如果一个函数内部

196
00:07:26,800 --> 00:07:29,040
你打算让它有多种不同的实现

197
00:07:29,040 --> 00:07:29,880
那这个地方呢

198
00:07:29,880 --> 00:07:31,510
你就可以传一个接口

199
00:07:31,510 --> 00:07:33,800
因为接口本来可以有多种不同实现嘛

200
00:07:33,800 --> 00:07:35,680
然后在这个函数内部呢

201
00:07:35,680 --> 00:07:38,180
你去通过这个接口来实现

202
00:07:38,180 --> 00:07:41,290
return l b f 

203
00:07:43,280 --> 00:07:44,700
透明在这个函数内部

204
00:07:44,700 --> 00:07:48,880
通过调这个 l BF 这个接口的 take 函数来实现

205
00:07:48,880 --> 00:07:50,340
然后再稍微的转变一下

206
00:07:50,340 --> 00:07:53,460
你又不想在这个里面加这么多参数

207
00:07:53,460 --> 00:07:55,180
你想只加一个参数

208
00:07:55,180 --> 00:07:58,580
如果想把这个参数移走的话怎么办

209
00:07:58,580 --> 00:08:01,870
你可以把这个参数放到这个 surface hub 里面去嘛

210
00:08:01,870 --> 00:08:02,350
是吧

211
00:08:02,350 --> 00:08:03,810
放到 hub 里面去的话

212
00:08:03,810 --> 00:08:05,010
那就变成了

213
00:08:06,870 --> 00:08:08,030
这种形式

214
00:08:09,090 --> 00:08:10,530
所以我觉得策略模式

215
00:08:10,530 --> 00:08:13,210
是一个自然而然的代码设计方法

216
00:08:13,210 --> 00:08:15,270
有人非要给他冠一个名号

217
00:08:15,270 --> 00:08:16,850
称之为策略模式
