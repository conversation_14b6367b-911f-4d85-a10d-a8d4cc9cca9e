1
00:00:00,099 --> 00:00:03,390
我们说接口是一组行为规范的集合

2
00:00:03,390 --> 00:00:05,590
那这个集合可以为空吗

3
00:00:05,590 --> 00:00:06,985
答案是可以的

4
00:00:06,985 --> 00:00:08,360
这是我的第六行

5
00:00:08,360 --> 00:00:09,820
我定义了一个接口

6
00:00:09,820 --> 00:00:11,392
这个接口里面呢

7
00:00:11,392 --> 00:00:14,510
没有定义任何的函数啊

8
00:00:14,510 --> 00:00:16,690
大括号里面空的空接口吗

9
00:00:16,690 --> 00:00:18,727
那空就意义何在呢

10
00:00:18,727 --> 00:00:21,660
我们之前说一个结构体里面

11
00:00:21,660 --> 00:00:22,820
某一个成员变量

12
00:00:22,820 --> 00:00:24,680
而它是一个接口类型

13
00:00:24,680 --> 00:00:28,050
那么我们希望将来通过这个成员变量

14
00:00:28,050 --> 00:00:31,475
能够调到这个接口里面的某个函数

15
00:00:31,475 --> 00:00:33,890
又或者说某一个函数

16
00:00:33,890 --> 00:00:35,710
其参数是接口类型

17
00:00:35,710 --> 00:00:38,970
那么我们是指望着通过这个接口

18
00:00:38,970 --> 00:00:41,750
能够调到某个函数，对吧

19
00:00:41,750 --> 00:00:43,102
供我们来使用

20
00:00:43,102 --> 00:00:46,920
那你现在你定义了一个接口里面空的

21
00:00:46,920 --> 00:00:49,127
那空接口意义何在呢

22
00:00:49,127 --> 00:00:52,120
那其实啊，我们一直都在使用空接口

23
00:00:52,120 --> 00:00:55,440
我们天天都在使用的这个 print 函数

24
00:00:55,440 --> 00:00:56,430
进去看看

25
00:00:56,430 --> 00:01:00,390
你看这个 print 它参数是 any 类型

26
00:01:00,390 --> 00:01:03,410
而这个 any 就是什么

27
00:01:03,410 --> 00:01:05,997
就是一个空接口类型

28
00:01:05,997 --> 00:01:08,360
那么构言标准库里面定义的

29
00:01:08,360 --> 00:01:09,880
这样的一种类型

30
00:01:09,880 --> 00:01:13,275
跟我们刚才自己定义的这个类型

31
00:01:13,275 --> 00:01:14,800
是完全一样的

32
00:01:14,800 --> 00:01:15,940
都是空接口

33
00:01:15,940 --> 00:01:19,140
那既然说这个接口里面没有任何函数

34
00:01:19,140 --> 00:01:24,390
就意味着任意的类型都实现了这个接口

35
00:01:24,390 --> 00:01:27,490
意味着这个接口没有任何的准入门槛

36
00:01:27,490 --> 00:01:31,110
那既然任意的类型都是空接口

37
00:01:31,110 --> 00:01:34,260
那么就意味着我可以把任意类型的数据

38
00:01:34,260 --> 00:01:36,610
丢给 print 函数啊

39
00:01:36,610 --> 00:01:37,630
我可以丢整数

40
00:01:37,630 --> 00:01:39,410
也可以丢字符串

41
00:01:39,410 --> 00:01:40,670
也可以丢布尔

42
00:01:40,670 --> 00:01:43,145
也可以丢结构体都可以

43
00:01:43,145 --> 00:01:47,150
同时呢，由于它本身还接受一个不定长参数嘛

44
00:01:47,150 --> 00:01:48,270
那么 K 是零个

45
00:01:48,270 --> 00:01:50,707
K 是一个，也可以是多个

46
00:01:50,707 --> 00:01:54,885
我们自己来写一个空接口的应用案例

47
00:01:54,885 --> 00:01:59,380
比如说我要写一个求和的函数 sum 

48
00:01:59,380 --> 00:02:03,512
他也接受一个不丁调参数或者切片都可以

49
00:02:03,512 --> 00:02:04,550
什么类型呢

50
00:02:04,550 --> 00:02:09,000
就是我刚才自己定义的这种空接口类型

51
00:02:09,000 --> 00:02:11,770
最后我希望返回一个整数

52
00:02:11,770 --> 00:02:14,030
那这个返回结果呢

53
00:02:14,030 --> 00:02:15,350
先负为零

54
00:02:15,350 --> 00:02:19,497
最终我是要返回这个结果的

55
00:02:19,497 --> 00:02:21,860
那我们说对于不定长参数而言

56
00:02:21,860 --> 00:02:23,100
就是一个切片嘛

57
00:02:23,100 --> 00:02:27,350
我们按照切片来便利这个不定长参数

58
00:02:27,350 --> 00:02:30,020
因为我们也不知道每一个参数

59
00:02:30,020 --> 00:02:32,505
它到底具体是什么类型嘛

60
00:02:32,505 --> 00:02:35,220
为了把它加到 react 里面去

61
00:02:35,220 --> 00:02:39,575
我们还是需要针对不同的情况来区分处理

62
00:02:39,575 --> 00:02:43,320
那如何获得这样一个空接口

63
00:02:43,320 --> 00:02:45,422
它的真实类型呢

64
00:02:45,422 --> 00:02:48,150
可以通过点 type 来获得

65
00:02:48,150 --> 00:02:50,010
同时这个点 type 呢

66
00:02:50,010 --> 00:02:54,165
必须放在随且表达式后面才能使用

67
00:02:54,165 --> 00:02:57,150
艾利。它是一个空接口类型

68
00:02:57,150 --> 00:03:01,630
通过调点 tab 来转成一个具体类型 case 

69
00:03:02,650 --> 00:03:04,870
比如 case 整数的话

70
00:03:04,870 --> 00:03:09,270
那么这个 react 就可以直接加上这个 V 

71
00:03:09,270 --> 00:03:11,900
好，如果是浮点三二的话

72
00:03:11,900 --> 00:03:14,420
那么呢，我就只取整数部分

73
00:03:14,420 --> 00:03:16,807
把它强行转为整数

74
00:03:16,807 --> 00:03:17,610
笨了吧

75
00:03:17,610 --> 00:03:18,930
其他类型我就不写了

76
00:03:18,930 --> 00:03:22,960
那如果说不是以上类型的话

77
00:03:22,960 --> 00:03:26,110
就不支持执行这个加法运算嘛

78
00:03:26,110 --> 00:03:30,120
我就打印一个错信息

79
00:03:30,120 --> 00:03:31,130
我说

80
00:03:32,600 --> 00:03:36,125
不支持的数据类型

81
00:03:36,125 --> 00:03:38,160
那么我如何打印出

82
00:03:38,160 --> 00:03:41,170
这个空接口的数据类型呢

83
00:03:41,170 --> 00:03:44,380
哎，通过百分号大 T 啊

84
00:03:44,380 --> 00:03:45,240
百分号大 T 

85
00:03:45,240 --> 00:03:48,500
我们之前说百分号小 T 表示是布尔变量

86
00:03:48,500 --> 00:03:49,800
现在百分号大 D 

87
00:03:49,800 --> 00:03:52,780
表示要打印一个变量的数据类型

88
00:03:54,460 --> 00:03:57,700
换行把 element 放这

89
00:03:59,120 --> 00:04:01,860
好看一下如何去应用这个函数啊

90
00:04:01,860 --> 00:04:06,700
SUI 这个函数 react 等于 some 

91
00:04:06,700 --> 00:04:07,200
i 

92
00:04:08,670 --> 00:04:09,590
不定长吗

93
00:04:09,590 --> 00:04:11,790
我可以传一个整数

94
00:04:11,790 --> 00:04:14,800
再传一个浮点数

95
00:04:14,800 --> 00:04:16,982
3.14

96
00:04:16,982 --> 00:04:20,499
再传一个布尔变量吧

97
00:04:20,499 --> 00:04:22,860
再传一个字符串

98
00:04:23,980 --> 00:04:28,060
好，我们最终把这个和打出来看看

99
00:04:29,230 --> 00:04:30,832
我们先来分析一下

100
00:04:30,832 --> 00:04:33,880
对于常规的整数和浮点三二而言

101
00:04:33,880 --> 00:04:37,500
它确实可以被加到这个 react 里面去

102
00:04:37,500 --> 00:04:40,770
而对于刚才这个布尔变量和字符串变量

103
00:04:40,770 --> 00:04:43,620
会命中我们的 default 是吧

104
00:04:43,620 --> 00:04:46,550
打印出它们各自的数据类型

105
00:04:46,550 --> 00:04:48,860
这里我强调一下

106
00:04:48,860 --> 00:04:52,620
就是通过点 tab 获得具体的类型

107
00:04:52,620 --> 00:04:53,820
不是付给了 V 吗

108
00:04:53,820 --> 00:04:56,340
那么这个 V 在不同的 case 里面

109
00:04:56,340 --> 00:04:59,780
它就已经是相应的数据类型了

110
00:04:59,780 --> 00:05:03,010
也就是说在这个 case 里面啊

111
00:05:03,010 --> 00:05:05,390
V 是一个

112
00:05:06,630 --> 00:05:10,840
int 类型的变量

113
00:05:10,840 --> 00:05:14,680
同理啊，在第二个 case 里面呢

114
00:05:14,680 --> 00:05:17,380
V 是一个浮点三二的变量

115
00:05:17,380 --> 00:05:19,780
所以说你别看这个 V 对吧

116
00:05:19,780 --> 00:05:21,397
就复制了一次

117
00:05:21,397 --> 00:05:23,610
好像它是一个确定的数据型

118
00:05:23,610 --> 00:05:24,410
其实不然

119
00:05:24,410 --> 00:05:26,530
它在不同的 case 里面

120
00:05:26,530 --> 00:05:28,270
对应的数学型是不一样的

121
00:05:28,270 --> 00:05:32,350
所以你对应的这个使用方式也是不一样的

122
00:05:32,350 --> 00:05:35,370
我们把代码跑起来看一看

123
00:05:35,370 --> 00:05:38,860
go run 指定目录是 basic 

124
00:05:38,860 --> 00:05:41,430
它从这个目录下扫描所有的 go 代码

125
00:05:41,430 --> 00:05:43,742
找到 main 函数来执行

126
00:05:43,742 --> 00:05:46,860
好，先是打印一个空行，对吧

127
00:05:46,860 --> 00:05:47,400
空行

128
00:05:47,400 --> 00:05:49,420
然后呢，是一个数字一

129
00:05:49,420 --> 00:05:50,140
这个数字一

130
00:05:50,140 --> 00:05:52,395
然后是一个字符串一

131
00:05:52,395 --> 00:05:55,010
然后会进到 sum 函数里面来

132
00:05:55,010 --> 00:05:56,960
sum 函数的话它输出啊

133
00:05:56,960 --> 00:06:00,040
对于这个布尔变量和字符串是不支持的

134
00:06:00,040 --> 00:06:00,827
数据类型

135
00:06:00,827 --> 00:06:02,310
最终输出这个和

136
00:06:02,310 --> 00:06:04,410
和的话是1+3.14

137
00:06:04,410 --> 00:06:06,045
而这个3.4的话

138
00:06:06,045 --> 00:06:07,330
小数被丢弃了

139
00:06:07,330 --> 00:06:09,510
因为强行转成了整数嘛

140
00:06:09,510 --> 00:06:10,950
所以1+3=4啊

141
00:06:10,950 --> 00:06:12,912
输出结果是四

142
00:06:12,912 --> 00:06:14,180
对于空接口

143
00:06:14,180 --> 00:06:15,660
除了通过百分号 T 

144
00:06:15,660 --> 00:06:17,780
可以输出它的这个具体类型之外

145
00:06:17,780 --> 00:06:20,180
我们还可以输出它具体的值

146
00:06:22,160 --> 00:06:24,910
值为百分号 V 

147
00:06:24,910 --> 00:06:28,910
那对任意的类型都可以使用百分号 V 

148
00:06:28,910 --> 00:06:29,950
来进行输出

149
00:06:29,950 --> 00:06:31,090
这边呢

150
00:06:31,090 --> 00:06:33,275
还是艾利

151
00:06:33,275 --> 00:06:35,050
或者我们可以之前讲过啊

152
00:06:35,050 --> 00:06:37,402
我们加一个中括号

153
00:06:37,402 --> 00:06:38,910
这边来个一

154
00:06:38,910 --> 00:06:42,440
这边呢，也来个中括号一

155
00:06:42,440 --> 00:06:45,275
这样的话就只需要放一

156
00:06:45,275 --> 00:06:46,290
一个案例

157
00:06:46,290 --> 00:06:48,155
那这样是可以的

158
00:06:48,155 --> 00:06:51,940
再来运行一次 run 一下是吧

159
00:06:51,940 --> 00:06:54,160
能够输出啊，值为 false 

160
00:06:54,160 --> 00:06:56,150
值为 A 、 B 、 C 

161
00:06:56,150 --> 00:06:57,600
那其实呢

162
00:06:57,600 --> 00:07:00,560
除了使用我们自己写的这个空接口之外

163
00:07:00,560 --> 00:07:02,120
我们直接使用标准库的

164
00:07:02,120 --> 00:07:05,100
就是说标准库也提供了一种类型

165
00:07:05,100 --> 00:07:09,090
叫做 in interface 大括号

166
00:07:09,090 --> 00:07:11,310
注意这边必须修大括号

167
00:07:11,310 --> 00:07:13,710
就是英特尔 face 和大括号

168
00:07:13,710 --> 00:07:15,830
它们两个合在一起啊

169
00:07:15,830 --> 00:07:19,070
它们整体表示空接口这样的类型

170
00:07:19,070 --> 00:07:21,480
好，我们再来 run 一下

171
00:07:21,480 --> 00:07:24,260
你看跟刚才这个结果是一样的啊

172
00:07:24,260 --> 00:07:26,292
其实我们也看到了

173
00:07:26,292 --> 00:07:27,840
就是在标准库里面

174
00:07:27,840 --> 00:07:31,120
这个 any i 和这个空接口是完全等价的

175
00:07:31,120 --> 00:07:34,580
所以呢，我这边把它换成 any 啊

176
00:07:34,580 --> 00:07:38,165
跟刚才那两种写法也是等价的

177
00:07:38,165 --> 00:07:41,960
再来运行一次 run 一下是吧

178
00:07:41,960 --> 00:07:44,250
也是这样的一个结果

179
00:07:44,250 --> 00:07:46,980
这边我注释强调一下这个点

180
00:07:46,980 --> 00:07:50,557
tab 这个语法呢，只能用在 switch 后面

181
00:07:50,557 --> 00:07:53,890
就如我们之前讲过的那个 recover 函数

182
00:07:53,890 --> 00:07:56,110
只能用在 def funk 后面啊

183
00:07:56,110 --> 00:07:58,680
它不能直接放在 differ 后面

184
00:07:58,680 --> 00:08:01,520
必须跟在一个 differ 的匿名函数里面

185
00:08:01,520 --> 00:08:03,602
收入 cover 才生效

186
00:08:03,602 --> 00:08:05,390
现在我要声明一个变量

187
00:08:05,390 --> 00:08:07,090
它是空接口类型

188
00:08:07,090 --> 00:08:09,710
那么我如何给这个 ABA 量赋值呢

189
00:08:09,710 --> 00:08:10,890
你可以给它赋任何值

190
00:08:10,890 --> 00:08:12,715
可以赋一个整数也可以

191
00:08:12,715 --> 00:08:16,370
你附一个字符串也可以

192
00:08:16,370 --> 00:08:19,860
你给它赋一个结构体类型也可以

193
00:08:19,860 --> 00:08:22,455
好，这样是没有任何语法错误的

194
00:08:22,455 --> 00:08:24,290
而且在 map 里面

195
00:08:24,290 --> 00:08:27,990
map 的 key 它实际上是可以为空接口的

196
00:08:27,990 --> 00:08:31,505
那 value 也是可以为空接口的

197
00:08:31,505 --> 00:08:34,429
所以我发现我给这个 map 复制的话

198
00:08:34,429 --> 00:08:36,049
K 有时候呢是字符串

199
00:08:36,049 --> 00:08:38,667
有时候呢是整数都可以

200
00:08:38,667 --> 00:08:41,840
value 的话类型也是可以变来变去

201
00:08:41,840 --> 00:08:43,100
不管什么类型

202
00:08:43,100 --> 00:08:46,320
其值都可以使用百分号 V 来进行输出

203
00:08:46,320 --> 00:08:49,480
其类型都是使用百分号大 T 来进行展示

204
00:08:50,620 --> 00:08:52,160
包括切片吧

205
00:08:52,160 --> 00:08:55,300
切片里面的数据类型也可以是空接口

206
00:08:55,300 --> 00:08:58,580
我把这个 interface 大括号用 any 来替

207
00:08:58,580 --> 00:09:00,150
也是等价的

208
00:09:00,150 --> 00:09:01,720
往里面追加一个整数

209
00:09:01,720 --> 00:09:03,440
或者追加一个字符串

210
00:09:03,440 --> 00:09:05,120
或者追加一个浮点数

211
00:09:05,120 --> 00:09:07,572
追加一个 BT 都是可以的

212
00:09:07,572 --> 00:09:09,030
有了这样一个切片之后

213
00:09:09,030 --> 00:09:12,430
我想调用上面写好的这个 SUI 

214
00:09:12,430 --> 00:09:15,210
这个函数需要传一个不定长参数吗

215
00:09:15,210 --> 00:09:19,980
所以呢，我们把刚才这个切片转成不定参数

216
00:09:19,980 --> 00:09:24,800
只需要在后面加上三个点就可以了

217
00:09:24,800 --> 00:09:28,960
这样的话就把切片转成了不定长参数

218
00:09:28,960 --> 00:09:30,860
其返回值是一个整数嘛

219
00:09:30,860 --> 00:09:33,990
这边是用百分号 D 保存了

220
00:09:33,990 --> 00:09:35,640
完全没问题

221
00:09:35,640 --> 00:09:37,530
然后来看一下类型

222
00:09:37,530 --> 00:09:40,980
断言 I 是一个空接口

223
00:09:40,980 --> 00:09:43,875
当我们拿到一个空接口变量时

224
00:09:43,875 --> 00:09:46,710
我们可以猜测它可能是某种类型

225
00:09:46,710 --> 00:09:47,950
比如说我猜测啊

226
00:09:47,950 --> 00:09:49,430
他应该是整形

227
00:09:49,430 --> 00:09:52,250
那么呢，你就断言它是整形

228
00:09:52,250 --> 00:09:55,150
当然了，你这个断言可能会断失败吧

229
00:09:55,150 --> 00:09:58,160
到底断言成功了还是断言失败了

230
00:09:58,160 --> 00:10:00,840
也说到底你猜对了还是猜错了

231
00:10:00,840 --> 00:10:04,530
可以通过 OK 这个比呢来进行识别

232
00:10:04,530 --> 00:10:06,120
如果 K 为 true 

233
00:10:06,120 --> 00:10:08,105
就代表你猜对了

234
00:10:08,105 --> 00:10:09,420
那么此时呢

235
00:10:09,420 --> 00:10:13,060
这个 V 他就已经是一个整形变量了

236
00:10:13,060 --> 00:10:17,385
可以通过百分号 D 对 V 呢进行打印

237
00:10:17,385 --> 00:10:19,800
这样的话我们就把一个 I 

238
00:10:19,800 --> 00:10:21,160
把一个空接

239
00:10:21,160 --> 00:10:24,762
转成了一个具体的整形

240
00:10:24,762 --> 00:10:28,230
或者说你也可以往浮点三二进行断言

241
00:10:28,230 --> 00:10:30,570
如果 OK ，如果你断言成功了

242
00:10:30,570 --> 00:10:32,440
那么此时这个 V 

243
00:10:32,440 --> 00:10:36,357
它就已经是一个浮点三二的数据类型了

244
00:10:36,357 --> 00:10:39,680
通过百分号 F 对 V 进行打印

245
00:10:39,680 --> 00:10:43,352
因为很多时候我们拿到一个空接口

246
00:10:43,352 --> 00:10:45,420
没法对它执行各种运算嘛

247
00:10:45,420 --> 00:10:48,040
还是要把它转成一个具体的数据类型

248
00:10:48,040 --> 00:10:49,040
才能去使用它
