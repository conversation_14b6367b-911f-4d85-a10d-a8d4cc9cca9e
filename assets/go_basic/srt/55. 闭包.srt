1
00:00:00,639 --> 00:00:02,900
构圆领 B 包确实不好理解

2
00:00:02,900 --> 00:00:04,140
但好在用的不多

3
00:00:04,140 --> 00:00:05,860
我们尽量的通过动画

4
00:00:05,860 --> 00:00:08,077
给大家清晰的进行一下展示

5
00:00:08,077 --> 00:00:11,720
B 包定义是说一个函数它绑定了一个变量

6
00:00:11,720 --> 00:00:14,610
导致呢，这个变量超出了它的作用域

7
00:00:14,610 --> 00:00:15,450
什么意思呢

8
00:00:15,450 --> 00:00:17,282
还得看这段代码

9
00:00:17,282 --> 00:00:19,860
左边先搞了一个 I 的函数

10
00:00:19,860 --> 00:00:22,360
里面搞了一个局部的变量 A 

11
00:00:22,360 --> 00:00:23,857
初始值为三

12
00:00:23,857 --> 00:00:25,830
然后呢，这个艾特函数啊

13
00:00:25,830 --> 00:00:27,550
它要返回一个什么

14
00:00:27,550 --> 00:00:29,490
返回一个函数类型是吧

15
00:00:29,490 --> 00:00:30,910
这个函数类型呢

16
00:00:30,910 --> 00:00:32,170
入参为空

17
00:00:32,170 --> 00:00:33,730
返回值为一个整形

18
00:00:33,730 --> 00:00:38,692
于是乎呢，第七行就 return 了这样一种函数类型

19
00:00:38,692 --> 00:00:40,580
而在这个函数内部的话

20
00:00:40,580 --> 00:00:43,860
他对这个外部的这个 AN 执行了加加

21
00:00:43,860 --> 00:00:45,490
然后返回了这个 A 

22
00:00:45,490 --> 00:00:47,620
为什么我说第八行即可

23
00:00:47,620 --> 00:00:50,500
它是外部的第六行即可 A 呢

24
00:00:50,500 --> 00:00:53,690
因为在第七行这个函数内部

25
00:00:53,690 --> 00:00:56,860
如果你第一次出现一个全新 DNA 的话

26
00:00:56,860 --> 00:00:59,050
你是需要通过 Y 关键字

27
00:00:59,050 --> 00:01:02,720
或者是通过冒号等于来声明这个变量

28
00:01:02,720 --> 00:01:05,420
但是呢，他并没有去声明 A 这个变量

29
00:01:05,420 --> 00:01:06,600
直接使用了 A 

30
00:01:06,600 --> 00:01:10,360
说明他使用的是外部的第六行这个 A 

31
00:01:10,360 --> 00:01:11,300
那这样的话

32
00:01:11,300 --> 00:01:14,040
第六方这个本来是属于 A 的函数的

33
00:01:14,040 --> 00:01:15,160
一个局部变量

34
00:01:15,160 --> 00:01:16,600
但是由于这个 I 的函数

35
00:01:16,600 --> 00:01:19,015
它返回了这样一个 s function 

36
00:01:19,015 --> 00:01:19,980
就导致呢

37
00:01:19,980 --> 00:01:22,640
它返回的这个方程只要还能用

38
00:01:22,640 --> 00:01:26,495
那么这个局部变 A 的生命周期就还在

39
00:01:26,495 --> 00:01:28,040
我们来看幂函数

40
00:01:28,040 --> 00:01:31,647
第14行调用二的函数返回一个函数

41
00:01:31,647 --> 00:01:33,390
函数名是 AF 

42
00:01:33,390 --> 00:01:37,557
然后呢，第15 Q 去调用 AF 这个函数

43
00:01:37,557 --> 00:01:39,660
那么在整个 main 函数里面

44
00:01:39,660 --> 00:01:42,825
这个 AF 是一直可以打问的

45
00:01:42,825 --> 00:01:44,480
但是按照常规理解

46
00:01:44,480 --> 00:01:47,120
第14行就 at 的函数已经返回了

47
00:01:47,120 --> 00:01:49,700
导致从第14行往后

48
00:01:49,700 --> 00:01:52,330
那么 at 的函数内部的局部变量

49
00:01:52,330 --> 00:01:55,525
这个 A 就应该不可以再访问了

50
00:01:55,525 --> 00:01:56,960
但是由于 B 

51
00:01:56,960 --> 00:02:00,392
导致了这个 BA 一直可以包围

52
00:02:00,392 --> 00:02:02,170
我们动画来理解一下

53
00:02:02,170 --> 00:02:03,730
首先进入幂函数

54
00:02:03,730 --> 00:02:05,510
幂函数来到第14行

55
00:02:05,510 --> 00:02:06,910
调用艾特函数

56
00:02:06,910 --> 00:02:08,716
进到了第六行

57
00:02:08,716 --> 00:02:11,720
吉利行呢，搞了一个局部变量 A 啊

58
00:02:11,720 --> 00:02:14,480
我们用这个黄色背景来表示生存

59
00:02:14,480 --> 00:02:15,320
一块内存

60
00:02:15,320 --> 00:02:17,050
内存里面存放着变成 A 

61
00:02:17,050 --> 00:02:18,440
A 的值等于三

62
00:02:18,440 --> 00:02:21,370
然后呢，第七行它返回了一个函数

63
00:02:21,370 --> 00:02:22,805
就是 AF 

64
00:02:22,805 --> 00:02:24,950
而这个 AF 这个函数

65
00:02:24,950 --> 00:02:27,250
由于引用了这个变量 A 嘛

66
00:02:27,250 --> 00:02:30,300
所以呢，就导致这个变量 A 他逃逸了

67
00:02:30,300 --> 00:02:32,080
它生命周期啊，逃逸了

68
00:02:32,080 --> 00:02:34,830
他这个函数 VF 绑定在了一起

69
00:02:34,830 --> 00:02:36,020
然后第15行

70
00:02:36,020 --> 00:02:37,420
调用函数 AF 

71
00:02:37,420 --> 00:02:39,667
它依然能够访问到变量 A 

72
00:02:39,667 --> 00:02:41,780
直行加加 A 变成四

73
00:02:41,780 --> 00:02:43,350
第16行继续执行

74
00:02:43,350 --> 00:02:44,570
加加 A 变成了五

75
00:02:44,570 --> 00:02:46,150
然后呢，同样道理

76
00:02:46,150 --> 00:02:48,252
我们来到第18行

77
00:02:48,252 --> 00:02:50,110
再次调用 IX 函数

78
00:02:50,110 --> 00:02:52,930
又创建了一个局部的变量 A 

79
00:02:52,930 --> 00:02:53,800
注意啊

80
00:02:53,800 --> 00:02:57,290
此时这个变量 A 我雇用蓝色背景是吧

81
00:02:57,290 --> 00:02:58,960
最开始是黄色背景吧

82
00:02:58,960 --> 00:02:59,950
背景不一样

83
00:02:59,950 --> 00:03:02,790
代表它们是不同的内存空间啊

84
00:03:02,790 --> 00:03:04,380
这个内存地址是不一样的

85
00:03:04,380 --> 00:03:07,070
所以这个边 A 初始值也是三

86
00:03:07,070 --> 00:03:08,840
跟刚才的逻辑一样对吧

87
00:03:08,840 --> 00:03:11,500
调 I 函数返回一个函数叫 BF 

88
00:03:11,500 --> 00:03:12,460
那这个 BF 呢

89
00:03:12,460 --> 00:03:15,210
结果这个蓝色的变 IA 搞定了

90
00:03:15,210 --> 00:03:17,800
每次降 BF 都是蓝色区域的

91
00:03:17,800 --> 00:03:21,650
这个 A 呢，加一也是输出四和五

92
00:03:21,650 --> 00:03:23,260
这是刚才的代码

93
00:03:23,260 --> 00:03:25,340
我们把这代码能跑一下看一看

94
00:03:25,340 --> 00:03:28,340
好，两次都是输出四和五是吧

95
00:03:28,340 --> 00:03:29,230
四和五

96
00:03:29,230 --> 00:03:32,420
我们也可以把这个变量 A 的地址打印出来

97
00:03:32,420 --> 00:03:33,300
看答案

98
00:03:33,300 --> 00:03:35,580
一个变量地址使用百分号 

99
00:03:35,580 --> 00:03:37,342
加一个取值符号

100
00:03:37,342 --> 00:03:39,010
这是在 I 的函数里面

101
00:03:39,010 --> 00:03:41,010
创建了一个局部的变量 A 

102
00:03:41,010 --> 00:03:43,650
然后我们来到这个 B 包函数里面

103
00:03:43,650 --> 00:03:45,980
看看它的地址是什么

104
00:03:45,980 --> 00:03:47,000
B 包函数

105
00:03:47,000 --> 00:03:48,350
我们再来运行一下

106
00:03:48,350 --> 00:03:50,920
OK ，那第一次调用 I 的函数

107
00:03:50,920 --> 00:03:55,630
这个 A 它对应的地址呢，是 A 0 F 8

108
00:03:55,630 --> 00:03:57,210
然后进 B 包里面

109
00:03:57,210 --> 00:03:59,330
这个 B 点依然是 A 0 F 8，对吧

110
00:03:59,330 --> 00:04:02,380
A 0 F 8， B 值 N 变还是它夹

111
00:04:02,380 --> 00:04:04,780
然后呢，执行加加，等下4 O 

112
00:04:04,780 --> 00:04:07,610
那第二次再次调用 I 函数

113
00:04:07,610 --> 00:04:10,300
重新创建一个全新的局域变 I 

114
00:04:10,300 --> 00:04:13,120
此时地址变成了 A 128

115
00:04:13,120 --> 00:04:15,342
最后都是 A 128

116
00:04:15,342 --> 00:04:18,529
再来看一个 B 包和 for 循环结合的案例

117
00:04:18,529 --> 00:04:19,890
首先先不管 B 包

118
00:04:19,890 --> 00:04:21,450
我们先来看这个 for 循环

119
00:04:21,450 --> 00:04:23,610
就是73行和第74行

120
00:04:23,610 --> 00:04:26,530
这两行代码是一个很简单的 for 循环嘛

121
00:04:26,530 --> 00:04:27,890
I 循环三次

122
00:04:27,890 --> 00:04:30,150
那么在这三次的 for 循环里面

123
00:04:30,150 --> 00:04:32,840
变量 I 它是同一个 I 嘛

124
00:04:32,840 --> 00:04:33,950
实际上不是

125
00:04:33,950 --> 00:04:37,190
我们如果说把 i id 地址打出来

126
00:04:37,190 --> 00:04:38,820
通过百分号 P 打出来

127
00:04:38,820 --> 00:04:40,740
你会发现三次 for 循环

128
00:04:40,740 --> 00:04:42,640
I 对应的地址都不一样

129
00:04:42,640 --> 00:04:44,862
是三个完全不同的 I 

130
00:04:44,862 --> 00:04:46,030
看第70行

131
00:04:46,030 --> 00:04:47,830
这边是声明了一个切片

132
00:04:47,830 --> 00:04:51,120
切片里面的每一个元素呢，是一种函数

133
00:04:51,120 --> 00:04:53,840
然后根据下面这个三次 for 循环呢

134
00:04:53,840 --> 00:04:56,280
就是想往这个切片里面

135
00:04:56,280 --> 00:04:59,910
通过判断去追加三个函数

136
00:04:59,910 --> 00:05:01,440
而每个函数呢

137
00:05:01,440 --> 00:05:03,440
它又引用了这个 I ，对吧

138
00:05:03,440 --> 00:05:07,565
引用了这个函数外部的这个 I 嘛

139
00:05:07,565 --> 00:05:09,330
导致形成地包

140
00:05:09,330 --> 00:05:10,360
正常来讲

141
00:05:10,360 --> 00:05:12,500
当这个 for 循环结束之后

142
00:05:12,500 --> 00:05:14,920
那么对应的 I ， I 是局部变量

143
00:05:14,920 --> 00:05:16,880
I 就不可访问了

144
00:05:16,880 --> 00:05:20,010
但是呢，由于 for 循环完全流出之后

145
00:05:20,010 --> 00:05:22,330
这个函数还是可以导入的

146
00:05:22,330 --> 00:05:24,750
因为函数放到了这个切片里面了

147
00:05:24,750 --> 00:05:27,610
所以呢，导致对应的 I 也还是可以导入

148
00:05:27,610 --> 00:05:30,000
我们在下面通过 for 循环

149
00:05:30,000 --> 00:05:32,310
把这个切片里面的每一个函数

150
00:05:32,310 --> 00:05:33,730
分别的执行一下

151
00:05:33,730 --> 00:05:37,682
通过加一个小括来调用这个函数嘛

152
00:05:37,682 --> 00:05:39,340
当我调用这个函数时候

153
00:05:39,340 --> 00:05:41,090
它打印出来的这个 I 呢

154
00:05:41,090 --> 00:05:44,530
就是当时创建这个函数的时候

155
00:05:44,530 --> 00:05:46,840
它所持有的那个 I 

156
00:05:46,840 --> 00:05:49,830
那我们说三次 for 循环是三个不同的 I 

157
00:05:49,830 --> 00:05:51,350
那么这三个函数

158
00:05:51,350 --> 00:05:54,280
它们所求的自然也是三个不同的 I 

159
00:05:54,280 --> 00:05:56,602
分别是零、一、二

160
00:05:56,602 --> 00:05:57,990
我们来运行一下

161
00:05:57,990 --> 00:05:58,930
好，看一下

162
00:05:58,930 --> 00:06:02,300
首先说在这三次的 for 循环里面

163
00:06:02,300 --> 00:06:04,190
那么这个 ID 的地址呢

164
00:06:04,190 --> 00:06:06,170
确实时代变化对吧

165
00:06:06,170 --> 00:06:07,210
这样三次

166
00:06:07,210 --> 00:06:09,710
然后在 B 包函数里面的第一次

167
00:06:09,710 --> 00:06:13,857
B 包函数是这个 A 1 E 8

168
00:06:13,857 --> 00:06:15,880
跟第一次这个是一样的

169
00:06:15,880 --> 00:06:18,350
第二次 B 保函数呢，是 A 210

170
00:06:18,350 --> 00:06:19,770
跟这边第二次是一样的

171
00:06:19,770 --> 00:06:23,850
第三次 A 218跟这个 A 118是一样

172
00:06:23,850 --> 00:06:27,530
对应的 I 的值分别是零、一、二

173
00:06:27,530 --> 00:06:29,110
不光是这种 for 循环

174
00:06:29,110 --> 00:06:32,370
会每次都给 I 申请全新的内衣空间

175
00:06:32,370 --> 00:06:35,950
包括类似于这种 for 循环吧

176
00:06:35,950 --> 00:06:37,340
去便利一个切片

177
00:06:37,340 --> 00:06:39,830
他每次也都会给这个 V 

178
00:06:39,830 --> 00:06:41,790
申请不同的那一组空间
