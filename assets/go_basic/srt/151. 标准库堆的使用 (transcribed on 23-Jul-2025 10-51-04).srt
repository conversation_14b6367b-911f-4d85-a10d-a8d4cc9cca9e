1
00:00:00,000 --> 00:00:05,100
正如 golang 标准库提供了双端量表这样一个数据结构一样

2
00:00:05,100 --> 00:00:08,560
golang 标准库同样也提供了一个堆的实现

3
00:00:08,560 --> 00:00:09,860
我们可以直接使用

4
00:00:09,860 --> 00:00:13,200
只不过不像双端量表用起来那么直接

5
00:00:13,200 --> 00:00:15,560
如果你想使用这个堆的话

6
00:00:15,560 --> 00:00:18,280
我们还需要自己去实现个结构体

7
00:00:18,280 --> 00:00:23,280
而且这个结构体还需要自己去实现以下这五个方法

8
00:00:23,280 --> 00:00:27,660
分别是 lag 就返回这个堆里面有几个元素

9
00:00:27,660 --> 00:00:33,020
less ig 就是返回第二个元素是否小于第几个元素

10
00:00:33,020 --> 00:00:36,300
swap 交换第二个和第几个元素

11
00:00:36,300 --> 00:00:40,640
push 就是我要往堆里面去添加一个新的元素

12
00:00:40,640 --> 00:00:44,260
pop 就是要返回并删除堆的元素

13
00:00:44,260 --> 00:00:47,040
具体的我们来看一下代码

14
00:00:47,040 --> 00:00:52,100
比方说这里面我要自己去定义一个priority queen

15
00:00:52,100 --> 00:00:53,780
我们称之为优先对冽

16
00:00:53,780 --> 00:00:57,240
我们把它搞成一个struct 九体

17
00:00:57,240 --> 00:01:00,640
它本质上就是这样的一个切片

18
00:01:00,640 --> 00:01:01,960
这个item是什么

19
00:01:01,960 --> 00:01:03,640
item是我们自己定义的

20
00:01:03,640 --> 00:01:06,520
它里面包含一个info和一个value

21
00:01:06,520 --> 00:01:09,200
这个info你可以自己随便定义

22
00:01:09,200 --> 00:01:09,680
我们不管

23
00:01:09,680 --> 00:01:12,540
你当然也可以包含其他的各种字段

24
00:01:12,540 --> 00:01:14,320
核心是有一个value

25
00:01:14,320 --> 00:01:18,680
因为这个value才是真正用来构建堆的

26
00:01:18,680 --> 00:01:20,040
因为在勾给堆的时候

27
00:01:20,040 --> 00:01:23,360
需要去比较两个基点之间时大时小

28
00:01:23,360 --> 00:01:25,240
我们是依据value来对比的

29
00:01:25,240 --> 00:01:29,039
但是每一个基点还可以包含其他很多信息

30
00:01:29,039 --> 00:01:32,640
这里面就简单的用一个info来代替

31
00:01:32,640 --> 00:01:37,080
好 我们说你定义的优先级对律

32
00:01:37,080 --> 00:01:38,320
这样一个结构体的话

33
00:01:38,320 --> 00:01:41,280
它必须实现以下五个方法

34
00:01:41,280 --> 00:01:47,120
lan的话就是返回数组或者切片的长度

35
00:01:47,120 --> 00:01:50,520
因为这个它本质上就是一个切片对吧

36
00:01:50,520 --> 00:01:52,320
好 返回切片长度

37
00:01:52,320 --> 00:01:56,600
好 这个地方是要比较第1个元素和第1个元素

38
00:01:56,600 --> 00:01:58,240
是否小于第1个元素

39
00:01:58,240 --> 00:01:59,240
那我们就说

40
00:01:59,240 --> 00:02:02,600
你看 切片里面第1个元素它的value

41
00:02:02,600 --> 00:02:04,880
小于第1个元素的value

42
00:02:04,880 --> 00:02:07,360
我们是根据value来进行大小比较的

43
00:02:07,360 --> 00:02:09,040
另外顺度来说一下

44
00:02:09,040 --> 00:02:11,320
那购物员标准库提供的这个hip

45
00:02:11,320 --> 00:02:13,400
这堆它是一颗小跟堆

46
00:02:13,400 --> 00:02:16,080
如果你想使用大跟堆的话

47
00:02:16,080 --> 00:02:17,840
你只需要在这个地方

48
00:02:17,840 --> 00:02:20,760
把这个小一号改成大一号就可以了

49
00:02:20,760 --> 00:02:21,560
为什么呢

50
00:02:21,560 --> 00:02:24,160
因为它这个lice本质上

51
00:02:24,160 --> 00:02:27,160
是让你告诉我什么情况下

52
00:02:27,160 --> 00:02:28,760
第1个小于第1个

53
00:02:28,760 --> 00:02:34,560
如果说你偏偏按照它的相反的意思来的话

54
00:02:34,560 --> 00:02:36,400
那自然就变成大分队了

55
00:02:36,400 --> 00:02:41,200
OK 好 刷本交换第1个和第1个元素

56
00:02:41,200 --> 00:02:45,200
那么就是交换这个数组里面的第1个和第1个元素

57
00:02:45,200 --> 00:02:47,800
下面这两个稍微复杂点

58
00:02:47,800 --> 00:02:49,000
一个是push

59
00:02:49,000 --> 00:02:52,680
push本质是要往堆里面去添加一个新元素

60
00:02:52,680 --> 00:02:58,520
但是因为这个里面我们其实只关心如何去操作这个切片

61
00:02:58,520 --> 00:03:00,880
你先不要想堆那一头东西

62
00:03:00,880 --> 00:03:05,400
堆的话它是说在外面那一层购物员提供的hip

63
00:03:05,400 --> 00:03:07,240
它帮你去崩溃了很多事情

64
00:03:07,240 --> 00:03:10,360
在这个里面你只需要关心这个切片就可以了

65
00:03:10,360 --> 00:03:15,400
好 你往里面添加一个元素实际上就是往切片尾部去追加元素

66
00:03:15,400 --> 00:03:18,960
通过append往切片尾部去追加一个元素

67
00:03:18,960 --> 00:03:19,840
那这里面注意

68
00:03:19,840 --> 00:03:25,480
由于它这个要求你必须传的x是一个interface空接口

69
00:03:25,480 --> 00:03:28,040
所以我们还是要先把这个interface

70
00:03:28,040 --> 00:03:32,840
先转成这个itm指针才能够往切片里面去添加

71
00:03:32,840 --> 00:03:38,280
因为最上面你这个切片它里面的每个元素本质上还是一个itm的指针

72
00:03:38,280 --> 00:03:44,480
好 那为什么往切片里面添加我们需要在这个切片前面加一个星星呢

73
00:03:44,480 --> 00:03:48,320
这其实就是指针的解析

74
00:03:48,320 --> 00:03:53,800
就是说本来这个pq我们看这个priority queen优先一堆列

75
00:03:53,800 --> 00:03:56,000
它是一个切片对吧

76
00:03:56,000 --> 00:03:59,120
前面加了一个指针它实际上是一个切片的指针

77
00:03:59,120 --> 00:04:02,480
好 pq是切片的指针

78
00:04:02,480 --> 00:04:06,000
那么我们说append的操作它本身支持的是切片

79
00:04:06,000 --> 00:04:07,640
而不是切片的指针

80
00:04:07,640 --> 00:04:11,440
所以你还是要把它从指针形式转为非指针形式

81
00:04:11,440 --> 00:04:12,560
就是前面加一个星

82
00:04:12,560 --> 00:04:14,240
我们称之为指针的解析

83
00:04:14,240 --> 00:04:15,720
那既然这样的话

84
00:04:15,720 --> 00:04:16,800
有同学就问了

85
00:04:16,800 --> 00:04:18,399
你为什么搞得这么麻烦

86
00:04:18,399 --> 00:04:20,180
那你直接传的时候

87
00:04:20,180 --> 00:04:22,840
你就把它传成非指针

88
00:04:22,840 --> 00:04:24,440
就传原始的切片

89
00:04:24,440 --> 00:04:25,140
不就可以了吗

90
00:04:25,140 --> 00:04:26,940
你看这个报数已经没有了

91
00:04:26,940 --> 00:04:27,200
对吧

92
00:04:27,200 --> 00:04:28,960
也不行

93
00:04:28,960 --> 00:04:29,520
也不行

94
00:04:29,520 --> 00:04:30,000
为什么呢

95
00:04:30,000 --> 00:04:32,520
就是因为这个append的本质上

96
00:04:32,520 --> 00:04:34,280
它反而是一个新切片

97
00:04:34,280 --> 00:04:36,960
相当来是你在这个函数里面

98
00:04:36,960 --> 00:04:38,560
构造了一个新切片

99
00:04:38,560 --> 00:04:39,400
附给了pq

100
00:04:39,400 --> 00:04:41,360
但是当这个push函数反过之后

101
00:04:41,360 --> 00:04:44,460
这个切片它并没有受影响

102
00:04:44,460 --> 00:04:45,600
并没有改变它

103
00:04:45,600 --> 00:04:47,300
所以如果你想在一个函数里面

104
00:04:47,300 --> 00:04:48,860
改变某一个变量的话

105
00:04:48,860 --> 00:04:50,400
必须传它的指针

106
00:04:50,400 --> 00:04:53,660
这个估计同学还是听不太明白

107
00:04:53,660 --> 00:04:56,180
那我们今天就讲这么多

108
00:04:56,180 --> 00:04:58,020
关于指针的问题

109
00:04:58,020 --> 00:04:59,720
我们只能在基础课程里面

110
00:04:59,720 --> 00:05:00,880
去详细的讲解

111
00:05:00,880 --> 00:05:02,200
好

112
00:05:02,200 --> 00:05:04,120
poppap的话就是518

113
00:05:04,120 --> 00:05:07,520
把这个切片最后面的元素把它给撑掉

114
00:05:07,520 --> 00:05:08,080
好

115
00:05:08,080 --> 00:05:11,800
那么首先我取得这个切片里面总共有n个元素

116
00:05:11,800 --> 00:05:15,200
然后取得最后面那个元素

117
00:05:15,200 --> 00:05:15,480
对吧

118
00:05:15,480 --> 00:05:16,200
n-1嘛

119
00:05:16,200 --> 00:05:17,800
取得切片里面最后面的元素

120
00:05:17,800 --> 00:05:18,880
付给itom

121
00:05:18,880 --> 00:05:19,400
最后呢

122
00:05:19,400 --> 00:05:20,800
要把itom进行返回

123
00:05:20,800 --> 00:05:21,320
好

124
00:05:21,320 --> 00:05:24,040
这是取得最后元素并且返回它

125
00:05:24,040 --> 00:05:26,800
但是我还要把最后元素把它给删掉

126
00:05:26,800 --> 00:05:27,400
好

127
00:05:27,400 --> 00:05:29,880
你删除一个切片或者删除一个数组

128
00:05:29,880 --> 00:05:31,120
最后面一个元素

129
00:05:31,120 --> 00:05:33,000
这个从代码层面来讲

130
00:05:33,000 --> 00:05:35,080
其实并没有真正的删除

131
00:05:35,080 --> 00:05:36,479
仅仅是说

132
00:05:36,479 --> 00:05:38,320
你把这个切片对吧

133
00:05:38,320 --> 00:05:38,479
你看

134
00:05:38,479 --> 00:05:40,159
你从0到n-1

135
00:05:40,159 --> 00:05:42,960
你把除了最后元素之外

136
00:05:42,960 --> 00:05:46,440
前面的这个子切片把它给接集出来

137
00:05:46,440 --> 00:05:48,479
付给了原始的这个切片

138
00:05:48,479 --> 00:05:51,719
从而实现了所谓的删除最后元素

139
00:05:51,720 --> 00:05:56,400
ok这样的话我们就自己实现了这样一个结构体

140
00:05:56,400 --> 00:05:58,560
它有以下五个方法

141
00:05:58,560 --> 00:06:02,800
这样的话我们就可以去套用构原标准库带了那个hift

142
00:06:02,800 --> 00:06:03,680
我们看一下怎么用

143
00:06:03,680 --> 00:06:05,680
这个priority queen到底怎么用

144
00:06:05,680 --> 00:06:05,960
好

145
00:06:05,960 --> 00:06:08,120
我们写了一个单侧函数

146
00:06:08,120 --> 00:06:10,680
通过make

147
00:06:10,680 --> 00:06:13,520
我们说这个priority queen本质上是一个切片

148
00:06:13,520 --> 00:06:17,120
所以可以通过make的方式去初时化一个切片

149
00:06:17,120 --> 00:06:19,240
初时里面是0

150
00:06:19,240 --> 00:06:22,720
没有任何元素只不过容量为10

151
00:06:22,720 --> 00:06:23,040
好

152
00:06:23,040 --> 00:06:24,640
然后通过push

153
00:06:24,640 --> 00:06:28,680
三次push往这个切片里面去添加了三个元素

154
00:06:28,680 --> 00:06:31,120
这里面这个information无关紧要

155
00:06:31,120 --> 00:06:32,400
它是一个附加信息

156
00:06:32,400 --> 00:06:34,840
真正参与大小笔制的

157
00:06:34,840 --> 00:06:36,280
是这里面的value

158
00:06:36,280 --> 00:06:37,640
324value

159
00:06:37,640 --> 00:06:38,480
好

160
00:06:38,480 --> 00:06:40,360
随便插了三个元素处之后

161
00:06:40,360 --> 00:06:41,800
我们调了这个unit

162
00:06:41,800 --> 00:06:47,720
这个unit就类似于我们之前自己实现这个时候调的那个build

163
00:06:47,720 --> 00:06:49,720
就类似于build

164
00:06:49,720 --> 00:06:51,000
什么意思

165
00:06:51,000 --> 00:06:51,800
就是构建堆

166
00:06:51,800 --> 00:06:54,680
本来这三个元素是完全无序的

167
00:06:54,680 --> 00:06:55,400
没有任何规律

168
00:06:55,400 --> 00:07:00,440
我通过unit使得它满足小跟堆这样一个顺序

169
00:07:00,440 --> 00:07:01,440
好

170
00:07:01,440 --> 00:07:02,840
堆构建好之后

171
00:07:02,840 --> 00:07:05,920
我又往里面去添加了一个元素

172
00:07:05,920 --> 00:07:06,440
6

173
00:07:06,440 --> 00:07:07,920
这个时候的话

174
00:07:07,920 --> 00:07:10,840
它内部会去触发堆的调整

175
00:07:10,840 --> 00:07:11,800
向上调整

176
00:07:11,800 --> 00:07:12,560
向下调整

177
00:07:12,560 --> 00:07:13,000
好

178
00:07:13,000 --> 00:07:16,280
然后我们就要开始实现一个堆排序

179
00:07:16,280 --> 00:07:18,360
堆排序实际上就是什么呢

180
00:07:18,360 --> 00:07:19,720
你不断的pop

181
00:07:19,720 --> 00:07:22,360
弹出这个堆顶元素

182
00:07:22,360 --> 00:07:24,240
你弹出一次

183
00:07:24,240 --> 00:07:26,120
这个堆顶的长度就要减一

184
00:07:26,120 --> 00:07:27,800
只要这个长度

185
00:07:27,800 --> 00:07:28,280
这个lance

186
00:07:28,280 --> 00:07:30,160
还没有进到0的话

187
00:07:30,160 --> 00:07:32,600
就不断的去弹出堆顶

188
00:07:32,600 --> 00:07:33,720
把它给打出来

189
00:07:33,720 --> 00:07:36,080
这样的话实际上堆顶元素

190
00:07:36,080 --> 00:07:38,240
总是剩下元素里面最小的一个

191
00:07:38,240 --> 00:07:41,000
最后就实现了一个从小到大的排序

192
00:07:41,000 --> 00:07:44,360
那这里面我们自己写的这个优先队列

193
00:07:44,360 --> 00:07:47,320
和这个购物标准库资在这个黑本

194
00:07:47,320 --> 00:07:49,400
是如何进行结合的呢

195
00:07:49,400 --> 00:07:50,640
我们注意一下

196
00:07:50,640 --> 00:07:51,320
我们注意一下

197
00:07:51,320 --> 00:07:52,000
再看

198
00:07:52,000 --> 00:07:55,480
在前面的这一拨代码里面

199
00:07:55,480 --> 00:07:58,120
完全没有引入这个黑本

200
00:07:58,120 --> 00:07:59,280
完全没有引入表情库

201
00:07:59,280 --> 00:08:01,040
全部是我们自己写的代码

202
00:08:01,040 --> 00:08:02,960
而第二十五花

203
00:08:02,960 --> 00:08:03,400
你看看

204
00:08:03,400 --> 00:08:04,640
这个黑本是什么

205
00:08:04,640 --> 00:08:05,920
这个黑本实际上是包

206
00:08:05,920 --> 00:08:07,400
是这个包名

207
00:08:07,400 --> 00:08:09,400
这个包下面有一个init

208
00:08:09,400 --> 00:08:11,000
只不过这个init

209
00:08:11,000 --> 00:08:13,600
需要把我们刚刚构建好的这个结构体

210
00:08:13,600 --> 00:08:14,960
本上是个切片码

211
00:08:14,960 --> 00:08:16,680
把这个结构体的地址

212
00:08:16,680 --> 00:08:18,040
直接给传进来

213
00:08:18,040 --> 00:08:20,520
而且下面的这个push

214
00:08:20,520 --> 00:08:20,760
你看

215
00:08:20,760 --> 00:08:22,040
这边这个push

216
00:08:22,040 --> 00:08:23,560
和这边这个push不一样

217
00:08:23,560 --> 00:08:25,160
上面这个push是什么

218
00:08:25,160 --> 00:08:26,680
是在操作这个切片

219
00:08:26,680 --> 00:08:28,020
而下面这个push

220
00:08:28,020 --> 00:08:30,400
它是通过堆的借口

221
00:08:30,400 --> 00:08:31,860
往里面去添加元素

222
00:08:31,860 --> 00:08:33,440
就是上面这个push

223
00:08:33,440 --> 00:08:35,360
仅仅是往切片里面

224
00:08:35,360 --> 00:08:36,600
很简单的

225
00:08:36,600 --> 00:08:38,460
很直接的去添加一个元素而已

226
00:08:38,460 --> 00:08:40,060
但是第二十六花这个push

227
00:08:40,060 --> 00:08:43,420
它会自动的触发那个堆的调整

228
00:08:43,420 --> 00:08:46,420
包括下面这个弹珠堆顶

229
00:08:46,420 --> 00:08:48,980
它也是通过这个标准库里面

230
00:08:48,980 --> 00:08:52,660
keep这个包来进行堆顶的删除

231
00:08:52,660 --> 00:08:54,260
好

232
00:08:54,260 --> 00:08:57,180
我们还是把这个代码测试一下

233
00:08:57,180 --> 00:08:58,780
看看这个堆卡序

234
00:08:58,780 --> 00:09:00,780
能不能够输出一个正确的结果

235
00:09:00,780 --> 00:09:04,300
理念不是有三二四六四个元素吗

236
00:09:04,300 --> 00:09:05,020
最后呢

237
00:09:05,020 --> 00:09:07,100
它输出二三四六

238
00:09:07,100 --> 00:09:09,980
就把按照value从小到的push了

239
00:09:09,980 --> 00:09:10,980
好

240
00:09:10,980 --> 00:09:11,580
好

241
00:09:11,580 --> 00:09:11,980
好

242
00:09:11,980 --> 00:09:12,620
好

