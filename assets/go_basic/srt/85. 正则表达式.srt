1
00:00:00,000 --> 00:00:01,740
来看一下正则表达式

2
00:00:01,740 --> 00:00:04,837
也是工作中非常实用的一个工具

3
00:00:04,837 --> 00:00:07,490
比如说我有这样一行日志吧

4
00:00:07,490 --> 00:00:09,450
record use time 38毫秒

5
00:00:09,450 --> 00:00:11,632
salt use time 20毫秒

6
00:00:11,632 --> 00:00:14,480
日志是很随意的一个字符串

7
00:00:14,480 --> 00:00:17,320
但是呢，只要是这一行打出来的是

8
00:00:17,320 --> 00:00:19,940
整体上这个格式是一模一样的

9
00:00:19,940 --> 00:00:23,370
无非是每次具体的这个耗时不一样

10
00:00:23,370 --> 00:00:25,245
这个数值不一样而已

11
00:00:25,245 --> 00:00:28,520
比如说现在我想把这些数字呢

12
00:00:28,520 --> 00:00:30,220
从日志里面抠出来

13
00:00:30,220 --> 00:00:31,540
然后进行一个统计

14
00:00:31,540 --> 00:00:36,930
比如说这个 record 平均下来它耗时是多少毫秒

15
00:00:36,930 --> 00:00:40,472
salt 平均下来耗时是多少毫秒

16
00:00:40,472 --> 00:00:43,125
我要去解析这个日志文件嘛

17
00:00:43,125 --> 00:00:46,990
那我怎么把我想要的内容给单独抠出来呢

18
00:00:46,990 --> 00:00:49,260
就可以使用正则表达式

19
00:00:49,260 --> 00:00:52,105
这边是使用的 REDX 这个包

20
00:00:52,105 --> 00:00:53,920
这个 boss 下面呢有两个

21
00:00:53,920 --> 00:00:55,120
一个是 must compile 

22
00:00:55,120 --> 00:00:56,640
一个是 compile 

23
00:00:56,640 --> 00:01:00,095
这个 compile 本身会返回两个值了

24
00:01:00,095 --> 00:01:01,840
可能会返回一个 error 

25
00:01:01,840 --> 00:01:04,019
因为传的这个正则表达式

26
00:01:04,019 --> 00:01:05,319
可能语法上有问题吗

27
00:01:05,319 --> 00:01:06,720
返回一个 error 

28
00:01:06,720 --> 00:01:10,290
而我们的这个 must company 呢

29
00:01:10,290 --> 00:01:11,430
它不返回 L 啊

30
00:01:11,430 --> 00:01:12,770
只返回一个值

31
00:01:12,770 --> 00:01:14,470
如果发生 L 的话

32
00:01:14,470 --> 00:01:18,640
它是直接调 panic 来结束整个进程

33
00:01:18,640 --> 00:01:22,872
所以你要评估一下你是否希望它发生 panic 

34
00:01:22,872 --> 00:01:25,460
如果你非常避讳发生 panic 的话

35
00:01:25,460 --> 00:01:27,080
那么你应该使用 compile 

36
00:01:27,080 --> 00:01:29,152
这样的话你需要去处理那个 error 

37
00:01:29,152 --> 00:01:30,050
正则表达式

38
00:01:30,050 --> 00:01:32,430
我们一般是使用的反引

39
00:01:32,430 --> 00:01:33,330
把它给引起来

40
00:01:33,330 --> 00:01:35,870
因为这里面可能涉及到各种斜线嘛

41
00:01:35,870 --> 00:01:37,110
各种转移很麻烦

42
00:01:37,110 --> 00:01:39,310
你不直接就用一个 ban 号

43
00:01:39,310 --> 00:01:40,190
那这样的话

44
00:01:40,190 --> 00:01:41,470
遇见这个斜线啊

45
00:01:41,470 --> 00:01:43,092
你也不用加两个斜线了

46
00:01:43,092 --> 00:01:45,140
斜杠 D 表示的是数字嘛

47
00:01:45,140 --> 00:01:47,160
那加号表示一个或多个

48
00:01:47,160 --> 00:01:48,060
表示这个地方

49
00:01:48,060 --> 00:01:51,100
我实际上期待它出现一个或者多个

50
00:01:51,100 --> 00:01:52,365
连续的数字

51
00:01:52,365 --> 00:01:54,320
关于正则表达式的详细语法

52
00:01:54,320 --> 00:01:56,980
我们在构圆这个课程里面就不详细展开了

53
00:01:56,980 --> 00:02:00,560
总之吧，他希望找到这样的一种模式啊

54
00:02:00,560 --> 00:02:01,627
一种子串

55
00:02:01,627 --> 00:02:04,290
我们调用 REDX 

56
00:02:04,290 --> 00:02:09,775
它有一个 find wall submatch index 这样一个函数

57
00:02:09,775 --> 00:02:12,470
其实啊，有好多个 find 的可以调用

58
00:02:12,470 --> 00:02:13,650
我们点一下啊

59
00:02:13,650 --> 00:02:15,790
你看有好多 find 的

60
00:02:15,790 --> 00:02:17,180
各种各样的 find 的

61
00:02:17,180 --> 00:02:18,897
那其中呢

62
00:02:18,897 --> 00:02:23,630
我觉得这个 find 的函数最好用啊

63
00:02:23,630 --> 00:02:25,465
它最基础、最万能

64
00:02:25,465 --> 00:02:26,870
从名字上来看

65
00:02:26,870 --> 00:02:31,450
他想是找到所有的能匹配上的部分的

66
00:02:31,450 --> 00:02:33,427
index 有下标吗

67
00:02:33,427 --> 00:02:34,500
参数两个

68
00:02:34,500 --> 00:02:35,400
第一个呢

69
00:02:35,400 --> 00:02:39,060
你要找的这个原始的字符串是谁啊

70
00:02:39,060 --> 00:02:40,380
是这个 log 

71
00:02:40,380 --> 00:02:43,615
把这个 log 呢转成一个 by 的切片

72
00:02:43,615 --> 00:02:46,540
第二个是你打算匹配几次

73
00:02:46,540 --> 00:02:49,180
比如说这个正则表达式是吧

74
00:02:49,180 --> 00:02:51,240
它实际上在这个字符串里面

75
00:02:51,240 --> 00:02:53,800
它能够匹配到两个地方

76
00:02:53,800 --> 00:02:55,367
这是一个地方

77
00:02:55,367 --> 00:02:57,122
这也是一个地方

78
00:02:57,122 --> 00:02:58,760
可能会匹配上很多次

79
00:02:58,760 --> 00:03:01,360
那么你打算只匹配前几次呢

80
00:03:01,360 --> 00:03:03,280
就是输给第二个参数

81
00:03:03,280 --> 00:03:07,020
一的话表示所有地方我都要匹配

82
00:03:07,020 --> 00:03:07,650
好

83
00:03:07,650 --> 00:03:10,190
所以返回的这个值啊

84
00:03:10,190 --> 00:03:11,050
它是什么

85
00:03:11,050 --> 00:03:13,397
它是一个二维的切片

86
00:03:13,397 --> 00:03:14,860
在刚才这个例子里面

87
00:03:14,860 --> 00:03:17,660
我们说能够匹配上两个地方

88
00:03:17,660 --> 00:03:21,910
所以呢，那么这个二维切片它的低维就是二

89
00:03:21,910 --> 00:03:23,410
它有两行

90
00:03:23,410 --> 00:03:26,107
那每一行有几列呢

91
00:03:26,107 --> 00:03:27,270
从目前来看

92
00:03:27,270 --> 00:03:29,390
每一行应该是有四列

93
00:03:29,390 --> 00:03:30,610
为什么是四列呢

94
00:03:30,610 --> 00:03:33,400
我们直接把大家跑一下来分析一下

95
00:03:33,400 --> 00:03:34,667
test 

96
00:03:34,667 --> 00:03:36,260
好，我们看一下啊

97
00:03:36,260 --> 00:03:40,560
这个 index 1它是两行啊

98
00:03:40,560 --> 00:03:41,860
每行是四列

99
00:03:41,860 --> 00:03:44,200
这个七和20表示什么呢

100
00:03:44,200 --> 00:03:45,200
这个七和20啊

101
00:03:45,200 --> 00:03:49,200
实际上表示的是匹配上的第一部分

102
00:03:49,200 --> 00:03:50,840
它的起始位置

103
00:03:50,840 --> 00:03:54,130
那这个七的话就表示这个字母 U 啊

104
00:03:54,130 --> 00:03:56,755
它在整个 logo 里面的下标

105
00:03:56,755 --> 00:03:59,200
因为 record 是六个字母

106
00:03:59,200 --> 00:04:02,070
空格七个 U 的话是第八个

107
00:04:02,070 --> 00:04:02,710
第八个

108
00:04:02,710 --> 00:04:04,757
所以下标是七嘛

109
00:04:04,757 --> 00:04:09,920
那这个 S 它的下标对应的是20

110
00:04:09,920 --> 00:04:12,130
那这个16和18是谁

111
00:04:12,130 --> 00:04:16,769
这个16、18实际上只就是这个数字38啊

112
00:04:16,769 --> 00:04:20,678
他是从16~18前闭后开区间

113
00:04:20,678 --> 00:04:23,370
为什么它单独的把这个38

114
00:04:23,370 --> 00:04:26,190
这个数字的下标返回的呢

115
00:04:26,190 --> 00:04:28,450
就是因为我们在正则表达式里面

116
00:04:28,450 --> 00:04:31,340
单独对这个数字加了个小括号

117
00:04:31,340 --> 00:04:33,550
那如果说我们对这个 time 

118
00:04:33,550 --> 00:04:35,840
也加一个小括号的话

119
00:04:35,840 --> 00:04:37,427
我们再来运行一遍

120
00:04:37,427 --> 00:04:39,900
它会把 time 所对应的那个位置

121
00:04:39,900 --> 00:04:41,960
下标也一并返回

122
00:04:41,960 --> 00:04:43,270
看这边对吧

123
00:04:43,270 --> 00:04:44,640
跟上次对比一下

124
00:04:44,640 --> 00:04:47,060
就多出了11~15

125
00:04:47,060 --> 00:04:49,580
指的就是我们的这个 time 啊

126
00:04:49,580 --> 00:04:51,870
它在整个 logo 里面下标

127
00:04:51,870 --> 00:04:53,042
同理

128
00:04:53,042 --> 00:04:56,250
我们后面还能匹配上这一部分是吧

129
00:04:56,250 --> 00:05:00,920
这一部分整体来说就是27~40

130
00:05:00,920 --> 00:05:03,730
然后 time 是31~35

131
00:05:03,730 --> 00:05:05,952
这个20呢

132
00:05:05,952 --> 00:05:09,180
就是36~38

133
00:05:09,180 --> 00:05:13,120
好，刚才说一表示所有地方我都要匹配

134
00:05:13,120 --> 00:05:15,160
那么如果显示指定一的话

135
00:05:15,160 --> 00:05:18,337
就只能匹配第一部分

136
00:05:18,337 --> 00:05:21,930
所以呢，他返回的这个二维切片啊

137
00:05:21,930 --> 00:05:23,010
他也是返回二维积分

138
00:05:23,010 --> 00:05:25,250
只不过这个二维切片只有一行，对吧

139
00:05:25,250 --> 00:05:28,690
只有一行跟上面这一行是一样的

140
00:05:28,690 --> 00:05:31,360
如果我把这个一改成二的话

141
00:05:31,360 --> 00:05:32,690
改成二

142
00:05:32,690 --> 00:05:35,695
然后我们再来跑一遍 test 

143
00:05:35,695 --> 00:05:36,990
好，这样的话

144
00:05:36,990 --> 00:05:38,730
index 1和 index 

145
00:05:38,730 --> 00:05:41,492
这个结果实际上是一模一样的

146
00:05:41,492 --> 00:05:42,340
好了

147
00:05:42,340 --> 00:05:45,660
现在你都能拿到每一部分的下标了是吧

148
00:05:45,660 --> 00:05:50,070
然后你就可以去 log 这个字符串里面

149
00:05:50,070 --> 00:05:51,450
根据对应的下标

150
00:05:51,450 --> 00:05:54,430
去截取出你所需要的某一个字串

151
00:05:54,430 --> 00:05:54,850
是吧

152
00:05:54,850 --> 00:05:58,630
你就可以把第一部分截出来，对吧

153
00:05:58,630 --> 00:06:01,230
你也可以把某一个小括号里面的某一部分

154
00:06:01,230 --> 00:06:02,500
把它给截出来

155
00:06:02,500 --> 00:06:04,360
所以思考一下

156
00:06:04,360 --> 00:06:08,032
我们什么情况下需要加这个小括号呢

157
00:06:08,032 --> 00:06:11,250
就是你想单独把哪一部分给提取出来

158
00:06:11,250 --> 00:06:12,830
你就对它加小括号啊

159
00:06:12,830 --> 00:06:13,890
比如说 time 

160
00:06:13,890 --> 00:06:16,490
我们一般不需要去单独的提取这部分

161
00:06:16,490 --> 00:06:18,670
因为这个是公共的部分吧

162
00:06:18,670 --> 00:06:22,030
我们更关注的是数字这一部分

163
00:06:22,030 --> 00:06:25,190
好，关于这个其他的各种泛的函数

164
00:06:25,190 --> 00:06:26,630
大家可以自行试验一下

165
00:06:26,630 --> 00:06:28,510
这里面呢，就不再一演示了
