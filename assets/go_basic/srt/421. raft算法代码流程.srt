1
00:00:00,379 --> 00:00:02,760
了解了 rap 协议的所有细节之后

2
00:00:02,760 --> 00:00:03,800
我们来考虑一下

3
00:00:03,800 --> 00:00:06,627
如何用 go 源去实现这个算法

4
00:00:06,627 --> 00:00:09,030
首先来分析一下一个低点

5
00:00:09,030 --> 00:00:11,210
它处于三种不同的状态之下

6
00:00:11,210 --> 00:00:14,530
那它分别有可能接收到哪些数据

7
00:00:14,530 --> 00:00:15,190
当然了

8
00:00:15,190 --> 00:00:17,230
所有的数据类型无非就

9
00:00:17,230 --> 00:00:21,770
心跳请求、心跳响应、投票请求和投票响应这四种

10
00:00:21,770 --> 00:00:22,990
作为 follower 

11
00:00:22,990 --> 00:00:26,850
它会受到 leader 发过来的新交请求

12
00:00:26,850 --> 00:00:28,180
这是 A 1请求

13
00:00:28,180 --> 00:00:30,250
他可能受到心跳响应吗

14
00:00:30,250 --> 00:00:30,770
不可能

15
00:00:30,770 --> 00:00:34,010
因为他不会给别人发送心跳请求

16
00:00:34,010 --> 00:00:36,620
自然不会受到心跳响应

17
00:00:36,620 --> 00:00:42,167
第二个，他可能收到 candidate 发过来的投票请求

18
00:00:42,167 --> 00:00:45,000
他可能会收到投票响应吗

19
00:00:45,000 --> 00:00:48,180
不会，因为他不会发出去投票请求

20
00:00:48,180 --> 00:00:51,060
他自然就不会收到投票响应

21
00:00:51,060 --> 00:00:55,785
所以 follow 只可能收到 A 1请求和投票请求

22
00:00:55,785 --> 00:00:58,330
再来看 candate candidate 

23
00:00:58,330 --> 00:01:01,580
它可能会收到投票响应

24
00:01:01,580 --> 00:01:04,720
因为他给比尔发送投票请求了嘛

25
00:01:04,720 --> 00:01:05,582
第二个

26
00:01:05,582 --> 00:01:07,450
他也可能会收到

27
00:01:07,450 --> 00:01:10,990
来自于另外一个 CANDATE 的投保请求

28
00:01:10,990 --> 00:01:14,150
你也可能在很短的时间之

29
00:01:14,150 --> 00:01:15,520
有两个人

30
00:01:15,520 --> 00:01:17,650
他们都变成了 candid 

31
00:01:17,650 --> 00:01:21,130
会互相给对方发送投票请求

32
00:01:21,130 --> 00:01:25,680
同时呢，可能有一个人已经率先的变成了 leader 

33
00:01:25,680 --> 00:01:27,327
他还是 candidate 

34
00:01:27,327 --> 00:01:28,370
所以这个时候呢

35
00:01:28,370 --> 00:01:31,380
他可能会收到一个心跳请求

36
00:01:31,380 --> 00:01:34,830
他不会给别人发送心跳请求

37
00:01:34,830 --> 00:01:37,730
所以呢，它不会受到心跳响应

38
00:01:37,730 --> 00:01:41,190
那有人说我也可能会受到心跳响应啊

39
00:01:41,190 --> 00:01:43,247
就说我本来是 leader 

40
00:01:43,247 --> 00:01:46,370
我给别人发送了心跳请求

41
00:01:46,370 --> 00:01:48,940
然后呢，紧接着由于种种原因

42
00:01:48,940 --> 00:01:50,400
我变成了 candidate 

43
00:01:50,400 --> 00:01:54,450
然后别人把心跳响应给我发过来了

44
00:01:54,450 --> 00:01:57,610
那这种情况下，是由于你的上一个状态

45
00:01:57,610 --> 00:02:00,660
导致了你收到了心跳响应

46
00:02:00,660 --> 00:02:02,710
那毕竟你状态已经变了

47
00:02:02,710 --> 00:02:06,050
所以说就算你收到了心跳响应

48
00:02:06,050 --> 00:02:08,430
你也不应该去处理这个响应

49
00:02:08,430 --> 00:02:10,075
不应该去处理这个数据

50
00:02:10,075 --> 00:02:13,270
所以说我这页 PPT 里面列出来的

51
00:02:13,270 --> 00:02:15,880
是说你需要处理的数据类型

52
00:02:15,880 --> 00:02:18,640
万一你收到了其他类型的数据

53
00:02:18,640 --> 00:02:20,600
那么呢，你直接忽略就可以了

54
00:02:20,600 --> 00:02:22,300
最后一个，作为 leader 

55
00:02:22,300 --> 00:02:24,300
作为 leader 它比较特殊

56
00:02:24,300 --> 00:02:28,155
它可能会收到来自于 client 的命令

57
00:02:28,155 --> 00:02:30,720
所以呢，他要去接收这个命令啊

58
00:02:30,720 --> 00:02:33,180
他把这个命令的封装成是一个日志

59
00:02:33,180 --> 00:02:35,360
然后把这个日志呢，广播给记取

60
00:02:35,360 --> 00:02:38,995
第二个，他可能会收到一个心跳响应

61
00:02:38,995 --> 00:02:42,930
同时他也可能会收到一个心跳请求

62
00:02:42,930 --> 00:02:45,280
就是说如果发生网络分区了

63
00:02:45,280 --> 00:02:47,360
大家回顾一下那个网络分区是吧

64
00:02:47,360 --> 00:02:48,920
分成了分区和分区域

65
00:02:48,920 --> 00:02:50,500
产生了两个 leader 

66
00:02:50,500 --> 00:02:52,510
那么一旦这个分区删除之后

67
00:02:52,510 --> 00:02:56,660
其中一个 leader 可能会收到另外一个 leader 

68
00:02:56,660 --> 00:02:59,040
发过来的新票请求，对吧

69
00:02:59,040 --> 00:03:00,200
他也是要去处理的

70
00:03:00,200 --> 00:03:04,135
第三个，他可能会收到一个投票请求

71
00:03:04,135 --> 00:03:07,870
就是说在另外一个分区里面产生了一个 candidate 

72
00:03:07,870 --> 00:03:10,700
他呢，向其他人发送投保请求

73
00:03:10,700 --> 00:03:13,997
这个时候刚好网络分区消除了

74
00:03:13,997 --> 00:03:15,130
互联互通了

75
00:03:15,130 --> 00:03:17,050
所以呢，他即使作为 leader 

76
00:03:17,050 --> 00:03:20,300
也有可能会收到一个投票请求

77
00:03:20,300 --> 00:03:23,380
所以啊，我们发现作为一个节点啊

78
00:03:23,380 --> 00:03:24,360
它在某一个瞬间

79
00:03:24,360 --> 00:03:26,070
它处于某一种状态

80
00:03:26,070 --> 00:03:28,002
在这个状态之下

81
00:03:28,002 --> 00:03:31,510
它有多种数据类型需要去处理

82
00:03:31,510 --> 00:03:33,860
比方说作为 candidate 

83
00:03:33,860 --> 00:03:36,480
它可能会收到这三种数据

84
00:03:36,480 --> 00:03:40,090
那么假如说这三种数据同时发过来

85
00:03:40,090 --> 00:03:41,897
他要同时去处理吗

86
00:03:41,897 --> 00:03:46,160
如果真的是并行的去处理这三种数据的话

87
00:03:46,160 --> 00:03:49,890
那么可能会导致一些中间状态数据的不一致

88
00:03:49,890 --> 00:03:53,310
因为我们也发现 rap 的协议是很复杂的

89
00:03:53,310 --> 00:03:55,900
每一个节点它有什么 term 

90
00:03:55,900 --> 00:03:57,452
有票数

91
00:03:57,452 --> 00:03:59,630
或者说给谁投过票

92
00:03:59,630 --> 00:04:04,310
有自己的 committing dex 、 last login dex 等等变量

93
00:04:04,310 --> 00:04:06,130
那如果在不同的函数里面

94
00:04:06,130 --> 00:04:08,410
去并行的修改这些变量的话

95
00:04:08,410 --> 00:04:09,490
可能会出问题

96
00:04:09,490 --> 00:04:11,810
所以我们希望对于这些数据

97
00:04:11,810 --> 00:04:14,150
请求串行的进行处理

98
00:04:14,150 --> 00:04:16,430
那数据是并行发过来的

99
00:04:16,430 --> 00:04:18,183
我如何串行处理呢

100
00:04:18,183 --> 00:04:21,829
自然是说把它们放到同一个 china 里面去

101
00:04:21,829 --> 00:04:25,240
然后一个一个的从 china 里面把数据取出来

102
00:04:25,240 --> 00:04:26,550
然后去处理这个数据

103
00:04:26,550 --> 00:04:28,200
但同时啊，一个节点

104
00:04:28,200 --> 00:04:31,620
它可能会收到另外一个优先级更高的信号

105
00:04:31,620 --> 00:04:33,835
比方说终止信号 stop 

106
00:04:33,835 --> 00:04:36,870
什么时候会收到这个 stop 信号呢

107
00:04:36,870 --> 00:04:39,810
就是说计划内的关机

108
00:04:39,810 --> 00:04:42,510
我想给这个节点增加一些 CPU 

109
00:04:42,510 --> 00:04:43,625
增加内存

110
00:04:43,625 --> 00:04:44,730
我要把它关机

111
00:04:44,730 --> 00:04:46,350
这个属于计划内关机

112
00:04:46,350 --> 00:04:48,257
但计划内关机的话

113
00:04:48,257 --> 00:04:52,680
我们希望这个程序把手头正在进行的工作

114
00:04:52,680 --> 00:04:53,610
先做完

115
00:04:53,610 --> 00:04:56,520
关机信号是比较高一线级的

116
00:04:56,520 --> 00:04:59,000
所以呢，我们需要把这个关键信号啊

117
00:04:59,000 --> 00:05:02,040
单独开辟一个 channel 来承接它

118
00:05:02,040 --> 00:05:04,510
再比方说那个超时

119
00:05:04,510 --> 00:05:06,240
一旦心跳超时

120
00:05:06,240 --> 00:05:08,320
我要立即把自己变为 CANDI 

121
00:05:08,320 --> 00:05:11,110
而不是说我还得把手头工作处理完

122
00:05:11,110 --> 00:05:12,370
再变成 candy date 

123
00:05:12,370 --> 00:05:13,750
所以这样分析下来呢

124
00:05:13,750 --> 00:05:17,850
我们认为一个节点它不管处于什么状态

125
00:05:17,850 --> 00:05:19,890
他应该有三个 channel 

126
00:05:19,890 --> 00:05:24,900
一个 channel 来处理这些正常的请求或者响应

127
00:05:24,900 --> 00:05:28,100
另外一个 CHANNE 呢，用来接收终止信号

128
00:05:28,100 --> 00:05:31,670
另外一个 CHANNE 用来接收那个倒计时超时信号

129
00:05:31,670 --> 00:05:33,985
看一下整体的代码流程

130
00:05:33,985 --> 00:05:35,290
最开始呢

131
00:05:35,290 --> 00:05:36,690
我们所有的节点

132
00:05:36,690 --> 00:05:38,542
所有的 rap 的服务器

133
00:05:38,542 --> 00:05:41,700
他们都是以 follow 身份来启动的

134
00:05:41,700 --> 00:05:43,430
那么每一个服务器

135
00:05:43,430 --> 00:05:47,310
它同时又是一个 HTTP 的 server 

136
00:05:47,310 --> 00:05:50,420
这是我们说服务器之间互相通信嘛

137
00:05:50,420 --> 00:05:53,160
通过 RPC 去发送请求和响应

138
00:05:53,160 --> 00:05:54,680
那这个 RPC 是什么呢

139
00:05:54,680 --> 00:05:56,280
你可以使用 HTTP 

140
00:05:56,280 --> 00:05:58,240
也可以使用 jr p c 等等

141
00:05:58,240 --> 00:05:59,500
我们在大码里面呢

142
00:05:59,500 --> 00:06:01,060
就使用 HHTP 

143
00:06:01,060 --> 00:06:03,300
所以说每一个服务器

144
00:06:03,300 --> 00:06:06,950
它都是做一个 HTTP server 来启动的

145
00:06:06,950 --> 00:06:10,985
就是这个和这个

146
00:06:10,985 --> 00:06:12,680
它作为 follower 启动之后

147
00:06:12,680 --> 00:06:15,630
会进到 follow loop 这个函数里面来

148
00:06:15,630 --> 00:06:18,212
这是一个无限循环的一个函数

149
00:06:18,212 --> 00:06:19,270
一方面呢

150
00:06:19,270 --> 00:06:23,330
他要去看这个心跳是否超时啊

151
00:06:23,330 --> 00:06:25,670
有没有收到来自于力量心跳

152
00:06:25,670 --> 00:06:26,750
如果超时了

153
00:06:26,750 --> 00:06:28,770
他就会变成 candidate 是吧

154
00:06:28,770 --> 00:06:31,070
进到 candidate 这个循环里面来

155
00:06:31,070 --> 00:06:32,667
那么在超时之前

156
00:06:32,667 --> 00:06:35,220
他要去监听自己的一个管道

157
00:06:35,220 --> 00:06:38,420
叫做 RPCCH 这个管道

158
00:06:38,420 --> 00:06:39,740
这个管道里面呢

159
00:06:39,740 --> 00:06:42,780
就是去容纳我们上一页 PPT 里面讲的

160
00:06:42,780 --> 00:06:44,970
各种各样的请求或者响应数据

161
00:06:44,970 --> 00:06:47,230
每个数据对应的都是一个任务啊

162
00:06:47,230 --> 00:06:48,890
好，这个管道里面呢

163
00:06:48,890 --> 00:06:51,030
就是去存放那些个任务的

164
00:06:51,030 --> 00:06:52,610
如果没有任何任务到来

165
00:06:52,610 --> 00:06:55,707
那这个堵管道操作自然要阻塞嘛

166
00:06:55,707 --> 00:06:57,820
什么时候任务会到来呢

167
00:06:57,820 --> 00:07:00,960
我们关注一下这根线是怎么来的

168
00:07:00,960 --> 00:07:04,155
那其实刚才一个 candidate 

169
00:07:04,155 --> 00:07:06,820
他不是会率先给自己的 term 加一

170
00:07:06,820 --> 00:07:08,340
然后给自己投一票

171
00:07:08,340 --> 00:07:11,855
然后呢，向其他节点发送投票请求吗

172
00:07:11,855 --> 00:07:12,660
怎么发

173
00:07:12,660 --> 00:07:16,860
具体而言就是去发送一个 HTTP 的 request 

174
00:07:16,860 --> 00:07:18,440
把头发请求发出去

175
00:07:18,440 --> 00:07:21,942
那么你做一个 HTTP 的调用方

176
00:07:21,942 --> 00:07:23,390
对应的服务端呢

177
00:07:23,390 --> 00:07:26,130
就是他 follow 其他 follower 作为服务端

178
00:07:26,130 --> 00:07:28,640
好，其他的这个 HTTP server 

179
00:07:28,640 --> 00:07:30,560
它在某一个特定的路径上

180
00:07:30,560 --> 00:07:34,360
就时刻准备着去接收这个投票请求

181
00:07:34,360 --> 00:07:36,370
拿到这个头发请求之后

182
00:07:36,370 --> 00:07:37,490
那这个 follow 啊

183
00:07:37,490 --> 00:07:39,070
他就把这个投保请求呢

184
00:07:39,070 --> 00:07:43,250
放到了这个 RPCCH 这个管道里面去啊

185
00:07:43,250 --> 00:07:45,760
于是乎这边正在监听着呢，对吧

186
00:07:45,760 --> 00:07:47,750
刚好呢，就可以从广告里面呢

187
00:07:47,750 --> 00:07:50,100
把这个头发请求给取出来

188
00:07:50,100 --> 00:07:51,520
取出投保请求

189
00:07:51,520 --> 00:07:54,280
调用对应的 process 函数啊

190
00:07:54,280 --> 00:07:55,960
去处理这个投保请求

191
00:07:55,960 --> 00:08:00,290
就是我到底要不要给投票执行这一坨逻辑

192
00:08:00,290 --> 00:08:02,790
最终拿到一个投票结果啊

193
00:08:02,790 --> 00:08:05,350
不管这个结果是投还是不投

194
00:08:05,350 --> 00:08:06,430
反正是一个结果

195
00:08:06,430 --> 00:08:07,910
他把这个结果呢

196
00:08:07,910 --> 00:08:11,407
再放回到 RPC 这个结构体里面去

197
00:08:11,407 --> 00:08:13,280
这个 RPC 这个结构体呀

198
00:08:13,280 --> 00:08:16,220
实际上就是这个 RPCCH 

199
00:08:16,220 --> 00:08:18,677
这个管道里面所存放的元素

200
00:08:18,677 --> 00:08:21,150
也就是说这个 RPCCH 

201
00:08:21,150 --> 00:08:22,130
这个管道里面

202
00:08:22,130 --> 00:08:24,850
存放的每一个元素是什么类型的

203
00:08:24,850 --> 00:08:27,190
是 RPC 类型

204
00:08:27,190 --> 00:08:28,850
而这个 RPC 类型里面

205
00:08:28,850 --> 00:08:31,950
这个结构体里面包含了两个信息

206
00:08:31,950 --> 00:08:33,180
第一个信息呢

207
00:08:33,180 --> 00:08:34,937
就是请求

208
00:08:34,937 --> 00:08:38,570
比方说头发请求或者是新的请求

209
00:08:38,570 --> 00:08:40,720
另外一个呢，又包含了响应

210
00:08:40,720 --> 00:08:44,120
比方说投票响应或者是新标响应

211
00:08:44,120 --> 00:08:46,220
包含两个全员变量

212
00:08:46,220 --> 00:08:47,190
那现在呢

213
00:08:47,190 --> 00:08:48,470
拿到这个响应之后

214
00:08:48,470 --> 00:08:50,590
又把显应的再放回 RPC 

215
00:08:50,590 --> 00:08:54,690
而这边他把这个 RPC 放进管子之后

216
00:08:54,690 --> 00:08:57,910
他紧接着会试图从 RPC 里面

217
00:08:57,910 --> 00:08:59,827
去读取那个响应

218
00:08:59,827 --> 00:09:03,140
那所以这边生成了响应是吧

219
00:09:03,140 --> 00:09:05,120
所以这个时候他才能够把这个响应呢

220
00:09:05,120 --> 00:09:05,870
给读出来

221
00:09:05,870 --> 00:09:09,500
否则它会在这一步阻塞很长时间啊

222
00:09:09,500 --> 00:09:11,260
当这个响应生成之前

223
00:09:11,260 --> 00:09:13,260
会在这一步阻塞很长时间

224
00:09:13,260 --> 00:09:14,320
拿到响应之后

225
00:09:14,320 --> 00:09:15,970
为他刚好可以返回吗

226
00:09:15,970 --> 00:09:18,690
因为你这边是 HTTP 的 client 

227
00:09:18,690 --> 00:09:19,910
把请求发过来

228
00:09:19,910 --> 00:09:23,210
现在我终于可以把 HTP 响应给你返回了

229
00:09:23,210 --> 00:09:26,290
那么返回之后他就要判断一下哦

230
00:09:26,290 --> 00:09:27,940
你到底有没有给我投票

231
00:09:27,940 --> 00:09:28,900
如果投了

232
00:09:28,900 --> 00:09:31,940
判断一下我是否已经获得了多数投票

233
00:09:31,940 --> 00:09:32,720
如果是的话

234
00:09:32,720 --> 00:09:34,660
我就成为了 leader 

235
00:09:34,660 --> 00:09:37,140
所以在这个图里面我们发现了是吧

236
00:09:37,140 --> 00:09:39,820
最开始都是 follower 是蓝色的

237
00:09:39,820 --> 00:09:41,300
然后呢，有人变成了 candid 

238
00:09:41,300 --> 00:09:42,830
it 变成黄色

239
00:09:42,830 --> 00:09:44,660
然后呢，有人变成了红色

240
00:09:44,660 --> 00:09:45,400
变成了 leader 

241
00:09:45,400 --> 00:09:48,525
这是整个 leader 的诞生过程

242
00:09:48,525 --> 00:09:49,880
从这个结构图里面

243
00:09:49,880 --> 00:09:52,730
我们可以想象一下代码是什么样子的

244
00:09:52,730 --> 00:09:56,412
代码的话，其实它有三个核心的函数

245
00:09:56,412 --> 00:10:00,470
follower loop 、 candidate loop 和 leader loop 啊

246
00:10:00,470 --> 00:10:02,020
三个循环函数

247
00:10:02,020 --> 00:10:06,405
另外呢，它还会异步的去启动一个 HTTP server 

248
00:10:06,405 --> 00:10:07,450
这个 server 呢

249
00:10:07,450 --> 00:10:12,607
它会在特定的路由上面去监听特定的请求

250
00:10:12,607 --> 00:10:15,320
然后每一个节点它有一个成变量

251
00:10:15,320 --> 00:10:18,260
就是这个 RPCCH 这样一个管道

252
00:10:18,260 --> 00:10:21,320
我们在 tt server 拿到请求之后

253
00:10:21,320 --> 00:10:24,680
会先把这个请求放到2 PCCH 

254
00:10:24,680 --> 00:10:26,117
这个管道里面去

255
00:10:26,117 --> 00:10:29,020
那另外头呢，在监听这个管

256
00:10:29,020 --> 00:10:30,272
取出请求

257
00:10:30,272 --> 00:10:32,920
调用 protest 函数进行处理

258
00:10:32,920 --> 00:10:34,020
得到结果

259
00:10:34,020 --> 00:10:37,660
再把这个结果再放回 RPC 协议器管道

260
00:10:37,660 --> 00:10:39,900
然后我们的 htp server 

261
00:10:39,900 --> 00:10:43,240
就可以把结果返回给客户端了

262
00:10:43,240 --> 00:10:44,850
leader 诞生之后

263
00:10:44,850 --> 00:10:47,830
leader 就开始发送心跳请求

264
00:10:47,830 --> 00:10:50,950
就是日志复制请求给 follow 

265
00:10:50,950 --> 00:10:53,067
我们先来看中间这一部分

266
00:10:53,067 --> 00:10:56,547
这个 leader 它进入了 leader loop 这个函数

267
00:10:56,547 --> 00:10:57,540
一方面呢

268
00:10:57,540 --> 00:10:59,680
他要以固定的时间间隔

269
00:10:59,680 --> 00:11:04,160
周期性的给所有的 follower 去发送 A 请求

270
00:11:04,160 --> 00:11:05,160
发送心跳

271
00:11:05,160 --> 00:11:08,470
那么 follower 它作为一个 HTTP server 

272
00:11:08,470 --> 00:11:10,610
它在某个特定的路径上

273
00:11:10,610 --> 00:11:13,280
准备去接收心跳请求

274
00:11:13,280 --> 00:11:14,810
接收到这个请求之后

275
00:11:14,810 --> 00:11:16,150
那还是把这个请求呢

276
00:11:16,150 --> 00:11:18,230
放到 RPCC

277
00:11:18,230 --> 00:11:19,770
这个管道里面去

278
00:11:19,770 --> 00:11:20,630
与此同时

279
00:11:20,630 --> 00:11:25,000
这个 follow 呢，它又在另外一个词型里面去

280
00:11:25,000 --> 00:11:26,820
听这个 RPCCH 

281
00:11:26,820 --> 00:11:28,780
发现有东西进来了

282
00:11:28,780 --> 00:11:30,490
于是乎呢，把它取出来

283
00:11:30,490 --> 00:11:32,980
发现哦，原来是一个心跳请求

284
00:11:32,980 --> 00:11:36,950
调用对应的 process 函数来处理一个请请求

285
00:11:36,950 --> 00:11:37,980
得到响应

286
00:11:37,980 --> 00:11:38,960
把响应呢

287
00:11:38,960 --> 00:11:41,400
再放回到 RPC 结构体里面去

288
00:11:41,400 --> 00:11:42,020
进而呢

289
00:11:42,020 --> 00:11:46,860
这边打算从 RPC 里面取出这个新调响应

290
00:11:46,860 --> 00:11:49,840
在这儿它会阻塞一段时间

291
00:11:49,840 --> 00:11:54,220
取到钱之后刚好可以返回给 HTTP 的 client 

292
00:11:54,220 --> 00:11:55,445
返回给调用方

293
00:11:55,445 --> 00:11:56,660
那我们的 leader 

294
00:11:56,660 --> 00:11:59,340
他拿到这个心跳响应之后

295
00:11:59,340 --> 00:12:03,545
他可以立即的去处理这个心跳显示数据

296
00:12:03,545 --> 00:12:08,620
但是呢，他并不是说立刻去调用对应的 process 函数

297
00:12:08,620 --> 00:12:09,880
来处理这个数据

298
00:12:09,880 --> 00:12:10,600
注意不是啊

299
00:12:10,600 --> 00:12:12,070
注意不是，我打个叉

300
00:12:12,070 --> 00:12:14,090
他并没有直接走这个路径

301
00:12:14,090 --> 00:12:16,750
而是把这个心跳响应呢

302
00:12:16,750 --> 00:12:19,930
先放到了这个 channel 里面去

303
00:12:19,930 --> 00:12:23,400
然后通过监听这个 channel 取出对应的数据

304
00:12:23,400 --> 00:12:25,520
再去调对应的 process 函数

305
00:12:25,520 --> 00:12:27,810
它为什么要绕这么一圈呢

306
00:12:27,810 --> 00:12:29,860
还是前面我们讲过的

307
00:12:29,860 --> 00:12:31,160
你拿到 A 

308
00:12:31,160 --> 00:12:33,250
显然之后不能立即去处理

309
00:12:33,250 --> 00:12:35,180
因为可能与此同时

310
00:12:35,180 --> 00:12:37,480
你在处理 A 请求

311
00:12:37,480 --> 00:12:39,642
或者是在处理投票请求

312
00:12:39,642 --> 00:12:42,830
你如果立即去处理这个 ASI 的话

313
00:12:42,830 --> 00:12:46,660
就会导致它们在并行的执行可能会出错

314
00:12:46,660 --> 00:12:48,360
所以为了确保串行

315
00:12:48,360 --> 00:12:52,350
那所有的数据统一先进 channel 

316
00:12:52,350 --> 00:12:54,457
再从 channel 里面取出来

317
00:12:54,457 --> 00:12:55,750
再串接处理

318
00:12:55,750 --> 00:12:57,750
以上就是整体的代码结构

319
00:12:57,750 --> 00:12:59,030
从下一节开始

320
00:12:59,030 --> 00:13:02,010
我们把每一部分分别的实现一下
