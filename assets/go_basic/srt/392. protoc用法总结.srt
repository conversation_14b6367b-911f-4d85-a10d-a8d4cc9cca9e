1
00:00:00,240 --> 00:00:02,100
今天把我在实际工作

2
00:00:02,100 --> 00:00:03,840
使用 PTOC 这个命令时

3
00:00:03,840 --> 00:00:05,180
遇到过的所有问题

4
00:00:05,180 --> 00:00:07,947
踩过的所有坑进行一个大总结

5
00:00:07,947 --> 00:00:10,850
我要把 port 文件转成具体的 go 代码

6
00:00:10,850 --> 00:00:12,192
或者其他源代码

7
00:00:12,192 --> 00:00:13,920
这里面比较难的就是

8
00:00:13,920 --> 00:00:17,220
当你的一个 port 文件引用了其他 port 文件

9
00:00:17,220 --> 00:00:19,640
而其他 port 文件和当前 port 文件

10
00:00:19,640 --> 00:00:21,130
还不在一个目录下

11
00:00:21,130 --> 00:00:24,017
那甚至都不在当前项目里面

12
00:00:24,017 --> 00:00:26,800
就会发生各种各样奇奇怪怪的问题

13
00:00:26,800 --> 00:00:28,070
那今天这个视频

14
00:00:28,070 --> 00:00:30,445
我们就来做一个统一的了结

15
00:00:30,445 --> 00:00:33,260
首先我们需要去安装 PROTOC 这个命令

16
00:00:33,260 --> 00:00:34,590
那我们来到 GITHUB 

17
00:00:34,590 --> 00:00:37,050
找到 PROTOBUFFERS 这个项目啊

18
00:00:37,050 --> 00:00:38,800
找到 PROTOBUF 这个目录

19
00:00:38,800 --> 00:00:42,350
然后呢，找到这个最近的一次 tag 

20
00:00:42,350 --> 00:00:43,810
他这个发布版本嘛

21
00:00:43,810 --> 00:00:47,602
release 目前最新的是 V 3.0.2

22
00:00:47,602 --> 00:00:49,500
那么我们往下翻的话

23
00:00:49,500 --> 00:00:51,930
下面有一个 access 

24
00:00:51,930 --> 00:00:57,437
access 它针对 LINUX 、 windows 、苹果都有编译好的可执行文件

25
00:00:57,437 --> 00:00:58,620
比如我是 windows 

26
00:00:58,620 --> 00:01:02,580
那么呢，就直接下载这个 windows 的缩包

27
00:01:02,580 --> 00:01:06,187
下载完之后一挤压里面就是那个可执行文件

28
00:01:06,187 --> 00:01:08,370
里面有一个 BIIB 目录

29
00:01:08,370 --> 00:01:09,550
把那个 bin 目录呢

30
00:01:09,550 --> 00:01:11,790
放到你的环境变量 pass 里面去

31
00:01:11,790 --> 00:01:13,740
这样的话，你在任意目录下

32
00:01:13,740 --> 00:01:16,500
就可以直接使用 PHOTOC 这个命令了

33
00:01:16,500 --> 00:01:18,920
比如打开 word vs code 

34
00:01:18,920 --> 00:01:19,850
打开终端

35
00:01:19,850 --> 00:01:25,110
好，我在任意目录下都可以使用 PROTOSC 这个命令

36
00:01:25,110 --> 00:01:26,427
看下版本号

37
00:01:26,427 --> 00:01:28,270
杠杠 version 

38
00:01:28,270 --> 00:01:30,190
好，本地呢是三

39
00:01:30,190 --> 00:01:31,790
0.2最新版本

40
00:01:31,790 --> 00:01:32,630
那么看一下

41
00:01:32,630 --> 00:01:35,640
我今天打算搞三个 port 文件

42
00:01:35,640 --> 00:01:40,027
分别放在了 model 到和 service 这三个目录下

43
00:01:40,027 --> 00:01:42,160
它们之间是有一定关系的

44
00:01:42,160 --> 00:01:46,270
那么我在最底层是 user 点 PROTO 

45
00:01:46,270 --> 00:01:50,350
它里面呢去引用了谷歌的 time stamp 点

46
00:01:50,350 --> 00:01:51,967
PROTO 这个文件

47
00:01:51,967 --> 00:01:55,760
然后呢，在我的盗目录下 production 点 PROTO 

48
00:01:55,760 --> 00:01:59,750
它里面引用了刚才那个 user 点 photo 

49
00:01:59,750 --> 00:02:01,620
最上层是我的 service 

50
00:02:01,620 --> 00:02:03,340
在 RECOR 点 port 里面呢

51
00:02:03,340 --> 00:02:08,068
他又去引用了到下面的 product 点 PROTO 

52
00:02:08,068 --> 00:02:10,779
那么 PROTO 文件基本语法就不讲了

53
00:02:10,779 --> 00:02:13,980
这里面重点讲一些有疑惑的地方

54
00:02:13,980 --> 00:02:15,897
先来看 user 

55
00:02:15,897 --> 00:02:16,710
这里面呢

56
00:02:16,710 --> 00:02:18,820
他搞了一个 package 名称

57
00:02:18,820 --> 00:02:21,510
这个 package 名称就类似于 go 源里面

58
00:02:21,510 --> 00:02:22,550
每一个构代码

59
00:02:22,550 --> 00:02:24,767
第一行都需要写下 package 

60
00:02:24,767 --> 00:02:25,980
有了 package 

61
00:02:25,980 --> 00:02:29,790
那别人在引用你里面的一些结构体系变量时

62
00:02:29,790 --> 00:02:32,480
可以把包名放在前面

63
00:02:32,480 --> 00:02:35,917
同理，我们的 PROTO 文件里面也是这个目的

64
00:02:35,917 --> 00:02:40,590
比如说我现在 package 叫做 model 点 com 啊

65
00:02:40,590 --> 00:02:42,450
即于说需不需要来个点

66
00:02:42,450 --> 00:02:44,455
这个看你的个人喜好

67
00:02:44,455 --> 00:02:46,190
这个点不是必须的啊

68
00:02:46,190 --> 00:02:48,055
它也没有任何的实际含义

69
00:02:48,055 --> 00:02:50,550
那么我在 product 点 port 里面

70
00:02:50,550 --> 00:02:52,640
不需要去引用那个 user 吗

71
00:02:52,640 --> 00:02:56,230
比如这里边啊，这个 PRODUCTOR 它是什么

72
00:02:56,230 --> 00:02:58,200
它是 user 这样一种类型

73
00:02:58,200 --> 00:03:01,620
而这个 user 就是刚才在这个文件里面

74
00:03:01,620 --> 00:03:03,540
我定义好的一个 message 

75
00:03:03,540 --> 00:03:04,902
它叫 user 

76
00:03:04,902 --> 00:03:07,260
就类似于构圆的一个结构体嘛

77
00:03:07,260 --> 00:03:09,270
那么在构代码里面

78
00:03:09,270 --> 00:03:12,610
我们去引用其他包下面的一个结构体

79
00:03:12,610 --> 00:03:16,010
是不是说在结构体前面加上对应的 package 名称

80
00:03:16,010 --> 00:03:20,140
而这个 package 名称就是我在这个 PROTO 文件里面

81
00:03:20,140 --> 00:03:22,610
定义好的这个 package 名称

82
00:03:22,610 --> 00:03:25,170
同理，在 user 里面呢

83
00:03:25,170 --> 00:03:28,550
它就引用了谷歌的这个 time ser 

84
00:03:28,550 --> 00:03:30,470
这也是一个 message 

85
00:03:30,470 --> 00:03:32,197
我们找到对应的源码

86
00:03:32,197 --> 00:03:35,030
好，其实就是这个 PROTO 文件

87
00:03:35,030 --> 00:03:39,185
它里面有一个 message 叫做 time stamp 

88
00:03:39,185 --> 00:03:40,740
而这个 PROTO 文件

89
00:03:40,740 --> 00:03:45,840
它所指定的 package 名称叫做 google 点 proto Buff 

90
00:03:45,840 --> 00:03:47,100
所以呢

91
00:03:47,100 --> 00:03:49,740
google 点 PROTOBUF 是 package 名称

92
00:03:49,740 --> 00:03:53,737
而这个 time step 则是那个 MASI 名称

93
00:03:53,737 --> 00:03:58,855
好，看一下如何把这个 PROTO 文件转成 go 代码

94
00:03:58,855 --> 00:04:01,630
PROTOC 后面是杠杠

95
00:04:01,630 --> 00:04:04,060
PROTOBUF 等于 PROTOC 

96
00:04:04,060 --> 00:04:04,910
注意啊

97
00:04:04,910 --> 00:04:07,307
这里面使用了什么相对路径

98
00:04:07,307 --> 00:04:11,020
那么我在哪个目录下去执行这个命令呢

99
00:04:11,020 --> 00:04:13,365
我是在 go basic 这个目录

100
00:04:13,365 --> 00:04:14,100
看一下是吧

101
00:04:14,100 --> 00:04:16,440
我当前是在 go basic 这个目录下

102
00:04:16,440 --> 00:04:19,600
从这个目录下去找到 PROTOC 这个目录

103
00:04:19,600 --> 00:04:21,064
就找到了这个目录吗

104
00:04:21,064 --> 00:04:24,440
好，那么这个 proto pass 的意思就是说

105
00:04:24,440 --> 00:04:28,755
我要从这个目录下去寻找 PROTO 文件

106
00:04:28,755 --> 00:04:30,860
到底寻找哪个 PO 文件呢

107
00:04:30,860 --> 00:04:32,530
我们先继续往后看

108
00:04:32,530 --> 00:04:35,360
后面啊，又指定了一个 proto pass 

109
00:04:35,360 --> 00:04:37,020
也就是说这个 proto pass 啊

110
00:04:37,020 --> 00:04:38,340
你可以出现多个啊

111
00:04:38,340 --> 00:04:39,140
一个也可以

112
00:04:39,140 --> 00:04:40,715
两个、三个都可以

113
00:04:40,715 --> 00:04:42,400
这个 proto pass 后面呢

114
00:04:42,400 --> 00:04:43,840
他跟的是一个什么

115
00:04:43,840 --> 00:04:45,920
跟的是一个绝对路径

116
00:04:45,920 --> 00:04:48,220
由于这个路径里面呢，包含空格

117
00:04:48,220 --> 00:04:51,950
所以呢，整个路径需要用引号把它给引起来

118
00:04:51,950 --> 00:04:53,700
不然的话中间就断开了嘛

119
00:04:53,700 --> 00:04:55,900
为了解决这个空格的问题

120
00:04:55,900 --> 00:04:59,640
最后面的这个 model user 点 PROTO 啊

121
00:04:59,640 --> 00:05:02,140
这个是我真正要处理的转换的那个

122
00:05:02,140 --> 00:05:02,930
PROTO 文件

123
00:05:02,930 --> 00:05:04,547
好，那问题来了

124
00:05:04,547 --> 00:05:07,290
那么这个 model j 级目录

125
00:05:07,290 --> 00:05:10,210
到底是相对谁的目录呢

126
00:05:10,210 --> 00:05:14,072
其实啊，它就是相对于 proto pass 的目录

127
00:05:14,072 --> 00:05:16,940
也就是说你这边有两个 port bus 

128
00:05:16,940 --> 00:05:20,330
那么呢，它会取这两个 proto pass 下面

129
00:05:20,330 --> 00:05:23,610
尝试着去找到 model 这一级子目录

130
00:05:23,610 --> 00:05:26,760
比如说他先去 PROTOC 这个目录下

131
00:05:26,760 --> 00:05:29,240
找一找有没有 model 这些子目录

132
00:05:29,240 --> 00:05:31,480
他发现 PROTOC 下面啊

133
00:05:31,480 --> 00:05:33,825
确实是有 model 这几字目录

134
00:05:33,825 --> 00:05:38,140
而且 model 这一子目录下面确实是有一个 user 点 PROTO 

135
00:05:38,140 --> 00:05:38,420
对吧

136
00:05:38,420 --> 00:05:40,215
有一个 user 点 PROTO 

137
00:05:40,215 --> 00:05:42,060
那么它就确定了

138
00:05:42,060 --> 00:05:45,170
你要转换的就是这个 PROTO 文件

139
00:05:45,170 --> 00:05:47,180
那如果说找不到的话

140
00:05:47,180 --> 00:05:50,480
他会尝试去第二个 pod pass 下面

141
00:05:50,480 --> 00:05:56,220
就是去这个 include 目录下面去寻找 model 子目录

142
00:05:56,220 --> 00:05:57,460
如果有的话

143
00:05:57,460 --> 00:05:59,300
继续寻找优作点

144
00:05:59,300 --> 00:06:00,712
PROTO 这个文件

145
00:06:00,712 --> 00:06:02,530
所以我们就知道了

146
00:06:02,530 --> 00:06:03,620
那其实呢

147
00:06:03,620 --> 00:06:05,450
这个 model 这个目录啊

148
00:06:05,450 --> 00:06:10,140
它是挂在了这个 PROTOCG 级目录后面的

149
00:06:10,140 --> 00:06:11,520
那既然是这样的话

150
00:06:11,520 --> 00:06:14,622
那这个目录呢，就可以换一下位置

151
00:06:14,622 --> 00:06:16,290
看我的第二个命令

152
00:06:16,290 --> 00:06:17,015
诶

153
00:06:17,015 --> 00:06:20,950
最后啊，我这个文件只剩下 user 点 PROTO 

154
00:06:20,950 --> 00:06:22,510
没有前面那句目录了

155
00:06:22,510 --> 00:06:26,230
那么我把这个 model 目录移到哪了呢

156
00:06:26,230 --> 00:06:29,870
我把它移到了我的 proto pass 里面去了啊

157
00:06:29,870 --> 00:06:32,820
直接把它放在了我的 PROTOC 后面

158
00:06:32,820 --> 00:06:34,402
这样也是可以的

159
00:06:34,402 --> 00:06:36,470
总之一句话就是

160
00:06:36,470 --> 00:06:38,650
当他遇到 PROTO 文件时

161
00:06:38,650 --> 00:06:40,290
去哪找这个文件呢

162
00:06:40,290 --> 00:06:43,050
不管这个文件是直接一个文件名也好

163
00:06:43,050 --> 00:06:44,880
还是带一些相应的路径也好

164
00:06:44,880 --> 00:06:49,180
他统一的尝试着去每一个 proto pass 下面

165
00:06:49,180 --> 00:06:50,575
去找这个文件

166
00:06:50,575 --> 00:06:52,520
好，那么第二个问题

167
00:06:52,520 --> 00:06:55,857
我为什么要指定这个 proto pass 呢

168
00:06:55,857 --> 00:07:00,250
其实是因为由于你在第六行这啊

169
00:07:00,250 --> 00:07:03,190
你 import 引入了一个 PROTO 文件

170
00:07:03,190 --> 00:07:07,887
那么这个 PROTO 文件它去哪儿找 google 这级目录呢

171
00:07:07,887 --> 00:07:09,080
它实际上啊

172
00:07:09,080 --> 00:07:11,120
需要去这个啊

173
00:07:11,120 --> 00:07:14,920
需要去这个 include 这级目录下去

174
00:07:14,920 --> 00:07:17,992
找到 google 这级目录

175
00:07:17,992 --> 00:07:20,530
那么当然你也可以这样写

176
00:07:20,530 --> 00:07:21,850
我拷贝一份

177
00:07:21,850 --> 00:07:23,800
把第六行先注释掉

178
00:07:23,800 --> 00:07:25,770
好，如果我这个 import 

179
00:07:25,770 --> 00:07:28,340
我去掉这个路径

180
00:07:28,340 --> 00:07:30,810
只保留 PROTO 文件名的话

181
00:07:30,810 --> 00:07:33,320
那么我的 proto pass 需要这样写

182
00:07:33,320 --> 00:07:35,930
你看，本来是指到 include 

183
00:07:35,930 --> 00:07:40,260
现在呢，我需要把这个 google protobuf 是吧

184
00:07:40,260 --> 00:07:43,930
也放在我的 proto pass 后面

185
00:07:43,930 --> 00:07:45,270
那么这样的话

186
00:07:45,270 --> 00:07:49,740
你这种写法和这种 import 形式就是匹配的

187
00:07:49,740 --> 00:07:50,950
也是可以的

188
00:07:50,950 --> 00:07:53,467
那么刚才我们讲了

189
00:07:53,467 --> 00:07:56,070
这个地方可以只写 PROTO 文件名

190
00:07:56,070 --> 00:07:57,267
可以不带路径

191
00:07:57,267 --> 00:07:59,960
那么这个地方也可以直接 PTO 匿名

192
00:07:59,960 --> 00:08:00,920
可以不带路径

193
00:08:00,920 --> 00:08:03,760
但是呢，这种不带路径这种方式啊

194
00:08:03,760 --> 00:08:06,670
可能会存在一个小小的风险

195
00:08:06,670 --> 00:08:07,810
什么风险呢

196
00:08:07,810 --> 00:08:09,245
大家想一下

197
00:08:09,245 --> 00:08:13,272
你这边不是指定了多个 proto pass 吗

198
00:08:13,272 --> 00:08:16,370
那假如说在多个 port pass 下面

199
00:08:16,370 --> 00:08:19,252
存在重名的 PROTO 文件

200
00:08:19,252 --> 00:08:20,350
比如说啊

201
00:08:20,350 --> 00:08:22,577
这个 user 点 PROTO 对吧

202
00:08:22,577 --> 00:08:25,610
它既在这个目录下有一个

203
00:08:25,610 --> 00:08:30,400
同时呢，它在这个目录下也有一个 U 的点. PROTO 

204
00:08:30,400 --> 00:08:34,950
那他就不知道你到底要转换的是哪个

205
00:08:34,950 --> 00:08:35,730
目录下的

206
00:08:35,730 --> 00:08:36,920
PROTO 文件了

207
00:08:36,920 --> 00:08:39,474
那么这个地方也是一样道理

208
00:08:39,474 --> 00:08:43,268
所以啊，为了避免这种决策不清的情况

209
00:08:43,268 --> 00:08:43,750
诶

210
00:08:43,750 --> 00:08:46,670
你可以在前面追加一些路径

211
00:08:46,670 --> 00:08:48,570
这样的来进行区分

212
00:08:48,570 --> 00:08:51,370
好，关于 proto pass 我们就讲完了

213
00:08:51,370 --> 00:08:53,902
然后来看这个 go out 

214
00:08:53,902 --> 00:08:56,940
go out 自然是说我生成的 go 文件

215
00:08:56,940 --> 00:08:59,400
需要输出到哪个目录下

216
00:08:59,400 --> 00:09:01,120
这边是一个点

217
00:09:01,120 --> 00:09:02,575
表示当前目录吗

218
00:09:02,575 --> 00:09:07,190
那么由于我是在 go basic 这个目录下

219
00:09:07,190 --> 00:09:08,947
去执行这个命令的

220
00:09:08,947 --> 00:09:12,500
那难道我的 go 文件就会输出到 go basic 

221
00:09:12,500 --> 00:09:13,600
这个目录下吗

222
00:09:13,600 --> 00:09:14,887
其实并不是

223
00:09:14,887 --> 00:09:19,522
其实啊，它是先根据这边指定的这个路径

224
00:09:19,522 --> 00:09:20,300
然后呢

225
00:09:20,300 --> 00:09:25,990
在这个路径下面再去拼接上这一级路径

226
00:09:25,990 --> 00:09:29,800
哎，这就是我们的 go package 分号

227
00:09:29,800 --> 00:09:31,860
前面这一部分所发挥的作用

228
00:09:31,860 --> 00:09:34,590
也就是说，将来我们生成的构代码

229
00:09:34,590 --> 00:09:38,580
它真实的输出目录是点儿

230
00:09:38,580 --> 00:09:42,770
还在拼接上这个 protoy model 是吧

231
00:09:42,770 --> 00:09:45,530
从 go basic 下面找到 PTOC 

232
00:09:45,530 --> 00:09:46,770
然后再找到 model ，诶

233
00:09:46,770 --> 00:09:50,675
我们的 go 文件就会输出到这个 model 目录下

234
00:09:50,675 --> 00:09:51,610
那同理

235
00:09:51,610 --> 00:09:52,870
你也可以在这个地方

236
00:09:52,870 --> 00:09:56,260
把 PROTOCJ 级目录把它给删掉

237
00:09:56,260 --> 00:09:58,570
然后呢，把 photo c 这个目录

238
00:09:58,570 --> 00:10:02,315
你把它追加到我们的 go out 后面

239
00:10:02,315 --> 00:10:04,850
因为本来 go out 的后面是一个点嘛

240
00:10:04,850 --> 00:10:08,355
现在你已把 PTOC 放在这个地方了

241
00:10:08,355 --> 00:10:09,540
那这样的话

242
00:10:09,540 --> 00:10:11,187
你这边这样写

243
00:10:11,187 --> 00:10:13,840
同时呢， go pk 这边呢

244
00:10:13,840 --> 00:10:15,820
这样写也是可以的

245
00:10:15,820 --> 00:10:18,402
反正是他们俩拼接起来嘛

246
00:10:18,402 --> 00:10:20,127
但是需要注意一下

247
00:10:20,127 --> 00:10:23,120
那么 go package 分号前面这一部分

248
00:10:23,120 --> 00:10:24,320
它作为一个路径的话

249
00:10:24,320 --> 00:10:26,680
它里面呢至少需要包含一个底

250
00:10:26,680 --> 00:10:29,167
或者包含一个斜线

251
00:10:29,167 --> 00:10:31,250
所以啊，这一边我们这样写

252
00:10:31,250 --> 00:10:33,530
就是加一个路径标识啊

253
00:10:33,530 --> 00:10:34,750
第二斜线

254
00:10:34,750 --> 00:10:36,867
好，这样写才不会报错

255
00:10:36,867 --> 00:10:41,010
那么他会去 go out 所指定的 PROTOC 这个目录下

256
00:10:41,010 --> 00:10:43,320
找到 model 这个目录

257
00:10:43,320 --> 00:10:45,865
这是我们的 go 文件的输出目录

258
00:10:45,865 --> 00:10:48,120
那封号后面还有一个 model 

259
00:10:48,120 --> 00:10:49,050
这是干嘛的

260
00:10:49,050 --> 00:10:50,710
分号后面的这个 model 啊

261
00:10:50,710 --> 00:10:53,790
意思是说我将来生成 go 代码之后

262
00:10:53,790 --> 00:10:57,052
我的 go 代码第一行不是要写一个 package 名称吗

263
00:10:57,052 --> 00:10:58,520
在 go 代码里面

264
00:10:58,520 --> 00:11:00,000
package 名称，哎

265
00:11:00,000 --> 00:11:02,560
就叫做 model 啊

266
00:11:02,560 --> 00:11:06,140
所以啊，这个名称和这个路径

267
00:11:06,140 --> 00:11:08,125
它们没有任何的必然关系

268
00:11:08,125 --> 00:11:09,620
可以完全不一样

269
00:11:09,620 --> 00:11:11,660
好，那么我们就来生成一下

270
00:11:11,660 --> 00:11:15,280
那这边呢，就多出一个 user 点 PB 点勾

271
00:11:15,280 --> 00:11:16,645
我们打开看一眼

272
00:11:16,645 --> 00:11:18,400
package 名称叫 model 啊

273
00:11:18,400 --> 00:11:21,600
这个 model 实际上就是由刚才的分号

274
00:11:21,600 --> 00:11:23,877
后面这一部分所决定的

275
00:11:23,877 --> 00:11:27,630
那这三个是由于我引用了谷歌的 time stamp 

276
00:11:27,630 --> 00:11:29,950
所以呢，它会引入这三个包

277
00:11:29,950 --> 00:11:32,572
那目前这三个包是飘红的

278
00:11:32,572 --> 00:11:33,750
因为大家看

279
00:11:33,750 --> 00:11:37,070
在我 photos sim 下有一个 go 点 mod 

280
00:11:37,070 --> 00:11:38,530
它是一个 module 嘛

281
00:11:38,530 --> 00:11:42,650
就是我的 go basic 下面也有一个 go 点 mode 

282
00:11:42,650 --> 00:11:47,452
相当于是说 PHOTOC 是我的 go basic 下面的一个子 module 

283
00:11:47,452 --> 00:11:50,160
由于 PHOTOC 这个构点 model 里面

284
00:11:50,160 --> 00:11:53,160
目前还没有任何的 require 

285
00:11:53,160 --> 00:11:56,190
所以呢，我们需要通过 go mod tidy 啊

286
00:11:56,190 --> 00:12:00,497
来去自动解决这些找不到第三方库的情况

287
00:12:00,497 --> 00:12:01,340
要注意啊

288
00:12:01,340 --> 00:12:04,240
我们一定要先进到 PROTOC 这个目录里面来

289
00:12:04,240 --> 00:12:08,890
就是进到跟这个 go 点 mode 评级的目录里面来

290
00:12:08,890 --> 00:12:12,485
然后呢，再来执行 go mode 

291
00:12:12,485 --> 00:12:13,290
teddy 

292
00:12:13,290 --> 00:12:16,170
好自动的去解决这些依赖是吧

293
00:12:16,170 --> 00:12:19,830
他正在这个 finding 找到对应的源码

294
00:12:19,830 --> 00:12:21,387
把它加进来

295
00:12:21,387 --> 00:12:26,160
再看一下我们的 go 点 mod 里面就多了一个 require 

296
00:12:26,160 --> 00:12:28,990
再看一下这个 go 文件啊

297
00:12:28,990 --> 00:12:32,190
刚才飘红呢，就已经不再飘红了

298
00:12:32,190 --> 00:12:34,500
我们再退回到上一级目录

299
00:12:34,500 --> 00:12:37,820
接下来看一下这个 product 点 PROTO 

300
00:12:37,820 --> 00:12:41,950
它里面呢，去引用了刚才的 user 点 PROTO 

301
00:12:41,950 --> 00:12:43,520
我们再来复习一下

302
00:12:43,520 --> 00:12:46,870
由于这边需要使用 user 这个 message 

303
00:12:46,870 --> 00:12:49,830
所以呢，前面需要加上对应的包名

304
00:12:49,830 --> 00:12:51,840
那这个包名 model 点

305
00:12:51,840 --> 00:12:54,180
com 就是刚才 user 点

306
00:12:54,180 --> 00:12:57,550
PROTO 它所对应的 package 名称

307
00:12:57,550 --> 00:13:01,480
import 这边是指定了 model 这级目录

308
00:13:01,480 --> 00:13:04,992
所以呢，在我们的 proto pass 里面

309
00:13:04,992 --> 00:13:07,950
它只指定到了 PROTOC 这级目录

310
00:13:07,950 --> 00:13:10,630
也就是说他会去 PROTOC 这集目录下

311
00:13:10,630 --> 00:13:14,000
尝试着去寻找 model 这个子目录

312
00:13:14,000 --> 00:13:15,700
哎，果然可以找到

313
00:13:15,700 --> 00:13:16,420
然后呢

314
00:13:16,420 --> 00:13:20,982
再去 model 子目录下去找一下有没有 UZ 点 photo 

315
00:13:20,982 --> 00:13:22,250
如果能找到

316
00:13:22,250 --> 00:13:23,870
那么就确定了啊

317
00:13:23,870 --> 00:13:25,150
你这边所有的 import 

318
00:13:25,150 --> 00:13:29,040
实际上就是像 import 这个 PROTO 文件

319
00:13:29,040 --> 00:13:30,610
但是呢，需要注意

320
00:13:30,610 --> 00:13:31,770
我这边啊

321
00:13:31,770 --> 00:13:34,320
又加了第二个 proto pass 

322
00:13:34,320 --> 00:13:37,290
就是把刚才那个谷歌的 time stamp 

323
00:13:37,290 --> 00:13:38,700
也把它放进来了

324
00:13:38,700 --> 00:13:39,650
为什么呢

325
00:13:39,650 --> 00:13:41,630
我在这个 PO 文件里面

326
00:13:41,630 --> 00:13:45,647
并没有去引用谷歌的 time stamp 

327
00:13:45,647 --> 00:13:49,480
但是呢，由于在 user 点 photo 里面

328
00:13:49,480 --> 00:13:52,480
他去引用了这个 time stamp 

329
00:13:52,480 --> 00:13:56,220
所以呢，不管是直接依赖还是间接依赖

330
00:13:56,220 --> 00:13:58,850
中途出现过的所有的 PO 文件

331
00:13:58,850 --> 00:14:02,000
那么呢，你都需要在 proto pass 里面

332
00:14:02,000 --> 00:14:04,302
来指定他们在哪个目录下

333
00:14:04,302 --> 00:14:07,485
所以啊，这边多出了第二个 proto pass 

334
00:14:07,485 --> 00:14:12,010
最后我要转换的文件是到下面的 product 

335
00:14:12,010 --> 00:14:13,690
那这个 dog 级目录呢

336
00:14:13,690 --> 00:14:17,857
实际上是相对于这个 proto pass 的子目录

337
00:14:17,857 --> 00:14:20,720
然后我的 go out 是注入到当前目录下

338
00:14:20,720 --> 00:14:26,000
实际上是说从当前目录下再去找到这个 PROTOC 

339
00:14:26,000 --> 00:14:27,710
再找到到啊

340
00:14:27,710 --> 00:14:30,600
最终是输出到到 G 级目录下

341
00:14:30,600 --> 00:14:35,080
这边还有一个 go o p t 等于 M 啊

342
00:14:35,080 --> 00:14:36,780
这个我们待会再讲

343
00:14:36,780 --> 00:14:40,835
我们先不加这个选项试一试

344
00:14:40,835 --> 00:14:43,100
把明天拷贝一下，粘过来

345
00:14:43,100 --> 00:14:47,287
我们先把这个 go o p t 等于 M 这项先删掉

346
00:14:47,287 --> 00:14:48,630
先执行一下

347
00:14:48,630 --> 00:14:52,010
好，它也是能够生成这个 go 文件的

348
00:14:52,010 --> 00:14:54,620
只不过我们看一下这个 go 文件

349
00:14:54,620 --> 00:14:56,740
第十行有一个飘红报错

350
00:14:56,740 --> 00:15:00,510
就是他要去依赖这个 model 目录嘛

351
00:15:00,510 --> 00:15:04,520
所以这边呢，就直接写了一个啊点斜杠 model 

352
00:15:04,520 --> 00:15:05,900
那这样显示不可以的

353
00:15:05,900 --> 00:15:08,420
因为我们知道在 go 代码里面

354
00:15:08,420 --> 00:15:10,900
你要去引用另外一个目录

355
00:15:10,900 --> 00:15:14,360
你不能一上来只就是这个目录名称

356
00:15:14,360 --> 00:15:16,420
一上来应该是什么

357
00:15:16,420 --> 00:15:18,660
应该是你的 module 名称

358
00:15:18,660 --> 00:15:21,390
而当前我们的 module 叫什么名字呢

359
00:15:21,390 --> 00:15:23,730
打开我们的勾点 mod 看一眼

360
00:15:23,730 --> 00:15:27,070
当前 module 叫 ABC 

361
00:15:27,070 --> 00:15:29,080
所以实际上我们是希望这样写

362
00:15:29,080 --> 00:15:32,160
就是把 ABC 拷贝过来改一下

363
00:15:32,160 --> 00:15:34,850
把这个点呢换成 ABC 

364
00:15:34,850 --> 00:15:38,160
下面的 model j 级目录保存一下

365
00:15:38,160 --> 00:15:40,822
好，这样的话就不再飘红了

366
00:15:40,822 --> 00:15:44,880
那难道我通过 PROTOC 命令生成 go 代码之后

367
00:15:44,880 --> 00:15:48,520
还需要手动的去修改这些偏航报错吗

368
00:15:48,520 --> 00:15:49,992
哎，所以呀

369
00:15:49,992 --> 00:15:53,250
刚才我们在这个 PROTOC 命令里

370
00:15:53,250 --> 00:15:54,430
需要再加一下

371
00:15:54,430 --> 00:15:57,840
就是加上这个 go o p t 

372
00:15:57,840 --> 00:15:59,040
注意看一下

373
00:15:59,040 --> 00:16:01,550
等于后面是一个大写的 M 

374
00:16:01,550 --> 00:16:05,145
这个大写 M 呢，就表示 modify 要修改

375
00:16:05,145 --> 00:16:09,830
那么大 M 后面跟的就是你的这个 import 

376
00:16:09,830 --> 00:16:11,170
后面的内容啊

377
00:16:11,170 --> 00:16:13,160
model user 点 PROTO 

378
00:16:13,160 --> 00:16:14,620
看这边是吧

379
00:16:14,620 --> 00:16:17,512
model user 点 PROTO 

380
00:16:17,512 --> 00:16:19,640
就说我在 PROTO 文件里面

381
00:16:19,640 --> 00:16:21,340
我的 import 是这样写的

382
00:16:21,340 --> 00:16:24,430
但是呢，我希望它转成 go 代码之后

383
00:16:24,430 --> 00:16:27,160
在 go 代码里面那个 import 该怎么写

384
00:16:27,160 --> 00:16:28,470
那个 import 

385
00:16:28,470 --> 00:16:29,080
诶

386
00:16:29,080 --> 00:16:31,832
应该这样写

387
00:16:31,832 --> 00:16:33,690
而这种写法啊

388
00:16:33,690 --> 00:16:37,520
就是我刚才人工改的这种写法

389
00:16:37,520 --> 00:16:41,410
好，我们把这个构文件删掉、删除

390
00:16:41,410 --> 00:16:42,200
然后呢

391
00:16:42,200 --> 00:16:46,620
我们使用这个命令重新的再来生成一次

392
00:16:46,620 --> 00:16:47,980
再点开看一眼

393
00:16:47,980 --> 00:16:51,240
你看此时呢，这个 import 的写法就对了

394
00:16:51,240 --> 00:16:53,075
不大需要人工修改了

395
00:16:53,075 --> 00:16:55,580
那么跟我们的 proto pass 类似啊

396
00:16:55,580 --> 00:16:57,060
proto pass 可以有多个

397
00:16:57,060 --> 00:17:00,060
同样我们的这个 go o p t 啊

398
00:17:00,060 --> 00:17:01,562
也可以出现多个

399
00:17:01,562 --> 00:17:04,470
就有时候你可能需要有多个 modify 

400
00:17:04,470 --> 00:17:08,727
因为你可能这边会 import 多个 port 文件嘛

401
00:17:08,727 --> 00:17:11,520
所以这个地方就需要多个 MULTIFY 

402
00:17:11,520 --> 00:17:13,840
再来看最后一个 service 

403
00:17:13,840 --> 00:17:16,012
有一个 WRC 点 photo 

404
00:17:16,012 --> 00:17:16,910
它里面呢

405
00:17:16,910 --> 00:17:20,440
它需要去依赖到下面的 product 点 photo 

406
00:17:20,440 --> 00:17:22,579
我们注意看这里面的 MESSI 名称

407
00:17:22,579 --> 00:17:24,480
它是一种蛇形形式

408
00:17:24,480 --> 00:17:27,280
那么蛇形形式转换成 J 代码之后

409
00:17:27,280 --> 00:17:29,377
都会转成驼峰形式

410
00:17:29,377 --> 00:17:31,490
就是这个 R 是大写

411
00:17:31,490 --> 00:17:33,930
然后呢，没有这个下划线

412
00:17:33,930 --> 00:17:37,380
然后这个 request 的 R 也会转为大写

413
00:17:37,380 --> 00:17:39,330
包括像这个 rank 啊

414
00:17:39,330 --> 00:17:42,020
首字母 R 也会转成大写

415
00:17:42,020 --> 00:17:44,590
那么这里面唯一需要讲的一个新知识点

416
00:17:44,590 --> 00:17:46,915
就是它多了一个 service 

417
00:17:46,915 --> 00:17:47,670
当然了

418
00:17:47,670 --> 00:17:49,350
在同一个 PROTO 文件里面

419
00:17:49,350 --> 00:17:51,070
你不光可以有一个 service 啊

420
00:17:51,070 --> 00:17:52,840
可以出现多个 service 

421
00:17:52,840 --> 00:17:56,635
那如果我们还是使用之前的命令的话

422
00:17:56,635 --> 00:17:59,550
他只能去转换这些个 message 

423
00:17:59,550 --> 00:18:01,750
把 message 转成 go 圆结构体

424
00:18:01,750 --> 00:18:04,022
它并不会去转这个 service 

425
00:18:04,022 --> 00:18:07,160
那如果我们想把这个 service 转成 go 

426
00:18:07,160 --> 00:18:09,120
代码里面的 interface 的话

427
00:18:09,120 --> 00:18:11,250
我们需要再加一个选项

428
00:18:11,250 --> 00:18:15,530
就是再加一个 go GRPC out 

429
00:18:15,530 --> 00:18:16,510
先运行一下

430
00:18:16,510 --> 00:18:17,380
看一看效果

431
00:18:17,380 --> 00:18:21,205
比如我们先不加这个 go 加 pc out 

432
00:18:21,205 --> 00:18:23,530
先使用上面这个命令

433
00:18:23,530 --> 00:18:24,880
先跑一下

434
00:18:24,880 --> 00:18:28,672
好，生成一个 WRC 点 P 1 B 点 go 

435
00:18:28,672 --> 00:18:30,100
在这个文件里面

436
00:18:30,100 --> 00:18:34,430
它只是简单的把 message 转成了 STRUCT 结构体

437
00:18:34,430 --> 00:18:38,330
我们搜索一下 interface 啊

438
00:18:38,330 --> 00:18:40,670
无结果找不到 interface 

439
00:18:40,670 --> 00:18:43,497
那如果我们使用下面这个命令

440
00:18:43,497 --> 00:18:46,880
把 go 加 pc out 给加上

441
00:18:46,880 --> 00:18:48,657
再来执行一下

442
00:18:48,657 --> 00:18:50,310
注意看左边呢

443
00:18:50,310 --> 00:18:52,075
它多生成了一个文件

444
00:18:52,075 --> 00:18:55,530
就是这个 rap GRPC 点 PP 点 go 啊

445
00:18:55,530 --> 00:18:56,310
多了一个文件

446
00:18:56,310 --> 00:18:58,125
那么在这个文件里面呢

447
00:18:58,125 --> 00:19:00,030
他就有这个 interface 

448
00:19:00,030 --> 00:19:01,560
而且还有好几个

449
00:19:01,560 --> 00:19:05,270
好，这次我们的 PROTOC 各种用法就总结完了
