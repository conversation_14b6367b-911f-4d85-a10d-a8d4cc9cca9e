package main

import "dqq/go/basic/concurrence"

func main() {
	// concurrence.SimpleGoroutine()
	// concurrence.SubRoutine()
	// concurrence.WaitGroup()
	// concurrence.Atomic()
	// concurrence.Lock()
	// concurrence.ReentranceRLock(3)
	// concurrence.ReentranceWLock(3)
	// concurrence.RLockExclusion()
	// concurrence.WLockExclusion()
	// concurrence.CollectionSafety()
	// concurrence.ServiceMain()
	// concurrence.CloseChannel()
	// concurrence.ChannelBlock()
	// concurrence.TraverseChannel()
	// concurrence.Block()
	// concurrence.Broadcast()
	// concurrence.CutDownLatch()
	// concurrence.CondSignal()
	// concurrence.ChannelSignal()
	// concurrence.CondBroadcast()
	// concurrence.ChannelBroadcast()
	// concurrence.DealMassFile("data/biz_log")
	// concurrence.QpsLimit()
	// concurrence.RoutineLimit()
	// concurrence.ListenMultiWay()
	// concurrence.SelectBlock()
	// concurrence.Timeout1()
	// concurrence.Timeout2()
	// concurrence.InheritTimeout()
	concurrence.RoutineID()
	// concurrence.ContextWithValue()
}
