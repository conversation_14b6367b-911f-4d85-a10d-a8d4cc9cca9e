package server_test

import (
	"testing"
)

func TestTcpServer(t *testing.T) {
	TcpServer()
}

func TestUdpServer(t *testing.T) {
	UdpServer()
}

func TestTcpLongConnection(t *testing.T) {
	TcpLongConnection()
}

func TestUdpLongConnection(t *testing.T) {
	UdpLongConnection()
}

func TestTcpStick(t *testing.T) {
	TcpStick()
}

func TestUdpConnectionCurrent(t *testing.T) {
	UdpConnectionCurrent()
}

func TestUdpRpcServer(t *testing.T) {
	UdpRpcServer()
}

// go test -v ./socket/server -run=^TestTcpServer$ -count=1
// go test -v ./socket/server -run=^TestUdpServer$ -count=1
// go test -v ./socket/server -run=^TestTcpLongConnection$ -count=1
// go test -v ./socket/server -run=^TestUdpLongConnection$ -count=1
// go test -v ./socket/server -run=^TestTcpStick$ -count=1
// go test -v ./socket/server -run=^TestUdpConnectionCurrent$ -count=1
// go test -v ./socket/server -run=^TestUdpRpcServer$ -count=1
