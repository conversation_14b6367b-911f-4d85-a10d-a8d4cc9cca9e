package main

import (
	"bytes"
	"context"
	"dqq/go/advanced/micro_service/idl"
	"fmt"
	"io"
	"net/http"
	"sync"
	"testing"

	"github.com/bytedance/sonic"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

const student_id = 10

func TestGrpc(t *testing.T) {
	conn, err := grpc.NewClient(
		"127.0.0.1:5678",
		grpc.WithTransportCredentials(insecure.NewCredentials()),
	)
	if err != nil {
		fmt.Printf("dial failed: %s", err)
		return
	}
	//创建client
	client := idl.NewStudentServiceClient(conn)

	//准备好请求参数
	request := idl.StudentID{Id: student_id}
	//发送请求，取得响应
	response, err := client.GetStudent(context.Background(), &request)
	if err != nil {
		fmt.Printf("get student failed: %s", err)
	} else {
		fmt.Println(response.Id)
	}
}

func TestHttp(t *testing.T) {
	client := http.Client{}

	//准备好请求参数
	sid := idl.StudentID{Id: student_id}
	bs, err := sonic.Marshal(&sid) //也可以使用pb进行序列化，然后Content-type指定为"application/protobuf"
	if err != nil {
		fmt.Printf("marshal request failed: %s\n", err)
		return
	}

	request, err := http.NewRequest(http.MethodPost, "http://127.0.0.1:5679/", bytes.NewReader(bs))
	if err != nil {
		fmt.Printf("build request failed: %s\n", err)
		return
	}
	resp, err := client.Do(request)
	if err != nil {
		fmt.Printf("http rpc failed: %s\n", err)
		return
	}
	bs, err = io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("read response stream failed: %s\n", err)
		return
	}
	resp.Body.Close()

	var stu idl.Student
	err = sonic.Unmarshal(bs, &stu)
	if err != nil {
		fmt.Printf("unmarshal student failed: %s\n", err)
		return
	}
	fmt.Println(stu.Id)
}

const P = 10

func BenchmarkGrpc(b *testing.B) {
	//连接到服务端
	conn, err := grpc.NewClient(
		"127.0.0.1:5678",
		grpc.WithTransportCredentials(insecure.NewCredentials()),
	)
	if err != nil {
		fmt.Printf("dial failed: %s", err)
		return
	}
	//创建client
	client := idl.NewStudentServiceClient(conn)
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		wg := sync.WaitGroup{}
		wg.Add(P)
		for j := 0; j < P; j++ {
			go func() {
				defer wg.Done()
				//准备好请求参数
				request := idl.StudentID{Id: student_id}
				//调用服务，取得结果，结果反序列化为结构体
				client.GetStudent(context.Background(), &request)
			}()
		}
		wg.Wait()
	}
}

func BenchmarkHttp(b *testing.B) {
	client := http.Client{}
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		wg := sync.WaitGroup{}
		wg.Add(P)
		for j := 0; j < P; j++ {
			go func() {
				defer wg.Done()
				//准备好请求参数
				sid := idl.StudentID{Id: student_id}
				bs, err := sonic.Marshal(&sid)
				if err != nil {
					fmt.Printf("marshal request failed: %s\n", err)
					return
				}

				request, err := http.NewRequest(http.MethodPost, "http://127.0.0.1:5679/", bytes.NewReader(bs))
				if err != nil {
					fmt.Printf("build request failed: %s\n", err)
					return
				}
				//调用服务
				resp, err := client.Do(request)
				if err != nil {
					fmt.Printf("http rpc failed: %s\n", err)
					return
				}
				//取得结果
				bs, err = io.ReadAll(resp.Body)
				if err != nil {
					fmt.Printf("read response stream failed: %s\n", err)
					return
				}
				resp.Body.Close()
				//结果反序列化为结构体
				var stu idl.Student
				sonic.Unmarshal(bs, &stu)
			}()
		}
		wg.Wait()
	}
}

// go test -v .\micro_service\client\ -run=^TestGrpc$ -count=1
// go test -v .\micro_service\client\ -run=^TestHttp$ -count=1
// go test .\micro_service\client\ -bench=^BenchmarkGrpc$ -run=^$ -count=1 -benchmem -benchtime=5s
// go test .\micro_service\client\ -bench=^BenchmarkHttp$ -run=^$ -count=1 -benchmem -benchtime=5s
// go test .\micro_service\client\ -bench=^Benchmark -run=^$ -count=1 -benchmem -benchtime=10s

/**
10个并发
BenchmarkGrpc-8            21346            279707 ns/op           60176 B/op       1260 allocs/op
BenchmarkHttp-8             4136          15979148 ns/op          135459 B/op       1209 allocs/op
1个并发
BenchmarkGrpc-8            10000            544725 ns/op            6219 B/op        136 allocs/op
BenchmarkHttp-8            59646            101727 ns/op            6918 B/op         90 allocs/op
*/
