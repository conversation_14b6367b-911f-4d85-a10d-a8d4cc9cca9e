// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: greet.proto

package idl

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	HelloHttp_Greeting_FullMethodName = "/idl.HelloHttp/Greeting"
)

// HelloHttpClient is the client API for HelloHttp service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type HelloHttpClient interface {
	Greeting(ctx context.Context, in *GreetRequest, opts ...grpc.CallOption) (*GreetResopnse, error)
}

type helloHttpClient struct {
	cc grpc.ClientConnInterface
}

func NewHelloHttpClient(cc grpc.ClientConnInterface) HelloHttpClient {
	return &helloHttpClient{cc}
}

func (c *helloHttpClient) Greeting(ctx context.Context, in *GreetRequest, opts ...grpc.CallOption) (*GreetResopnse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GreetResopnse)
	err := c.cc.Invoke(ctx, HelloHttp_Greeting_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// HelloHttpServer is the server API for HelloHttp service.
// All implementations must embed UnimplementedHelloHttpServer
// for forward compatibility.
type HelloHttpServer interface {
	Greeting(context.Context, *GreetRequest) (*GreetResopnse, error)
	mustEmbedUnimplementedHelloHttpServer()
}

// UnimplementedHelloHttpServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedHelloHttpServer struct{}

func (UnimplementedHelloHttpServer) Greeting(context.Context, *GreetRequest) (*GreetResopnse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Greeting not implemented")
}
func (UnimplementedHelloHttpServer) mustEmbedUnimplementedHelloHttpServer() {}
func (UnimplementedHelloHttpServer) testEmbeddedByValue()                   {}

// UnsafeHelloHttpServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to HelloHttpServer will
// result in compilation errors.
type UnsafeHelloHttpServer interface {
	mustEmbedUnimplementedHelloHttpServer()
}

func RegisterHelloHttpServer(s grpc.ServiceRegistrar, srv HelloHttpServer) {
	// If the following call pancis, it indicates UnimplementedHelloHttpServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&HelloHttp_ServiceDesc, srv)
}

func _HelloHttp_Greeting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GreetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HelloHttpServer).Greeting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HelloHttp_Greeting_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HelloHttpServer).Greeting(ctx, req.(*GreetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// HelloHttp_ServiceDesc is the grpc.ServiceDesc for HelloHttp service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var HelloHttp_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "idl.HelloHttp",
	HandlerType: (*HelloHttpServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Greeting",
			Handler:    _HelloHttp_Greeting_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "greet.proto",
}
