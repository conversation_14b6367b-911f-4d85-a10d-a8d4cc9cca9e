// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: student.proto

package idl

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	StudentService_GetStudent_FullMethodName   = "/StudentService/GetStudent"
	StudentService_GetStudents_FullMethodName  = "/StudentService/GetStudents"
	StudentService_GetStudents2_FullMethodName = "/StudentService/GetStudents2"
	StudentService_GetStudents3_FullMethodName = "/StudentService/GetStudents3"
	StudentService_GetStudents4_FullMethodName = "/StudentService/GetStudents4"
)

// StudentServiceClient is the client API for StudentService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type StudentServiceClient interface {
	// Unary RPC
	GetStudent(ctx context.Context, in *StudentID, opts ...grpc.CallOption) (*Student, error)
	GetStudents(ctx context.Context, in *StudentIDs, opts ...grpc.CallOption) (*Students, error)
	// Server streaming RPC
	GetStudents2(ctx context.Context, in *StudentIDs, opts ...grpc.CallOption) (grpc.ServerStreamingClient[Student], error)
	// Client streaming RPC
	GetStudents3(ctx context.Context, opts ...grpc.CallOption) (grpc.ClientStreamingClient[StudentID, Students], error)
	// Bidirectional streaming RPC
	GetStudents4(ctx context.Context, opts ...grpc.CallOption) (grpc.BidiStreamingClient[StudentID, Student], error)
}

type studentServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewStudentServiceClient(cc grpc.ClientConnInterface) StudentServiceClient {
	return &studentServiceClient{cc}
}

func (c *studentServiceClient) GetStudent(ctx context.Context, in *StudentID, opts ...grpc.CallOption) (*Student, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Student)
	err := c.cc.Invoke(ctx, StudentService_GetStudent_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *studentServiceClient) GetStudents(ctx context.Context, in *StudentIDs, opts ...grpc.CallOption) (*Students, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Students)
	err := c.cc.Invoke(ctx, StudentService_GetStudents_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *studentServiceClient) GetStudents2(ctx context.Context, in *StudentIDs, opts ...grpc.CallOption) (grpc.ServerStreamingClient[Student], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &StudentService_ServiceDesc.Streams[0], StudentService_GetStudents2_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[StudentIDs, Student]{ClientStream: stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type StudentService_GetStudents2Client = grpc.ServerStreamingClient[Student]

func (c *studentServiceClient) GetStudents3(ctx context.Context, opts ...grpc.CallOption) (grpc.ClientStreamingClient[StudentID, Students], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &StudentService_ServiceDesc.Streams[1], StudentService_GetStudents3_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[StudentID, Students]{ClientStream: stream}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type StudentService_GetStudents3Client = grpc.ClientStreamingClient[StudentID, Students]

func (c *studentServiceClient) GetStudents4(ctx context.Context, opts ...grpc.CallOption) (grpc.BidiStreamingClient[StudentID, Student], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &StudentService_ServiceDesc.Streams[2], StudentService_GetStudents4_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[StudentID, Student]{ClientStream: stream}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type StudentService_GetStudents4Client = grpc.BidiStreamingClient[StudentID, Student]

// StudentServiceServer is the server API for StudentService service.
// All implementations must embed UnimplementedStudentServiceServer
// for forward compatibility.
type StudentServiceServer interface {
	// Unary RPC
	GetStudent(context.Context, *StudentID) (*Student, error)
	GetStudents(context.Context, *StudentIDs) (*Students, error)
	// Server streaming RPC
	GetStudents2(*StudentIDs, grpc.ServerStreamingServer[Student]) error
	// Client streaming RPC
	GetStudents3(grpc.ClientStreamingServer[StudentID, Students]) error
	// Bidirectional streaming RPC
	GetStudents4(grpc.BidiStreamingServer[StudentID, Student]) error
	mustEmbedUnimplementedStudentServiceServer()
}

// UnimplementedStudentServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedStudentServiceServer struct{}

func (UnimplementedStudentServiceServer) GetStudent(context.Context, *StudentID) (*Student, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStudent not implemented")
}
func (UnimplementedStudentServiceServer) GetStudents(context.Context, *StudentIDs) (*Students, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStudents not implemented")
}
func (UnimplementedStudentServiceServer) GetStudents2(*StudentIDs, grpc.ServerStreamingServer[Student]) error {
	return status.Errorf(codes.Unimplemented, "method GetStudents2 not implemented")
}
func (UnimplementedStudentServiceServer) GetStudents3(grpc.ClientStreamingServer[StudentID, Students]) error {
	return status.Errorf(codes.Unimplemented, "method GetStudents3 not implemented")
}
func (UnimplementedStudentServiceServer) GetStudents4(grpc.BidiStreamingServer[StudentID, Student]) error {
	return status.Errorf(codes.Unimplemented, "method GetStudents4 not implemented")
}
func (UnimplementedStudentServiceServer) mustEmbedUnimplementedStudentServiceServer() {}
func (UnimplementedStudentServiceServer) testEmbeddedByValue()                        {}

// UnsafeStudentServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to StudentServiceServer will
// result in compilation errors.
type UnsafeStudentServiceServer interface {
	mustEmbedUnimplementedStudentServiceServer()
}

func RegisterStudentServiceServer(s grpc.ServiceRegistrar, srv StudentServiceServer) {
	// If the following call pancis, it indicates UnimplementedStudentServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&StudentService_ServiceDesc, srv)
}

func _StudentService_GetStudent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StudentID)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StudentServiceServer).GetStudent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StudentService_GetStudent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StudentServiceServer).GetStudent(ctx, req.(*StudentID))
	}
	return interceptor(ctx, in, info, handler)
}

func _StudentService_GetStudents_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StudentIDs)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StudentServiceServer).GetStudents(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StudentService_GetStudents_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StudentServiceServer).GetStudents(ctx, req.(*StudentIDs))
	}
	return interceptor(ctx, in, info, handler)
}

func _StudentService_GetStudents2_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(StudentIDs)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(StudentServiceServer).GetStudents2(m, &grpc.GenericServerStream[StudentIDs, Student]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type StudentService_GetStudents2Server = grpc.ServerStreamingServer[Student]

func _StudentService_GetStudents3_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(StudentServiceServer).GetStudents3(&grpc.GenericServerStream[StudentID, Students]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type StudentService_GetStudents3Server = grpc.ClientStreamingServer[StudentID, Students]

func _StudentService_GetStudents4_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(StudentServiceServer).GetStudents4(&grpc.GenericServerStream[StudentID, Student]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type StudentService_GetStudents4Server = grpc.BidiStreamingServer[StudentID, Student]

// StudentService_ServiceDesc is the grpc.ServiceDesc for StudentService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var StudentService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "StudentService",
	HandlerType: (*StudentServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetStudent",
			Handler:    _StudentService_GetStudent_Handler,
		},
		{
			MethodName: "GetStudents",
			Handler:    _StudentService_GetStudents_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "GetStudents2",
			Handler:       _StudentService_GetStudents2_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "GetStudents3",
			Handler:       _StudentService_GetStudents3_Handler,
			ClientStreams: true,
		},
		{
			StreamName:    "GetStudents4",
			Handler:       _StudentService_GetStudents4_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "student.proto",
}
