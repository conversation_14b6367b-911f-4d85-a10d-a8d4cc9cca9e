package concurrence

import (
	"fmt"
	"runtime"
	"sync"
	"sync/atomic"
	"testing"
	"time"
)

func TestCompareAndSwap(t *testing.T) {
	const oldValue uint32 = 5
	const newValue uint32 = 13
	var value uint32 = oldValue
	var updateCnt int32

	const P = 10000000
	wg := sync.WaitGroup{}
	wg.Add(P)
	for i := 0; i < P; i++ {
		go func() {
			defer wg.Done()

			if value == oldValue { // 开启1000万个协程，跟atomic.CompareAndSwap效果是一样的
				value = newValue
				atomic.AddInt32(&updateCnt, 1)
			}

			// if atomic.CompareAndSwapUint32(&value, oldValue, newValue) {
			// 	atomic.AddInt32(&updateCnt, 1)
			// }
		}()
	}
	wg.Wait()
	if value != newValue {
		fmt.Println("value is", value)
		t.Fail()
	}
	if updateCnt != 1 {
		fmt.Println("updateCnt is", updateCnt)
		t.Fail()
	}
}

func TestSpinlock(t *testing.T) {
	const P = 100
	const LOOP = 100
	wg := sync.WaitGroup{}
	var lock sync.Locker
	var begin time.Time

	lock = new(sync.Mutex)
	wg.Add(P)
	begin = time.Now()
	for i := 0; i < P; i++ {
		go func() {
			defer wg.Done()
			for j := 0; j < LOOP; j++ {
				lock.Lock()
				runtime.Gosched()
				lock.Unlock()
			}
		}()
	}
	wg.Wait()
	fmt.Println("mutex use time", time.Since(begin).Microseconds(), "us")

	lock = NewSpinLock(SCHED)
	wg.Add(P)
	begin = time.Now()
	for i := 0; i < P; i++ {
		go func() {
			defer wg.Done()
			for j := 0; j < LOOP; j++ {
				lock.Lock()
				runtime.Gosched()
				lock.Unlock()
			}
		}()
	}
	wg.Wait()
	fmt.Println("sched lock use time", time.Since(begin).Microseconds(), "us")

	lock = NewSpinLock(SLEEP)
	wg.Add(P)
	begin = time.Now()
	for i := 0; i < P; i++ {
		go func() {
			defer wg.Done()
			for j := 0; j < LOOP; j++ {
				lock.Lock()
				runtime.Gosched()
				lock.Unlock()
			}
		}()
	}
	wg.Wait()
	fmt.Println("sleep lock use time", time.Since(begin).Microseconds(), "us")
}

// go test -v ./concurrence -run=^TestSyscall$ -count=1
// go test -v ./concurrence -run=^TestCompareAndSwap$ -count=1
// go test -v ./concurrence -run=^TestSpinlock$ -count=1
/**
2个协程
mutex use time 0 us
sched lock use time 0 us
sleep lock use time 1048 us

20个协程
mutex use time 1085 us
sched lock use time 4168 us
sleep lock use time 16979 us

40个协程
mutex use time 1566 us
sched lock use time 20520 us
sleep lock use time 52145 us

100个协程
mutex use time 3644 us
sched lock use time 146596 us
sleep lock use time 81282 us
*/
