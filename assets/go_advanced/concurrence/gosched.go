package concurrence

import (
	"fmt"
	"runtime"
	"sync"
)

func GoSched() {
	const P = 10
	wg := sync.WaitGroup{}
	wg.Add(P)
	for i := 0; i < P; i++ {
		go func() {
			defer wg.Done()
			fmt.Printf("%d begin\n", i)
			runtime.Gosched() //把goroutine放到等待队列的尾部
			fmt.Printf("%d back\n", i)
		}()
	}
	wg.Wait()
}

func GoExit() {
	const P = 10
	wg := sync.WaitGroup{}
	wg.Add(P)
	for i := 0; i < P; i++ {
		go func() {
			defer wg.Done()
			fmt.Printf("%d begin\n", i)
			runtime.Goexit() //结束goroutine
			fmt.Printf("%d back\n", i)
		}()
	}
	wg.Wait()
}
