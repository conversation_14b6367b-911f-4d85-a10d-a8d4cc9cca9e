package concurrence

import (
	"runtime"
	"sync"
	"sync/atomic"
	"time"
)

const (
	SCHED = iota
	SLEEP
)

type spinlock struct { //实现了sync.Locker接口
	state uint32
	mode  int
}

const maxBackoff = 16 //指数退让周期的上限

func (sl *spinlock) Lock() {
	backoff := 1 //初始退让周期是1
	//自旋锁的缺点：如果锁竞争激烈，此处for循环会持续很长时间，造成大量浪费，不如进入挂起线程，进入内核态
	for !atomic.CompareAndSwapUint32(&sl.state, 0, 1) {
		//退让
		switch sl.mode {
		case SLEEP:
			time.Sleep(100 * time.Duration(backoff) * time.Microsecond) // 通过休眠让出cpu
		default:
			for i := 0; i < backoff; i++ {
				runtime.Gosched() //把goroutine放到等待队列的尾部
			}
		}
		// 指数退让
		if backoff < maxBackoff {
			backoff <<= 1 //退让周期乘2
		}
	}
}

func (sl *spinlock) Unlock() {
	atomic.StoreUint32(&sl.state, 0)
}

func NewSpinLock(mode int) sync.Locker {
	return &spinlock{
		mode: mode,
	}
}
