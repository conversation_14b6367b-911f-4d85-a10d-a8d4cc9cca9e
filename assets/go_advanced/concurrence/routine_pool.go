package concurrence

import (
	"container/list"
	"errors"
	"sync"
	"sync/atomic"
)

var (
	// 协程池里放的是任务，而不是协程
	a = func() {} // 函数可以赋给一个变量，可以把变量放入一个集合（即“池”）
	// b=go func(){}()  // 协程不能赋给一个变量
)

type SimpleRoutinePool struct {
	tasks chan func()
	wg    sync.WaitGroup
}

func NewSimpleRoutinePool(cap int) *SimpleRoutinePool {
	if cap <= 0 {
		panic("pool capacity must more than 0")
	}
	pool := new(SimpleRoutinePool)
	pool.tasks = make(chan func(), cap)
	pool.wg.Add(cap)
	for i := 0; i < cap; i++ {
		go func() {
			for task := range pool.tasks {
				task()
			}
			pool.wg.Done()
		}()
	}
	return pool
}

func (pool *SimpleRoutinePool) Submit(task func()) {
	pool.tasks <- task
}

func (pool *SimpleRoutinePool) WaitForStop() {
	close(pool.tasks)
	pool.wg.Wait()
}

const (
	INIT = iota
	OPENED
	STOPPING
	STOPPED
)

// 一个worker就是一个协程
type worker struct {
	tasks chan func() //协程有很多工作需要做，一项工作就是一个func，这些func放在一个channel里排队
	state int32
}

func newWorker() *worker {
	worker := new(worker)
	worker.tasks = make(chan func(), 1)
	worker.state = INIT
	return worker
}

func (w *worker) run() {
	if atomic.CompareAndSwapInt32(&w.state, INIT, OPENED) { //防止并发多次调用run()，确保只启动一个协程
		go func() {
			for f := range w.tasks {
				f()
			}
			w.state = STOPPED
		}()
	}
}

func (w *worker) stop() {
	if atomic.CompareAndSwapInt32(&w.state, OPENED, STOPPING) { //防止并发多次调用stop()，确保channel只被关闭一次
		close(w.tasks) //channel只能被关闭一次
	}
}

func (w *worker) isStopped() bool {
	return w.state == STOPPED
}

type RoutinePool struct {
	workers *list.List // 可以改成对象池sync.Pool，按需要分配
	state   int32
}

func NewRoutinePool(cap int) *RoutinePool {
	if cap <= 0 {
		panic("pool capacity must more than 0")
	}
	pool := new(RoutinePool)
	pool.workers = list.New()
	// Preallocated模式
	for i := 0; i < cap; i++ {
		worker := newWorker()
		worker.run()
		pool.workers.PushBack(worker)
	}
	pool.state = OPENED
	return pool
}

func (pool *RoutinePool) Stop() {
	if atomic.CompareAndSwapInt32(&pool.state, OPENED, STOPPING) {
		curr := pool.workers.Front()
		for {
			if curr == nil {
				break
			}
			worker := curr.Value.(*worker)
			worker.stop()
			curr = curr.Next()
		}
	}
}

// 判断pool里的任务是不是已经全部完成了（前提是已执行过了Stop()函数）
func (pool *RoutinePool) IsStopped() bool {
	result := true
	curr := pool.workers.Front()
	for {
		if curr == nil {
			break
		}
		worker := curr.Value.(*worker)
		if !worker.isStopped() { // 只要有一个worker未终止，就说明整个pool未完全终止
			result = false
			break
		}
		curr = curr.Next()
	}
	return result
}

func (pool *RoutinePool) Submit(task func()) error {
	if atomic.LoadInt32(&pool.state) != OPENED {
		return errors.New("pool is closed")
	}

	curr := pool.workers.Front()
	worker := curr.Value.(*worker)
	worker.tasks <- task //把任务分配给队首元素。这一行写channel，可能会阻塞
	pool.workers.Remove(curr)
	pool.workers.PushBack(worker) //再把队首元素移到队尾。虽然整体来说各worker负载均衡，但是有可能存在空闲的worker却没拿到新任务

	return nil
}
