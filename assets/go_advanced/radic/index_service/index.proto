syntax = "proto3";

package index_service;

option go_package="./index_service;index_service"; //分号前面的go_out路径至少得包含一个.或一个/

import "types/doc.proto";  //从-I指定的目录下寻找该proto文件
import "types/term_query.proto";


message DocId {
    string DocId = 1;
}

message AffectedCount {
    int32 Count = 1;
}

message SearchRequest {
    types.TermQuery Query = 1;  //TermQuery类型引用自term_query.proto
    uint64 OnFlag = 2;
    uint64 OffFlag = 3;
    repeated uint64 OrFlags = 4;
}

message SearchResult {
    repeated types.Document Results = 1;
}

message CountRequest {
}

service IndexService {
    rpc DeleteDoc(DocId) returns (AffectedCount);
    rpc AddDoc(types.Document) returns (AffectedCount);
    rpc Search(SearchRequest) returns (SearchResult);
    rpc Count(CountRequest) returns (AffectedCount);
}

// protoc --go_out=plugins=grpc:. -I=D:/go_project/go2career/radic --proto_path=./index_service index.proto --go_opt=Mtypes/doc.proto=github.com/Orisun/radic/v2/types --go_opt=Mtypes/term_query.proto=github.com/Orisun/radic/v2/types 
// 在windows上-I需使用绝对路径。-I指定到module一级，即有go.mod文件的那一级目录。
// --go_opt=M指示了.proto里的import转到.go里该怎么写，比如.proto里写import "doc.proto"，转到.go里就应该写 import "github.com/Orisun/radic/v2/types"
// -I和--go_opt=M可以有多个
