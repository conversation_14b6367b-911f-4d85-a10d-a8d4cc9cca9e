.range-slider {
    height: 5px;
    position: relative;
    background-color: #e1e9f6;
    border-radius: 2px;
  }
  .range-selected {
    height: 100%;
    left: 1%;
    right: 80%;
    position: absolute;
    border-radius: 5px;
    background-color: #FA9862;
  }
  
  .range-input {
    position: relative;
  }
  .range-input input {
    position: absolute;
    width: 100%;
    height: 5px;
    top: -7px;
    background: none;
    pointer-events: none;
    -webkit-appearance: none;
    -moz-appearance: none;
  }
  .range-input input::-webkit-slider-thumb {
    height: 20px;
    width: 20px;
    border-radius: 50%;
    border: 3px solid #FA9862;
    background-color: #fff;
    pointer-events: auto;
    -webkit-appearance: none;
  }
  .range-input input::-moz-range-thumb {
    height: 15px;
    width: 15px;
    border-radius: 50%;
    border: 3px solid #FA9862;
    background-color: #fff;
    pointer-events: auto;
    -moz-appearance: none;
  }
  
  .range-price {
      margin: 30px 0;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .range-price label {
      margin-right: 5px;
    }
    .range-price input {
      width: 80px;
      padding: 5px;
    }
    .range-price input:first-of-type {
      margin-right: 5px;
    }