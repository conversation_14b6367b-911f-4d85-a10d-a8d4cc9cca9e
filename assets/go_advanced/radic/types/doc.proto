syntax = "proto3";

package types;

option go_package=".;types";

message Keyword {
    string Field = 1;
    string Word = 2;
}

message Document {
    string Id = 1;          //业务使用的唯一Id，索引上此Id不会重复。在正排索引上，Document的业务ID作为key，Document作为value。
    uint64 IntId = 2;       //倒排索引上使用的文档id。业务侧不用管这个字段，该字段是radic内部自动生成的
    uint64 BitsFeature = 3; //每个bit都表示某种特征的取值
    repeated Keyword Keywords = 4;      //倒排索引的key
    bytes Bytes = 5;        //业务实体序列化之后的结果
}

// protoc --go_out=./types --proto_path=./types doc.proto