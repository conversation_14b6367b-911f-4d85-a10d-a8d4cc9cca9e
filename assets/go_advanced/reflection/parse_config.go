package main

import (
	"encoding/json"
	"fmt"
	"os"
	"reflect"
	"strings"

	"github.com/dgryski/go-farm"
	"github.com/spaolacci/murmur3"
)

type IHash interface {
	Hash(string) uint64
}

type Murmur struct{}

func (Murmur) Hash(str string) uint64 {
	hash := murmur3.New64()
	hash.Write([]byte(str))
	z := hash.Sum64()
	hash.Reset()
	return z
}

type FarmHash struct{}

func (FarmHash) Hash(str string) uint64 {
	return farm.Hash64WithSeed([]byte(str), uint64(0))
}

type FeatureConfig struct {
	Path     string `json:"path"`
	Hash     string `json:"hash"`
	HashFunc IHash  `json:"-"`
}

type FeatureExtractor []*FeatureConfig

// 根据配置文件解析出FeatureConfig。在这一步充分检查配置文件的正确性。把尽量多的反射都放到初始化阶段，一次性执行
func (m *FeatureExtractor) Init(confFile string) error {
	if fin, err := os.Open(confFile); err != nil {
		return err
	} else {
		defer fin.Close()
		const MAX_CONFIG_SIZE = 1 << 20 //配置文件的大小不能超过1M
		bs := make([]byte, MAX_CONFIG_SIZE)
		if n, err := fin.Read(bs); err != nil {
			return err
		} else {
			if n >= MAX_CONFIG_SIZE {
				return fmt.Errorf("config file size more than %dB", MAX_CONFIG_SIZE)
			}
			var confList FeatureExtractor
			if err = json.Unmarshal(bs[:n], &confList); err != nil {
				return err
			} else {
				bookType := reflect.TypeOf(Book{})
				for _, conf := range confList {
					paths := strings.Split(conf.Path, ".")
					if field, ok := bookType.FieldByName(paths[0]); !ok { //确保Field存在
						return fmt.Errorf("field %s not found, full path %s", paths[0], conf.Path)
					} else {
						if !field.IsExported() {
							return fmt.Errorf("field %s is not exported", paths[0]) //确保每一个Field都是外部可见的
						}
						for i := 1; i < len(paths); i++ { //处理struct嵌套的情况
							fieldType := field.Type
							if fieldType.Kind() == reflect.Ptr {
								fieldType = fieldType.Elem() //不能在指针类型上调用FieldByName
							}
							if field, ok = fieldType.FieldByName(paths[i]); !ok {
								return fmt.Errorf("field %s not found, full path %s", paths[i], conf.Path)
							} else {
								if !field.IsExported() {
									return fmt.Errorf("field %s is not exported", paths[i])
								}
							}
						}
					}
					//解析出哈希函数
					conf.HashFunc = parseHashFunc(conf.Hash)
					if conf.HashFunc == nil {
						return fmt.Errorf("path %s Hash %s is INVALID", conf.Path, conf.Hash)
					}
					*m = append(*m, conf)
				}
			}
		}
	}
	return nil
}

func parseHashFunc(str string) IHash {
	switch strings.ToLower(str) {
	case "murmur":
		return Murmur{}
	case "farm":
		return FarmHash{}
	default:
		return FarmHash{} //默认采用farm-hash，性能更好一些
	}

}

// 对一个Field进行特征抽取。该field只能是基础数据类型或者slice、time.Time，如果是slice还需要递归遍历里面的每一个元素
func (m FeatureExtractor) extractOneField(conf *FeatureConfig, field reflect.Value, result *[]uint64) {
	switch field.Kind() {
	case reflect.Slice:
		for i := 0; i < field.Len(); i++ { //遍历slice里的每一个元素
			m.extractOneField(conf, field.Index(i), result) //递归，result用于容纳每一层递归的结果
		}
	case reflect.Array, reflect.Map, reflect.Chan: //暂不支持这些类型
		return
	case reflect.Struct: //如果是struct则仅支持time.Time
		fieldType := field.Type()
		if fieldType.String() != "time.Time" && fieldType.String() != "*time.Time" {
			return
		}
		fallthrough //强制执行下一个case（或default）
	default:
		str := fmt.Sprintf("%v", field.Interface())
		h := conf.HashFunc.Hash(str)
		*result = append(*result, h)
	}
}

func (m FeatureExtractor) Extract(book *Book) []uint64 {
	rect := make([]uint64, 0, 100)
	bookValue := reflect.ValueOf(book).Elem() //不能在指针上调用FieldByName
	for _, conf := range m {                  //遍历配置文件里的特征抽取项目
		paths := strings.Split(conf.Path, ".")
		field := bookValue.FieldByName(paths[0])
		for i := 1; i < len(paths); i++ {
			//如果存在struct嵌套的情况，则取到最内层的Field
			if field.Kind() == reflect.Ptr {
				field = field.Elem() //解析指针
			}
			field = field.FieldByName(paths[i])
		}
		result := make([]uint64, 0, 10)
		m.extractOneField(conf, field, &result)
		fmt.Println(conf.Path, field.Interface(), result)
		rect = append(rect, result...)
	}
	return rect
}
