package main

import (
	"fmt"
	"testing"
)

var extractor FeatureExtractor

func init() {
	err := extractor.Init("hash.conf") //把尽量多的反射都放到初始化阶段，一次性执行
	if err != nil {
		panic(err)
	}
}

func TestFeatureExtract(t *testing.T) {
	book := Book{
		ISBN: "43275495",
		Author: &User{
			Name: "钱钟书",
		},
		Price: 58.0,
	}
	numbers := extractor.Extract(&book)
	fmt.Println(numbers)
}

// go test -v ./reflection -run=^TestFeatureExtract$ -count=1
