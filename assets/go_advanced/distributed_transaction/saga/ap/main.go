package main

import (
	"database/sql"
	"dqq/go/advanced/distributed_transaction/dao"
	"errors"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/dtm-labs/dtm/client/dtmcli"
	"github.com/dtm-labs/dtm/dtmutil"
	"github.com/dtm-labs/logger"
	"github.com/gin-gonic/gin"
	"github.com/lithammer/shortuuid/v3"
)

const (
	dtmServer = "http://localhost:36789/api/dtmsvr"
	bankAUrl  = "http://localhost:5678"
	bankBUrl  = "http://localhost:5679"
)

type TransReq struct {
	FromUid int `form:"from" binding:"gt=0,required"`   //转出账户
	ToUid   int `form:"to" binding:"gt=0,required"`     //转入账户
	Amount  int `form:"amount" binding:"gt=0,required"` //转出金额
}

type AdjustBalance struct {
	Account int `json:"account" binding:"gt=0,required"` //账户
	Amount  int `json:"amount" binding:"gt=0,required"`  //余额增量
}

func postForm(url string, data url.Values) error {
	request, err := http.NewRequest(http.MethodPost, url, strings.NewReader(data.Encode()))
	if err != nil {
		return err
	}
	request.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	client := http.Client{
		Timeout: 300 * time.Millisecond, //如果不是为了控制超时，可以直接使用http.PostForm()
	}
	resp, err := client.Do(request)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	bs, _ := io.ReadAll(resp.Body)
	if resp.StatusCode != 200 {
		return errors.New(string(bs))
	}
	return nil
}

// 返回值只有3种：dtmcli.ErrOngoing(框架会执行重试)、dtmcli.ErrFailure(框架会执行补偿)、nil(代表成功)
func transOut(ctx *gin.Context) any {
	barrier, _ := dtmcli.BarrierFromQuery(ctx.Request.URL.Query())
	return barrier.CallWithDB(dao.GetBarrierDB(), func(tx *sql.Tx) error {
		logger.Infof("准备转出")
		var req AdjustBalance
		err := ctx.BindJSON(&req) //dtm框架去调外部接口时，传的http参数都是json body
		if err != nil {
			return dtmcli.ErrFailure
		}
		if postForm(bankAUrl+"/adjust_balance", url.Values{"account": []string{strconv.Itoa(req.Account)}, "amount": []string{strconv.Itoa(-req.Amount)}}) != nil {
			return dtmcli.ErrFailure
		} else {
			return nil
		}
	})
}

func transOutComp(ctx *gin.Context) any {
	barrier, _ := dtmcli.BarrierFromQuery(ctx.Request.URL.Query())
	return barrier.CallWithDB(dao.GetBarrierDB(), func(tx *sql.Tx) error {
		logger.Infof("准备转出补偿")
		var req AdjustBalance
		err := ctx.BindJSON(&req)
		if err != nil {
			return dtmcli.ErrFailure
		}
		if postForm(bankAUrl+"/adjust_balance", url.Values{"account": []string{strconv.Itoa(req.Account)}, "amount": []string{strconv.Itoa(req.Amount)}}) != nil {
			return dtmcli.ErrFailure // 补偿暂时失败，框架会不断重试，直到成功
		} else {
			return nil
		}
	})
}

func transIn(ctx *gin.Context) any {
	barrier, _ := dtmcli.BarrierFromQuery(ctx.Request.URL.Query())
	return barrier.CallWithDB(dao.GetBarrierDB(), func(tx *sql.Tx) error {
		logger.Infof("准备转入")
		var req AdjustBalance
		err := ctx.BindJSON(&req)
		if err != nil {
			return dtmcli.ErrFailure
		}
		if postForm(bankBUrl+"/adjust_balance", url.Values{"account": []string{strconv.Itoa(req.Account)}, "amount": []string{strconv.Itoa(req.Amount)}}) != nil {
			return dtmcli.ErrFailure
		} else {
			return nil
		}
	})
}

func transInComp(ctx *gin.Context) any {
	barrier, _ := dtmcli.BarrierFromQuery(ctx.Request.URL.Query())
	return barrier.CallWithDB(dao.GetBarrierDB(), func(tx *sql.Tx) error {
		logger.Infof("准备转入补偿")
		var req AdjustBalance
		err := ctx.BindJSON(&req)
		if err != nil {
			return dtmcli.ErrFailure
		}
		if postForm(bankBUrl+"/adjust_balance", url.Values{"account": []string{strconv.Itoa(req.Account)}, "amount": []string{strconv.Itoa(-req.Amount)}}) != nil {
			return dtmcli.ErrFailure
		} else {
			return nil
		}
	})
}

func Transfer(ctx *gin.Context) {
	var req TransReq
	err := ctx.ShouldBind(&req)
	if err != nil {
		logger.Infof("参数绑定失败:%s", err)
		ctx.String(http.StatusBadRequest, "参数绑定失败")
		return
	}
	transOutReq := AdjustBalance{Account: req.FromUid, Amount: req.Amount}
	transInReq := AdjustBalance{Account: req.ToUid, Amount: req.Amount}

	const apUrl = "http://localhost:7000/branch"
	saga := dtmcli.NewSaga(dtmServer, shortuuid.New()). //利用shortuuid生成gid
								Add(apUrl+"/trans_out", apUrl+"/trans_out_comp", transOutReq). //添加一条分支事务，分支ID为01。在请求actoin和compensate接口时会带上query参数--/branch/trans_out?branch_id=01&gid=DvF9N4sJaDdCS5KYVaPHuT&op=action&trans_type=saga，transOutReq会被json序列化然后放入请求体
								Add(apUrl+"/trans_in", apUrl+"/trans_in_comp", transInReq)     //添加一条分支事务，分支ID为02。
	saga.SetConcurrent()    //各分支并发执行（默认是顺序执行）
	saga.RetryInterval = 60 //当分支返回结果为ONGOING时，dtm默认采用的重试策略为指数递增。这里指定采用固定的重试间隔--60秒。
	err = saga.Submit()     //提交SAGA事务

	if err != nil {
		ctx.String(http.StatusInternalServerError, err.Error())
		return
	}
}

func main() {
	logger.InitLog2("DEBUG", "log/dtm.log", 0, ``)

	gin.SetMode(gin.ReleaseMode)
	engine := gin.Default()

	// 让dtm来调用
	g1 := engine.Group("/branch")
	g1.POST("/trans_out", dtmutil.WrapHandler2(transOut))
	g1.POST("/trans_out_comp", dtmutil.WrapHandler2(transOutComp))
	g1.POST("/trans_in", dtmutil.WrapHandler2(transIn))
	g1.POST("/trans_in_comp", dtmutil.WrapHandler2(transInComp))

	// 让终端用户来调用
	engine.POST("/transfer", Transfer)
	engine.Run("127.0.0.1:7000")
}

// go run ./distributed_transaction/saga/ap
