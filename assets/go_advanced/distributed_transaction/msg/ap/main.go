package main

import (
	"database/sql"
	"dqq/go/advanced/distributed_transaction/dao"
	"errors"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/dtm-labs/dtm/client/dtmcli"
	"github.com/dtm-labs/dtm/dtmutil"
	"github.com/dtm-labs/logger"
	"github.com/gin-gonic/gin"
	"github.com/lithammer/shortuuid/v3"
)

const (
	dtmServer = "http://localhost:36789/api/dtmsvr"
	bankAUrl  = "http://localhost:5678"
	bankBUrl  = "http://localhost:5679"
)

type TransReq struct {
	FromUid int `form:"from" binding:"gt=0,required"`   //转出账户
	ToUid   int `form:"to" binding:"gt=0,required"`     //转入账户
	Amount  int `form:"amount" binding:"gt=0,required"` //转出金额
}

type AdjustBalance struct {
	Account int `json:"account" binding:"gt=0,required"` //账户
	Amount  int `json:"amount" binding:"gt=0,required"`  //余额增量
}

func postForm(url string, data url.Values) error {
	request, err := http.NewRequest(http.MethodPost, url, strings.NewReader(data.Encode()))
	if err != nil {
		return err
	}
	request.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	client := http.Client{
		Timeout: 300 * time.Millisecond, //如果不是为了控制超时，可以直接使用http.PostForm()
	}
	resp, err := client.Do(request)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	bs, _ := io.ReadAll(resp.Body)
	if resp.StatusCode != 200 {
		return errors.New(string(bs))
	}
	return nil
}

func transIn(ctx *gin.Context) any {
	barrier, _ := dtmcli.BarrierFromQuery(ctx.Request.URL.Query()) // 通过barrier进行幂等控制。DTM框架可能会重复地调用transIn
	return barrier.CallWithDB(dao.GetBarrierDB(), func(tx *sql.Tx) error {
		logger.Infof("准备转入")
		var req AdjustBalance
		err := ctx.BindJSON(&req)
		if err != nil {
			return dtmcli.ErrFailure
		}
		return postForm(bankBUrl+"/adjust_balance", url.Values{"account": []string{strconv.Itoa(req.Account)}, "amount": []string{strconv.Itoa(req.Amount)}})
	})
}

func Transfer(ctx *gin.Context) {
	var req TransReq
	err := ctx.ShouldBind(&req)
	if err != nil {
		logger.Infof("参数绑定失败:%s", err)
		ctx.String(http.StatusBadRequest, "参数绑定失败")
		return
	}
	transInReq := AdjustBalance{Account: req.ToUid, Amount: req.Amount}

	const apUrl = "http://localhost:7000/branch"

	prepareGid := shortuuid.New()                                                  // DTM调用trans_in和QueryPrepared的时候query里使用的都是这个prepareGid，使用的branch_id分别是01和00
	msg := dtmcli.NewMsg(dtmServer, prepareGid).Add(apUrl+"/trans_in", transInReq) //分支事务。dtm认为分支事务一定不会失败
	logger.Infof("分支事务准备完毕")
	err = msg.DoAndSubmitDB(apUrl+"/QueryPrepared", dao.GetBarrierDB(), func(tx *sql.Tx) error {
		// 本地事务。本地事务如果成功会Submit，dtm收到Submit后会去执行分支事务；如果返回error，DTM就认为本地事务失败了，不去执行分支事务
		return postForm(bankAUrl+"/adjust_balance", url.Values{"account": []string{strconv.Itoa(req.FromUid)}, "amount": []string{strconv.Itoa(-req.Amount)}})
		// 如果返回error，则会向barrier表里插入一条记录：gid=prepareGid,branch_id=00, op=msg, reason=rollback。DTM将全局事务标记为fail
		// 如果返回nil，则会向barrier表里插入一条记录：gid=prepareGid,branch_id=00, op=msg, reason=msg。DTM将执行分支事务
		// 如果还没来得及返回就宕机了，DTM会默认是rollback，因为DTM回查时执行的QueryPrepared()函数一上来会先向barrier表里插入一条记录：gid=prepareGid,branch_id=00, op=msg, reason=rollback
	}) //DoAndSubmitDB()函数返回后，分支事务还没执行
	// os.Exit(0)  // 显式退出后，再重启，从GIN的访问日志里能看到DTM请求了/QueryPrepared接口
	if err != nil {
		ctx.String(http.StatusInternalServerError, err.Error())
		return
	}
}

func main() {
	logger.InitLog2("DEBUG", "log/dtm.log", 0, ``)
	dtmcli.SetCurrentDBType("mysql")

	// gin.SetMode(gin.ReleaseMode)
	engine := gin.Default()

	// 让dtm来调用
	g1 := engine.Group("/branch")
	g1.POST("/trans_in", dtmutil.WrapHandler2(transIn))
	// 如果本地事务成功Commit了，但是在submit给DTM之前AP宕机了。DTM会在一定超时时间之后，取出只Prepare但未Submit的msg事务，调用msg事务指定的回查服务（即这里的QueryPrepared）来判断本地事务有没有执行成功。
	g1.GET("/QueryPrepared", dtmutil.WrapHandler2(func(ctx *gin.Context) any {
		// 回查函数的固定写法
		barrier, _ := dtmcli.BarrierFromQuery(ctx.Request.URL.Query())
		return barrier.QueryPrepared(dao.GetBarrierDB()) //根据barrier判断本地事务是否执行成功
	}))

	// 让终端用户来调用
	engine.POST("/transfer", Transfer)
	engine.Run("127.0.0.1:7000")
}

// go run ./distributed_transaction/msg/ap
