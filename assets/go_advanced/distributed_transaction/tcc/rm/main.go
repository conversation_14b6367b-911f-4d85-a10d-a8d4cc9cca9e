package main

import (
	"dqq/go/advanced/distributed_transaction/dao"
	"flag"
	"log"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// 转账请求
type TransReq struct {
	Account int `form:"account" binding:"gt=0,required"` //账户
	Amount  int `form:"amount" binding:"required"`       //余额增量
}

func AdjustTrading(ctx *gin.Context) {
	var req TransReq
	err := ctx.ShouldBind(&req)
	if err != nil {
		log.Printf("参数绑定失败:%s", err)
		ctx.String(http.StatusBadRequest, "参数绑定失败")
		return
	}
	err = dao.AdjustTrading(req.Account, req.Amount)
	if err != nil {
		ctx.String(http.StatusInternalServerError, "修改TradingBalance异常")
		return
	}
	log.Printf("账户%d, TradingBalance调整量%d", req.Account, req.Amount)
	ctx.String(http.StatusOK, "")
}

func ConfirmBalance(ctx *gin.Context) {
	var req TransReq
	err := ctx.ShouldBind(&req)
	if err != nil {
		log.Printf("参数绑定失败:%s", err)
		ctx.String(http.StatusBadRequest, "参数绑定失败")
		return
	}
	err = dao.ConfirmBalance(req.Account, req.Amount)
	if err != nil {
		ctx.String(http.StatusInternalServerError, "修改账户余额异常")
		return
	}
	log.Printf("账户%d, 调整量%d", req.Account, req.Amount)
	ctx.String(http.StatusOK, "")
}

func main() {
	port := flag.Int("port", 5678, "http server port")
	flag.Parse()

	engine := gin.Default()
	engine.POST("/adjust_trading", AdjustTrading)
	engine.POST("/confirm_balance", ConfirmBalance)
	engine.Run("127.0.0.1:" + strconv.Itoa(*port))
}

// go run ./distributed_transaction/tcc/rm -port=5678
// go run ./distributed_transaction/tcc/rm -port=5679
