package main

import (
	"database/sql"
	"errors"
	"flag"
	"log"
	"strconv"

	"github.com/dtm-labs/dtm/client/dtmcli"
	"github.com/dtm-labs/dtm/dtmutil"
	"github.com/gin-gonic/gin"
)

// 转账请求
type TransReq struct {
	Account int `json:"account" binding:"gt=0,required"` //账户
	Amount  int `json:"amount" binding:"required"`       //余额增量
}

var dbConf = dtmcli.DBConf{
	Driver:   "mysql",
	Host:     "localhost",
	Port:     3306,
	User:     "tester",
	Password: "123456",
	Db:       "test",
}

func AdjustBalance(db *sql.DB, uid int, delta int) error {
	result, err := db.Exec("update bank_account set balance=balance+? where user_id=? and balance+?>=0", delta, uid, delta)
	if err != nil {
		log.Printf("修改账户余额失败:%s", err)
		return err
	}
	affected, _ := result.RowsAffected()
	if affected == 0 {
		return errors.New("账户余额将减成了负数或uid不存在")
	}
	return nil
}

func AdjustBalanceXa(ctx *gin.Context) any {
	var req TransReq
	ctx.ShouldBindJSON(&req) // 一定要使用ShouldBind，不要使用Bind。因为在XA事务中AdjustBalanceXa()函数会被多次调用，有时候是不带请求体的，如果使用Bind，GIN框架会设置Status为400并中止，这样dtm会认为本次调用失败，然后进入不断的重试。XA事务的后续步骤也得不到执行
	// 借助于数据库自带的XA事务支持，没有使用Barrier技术
	return dtmcli.XaLocalTransaction(ctx.Request.URL.Query(), dbConf, func(db *sql.DB, xa *dtmcli.Xa) error { //xaID 就是 gid + "-" + branchID
		return AdjustBalance(db, req.Account, req.Amount)
	})
}

func main() {
	port := flag.Int("port", 5678, "http server port")
	flag.Parse()

	engine := gin.Default()
	engine.POST("/adjust_balance", dtmutil.WrapHandler(AdjustBalanceXa))
	engine.Run("127.0.0.1:" + strconv.Itoa(*port))
}

// go run ./distributed_transaction/xa/rm -port=5678
// go run ./distributed_transaction/xa/rm -port=5679
