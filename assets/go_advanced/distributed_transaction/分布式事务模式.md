# SAGAS
将长事务拆分为多个短事务，如果每个短事务都成功那么全局事务就正常完成，如果某个步骤失败，则根据相反顺序一次调用补偿操作。  
SAGA是最简单、应用最广泛的分布式事务模式。

# 二阶段消息
第一阶段执行本地事务；第二阶段执行分支事务。且假设分支事务一定会成功，不需要回滚。  

# TCC
TCC分为3个阶段:
1. Try 阶段：尝试执行，完成所有业务检查（一致性）, 预留必须业务资源（准隔离性）
2. Confirm 阶段：如果所有分支的Try都成功了，则走到Confirm阶段。Confirm真正执行业务，不作任何业务检查，只使用 Try 阶段预留的业务资源
3. Cancel 阶段：如果所有分支的Try有一个失败了，则走到Cancel阶段。Cancel释放 Try 阶段预留的业务资源。  

Confirm/Cancel操作是要求最终成功的，遇见失败的情况，都是由于临时故障或者程序bug。dtm在Confirm/Cancel操作遇见失败时，会不断进行重试，直到成功。建议开发者对全局事务表进行监控，发现重试超过3次的事务，发出报警，由运维人员找开发手动处理。

## TCC只适合短事务
SAGA是一次性把事务编排信息提交给dtm，事务的执行完全由dtm进行调度。  
而TCC需要自己写代码决定各个子事务分支的前后顺序，如果某个分支的Try失败了，后面的分支就不再执行。  
各分支的执行时间不宜较长，如果时间较长，那么AP程序突然退出后再重启，全局事务只能回滚，不能继续往后执行。  

# XA
普通的transaction是单机版的数据库事务，XA是分布式版的，可以涉及多个数据库。XA一共分为两阶段：
1. 第一阶段（prepare）：即所有的参与者RM准备执行事务并锁住需要的资源。参与者ready时，向TM报告已准备就绪。
2. 第二阶段 (commit/rollback)：当事务管理者(TM)确认所有参与者(RM)都ready后，向所有参与者发送commit命令。  

目前主流的数据库基本都支持XA事务，包括mysql、oracle、sqlserver、postgre。不需要额外使用子事务屏障技术(Barrier)。
```sql
xa start '1';
update bank_account set balance=balance-1 where user_id=1 and balance-1>=0;
xa end '1';
xa prepare '1';
xa commit '1';
```
`xa end`之后，`xa prepare`之前，在同一个终端(连接)里执行数据库读写操作会报错：ERROR 1399 (XAE07): XAER_RMFAIL: The command cannot be executed when global transaction is in the  IDLE state

## 出现锁超时的根本原因
报错信息： ERROR 1205 (HY000): Lock wait timeout exceeded  
<img src="img/timeout.png" width=500/>
可以修改等待超时时间`set innodb_lock_wait_timeout = 1;`，避免长时间等待。

如果执行分布式事务的mysql crash了，或者还没有commit或rollback就关闭终端了，MySQL按照如下逻辑进行恢复：
- 如果这个xa事务commit了，那么什么也不用做。
- 如果这个xa事务还没有prepare，那么直接回滚它。
- 如果这个xa事务prepare了，还没commit，那么把它恢复到prepare的状态，由用户去决定commit 或 rollback。  

当mysql crash后重新启动之后，执行`xa recover;`，查看当前处于 prepare 状态的XA事务，然后 commit 或 rollback 它们。(注意要使用root用户)
<img src="img/recover.png" width=500/>
如果执行xa commit/rollback时可能会报错：ERROR 1399 (XAE07): XAER_RMFAIL: The command cannot be executed when global transaction is in the  NON-EXISTING state，可以打开autocommit`set autocommit=1;`后再试一次

## XA的问题
- Prepare如果执行时间较长，数据库锁一直不释放，会影响系统吞吐量，所以XA模式不适合高并发业务。
- Prepare执行成功以后就直接按成功状态返回给客户端了，Commit最终有没有执行成功客户端并不知道。需要监控dtm上那些长时间没有结束的事务。

# 应用总结
对于不需要回滚的场景，dtm 推荐使用二阶段消息，对于需要回滚的场景，dtm 推荐使用saga。  
如果业务对一致性要求高，不能接受 SAGA 模式中先修改再补偿的做法，那么可以考虑 TCC 模式。如果业务对并发的要求不高，不会出现热点数据争抢，那么可以使用 XA 模式。  
1. 秒杀场景。导致创建订单和库存扣减不是原子操作，如果两个操作中间，遇到进程crash等问题，就会导致数据不一致。采用两阶段消息法：Prepare阶段去Redis上扣减库存，当然大概率这个阶段都会失败（秒杀成功是小概率事件）；Commit阶段向Mysql里写入订单数据。  
2. 订单系统。通常一个下单操作会涉及：创建订单、扣减库存、扣减优惠券、创建支付单等等，如果这中间发生进程crash，那么会导致几个服务间数据不一致的问题。采用SAGA模式，用户只需要写好每一步的正向操作和补偿操作即可，就不需要担心数据一致性问题了。
3. 缓存一致性。缓存里的数据可能跟数据库里的不一致。采用两阶段消息法：Prepare阶段修改数据库；Commit阶段删除缓存里的数据。
