package database_test

import (
	"testing"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

func TestTraverse1(t *testing.T) {
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{PrepareStmt: true})
	if err != nil {
		panic(err)
	}

	Traverse1(db)
}

func TestTraverse2(t *testing.T) {
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{PrepareStmt: true})
	if err != nil {
		panic(err)
	}

	Traverse2(db)
}

// go test -v ./database -run=^TestTraverse -count=1

/**
=== RUN   TestTraverse1
0 1096 us
10 515 us
20 536 us
30 545 us
40 719 us
50 816 us
60 949 us
70 1066 us
80 1760 us
90 1298 us
100 595 us
total 92.646ms
=== RUN   TestTraverse2
0 565 us
10 630 us
20 0 us
30 0 us
40 0 us
50 0 us
60 0 us
70 0 us
80 502 us
90 0 us
100 0 us
total 26.5669ms
*/
