package algorithm

type TrieSearcherNode struct {
	Word     rune                       //当前节点存储的字符。byte只能表示英文字符，rune可以表示任意字符
	Children map[rune]*TrieSearcherNode //孩子节点，用一个map存储
	Term     string                     //如果Word是一个Term的结尾，则记录下该Term
	DocList  []string                   //如果Word是一个Term的结尾，则记录哪些文档里包含了这个Term
}

// 基于Trie树的搜索引擎
type TrieSearcher struct {
	root *TrieSearcherNode
}

// 向Trie树中添加一个Term
func (tree *TrieSearcher) AddTerm(term, docId string) {
	if len(term) <= 1 {
		return
	}
	words := []rune(term)

	if tree.root == nil {
		tree.root = new(TrieSearcherNode)
	}

	tree.root.add(words, term, docId, 0)
}

// add 把words[beginIndex:]插入到Trie树中
func (node *TrieSearcherNode) add(words []rune, term, docId string, beginIndex int) {
	if beginIndex >= len(words) { //words已经遍历完了
		node.Term = term
		node.DocList = append(node.DocList, docId) //该Term下多了一个文档
		return
	}

	if node.Children == nil {
		node.Children = make(map[rune]*TrieSearcherNode)
	}

	word := words[beginIndex] //把这个word放到node的子节点中
	if child, exists := node.Children[word]; !exists {
		newNode := &TrieSearcherNode{Word: word}
		node.Children[word] = newNode
		newNode.add(words, term, docId, beginIndex+1) //递归
	} else {
		child.add(words, term, docId, beginIndex+1) //递归
	}
}

// 根据term检索文档
func (tree *TrieSearcher) Search(term string) []string {
	if tree.root == nil || len(tree.root.Children) == 0 {
		return nil
	}
	words := []rune(term)
	firstWord := words[0]
	if child, exists := tree.root.Children[firstWord]; exists {
		end := child.walk(words, 0) //找到prefix的终点--end
		if end != nil && len(end.Term) > 0 {
			return end.DocList
		}

	}
	return nil
}

// walk words[0]就是当前节点上存储的字符，按照words的指引顺着树往下走，最终返回words最后一个字符对应的节点
func (node *TrieSearcherNode) walk(words []rune, beginIndex int) *TrieSearcherNode {
	if beginIndex == len(words)-1 {
		return node
	}
	beginIndex += 1
	word := words[beginIndex]
	if child, exists := node.Children[word]; exists {
		return child.walk(words, beginIndex)
	} else {
		return nil
	}
}
