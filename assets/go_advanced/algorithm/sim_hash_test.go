package algorithm_test

import (
	"fmt"
	"strings"
	"testing"
)

func TestSimHash(t *testing.T) {
	hash1, _ := SimHash(strings.Split("联系|确保|客户|了解|符合|有关|规则|的|进口|清关|信息|以|专业|礼貌|及|乐于|帮忙|的|态度|协助|客户|解决|进口|清关|流程|中|产生|的|问题|面对|争议", "|"), nil)
	hash2, _ := SimHash(strings.Split("联系|确保|客户|了解|符合|有关|规则|进口|清关|信息|以|专业|礼貌|及|乐于|帮忙|的|态度|协助|客户|解决|进口|清关|流程|中|产生|的|问题|面对|争议", "|"), nil)
	hash3, _ := SimHash(strings.Split("联系|确保|客户|了解|符合|有关|规则|进口|清关|信息|专业|礼貌|及|乐于|帮忙|的|态度|协助|客户|解决|进口|清关|流程|中|产生|的|问题|面对|争议", "|"), nil)
	hash4, _ := SimHash(strings.Split("联系|确保|客户|了解|符合|有关|规则|进口|清关|信息|专业|礼貌|及|乐于|的|态度|协助|客户|解决|进口|清关|流程|中|产生|的|问题|面对|争议", "|"), nil)
	n := 8
	segs := SplitUint64(hash1, n)
	for i := 0; i < n; i++ {
		fmt.Printf("%08b ", segs[i])
	}
	fmt.Println()
	segs = SplitUint64(hash2, n)
	for i := 0; i < n; i++ {
		fmt.Printf("%08b ", segs[i])
	}
	fmt.Println()
	segs = SplitUint64(hash3, n)
	for i := 0; i < n; i++ {
		fmt.Printf("%08b ", segs[i])
	}
	fmt.Println()
	segs = SplitUint64(hash4, n)
	for i := 0; i < n; i++ {
		fmt.Printf("%08b ", segs[i])
	}
	fmt.Println()

	fmt.Printf("1和2的距离 %d\n", HammingDistance(hash1, hash2))
	fmt.Printf("1和3的距离 %d\n", HammingDistance(hash1, hash3))
	fmt.Printf("1和4的距离 %d\n", HammingDistance(hash1, hash4))

}

// go test -v ./algorithm -run=^TestSimHash$ -count=1
