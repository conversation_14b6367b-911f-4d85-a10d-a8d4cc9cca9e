package algorithm

import (
	"sort"
)

type TrieSuggestionNode struct {
	Word     rune                         //当前节点存储的字符。byte只能表示英文字符，rune可以表示任意字符
	Children map[rune]*TrieSuggestionNode //孩子节点，用一个map存储
	Term     string                       //如果Word是一个Term的结尾，则记录下该Term
	Hot      int                          //term的热度
}

// Trie树
type TrieSuggestion struct {
	root *TrieSuggestionNode
}

// 向Trie树中添加一个Term
func (tree *TrieSuggestion) AddTerm(term string, hot int) {
	if len(term) <= 1 {
		return
	}
	words := []rune(term)

	if tree.root == nil {
		tree.root = new(TrieSuggestionNode)
	}

	tree.root.add(words, term, 0, hot)
}

// add 把words[beginIndex:]插入到Trie树中
func (node *TrieSuggestionNode) add(words []rune, term string, beginIndex int, hot int) {
	if beginIndex >= len(words) { //words已经遍历完了
		node.Term = term
		node.Hot = hot
		return
	}

	if node.Children == nil {
		node.Children = make(map[rune]*TrieSuggestionNode)
	}

	word := words[beginIndex] //把这个word放到node的子节点中
	if child, exists := node.Children[word]; !exists {
		newNode := &TrieSuggestionNode{Word: word}
		node.Children[word] = newNode
		newNode.add(words, term, beginIndex+1, hot) //递归
	} else {
		child.add(words, term, beginIndex+1, hot) //递归
	}
}

type SugItem struct {
	Term string
	Hot  int
}

// 检索满足前缀的所有Term
func (tree *TrieSuggestion) Retrieve(prefix string) []*SugItem {
	if tree.root == nil || len(tree.root.Children) == 0 {
		return nil
	}
	words := []rune(prefix)
	firstWord := words[0]
	if child, exists := tree.root.Children[firstWord]; exists {
		end := child.walk(words, 0) //找到prefix的终点--end
		if end == nil {
			return nil
		} else {
			items := make([]*SugItem, 0, 100)
			end.traverseTerms(&items) //在以end为根节点的子树上遍历所有的term
			sort.Slice(items, func(i, j int) bool {
				return items[i].Hot > items[j].Hot //按Hot降序排序
			})
			return items
		}
	} else {
		return nil
	}
}

// walk words[0]就是当前节点上存储的字符，按照words的指引顺着树往下走，最终返回words最后一个字符对应的节点
func (node *TrieSuggestionNode) walk(words []rune, beginIndex int) *TrieSuggestionNode {
	if beginIndex == len(words)-1 {
		return node
	}
	beginIndex += 1
	word := words[beginIndex]
	if child, exists := node.Children[word]; exists {
		return child.walk(words, beginIndex)
	} else {
		return nil
	}
}

// traverseTerms 遍历一个node下面所有的term。注意要传数组的指针，才能真正修改这个数组
func (node *TrieSuggestionNode) traverseTerms(terms *[]*SugItem) {
	if len(node.Term) > 0 {
		*terms = append(*terms, &SugItem{Term: node.Term, Hot: node.Hot})
	}
	for _, child := range node.Children {
		child.traverseTerms(terms)
	}
}
