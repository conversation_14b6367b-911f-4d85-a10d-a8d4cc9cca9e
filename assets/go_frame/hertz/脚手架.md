# server端
1. 安装hz工具
```Shell
go get github.com/cloudwego/hertz
go install github.com/cloudwego/hertz/cmd/hz@latest
```
2. 在idl目录下编写math.proto
```Shell
cd hertz
mkdir idl
```
descriptor.proto和api.proto直接拷贝你的项目里面。[descriptor.proto](https://github.com/protocolbuffers/protobuf/blob/main/src/google/protobuf/descriptor.proto)是google提供的，[api.proto](https://github.com/cloudwego/hertz/blob/develop/cmd/hz/protobuf/api/api.proto)是hertz提供的。
3. 自动生成代码
```Shell
hz new -module dqq/go/math -idl idl/math.proto -force
go mod tidy
```
- 根据-module自动生成go.mod文件，内含module名称。  
- 根据-idl指定的proto文件生成biz目录和main.go
- 如果math.proto又依赖了其他proto文件，且其他proto文件不在idl/目录下，则需要通过-I选项来指定其他proto文件在哪个目录下（跟protoc的使用方式相同） 
- -force表示如果之前用hz生成过，则把之前的删掉，重新生成。如果不希望清空老目录，请使用以下命令：  
```Shell
hz update -idl idl/math.proto
hz update -idl idl/student.proto
```
4. 填充handler  
修改biz/handler/math_service/math.go  
在Add()函数里`resp := new(math_service.AddResponse)`的下面加一行`resp.Sum = req.Left + req.Right`  
在Sub()函数里`resp := new(math_service.SubResponse)`的下面加一行`resp.Diff = req.Left - req.Right`  
5. 运行web server  
`go run .`
6. 测试
再开一个终端`cd hertz && go run ./my_client`

# client端
```Shell
hz client -client_dir hertz_client -idl idl/math.proto
hz client -client_dir hertz_client -idl idl/student.proto
```