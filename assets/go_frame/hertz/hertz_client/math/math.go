// Code generated by hertz generator.

package math

import (
	"context"
	"fmt"

	math_service "dqq/go/math/biz/model/math_service"
	"github.com/cloudwego/hertz/pkg/common/config"
	"github.com/cloudwego/hertz/pkg/protocol"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
)

type Client interface {
	Add(context context.Context, req *math_service.AddRequest, reqOpt ...config.RequestOption) (resp *math_service.AddResponse, rawResponse *protocol.Response, err error)

	Sub(context context.Context, req *math_service.SubRequest, reqOpt ...config.RequestOption) (resp *math_service.SubResponse, rawResponse *protocol.Response, err error)
}

type MathClient struct {
	client *cli
}

func NewMathClient(hostUrl string, ops ...Option) (Client, error) {
	opts := getOptions(append(ops, withHostUrl(hostUrl))...)
	cli, err := newClient(opts)
	if err != nil {
		return nil, err
	}
	return &MathClient{
		client: cli,
	}, nil
}

func (s *MathClient) Add(context context.Context, req *math_service.AddRequest, reqOpt ...config.RequestOption) (resp *math_service.AddResponse, rawResponse *protocol.Response, err error) {
	httpResp := &math_service.AddResponse{}
	ret, err := s.client.r().
		setContext(context).
		setQueryParams(map[string]interface{}{
			"left":  req.GetLeft(),
			"right": req.GetRight(),
		}).
		setPathParams(map[string]string{}).
		addHeaders(map[string]string{}).
		setFormParams(map[string]string{}).
		setFormFileParams(map[string]string{}).
		setBodyParam(req).
		setRequestOption(reqOpt...).
		setResult(httpResp).
		execute("GET", "/add")
	if err != nil {
		return nil, nil, err
	}

	resp = httpResp
	rawResponse = ret.rawResponse
	return resp, rawResponse, nil
}

func (s *MathClient) Sub(context context.Context, req *math_service.SubRequest, reqOpt ...config.RequestOption) (resp *math_service.SubResponse, rawResponse *protocol.Response, err error) {
	httpResp := &math_service.SubResponse{}
	ret, err := s.client.r().
		setContext(context).
		setQueryParams(map[string]interface{}{}).
		setPathParams(map[string]string{}).
		addHeaders(map[string]string{}).
		setFormParams(map[string]string{
			"left":  fmt.Sprint(req.GetLeft()),
			"right": fmt.Sprint(req.GetRight()),
		}).
		setFormFileParams(map[string]string{}).
		setRequestOption(reqOpt...).
		setResult(httpResp).
		execute("POST", "/sub")
	if err != nil {
		return nil, nil, err
	}

	resp = httpResp
	rawResponse = ret.rawResponse
	return resp, rawResponse, nil
}

var defaultClient, _ = NewMathClient("")

func ConfigDefaultClient(ops ...Option) (err error) {
	defaultClient, err = NewMathClient("", ops...)
	return
}

func Add(context context.Context, req *math_service.AddRequest, reqOpt ...config.RequestOption) (resp *math_service.AddResponse, rawResponse *protocol.Response, err error) {
	return defaultClient.Add(context, req, reqOpt...)
}

func Sub(context context.Context, req *math_service.SubRequest, reqOpt ...config.RequestOption) (resp *math_service.SubResponse, rawResponse *protocol.Response, err error) {
	return defaultClient.Sub(context, req, reqOpt...)
}
