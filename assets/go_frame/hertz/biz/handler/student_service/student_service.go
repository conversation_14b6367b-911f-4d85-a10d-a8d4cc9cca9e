// Code generated by hertz generator.

package student_service

import (
	"context"
	"net/url"

	student_service "dqq/go/math/biz/model/student_service"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol/consts"
)

// Query .
// @router /student [POST]
func Query(ctx context.Context, c *app.RequestContext) {
	var err error
	var req student_service.Student
	// err = c.BindAndValidate(&req) // 绑定+校验
	err = c.Bind(&req) //参数绑定优先级  path > form > query > cookie > header > json > raw_body
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}
	err = c.Validate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp := new(student_service.Info)
	resp.DetailInfo = req.Name + " live in " + req.Address

	c.J<PERSON>N(consts.StatusOK, resp)
}

// Restful .
// @router /student/:name/*addr [GET]
func Restful(ctx context.Context, c *app.RequestContext) {
	var err error
	var req student_service.Student
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp := new(student_service.Info)
	resp.DetailInfo = req.Name + " live in " + req.Address

	c.JSON(consts.StatusOK, resp)
}

// PostForm .
// @router /student/form [POST]
func PostForm(ctx context.Context, c *app.RequestContext) {
	var err error
	var req student_service.Student
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp := new(student_service.Info)
	resp.DetailInfo = req.Name + " live in " + req.Address

	c.JSON(consts.StatusOK, resp)
}

// PostJson .
// @router /student/json [POST]
func PostJson(ctx context.Context, c *app.RequestContext) {
	var err error
	var req student_service.Student
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp := new(student_service.Info)
	resp.DetailInfo = req.Name + " live in " + req.Address

	c.JSON(consts.StatusOK, resp)
}

// PostPb .
// @router /student/pb [POST]
func PostPb(ctx context.Context, c *app.RequestContext) {
	var err error
	var req student_service.Student
	err = c.BindProtobuf(&req) //注意：要使用BindProtobuf
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp := new(student_service.Info)
	resp.DetailInfo = req.Name + " live in " + req.Address

	c.JSON(consts.StatusOK, resp)
}

// Header .
// @router /student/header [GET]
func Header(ctx context.Context, c *app.RequestContext) {
	var err error
	var req student_service.Student
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp := new(student_service.Info)
	resp.DetailInfo = req.Name + " live in " + req.Address

	c.JSON(consts.StatusOK, resp)
}

// Cookie .
// @router /student/cookie [GET]
func Cookie(ctx context.Context, c *app.RequestContext) {
	var err error
	var req student_service.Student
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp := new(student_service.Info)
	name, _ := url.QueryUnescape(req.Name)
	addr, _ := url.QueryUnescape(req.Address)
	resp.DetailInfo = name + " live in " + addr

	c.JSON(consts.StatusOK, resp)
}
