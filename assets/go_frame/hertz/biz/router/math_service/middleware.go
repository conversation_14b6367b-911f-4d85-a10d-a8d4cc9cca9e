// Code generated by hertz generator.

package math_service

import (
	"github.com/cloudwego/hertz/pkg/app"
)

func rootMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _addMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _subMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _add2Mw() []app.HandlerFunc {
	// your code...
	return nil
}

func _leftMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _add20Mw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getstudentMw() []app.HandlerFunc {
	// your code...
	return nil
}
