package xorm_test

import (
	"log/slog"
	"os"
	"sync"
	"testing"
)

var (
	engine = CreateEngine("localhost", "test", "tester", "123456", 3306)
)

func init() {
	slog.SetDefault(
		slog.New(
			slog.NewTextHandler(os.Stdout,
				&slog.HandlerOptions{
					AddSource: true,
				},
			),
		),
	)
}

func TestXormQuickStart(t *testing.T) {
	XormQuickStart()
}

func TestCreate(t *testing.T) {
	Create(engine)
}

func TestDelete(t *testing.T) {
	Delete(engine)
}

func TestUpdate(t *testing.T) {
	Update(engine)
}

func TestUpdateByVersion(t *testing.T) {
	const P = 10
	wg := sync.WaitGroup{}
	wg.Add(P)
	for i := 0; i < P; i++ {
		go func() {
			defer wg.Done()
			UpdateByVersion(engine)
		}()
	}
	wg.Wait()
}

func TestRead(t *testing.T) {
	Read(engine)
}

func TestReadWithStatistics(t *testing.T) {
	ReadWithStatistics(engine)
}

func TestTransaction(t *testing.T) {
	Transaction(engine)
}

func TestRawSql(t *testing.T) {
	RawSelect(engine)
	RawExec(engine)
}

func TestHandleError(t *testing.T) {
	HandleError(engine)
}

// go test -v ./orm/xorm -run=^TestXormQuickStart$ -count=1
// go test -v ./orm/xorm -run=^TestCreate$ -count=1
// go test -v ./orm/xorm -run=^TestDelete$ -count=1
// go test -v ./orm/xorm -run=^TestUpdate$ -count=1
// go test -v ./orm/xorm -run=^TestUpdateByVersion$ -count=1
// go test -v ./orm/xorm -run=^TestRead$ -count=1
// go test -v ./orm/xorm -run=^TestReadWithStatistics$ -count=1
// go test -v ./orm/xorm -run=^TestTransaction$ -count=1
// go test -v ./orm/xorm -run=^TestRawSql$ -count=1
// go test -v ./orm/xorm -run=^TestHandleError$ -count=1
