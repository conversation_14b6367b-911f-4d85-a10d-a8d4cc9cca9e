package database_test

import (
	"crypto/md5"
	"dqq/go/frame/post/util"
	"encoding/hex"
	"fmt"
	"testing"
)

var (
	uid = 5
)

func hash(pass string) string {
	hasher := md5.New()
	hasher.Write([]byte(pass))
	digest := hasher.Sum(nil)
	return hex.EncodeToString(digest) //md5的输出是128bit，十六进制编码之后长度是32
}

func init() {
	util.InitSlog("../../../log/post.log")
	ConnectPostDB("../../conf", "db", util.YAML, "../../../log")
}

func TestRegistUser(t *testing.T) {
	err := RegistUser("大乔乔", hash("123456"))
	if err != nil {
		t.Fatal(err)
	} else {
		fmt.Printf("注册成功\n")
	}

	err = RegistUser("大乔乔", hash("123456"))
	if err != nil {
		fmt.Printf("注册失败: %s\n", err)
	} else {
		fmt.Println("重复注册成功！")
		t.Fail()
	}
}

func TestGetUserById(t *testing.T) {
	user := GetUserById(uid)
	if user == nil {
		t.Fatalf("could not get user by id %d", uid)
	}

	tmpUid := -1
	user = GetUserById(tmpUid)
	if user != nil {
		t.Fatalf("get user by id %d, user %v", tmpUid, *user)
	}
}

func TestGetUserByName(t *testing.T) {
	user := GetUserByName("大乔乔")
	if user == nil {
		t.Fail()
	}

	user = GetUserByName("ok")
	if user != nil {
		t.Fail()
	}
}

func TestUpdateUserName(t *testing.T) {
	err := UpdateUserName(uid, "zcy")
	if err != nil {
		t.Fatal(err)
	}
	user := GetUserById(uid)
	if user == nil {
		t.Fail()
		return
	}
	if user.Name != "zcy" {
		t.Fatalf("user name %s", user.Name)
	}

	tmpUid := -1
	err = UpdateUserName(tmpUid, "zcy")
	if err == nil {
		t.Fatal(err)
	}
}

func TestUpdatePassword(t *testing.T) {
	err := UpdatePassword(uid, hash("abcdefg"), hash("123456"))
	if err != nil {
		t.Fatal(err)
	}
	user := GetUserById(uid)
	if user == nil {
		t.Fail()
		return
	}
	if user.PassWord != hash("abcdefg") {
		t.Fatalf("user password %s", user.PassWord)
	}

	err = UpdatePassword(uid, hash("abcdefg"), hash("123456"))
	if err == nil {
		t.Fatal(err)
	}
}

func TestLogOffUser(t *testing.T) {
	err := LogOffUser(uid)
	if err != nil {
		t.Fatal(err)
	}

	user := GetUserById(uid)
	if user != nil {
		t.Fail()
		return
	}

	err = LogOffUser(uid)
	if err == nil {
		t.Fatalf("用户%d第二次删除成功！", uid)
	} else {
		fmt.Printf("用户%d第二次删除失败：%s", uid, err)
	}
}

// go test -v ./post/database/gorm -run=^TestRegistUser$ -count=1
// go test -v ./post/database/gorm -run=^TestGetUserById$ -count=1
// go test -v ./post/database/gorm -run=^TestGetUserByName$ -count=1
// go test -v ./post/database/gorm -run=^TestUpdateUserName$ -count=1
// go test -v ./post/database/gorm -run=^TestUpdatePassword$ -count=1
// go test -v ./post/database/gorm -run=^TestLogOffUser$ -count=1
