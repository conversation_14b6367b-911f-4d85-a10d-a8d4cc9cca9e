请为Go语言后端学习路线图的模块1创建详细的多章节文档结构。具体要求如下：

1. **文件结构组织**：
    - 在项目根目录下创建 `module_01` 文件夹
    - 在 `module_01` 下为每个章节创建独立的文件夹，命名格式为 `chapter_01`, `chapter_02`, 等等
    - 每个章节文件夹包含以下标准结构：
      ```
      module_01/
      ├── chapter_01/
      │   ├── README.md          # 章节主要内容和说明
      │   ├── examples/          # 代码示例文件
      │   ├── exercises/         # 练习题和解答
      │   ├── resources/         # 相关资源文件
      │   └── notes/             # 学习笔记模板
      ├── chapter_02/
      │   └── [相同结构]
      └── ...
      ```

2. **内容要求**：
    - 每个章节的 README.md 应包含：章节目标、核心概念、学习要点、参考资料链接
    - 将所有代码示例从 README.md 中抽离到独立的 `.go` 文件中，存放在 `examples/` 目录
    - 在 README.md 中使用相对路径引用这些代码文件，格式如：`[示例代码](./examples/hello_world.go)`
    - 为每个代码示例添加详细的注释说明

3. **章节划分**：
    - 根据 `golang_backend_learning_roadmap.md` 中模块1的内容进行合理的章节划分
    - 确保每个章节的学习量适中，便于循序渐进

4. **代码组织**：
    - 所有 `.go` 文件应该是可以直接运行的完整示例
    - 为复杂示例提供 `go.mod` 文件
    - 在 `exercises/` 目录中提供练习题目和参考答案

请先分析现有的 `golang_backend_learning_roadmap.md` 文件内容，然后创建完整的模块1文档结构。